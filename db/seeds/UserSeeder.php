<?php

use Phinx\Seed\AbstractSeed;

class UserSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'CountrySeeder',
            'StateSeeder',
        ]);
    }

    public function run()
    {
        parent::run();

        // Hash of 'Sh1pE@rly'
        $password = '3be70f15222270d5c843692c1247b57b';

        // Assign fbpixel dev access_token and test_event_code to users with the dev `fbpixelId` created before seeders.
        $this->getAdapter()
            ->getQueryBuilder()
            ->update('ship_users')
            ->set('fbpixel_access_token', 'EAAjoaGvHGOIBOy8aUDfsn5OrqZA2ucli2G5MBlfwxeO22cynismjKtTNVe7R8r1jSNAZBxNzvjM55WsuHPIAjbJZCvt3h6cvH2LZCH8jQufrd8fYime1qB5AZB4k5knJBZBXjyyvAIbIsOUS5XfpHxiPHWkV8z32wteBZBV5ozrRD7nWsiZAwyRDZCWJal13j3gu4DwZDZD')
            ->set('fbpixel_test_event_code', 'TEST1109')
            ->where(['fbpixelId' => '904599178183609'])
            ->execute();

        $data = [
            [
                'id' => '24',
                'uuid' => '5f9b10e5-4fe8-4d8e-a5c6-4d0191c2de43',
                'email_address' => '<EMAIL>',
                'password' => $password,
                'company_name' => 'Fake BrandStaff',
                'status' => 'Active',
                'user_type' => 'BrandStaff',
                'setup_status' => true,
                'created' => '2020-10-28 19:59:27',
                'modified' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '25',
                'uuid' => '08c381e4-1ae7-11eb-b37f-f832e4f18bd5',
                'email_address' => '<EMAIL>',
                'password' => $password,
                'company_name' => 'Test BrandStaff',
                'status' => 'Active',
                'user_type' => 'BrandStaff',
                'setup_status' => true,
                'created' => '2020-10-28 19:59:27',
                'modified' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '26',
                'uuid' => '57bc657c-1ae7-11eb-b37f-f832e4f18bd5',
                'email_address' => '<EMAIL>',
                'password' => $password,
                'company_name' => 'WooCommerce BrandStaff',
                'status' => 'Active',
                'user_type' => 'BrandStaff',
                'setup_status' => true,
                'created' => '2020-10-28 19:59:27',
                'modified' => '2020-10-28 19:59:27',
            ],
            27 => [
                'id' => '27',
                'uuid' => '61114139-61c8-4980-91af-381e91c2de43',
                'email_address' => '<EMAIL>',
                'password' => $password,
                'company_name' => 'Sirisha Test Branch',
                'company_description' => '',
                'company_code' => '',
                'status' => 'Active',
                'Branch' => '15',
                'user_type' => 'Retailer',
                'staff_role' => null,
                'minOrderAmount' => '0',
                'avatar' => null,
                'shipping_infographic' => null,
                'flatshipping' => '0',
                'site_type' => null,
                'shop_url' => null,
                'shop_home_url' => '',
                'shop_cart_url' => '',
                'shop_app_id' => null,
                'gtm_container_id' => '',
                'enable_ga_tracking' => '1',
                'ga_tracking_id' => '',
                'google_conversion_id' => '',
                'google_conversion_label' => '',
                'fbpixelId' => '',
                'fbpixel_access_token' => '',
                'fbpixel_test_event_code' => '',
                'currency_code' => 'CAD',
                'api_key' => null,
                'secret_key' => null,
                'webhook_shared_secret' => '',
                'klaviyo_public_key' => null,
                'inventory_apiuser' => '',
                'inventory_password' => '',
                'Inventory_Emp_ID' => '',
                'Inventory_Reg_ID' => '',
                'Inventory_Store_ID' => '',
                'defaultTax' => '13.000000',
                'enable_affirm_financing' => '0',
                'tax_id_number' => null,
                'enable_install' => '0',
                'install_rate_option' => '0',
                'install_flat_rate' => '0.00',
                'install_hourly_rate' => '0.00',
                'otherInventory' => 'None',
                'last_login' => '2021-08-09 14:57:42',
                'created' => '2021-08-09 14:52:41',
                'modified' => '2021-08-09 14:59:41',
                'timezone' => 'America/Thunder_Bay',
                'address' => '644 Winnipeg Ave_,_',
                'store_timing' => '{"1":{"start":"9:30 AM","end":"9:00 PM","Closed":"0"},"2":{"start":"9:30 AM","end":"9:00 PM","Closed":"0"},"3":{"start":"9:30 AM","end":"9:00 PM","Closed":"0"},"4":{"start":"9:30 AM","end":"9:00 PM","Closed":"0"},"5":{"start":"9:30 AM","end":"9:00 PM","Closed":"0"},"6":{"start":"9:30 AM","end":"6:00 PM","Closed":"0"},"7":{"start":"12:00 PM","end":"5:00 PM","Closed":"0"}}',
                'country_id' => '39',
                'state_id' => '611',
                'city' => 'Thunder Bay',
                'zipcode' => 'P7B 2P9',
                'latitude' => '48.4143423',
                'longitude' => '-89.2410428',
                'inventory_type' => 'other',
                'permission' => '',
                'send_store_emails_to_master_only' => '0',
                'do_not_list' => '0',
                'instore' => '1',
                'shipment' => '0',
                'shipment_type' => '',
                'shipment_option' => '',
                'free_shipping' => null,
                'shipfromstore_instock_only' => '0',
                'ship_from_store_double_ship' => '0',
                'shiptostore_free_shipping' => '0',
                'shiptostore_tax' => '0',
                'local_delivery' => '0',
                'local_delivery_shipping' => null,
                'local_delivery_shipping_title' => '',
                'local_delivery_shipping_option' => '0',
                'local_delivery_percent_source' => 'product_price',
                'local_delivery_no_retailer_listing' => '0',
                'enable_wholesale_topup' => '0',
                'wholesale_topup_infographic' => null,
                'subscription_plan' => null,
                'coupon_code' => null,
                'store_associate_pin' => null,
                'setup_status' => '1',
                'revenue_model' => null,
                'retailer_default_amount' => '0.99',
                'retailer_revenue_maximum' => null,
                'store_associate_default_amount' => '0.00',
                'in-store_radius' => null,
                'ship_from_store_radius' => null,
                'admin_sell_direct' => '0',
                'sell_direct_percentage' => null,
                'sell_direct' => '0',
                'sell_direct_authorize' => '0',
                'enable_b2b_cart' => '0',
                'b2b_hero_image' => null,
                'b2b_product_display_list' => '0',
                'enable_online_to_offline' => '1',
                'enable_velofix' => '0',
                'brand_revenue_model' => null,
                'brand_direct_default_amount' => '0.99',
                'brand_revenue_maximum' => null,
                'splitpayment_percentage' => '2.00',
                'enable_stripe_payment_request' => '0',
                'require_terms_of_service' => '0',
                'enable_product_discovery' => '0',
                'brand_abandon_cart' => '0',
                'brand_abandon_cart_message' => null,
                'brand_abandon_cart_subject' => null,
                'brand_accent_color' => null,
                'vend_access_token_expires' => null,
                'vend_refresh_access_token' => null,
                'ship_to_door_title' => '',
                'ship_to_door_message' => '',
                'ship_to_door_backorder_message' => '',
                'ship_from_store_title' => '',
                'instore_pickup_title' => '',
                'instore_pickup_message' => '',
                'ship_to_store_message' => '',
                'ship_to_store_backorder_message' => '',
                'ship_to_store_noretailer_message' => '',
                'local_install_title' => null,
                'local_install_in_stock_message' => null,
                'local_install_no_stock_message' => null,
                'local_delivery_title' => '',
                'local_delivery_in_stock_message' => '',
                'local_delivery_no_stock_message' => '',
                'local_delivery_backorder_message' => '',
                'local_delivery_noretailer_message' => '',
            ],
        ];

        //TODO Should be declared as default values in the users table
        $requiredDefaultPlaceholders = [
            'company_description' => '',
            'company_code' => '',
            'inventory_apiuser' => '',
            'inventory_password' => '',
            'store_timing' => '',
            'permission' => '',
            'instore' => '0',
            'shipment' => '0',
            'shipment_type' => '',
            'shipment_option' => '',
            'ship_to_door_title' => '',
            'ship_to_door_message' => '',
            'ship_to_door_backorder_message' => '',
            'ship_from_store_title' => '',
            'instore_pickup_title' => '',
            'instore_pickup_message' => '',
            'ship_to_store_message' => '',
            'ship_to_store_backorder_message' => '',
            'ship_to_store_noretailer_message' => '',
            'local_delivery_title' => '',
            'local_delivery_in_stock_message' => '',
            'local_delivery_no_stock_message' => '',
            'local_delivery_backorder_message' => '',
            'local_delivery_noretailer_message' => '',
            'customer_accepts_marketing_label' => '',
        ];
        foreach ($data as &$user) {
            $user += $requiredDefaultPlaceholders;
        }

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_users')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('users')
            ->insert($data)
            ->save();
    }
}
