<?php

use Phinx\Seed\AbstractSeed;

class CourierSeeder extends AbstractSeed
{
    public function run()
    {
        parent::run();
        $data = [
            [
                'id' => '370',
                'slug' => 'exists courier slug',
                'name' => 'exists courier name',
                'phone' => 'exists courier phone',
                'other_name' => 'exists courier other',
                'web_url' => 'www.existscourier.com',
                'deleted' => false,
                'created_at' => '2020-11-25 21:35:18',
                'updated_at' => '2020-11-25 21:35:18',
            ],
            [
                'id' => '371',
                'slug' => 'deleted courier slug',
                'name' => 'deleted courier name',
                'phone' => 'deleted courier phone',
                'other_name' => 'deleted courier other',
                'web_url' => 'www.deletedcourier.com',
                'deleted' => true,
                'created_at' => '2021-04-27 14:07:18',
                'updated_at' => '2021-04-27 14:07:18',
            ],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_couriers')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('couriers')
            ->insert($data)
            ->save();
    }
}
