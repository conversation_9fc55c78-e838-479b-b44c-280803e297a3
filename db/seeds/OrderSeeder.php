<?php

use Phinx\Seed\AbstractSeed;

class OrderSeeder extends AbstractSeed
{
    const CASE_IDS = [
        'split_payment' => '1',
        'split_payment_refunded' => '2',
        'purchase_order_on_file' => '3',
        'sell_direct' => '4',
        'wc_sell_direct' => '5',
        'api_sell_direct' => '6',
        'api_wholesale' => '7',
        'api_wholesale_unconfirmed_price' => '8',
        'wholesale_credit_order' => '9',
    ];
    const STRIPE_ACCOUNT_BRAND = 'acct_194DCmJKdns8gzMQ';
    const STRIPE_ACCOUNT_RETAILER = 'acct_17FVVxLBFYPhk3Kp';

    public static function deriveOrderID($orderId)
    {
        return '#SE' . str_pad(12501 + $orderId, 7, '0', STR_PAD_LEFT);
    }

    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'DiscountSeeder',
            'InventoryTransferSeeder',
            'PreorderSeeder',
            'ProductSeeder',
            'WarehouseSeeder',
            // Dependencies not yet implemented
            //'CustomerSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        // Order ids are hardcoded here but avoid referencing their values
        // outside of seeder classes in case they change
        $data = array_values([
            'split_payment' => [
                'Order' => [
                    'id' => static::CASE_IDS['split_payment'],
                    'user_id' => '8',
                    'retailer_id' => '7',
                    'branch_id' => null,
                    'store_associate_id' => null,
                    'sales_rep_id' => '21',
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4037',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => null,
                    'orderID' => static::deriveOrderID(static::CASE_IDS['split_payment']),
                    'parentOrderId' => null,
                    'source_id' => null,
                    'orderNO' => null,
                    'source_order_name' => null,
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => 'In_store',
                    'subType' => 'nonstock',
                    'is_install' => false,
                    'secretcode' => 'UW5NFZ',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => '2020-03-01 00:00:00',
                    'dealer_qty_ordered' => '{"products":{"21":{"quantity":2,"dealer_price":"499.00"},"23":{"quantity":2,"dealer_price":"100.00"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"25.59","shipment_date":"2020-03-01 00:00:00"}',
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'Not picked up',
                    'fulfillment_status' => 'fulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'stripe',
                    'payment_method_subtype' => 'card',
                    'payment_captured_at' => '2020-02-29 23:59:59',
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '3519.51',
                    'total_discount' => '800.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '0',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '312.87',
                    'tax_included' => false,
                    'shipping_amount' => '6.66',
                    'transactionID' => 'ch_1GO6lpLBFYPhk3KpV3E0f65w',
                    'stripe_account' => static::STRIPE_ACCOUNT_RETAILER,
                    'balance_transaction_id' => 'txn_1GO6u8LBFYPhk3KpE1ZwLew3',
                    'stripe_fees' => '79.17',
                    'risk_level' => 'not_assessed',
                    'discount_code' => 'BIKESFORDAYS',
                    'discount_id' => null,
                    'billing_firstname' => 'Aron',
                    'billing_lastname' => 'Schmidt',
                    'shipping_company_name' => null,
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'Ontario',
                    'shipping_country' => 'ca',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '48.400059',
                    'longitude' => '-89.269111',
                    'shipping_telephone' => '(*************',
                    'shipping_statecode' => '611',
                    'shipping_countrycode' => '39',
                    'total_qty_ordered' => null,
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '145.93',
                    'retailerAmount' => '3477.58',
                    'card_type' => 'Visa',
                    'last_four_digit' => '4242',
                    'fraud_check_cvc' => 'pass',
                    'fraud_check_address' => 'pass',
                    'fraud_check_postal_code' => 'pass',
                    'notes' => null,
                    'created_at' => '2020-02-29 23:59:58',
                    'updated_at' => '2020-03-01 00:00:00',
                ],
                'OrderProduct' => [
                    [
                        'product_id' => '21',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '234.00',
                        'total_price' => '2400.00',
                        'total_discount' => '600',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2020-02-29 23:59:58',
                        'modified' => '2020-02-29 23:59:58',
                    ],
                    [
                        'product_id' => '23',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '78.00',
                        'total_price' => '799.98',
                        'total_discount' => '200',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2020-02-29 23:59:58',
                        'modified' => '2020-02-29 23:59:58',
                    ],
                ],
            ],
            'split_payment_refunded' => [
                'Order' => [
                    'id' => static::CASE_IDS['split_payment_refunded'],
                    'user_id' => '8',
                    'retailer_id' => '7',
                    'branch_id' => null,
                    'store_associate_id' => '20',
                    'sales_rep_id' => '21',
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4037',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => null,
                    'orderID' => static::deriveOrderID(static::CASE_IDS['split_payment_refunded']),
                    'parentOrderId' => null,
                    'source_id' => null,
                    'orderNO' => null,
                    'source_order_name' => null,
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => 'In_store',
                    'subType' => 'nonstock',
                    'is_install' => false,
                    'secretcode' => 'UW5NFZ',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => '2020-03-15 23:59:59',
                    'dealer_qty_ordered' => '{"products":{"21":{"quantity":2,"dealer_price":"499.00"},"23":{"quantity":2,"dealer_price":"100.00"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"25.59","shipment_date":"2020-03-15 23:59:59"}',
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'Not picked up',
                    'fulfillment_status' => 'fulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'stripe',
                    'payment_method_subtype' => 'card',
                    'payment_captured_at' => '2020-03-15 19:37:34',
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '3519.51',
                    'total_discount' => '800.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '0',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '312.87',
                    'tax_included' => false,
                    'shipping_amount' => '6.66',
                    'transactionID' => 'ch_1GO6lpLBFYPhk3KpV3E0f65w',
                    'stripe_account' => static::STRIPE_ACCOUNT_RETAILER,
                    'balance_transaction_id' => 'txn_1GO6u8LBFYPhk3KpE1ZwLew3',
                    'stripe_fees' => '79.17',
                    'risk_level' => 'not_assessed',
                    'discount_code' => 'BIKESFORDAYS',
                    'discount_id' => null,
                    'billing_firstname' => 'Aron',
                    'billing_lastname' => 'Schmidt',
                    'shipping_company_name' => null,
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'Ontario',
                    'shipping_country' => 'ca',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '48.400059',
                    'longitude' => '-89.269111',
                    'shipping_telephone' => '(*************',
                    'shipping_statecode' => '611',
                    'shipping_countrycode' => '39',
                    'total_qty_ordered' => null,
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '145.93',
                    'retailerAmount' => '3477.58',
                    'card_type' => 'Visa',
                    'last_four_digit' => '4242',
                    'fraud_check_cvc' => 'pass',
                    'fraud_check_address' => 'pass',
                    'fraud_check_postal_code' => 'pass',
                    'notes' => null,
                    'created_at' => '2020-03-15 19:37:33',
                    'updated_at' => '2020-03-15 23:59:59',
                ],
                'OrderProduct' => [
                    [
                        'product_id' => '21',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '234.00',
                        'total_price' => '2400.00',
                        'total_discount' => '600',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'created' => '2020-03-15 19:37:40',
                        'modified' => '2020-03-15 19:37:40',
                    ],
                    [
                        'product_id' => '23',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '78.00',
                        'total_price' => '799.98',
                        'total_discount' => '200',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2020-03-15 19:37:40',
                        'modified' => '2020-03-15 19:37:40',
                    ],
                ],
            ],
            'purchase_order_on_file' => [
                'Order' => [
                    'id' => static::CASE_IDS['purchase_order_on_file'],
                    'user_id' => '8',
                    'retailer_id' => '7',
                    'branch_id' => null,
                    'store_associate_id' => null,
                    'sales_rep_id' => '21',
                    'distributor_id' => null,
                    'created_by_user_id' => '7',
                    'b2b_ship_to_user_id' => '17',
                    'customerID' => null,
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => null,
                    'orderID' => static::deriveOrderID(static::CASE_IDS['purchase_order_on_file']),
                    'parentOrderId' => null,
                    'source_id' => null,
                    'orderNO' => null,
                    'source_order_name' => null,
                    'purchase_order_number' => '#PO-12345',
                    'external_invoice_id' => null,
                    'order_type' => 'wholesale',
                    'subType' => 'regular',
                    'is_install' => false,
                    'secretcode' => '',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => '2020-05-15',
                    'shipped_date' => null,
                    'dealer_qty_ordered' => '{"products":{"21":{"quantity":3,"dealer_price":"499.00"},"23":{"quantity":2,"dealer_price":"100.00"}},"currencytype":"USD","b2b_tax":"5.5000"}',
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'Purchase Order',
                    'fulfillment_status' => 'unfulfilled',
                    'payment_status' => '0',
                    'payment_method' => 'external',
                    'payment_method_subtype' => null,
                    'payment_captured_at' => null,
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '1821.99',
                    'total_discount' => '0.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '0',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '94.99',
                    'tax_included' => false,
                    'shipping_amount' => '30.00',
                    'transactionID' => '',
                    'stripe_account' => null,
                    'balance_transaction_id' => '',
                    'stripe_fees' => '0.00',
                    'risk_level' => 'not_assessed',
                    'discount_code' => '',
                    'discount_id' => null,
                    'billing_firstname' => '',
                    'billing_lastname' => '',
                    'shipping_company_name' => 'Local Shop Branch',
                    'shipping_address1' => '216 Brodie St S',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'ON',
                    'shipping_country' => 'CA',
                    'shipping_zipcode' => 'P7E 1C2',
                    'latitude' => '48.381709',
                    'longitude' => '-89.246322',
                    'shipping_telephone' => '************',
                    'shipping_statecode' => '611',
                    'shipping_countrycode' => '39',
                    'total_qty_ordered' => '5',
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '0.00',
                    'retailerAmount' => '1821.99',
                    'card_type' => null,
                    'last_four_digit' => null,
                    'fraud_check_cvc' => null,
                    'fraud_check_address' => null,
                    'fraud_check_postal_code' => null,
                    'notes' => 'Example notes...',
                    'created_at' => '2020-05-12 14:30:39',
                    'updated_at' => '2020-05-12 14:30:42',
                ],
                'OrderProduct' => [
                    [
                        'product_id' => '21',
                        'warehouse_id' => '1',
                        'inventory_transfer_id' => null,
                        'quantity' => '1',
                        'total_tax' => '27.445',
                        'total_price' => '499.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2020-05-12 16:30:42',
                        'modified' => '2020-05-12 16:30:42',
                    ],
                    [
                        'product_id' => '21',
                        'warehouse_id' => '2',
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '54.89',
                        'total_price' => '998.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2020-05-12 16:30:42',
                        'modified' => '2020-05-12 16:30:42',
                    ],
                    [
                        'product_id' => '23',
                        'warehouse_id' => '1',
                        'inventory_transfer_id' => '1',
                        'quantity' => '2',
                        'total_tax' => '11',
                        'total_price' => '200.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => '2021-09-30 00:00:00',
                        'created' => '2020-05-12 16:30:42',
                        'modified' => '2020-05-12 16:30:42',
                    ],
                ],
            ],
            'sell_direct' => [
                'Order' => [
                    'id' => static::CASE_IDS['sell_direct'],
                    'user_id' => '8',
                    'retailer_id' => null,
                    'branch_id' => null,
                    'store_associate_id' => null,
                    'sales_rep_id' => null,
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4037',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => '990',
                    'orderID' => static::deriveOrderID(static::CASE_IDS['sell_direct']),
                    'parentOrderId' => null,
                    'source_id' => '1001',
                    'orderNO' => '2001',
                    'source_order_name' => '#2001',
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => 'shipearly',
                    'subType' => 'Sell_direct',
                    'is_install' => false,
                    'secretcode' => '',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => null,
                    'dealer_qty_ordered' => null,
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'paid',
                    'fulfillment_status' => 'unfulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'stripe',
                    'payment_method_subtype' => 'card',
                    'payment_captured_at' => '2022-12-19 18:36:29',
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '3519.51',
                    'total_discount' => '800.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '3519.51',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '312.87',
                    'tax_included' => false,
                    'shipping_amount' => '6.66',
                    'transactionID' => 'pi_fake',
                    'stripe_account' => static::STRIPE_ACCOUNT_BRAND,
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '79.17',
                    'risk_level' => 'normal',
                    'discount_code' => 'BIKESFORDAYS',
                    'discount_id' => null,
                    'billing_firstname' => 'Aron',
                    'billing_lastname' => 'Schmidt',
                    'shipping_company_name' => 'ShipEarly',
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => null,
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'Ontario',
                    'shipping_country' => 'Canada',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '48.400059',
                    'longitude' => '-89.269111',
                    'shipping_telephone' => '+1 (*************',
                    'shipping_statecode' => 'ON',
                    'shipping_countrycode' => 'CA',
                    'total_qty_ordered' => '4',
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '55.38',
                    'retailerAmount' => '3568.13',
                    'card_type' => null,
                    'last_four_digit' => null,
                    'fraud_check_cvc' => null,
                    'fraud_check_address' => null,
                    'fraud_check_postal_code' => null,
                    'notes' => null,
                    'created_at' => '2022-12-19 13:36:24',
                    'updated_at' => '2022-12-19 13:36:26',
                ],
                'OrderProduct' => [
                    [
                        'product_id' => '21',
                        'warehouse_id' => '2',
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '234.00',
                        'total_price' => '2400.00',
                        'total_discount' => '600.00',
                        'totalPriceConversion' => '2400.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2022-12-19 13:36:24',
                        'modified' => '2022-12-19 13:36:24',
                    ],
                    [
                        'product_id' => '23',
                        'warehouse_id' => '2',
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '78.00',
                        'total_price' => '799.98',
                        'total_discount' => '200.00',
                        'totalPriceConversion' => '799.98',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2020-12-10 12:47:44',
                        'modified' => '2020-12-10 12:47:44',
                    ],
                ],
            ],
            'wc_sell_direct' => [
                'Order' => [
                    'id' => static::CASE_IDS['wc_sell_direct'],
                    'user_id' => '19',
                    'retailer_id' => null,
                    'branch_id' => null,
                    'store_associate_id' => null,
                    'sales_rep_id' => null,
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4045',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => '1',
                    'orderID' => static::deriveOrderID(static::CASE_IDS['wc_sell_direct']),
                    'parentOrderId' => null,
                    'source_id' => '1001',
                    'orderNO' => 'CUSTOM_1001',
                    'source_order_name' => null,
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => '',
                    'subType' => null,
                    'is_install' => false,
                    'secretcode' => '',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => null,
                    'dealer_qty_ordered' => null,
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'processing',
                    'fulfillment_status' => 'unfulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'stripe',
                    'payment_method_subtype' => 'card',
                    'payment_captured_at' => '2021-04-06 20:24:42',
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '148.48',
                    'total_discount' => '0.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '148.48',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '13.50',
                    'tax_included' => false,
                    'shipping_amount' => '15.00',
                    'transactionID' => 'ch_fake',
                    'stripe_account' => static::STRIPE_ACCOUNT_BRAND,
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '4.61',
                    'risk_level' => 'normal',
                    'discount_code' => '',
                    'discount_id' => null,
                    'billing_firstname' => 'Aron',
                    'billing_lastname' => 'Schmidt',
                    'shipping_company_name' => 'ShipEarly',
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'ON',
                    'shipping_country' => 'CA',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '',
                    'longitude' => '',
                    'shipping_telephone' => '',
                    'shipping_statecode' => '',
                    'shipping_countrycode' => '',
                    'total_qty_ordered' => '4',
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '3.96',
                    'retailerAmount' => '144.52',
                    'card_type' => null,
                    'last_four_digit' => null,
                    'fraud_check_cvc' => null,
                    'fraud_check_address' => null,
                    'fraud_check_postal_code' => null,
                    'notes' => null,
                    'created_at' => '2021-04-06 20:24:41',
                    'updated_at' => '2021-04-06 20:24:42',
                ],
                'OrderProduct' => [
                    0 => [
                        'product_id' => '48',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '8.00',
                        'total_price' => '80.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '80.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-04-06 20:25:34',
                        'modified' => '2021-04-06 20:25:35',
                    ],
                    1 => [
                        'product_id' => '53',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'quantity' => '2',
                        'total_tax' => '4.00',
                        'total_price' => '39.98',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '39.98',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-04-06 20:25:34',
                        'modified' => '2021-04-06 20:25:35',
                    ],
                ],
            ],
            'api_sell_direct' => [
                'Order' => [
                    'id' => static::CASE_IDS['api_sell_direct'],
                    'user_id' => '12',
                    'retailer_id' => null,
                    'branch_id' => null,
                    'store_associate_id' => null,
                    'sales_rep_id' => null,
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4045',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => '1',
                    'orderID' => static::deriveOrderID(static::CASE_IDS['api_sell_direct']),
                    'parentOrderId' => null,
                    'source_id' => '1001',
                    'orderNO' => 'API1000',
                    'source_order_name' => null,
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => 'Sell_Direct',
                    'subType' => null,
                    'is_install' => false,
                    'secretcode' => '',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => null,
                    'dealer_qty_ordered' => null,
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'paid',
                    'fulfillment_status' => 'unfulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'stripe',
                    'payment_method_subtype' => 'card',
                    'payment_captured_at' => null,
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '148.48',
                    'total_discount' => '0.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '148.48',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '13.50',
                    'tax_included' => false,
                    'shipping_amount' => '15.00',
                    'transactionID' => 'ch_fake',
                    'stripe_account' => static::STRIPE_ACCOUNT_BRAND,
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '4.61',
                    'risk_level' => 'normal',
                    'discount_code' => '',
                    'billing_firstname' => 'Erick',
                    'billing_lastname' => 'Tubaro',
                    'shipping_company_name' => 'ShipEarly',
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'ON',
                    'shipping_country' => 'CA',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '',
                    'longitude' => '',
                    'shipping_telephone' => '',
                    'shipping_statecode' => '',
                    'shipping_countrycode' => '',
                    'total_qty_ordered' => '4',
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '3.96',
                    'retailerAmount' => '144.52',
                    'card_type' => null,
                    'last_four_digit' => null,
                    'fraud_check_cvc' => null,
                    'fraud_check_address' => null,
                    'fraud_check_postal_code' => null,
                    'notes' => null,
                    'created_at' => '2021-05-10 20:24:41',
                    'updated_at' => '2021-05-10 20:24:42',
                ],
                'OrderProduct' => [
                    [
                        'product_id' => '48',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '8.00',
                        'total_price' => '80.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '80.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                    [
                        'product_id' => '53',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '4.00',
                        'total_price' => '39.98',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '39.98',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                ]
            ],
            'api_wholesale' => [
                'Order' => [
                    'id' => static::CASE_IDS['api_wholesale'],
                    'user_id' => '12',
                    'retailer_id' => '15',
                    'branch_id' => '15',
                    'store_associate_id' => null,
                    'sales_rep_id' => null,
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4045',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => '1',
                    'orderID' => static::deriveOrderID(static::CASE_IDS['api_wholesale']),
                    'parentOrderId' => null,
                    'source_id' => '1001',
                    'orderNO' => 'API1001',
                    'source_order_name' => null,
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => 'wholesale',
                    'subType' => 'regular',
                    'is_install' => false,
                    'secretcode' => '',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => null,
                    'dealer_qty_ordered' => null,
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'processing',
                    'fulfillment_status' => 'unfulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'stripe',
                    'payment_method_subtype' => 'card',
                    'payment_captured_at' => null,
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '148.48',
                    'total_discount' => '0.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '148.48',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '13.50',
                    'tax_included' => false,
                    'shipping_amount' => '15.00',
                    'transactionID' => 'ch_fake',
                    'stripe_account' => static::STRIPE_ACCOUNT_BRAND,
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '4.61',
                    'risk_level' => 'normal',
                    'discount_code' => '',
                    'billing_firstname' => 'Erick',
                    'billing_lastname' => 'Tubaro',
                    'shipping_company_name' => 'ShipEarly',
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'ON',
                    'shipping_country' => 'CA',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '',
                    'longitude' => '',
                    'shipping_telephone' => '',
                    'shipping_statecode' => '',
                    'shipping_countrycode' => '',
                    'total_qty_ordered' => '4',
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '3.96',
                    'retailerAmount' => '144.52',
                    'card_type' => null,
                    'last_four_digit' => null,
                    'fraud_check_cvc' => null,
                    'fraud_check_address' => null,
                    'fraud_check_postal_code' => null,
                    'notes' => null,
                    'created_at' => '2021-05-10 20:24:41',
                    'updated_at' => '2021-05-10 20:24:42',
                ],
                'OrderProduct' => [
                    0 => [
                        'product_id' => '48',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '8.00',
                        'total_price' => '80.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '80.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                    1 => [
                        'product_id' => '53',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '4.00',
                        'total_price' => '39.98',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '39.98',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                ]
            ],
            'api_wholesale_unconfirmed_price' => [
                'Order' => [
                    'id' => static::CASE_IDS['api_wholesale_unconfirmed_price'],
                    'user_id' => '12',
                    'retailer_id' => '15',
                    'branch_id' => '15',
                    'store_associate_id' => null,
                    'sales_rep_id' => null,
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4045',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => '1',
                    'orderID' => static::deriveOrderID(static::CASE_IDS['api_wholesale_unconfirmed_price']),
                    'parentOrderId' => null,
                    'source_id' => '1001',
                    'orderNO' => 'API1001',
                    'source_order_name' => null,
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => 'wholesale',
                    'subType' => 'regular',
                    'is_install' => false,
                    'secretcode' => '',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => null,
                    'dealer_qty_ordered' => '{"products": {"48": {"quantity": 5,"dealer_price": "20.00"},"53": {"quantity": 5,"dealer_price": "10.00"},"25": {"quantity": 5,"dealer_price": "5.00"}},"currencytype": "CAD","b2b_tax": "13.0000"}',
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'Purchase Order',
                    'fulfillment_status' => 'unfulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'stripe',
                    'payment_method_subtype' => 'card',
                    'payment_captured_at' => null,
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '148.48',
                    'total_discount' => '0.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '148.48',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '13.50',
                    'tax_included' => false,
                    'shipping_amount' => '15.00',
                    'transactionID' => 'ch_fake',
                    'stripe_account' => static::STRIPE_ACCOUNT_BRAND,
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '4.61',
                    'risk_level' => 'normal',
                    'discount_code' => '',
                    'billing_firstname' => 'Erick',
                    'billing_lastname' => 'Tubaro',
                    'shipping_company_name' => 'ShipEarly',
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'ON',
                    'shipping_country' => 'CA',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '',
                    'longitude' => '',
                    'shipping_telephone' => '',
                    'shipping_statecode' => '',
                    'shipping_countrycode' => '',
                    'total_qty_ordered' => '4',
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '3.96',
                    'retailerAmount' => '144.52',
                    'card_type' => null,
                    'last_four_digit' => null,
                    'fraud_check_cvc' => null,
                    'fraud_check_address' => null,
                    'fraud_check_postal_code' => null,
                    'notes' => null,
                    'created_at' => '2021-05-10 20:24:41',
                    'updated_at' => '2021-05-10 20:24:42',
                ],
                'OrderProduct' => [
                    0 => [
                        'product_id' => '48',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '8.00',
                        'total_price' => '80.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '80.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                    1 => [
                        'product_id' => '53',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '4.00',
                        'total_price' => '40.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '40.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                    2 => [
                        'product_id' => '25',
                        'warehouse_id' => '3',
                        'inventory_transfer_id' => '6',
                        'quantity' => '5',
                        'total_tax' => '4.00',
                        'total_price' => '25.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '25.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                ]
            ],
            'wholesale_credit_order' => [
                'Order' => [
                    'id' => static::CASE_IDS['wholesale_credit_order'],
                    'user_id' => '12',
                    'retailer_id' => '15',
                    'branch_id' => '15',
                    'store_associate_id' => null,
                    'sales_rep_id' => null,
                    'distributor_id' => null,
                    'created_by_user_id' => null,
                    'b2b_ship_to_user_id' => null,
                    'customerID' => '4045',
                    'customerEmail' => '<EMAIL>',
                    'preOrderId' => '1',
                    'orderID' => static::deriveOrderID(static::CASE_IDS['wholesale_credit_order']),
                    'parentOrderId' => null,
                    'source_id' => '1001',
                    'orderNO' => 'API1001',
                    'source_order_name' => null,
                    'purchase_order_number' => null,
                    'external_invoice_id' => null,
                    'order_type' => 'wholesale',
                    'subType' => 'regular',
                    'is_install' => false,
                    'secretcode' => '',
                    'is_commission_retailer' => false,
                    'has_distributor' => false,
                    'requested_b2b_ship_date' => null,
                    'shipped_date' => null,
                    'dealer_qty_ordered' => null,
                    'deliveryDate' => null,
                    'verification_image_url' => null,
                    'order_status' => 'processing',
                    'fulfillment_status' => 'unfulfilled',
                    'payment_status' => '2',
                    'payment_method' => 'credit',
                    'payment_method_subtype' => null,
                    'payment_captured_at' => null,
                    'credit_term' => null,
                    'credit_term_id' => null,
                    'total_price' => '148.48',
                    'total_discount' => '0.00',
                    'shipping_discount' => '0.00',
                    'totalPriceConversion' => '148.48',
                    'total_sales_commission' => '0.00',
                    'shipearly_commission_fee' => '0.00',
                    'sales_commission_payment_method' => '',
                    'sales_commission_payment_id' => '',
                    'total_retailer_commission' => '0.00',
                    'retailer_commission_payment_method' => '',
                    'retailer_commission_payment_id' => '',
                    'currency_code' => 'USD',
                    'total_tax' => '13.50',
                    'tax_included' => false,
                    'shipping_amount' => '15.00',
                    'transactionID' => 'ch_fake',
                    'stripe_account' => null,
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '4.61',
                    'risk_level' => 'normal',
                    'discount_code' => '',
                    'billing_firstname' => 'Erick',
                    'billing_lastname' => 'Tubaro',
                    'shipping_company_name' => 'ShipEarly',
                    'shipping_address1' => '2400 Nipigon Rd.',
                    'shipping_address2' => '',
                    'shipping_city' => 'Thunder Bay',
                    'shipping_state' => 'ON',
                    'shipping_country' => 'CA',
                    'shipping_zipcode' => 'P7C 4W1',
                    'latitude' => '',
                    'longitude' => '',
                    'shipping_telephone' => '',
                    'shipping_statecode' => '',
                    'shipping_countrycode' => '',
                    'total_qty_ordered' => '4',
                    'courier' => null,
                    'trackingno' => '',
                    'last_check_point' => '',
                    'unique_token' => null,
                    'masspay' => null,
                    'shipearlyFees' => '3.96',
                    'retailerAmount' => '144.52',
                    'card_type' => null,
                    'last_four_digit' => null,
                    'fraud_check_cvc' => null,
                    'fraud_check_address' => null,
                    'fraud_check_postal_code' => null,
                    'notes' => null,
                    'created_at' => '2021-05-10 20:24:41',
                    'updated_at' => '2021-05-10 20:24:42',
                ],
                'OrderProduct' => [
                    0 => [
                        'product_id' => '48',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '8.00',
                        'total_price' => '80.00',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '80.00',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                    1 => [
                        'product_id' => '53',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'quantity' => '5',
                        'total_tax' => '4.00',
                        'total_price' => '39.98',
                        'total_discount' => '0.00',
                        'totalPriceConversion' => '39.98',
                        'total_sales_commission' => '0.00',
                        'total_retailer_commission' => '0.00',
                        'status' => 'Active',
                        'cancelled_at' => null,
                        'notes' => null,
                        'restock_date' => null,
                        'created' => '2021-05-10 20:24:41',
                        'modified' => '2021-05-10 20:24:41',
                    ],
                ]
            ],
        ]);

        $orders = array_column($data, 'Order');

        $this->deleteOrders(array_column($orders, 'id'));
        $this->table('orders')
             ->insert($orders)
             ->save();

        $orderProducts = array_reduce($data, function($list, $record) {
            $order_id = $record['Order']['id'];
            foreach ($record['OrderProduct'] as $orderProduct) {
                $list[] = $orderProduct + compact('order_id');
            }

            return $list;
        }, []);

        $this->table('order_products')
             ->insert($orderProducts)
             ->save();
    }

    private function deleteOrders(array $orderIds)
    {
        $refundRows = $this->getAdapter()
                     ->getQueryBuilder()
                     ->select('id')
                     ->from('ship_order_refunds')
                     ->whereInList('order_id', $orderIds)
                     ->execute()
                     ->fetchAll();
        $orderRefundIds = array_column($refundRows, 0);

        $fulfillmentRows = $this->getAdapter()
                     ->getQueryBuilder()
                     ->select('id')
                     ->from('ship_fulfillments')
                     ->whereInList('order_id', $orderIds)
                     ->execute()
                     ->fetchAll();
        $fulfillmentIds = array_column($fulfillmentRows, 0);

        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_order_refund_products')
             ->whereInList('order_refund_id', $orderRefundIds, ['allowEmpty' => true])
             ->execute();

        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_order_refunds')
             ->whereInList('order_id', $orderIds)
             ->execute();

        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_fulfillment_products')
             ->whereInList('fulfillment_id', $fulfillmentIds, ['allowEmpty' => true])
             ->execute();

        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_fulfillments')
             ->whereInList('order_id', $orderIds)
             ->execute();

        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_order_products')
             ->whereInList('order_id', $orderIds)
             ->execute();

        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_orders')
             ->whereInList('id', $orderIds)
             ->execute();
    }
}
