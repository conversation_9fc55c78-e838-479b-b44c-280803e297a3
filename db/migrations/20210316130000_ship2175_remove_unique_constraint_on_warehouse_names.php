<?php

use Phinx\Migration\AbstractMigration;

class Ship2175RemoveUniqueConstraintOnWarehouseNames extends AbstractMigration
{
    public function up()
    {
        // Remove unique constraint that synced ecommerce APIs are not guaranteed to follow
        $this->table('warehouses')
            ->removeIndexByName('unique_user_id_name')
            ->update();
    }

    public function down()
    {
        // Set unique names for duplicates of (user_id, name) excluding the first active record
        $this->execute(<<<'SQL'
UPDATE `ship_warehouses` AS `Duplicate`
INNER JOIN `ship_warehouses` AS `Original` ON (
    `Duplicate`.`user_id` = `Original`.`user_id` AND
    `Duplicate`.`name` = `Original`.`name` AND
    `Duplicate`.`id` != `Original`.`id`
)
INNER JOIN (
    SELECT MIN(`id`) AS `id`, `user_id`, `name`
    FROM `ship_warehouses`
    WHERE `is_active` = TRUE
    GROUP BY `user_id`, `name`
) AS `Canonical` ON (
    `Duplicate`.`user_id` = `Canonical`.`user_id` AND
    `Duplicate`.`name` = `Canonical`.`name` AND
    `Duplicate`.`id` != `Canonical`.`id`
)
SET `Duplicate`.`name` = CONCAT_WS(' ', `Duplicate`.`name`, 'Copy', `Duplicate`.`id`);
SQL
        );

        $this->table('warehouses')
            ->addIndex(['user_id', 'name'], ['unique' => true, 'name' => 'unique_user_id_name'])
            ->update();
    }
}
