#!/bin/sh
CONFIG_FILE="../app/Config/.env"
if [ -z "${MAIN_BASE_URL}" ] && [ -f "${CONFIG_FILE}" ]; then
  . "${CONFIG_FILE}"
fi
if [ -z "${MAIN_BASE_URL}" ]; then
  echo "Environment var \"MAIN_BASE_URL\" is empty!" 1>&2
  exit 1
fi

cat <<Crontab
# ShipEarly cron jobs that must be configured on the server.


# The commented cron below does not work when run as root.
# Reference this to set up the crontab of a user that can push to the remote repo in the script.
#
# # Push log files to the cronlogs branch for the server name given by BASE_PATH
# # This Cron job runs every 2 hours
# 0 */2 * * * sh -c 'cd /var/www/html/shipearly && sh cronlogs-script.sh && cd ./app/webroot/shopify && sh cronlogs-script.sh'


# Cron jobs that run every minute

# Get all the Btask based the status and update the status to success when it completed.
*/1 * * * * curl ${MAIN_BASE_URL}crons/runBackJobs

# Check the mailQueue table and send the mails one by one
*/1 * * * * curl ${MAIN_BASE_URL}crons/runSendMails

# Check if any data is available in updateCron
*/1 * * * * curl ${MAIN_BASE_URL}ws/checkUpdatecron

# Synchronize brand order, product and customer data. When Brand configuration is updated
*/1 * * * * curl ${MAIN_BASE_URL}ws/runCron


# Cron jobs that run every 10 minutes

# Get all ship from store order and check if the order have any tracking number if there is no
# tracking number, then send a reminder mail to retailer
*/10 * * * * curl ${MAIN_BASE_URL}crons/shipFromStoreOrderNotificationEmail

# Get all Non Stock order and check if the order have any Delivery date if there is no date,
# then send a reminder mail to retailer
*/10 * * * * curl ${MAIN_BASE_URL}crons/nonStockOrderNotificationEmail

# Get all uncaptured(payment) orders from order table get the transactionId and capture
# the amount using Stripe(Stripe only holds the captured amount up to 6 days)
*/10 * * * * curl ${MAIN_BASE_URL}crons/checkUnCaptureOrders

# Get all shipfromstore orders that exceeds 2 days limit and cancel that orders and payment
*/10 * * * * curl ${MAIN_BASE_URL}crons/shipFromStoreOrderCancelNotification

# Get all non stock orders that exceed 36hrs limit and cancel those orders and payment
*/10 * * * * curl ${MAIN_BASE_URL}crons/nonStockOrderCancelNotification

# Get all non stock orders that exceed 24hrs limit and place those orders as dealer order
*/10 * * * * curl ${MAIN_BASE_URL}crons/automaticDealerOrder

# Check the imported retailer from ImportEmail table.
# Check the the no of time and last triggered date.
# Send the invitation email up to 10 times
*/10 * * * * curl ${MAIN_BASE_URL}crons/inviteRetailers

# It updates last checkpoint for the customer Order
*/10 * * * * curl ${MAIN_BASE_URL}crons/lastcheck

# Gets all vend pos refresh token that exceeds 24hrs limit
*/10 * * * * curl ${MAIN_BASE_URL}crons/vendRefreshToken

# Get all abandon cart notifications that exceed 3hrs limit
*/10 * * * * curl ${MAIN_BASE_URL}crons/abandonCartNotification

# Remove old Shopify Success and Error Pages
*/10 * * * * curl ${MAIN_BASE_URL}crons/clearShopifyDetails

# Check for FTP inventory imports and proccess if present
*/10 * * * * curl ${MAIN_BASE_URL}crons/ftpInventoryUpdate

# Check for Email inventory imports and proccess if present
*/10 * * * * curl ${MAIN_BASE_URL}crons/emailInventoryUpdate

# These Cron jobs run every 2 hours

# Update the current month's retailer count on database
0 */2 * * * curl ${MAIN_BASE_URL}crons/UpdateRetailerCount

# Just clears all successfully triggered mails from mailqueue
0 */2 * * * curl ${MAIN_BASE_URL}crons/clearMails

# Clears all Successfully completed background tasks from shipearly
0 */2 * * * curl ${MAIN_BASE_URL}crons/clearBackJobs


# Daily Cron jobs

# Get all retailers and stores inventory and update on shipearly database
0 4 * * * curl ${MAIN_BASE_URL}crons/index

# Reset inventory for retailers with manually uploaded inventory
0 4 * * * curl ${MAIN_BASE_URL}crons/resetRetailerUploadedInventory

# Queue eCommerce sync jobs for active brands
0 6 * * * curl ${MAIN_BASE_URL}crons/queueEcommerceSync


# Weekly Cron jobs

# Get all shopify stores tax information and update on shipearly database
0 8 * * 1 curl ${MAIN_BASE_URL}shopify/shopifypos/shop/map

# Update courier data from AfterShip
0 8 * * 2 curl ${MAIN_BASE_URL}crons/newcouriers

Crontab
