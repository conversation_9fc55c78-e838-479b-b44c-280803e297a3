#!/bin/bash
# Run from script directory even if symlinked
cd "$(dirname -- "$(readlink -f -- "${BASH_SOURCE[0]}")")" || exit 1

for FILE in "../Sql/reset-test-database.sh" "./Console/cake"; do
    if [ ! -f "${FILE}" ]; then
        echo "${FILE} not found!" 1>&2
        exit 1
    fi
done

RESET_DATABASE=true
TESTSUITE=Suite/UnitTests
for i in "$@"; do
  case $i in
    --no-reset-database)
      RESET_DATABASE=false
      shift # past argument with no value
      ;;
    --testsuite=*)
      TESTSUITE="${i#*=}"
      shift # past argument=value
      ;;
    *)
      echo "Unexpected arg: '$i'" 1>&2
      exit 1
      ;;
  esac
done

if [ "$RESET_DATABASE" = "true" ]; then
  (
    cd ../Sql || exit 1
    bash ./reset-test-database.sh --target="test" &
    bash ./reset-test-database.sh --target="test_seed" &
    wait
  )
fi

./Console/cake cache clear_all

API_TESTS=(
    "Api(V\d+)?.*?Test"
)

SHOPIFY_TESTS=(
    "ShopifyAPIComponentTest"
    "ShopifyComponentTest"
    "ShopifyWebhookHandlerComponentTest"
    "ShopifyWebhookListenerComponentTest"
    "ShopifyAncillaryFeeTest"
    "ShopifyPaymentSuccessTest"
#    "ShopifyPlaceOrderTest"
    "ShopifyStripePaymentTest"
)

STRIPE_TESTS=(
    "StripeWebhookHandlerComponentTest"
)

WIDGETS_TESTS=(
    "CheckoutWidgetsControllerTest"
)

WOOCOMERCE_TESTS=(
    "WoocommerceComponentTest"
    "WoocommerceWebhookHandlerComponentTest"
    "WoocommerceContractTest"
    "WoocommerceGetStatesTest"
    "WoocommercePreordersTest"
    "WoocommerceStripePaymentTest"
    "WoocommerceValidateItemsTest"
)

APP_CONSOLE_TESTS=(
    "CacheShellTest"
)

APP_COMPONENT_TESTS=(
    "OrderLogicCaptureStripeChargeTest"
    "OrderLogicDealerOrderEventTest"
    "AddressValidatorComponentTest"
    "FulfillmentLogicComponentTest"
    "IndexQueryHandlerComponentTest"
    "NotificationLogicComponentTest"
    "OrderPlacerComponentTest"
    "PermissionsComponentTest"
    "ShippingCalculatorComponentTest"
    "SpoutComponentTest"
    "TaxCalculatorComponentTest"
)

APP_CONTROLLER_TESTS=(
    "CronsReserveEcommerceInventoryTest"
    "CronsControllerTest"
    "OrdersAddDealerOrderProductTest"
    "OrdersAdminViewTest"
    "OrdersCancelTest"
    "OrdersDealerOrderShipmentTrackingTest"
    "OrdersDealerorderTotalsTest"
#    "OrdersExportTest"
    "OrdersRechargeExpiredTest"
    "OrdersSplitpaymentTest"
#    "ProductsAdminDeleteIncompleteTest"
    "ProductsExportImportTest"
    "ProductsIndexRetailerTest"
    "RetailersExportImportTest"
    "RetailersExtractIndexQueryParamsTest"
    "UsersPayoutsTest"
    "UsersStripeConnectTest"
    "WsCheckProductTest"
    "WsCheckUpdatecronTest"
    "WsFilterRetailersByCommissionTierTest"
    "WsFormatGetRetailerInputsTest"
    "WsGetRetailerIdsByRadiusTest"
    "WsGetRetailerIdsTest"
    "WsSearchEcommerceSoftwareTest"
    "WsSyncShopifyCollectionsTest"
    "B2bShipToAddressesControllerTest"
    "DealerOrderRefundsControllerTest"
    "FulfillmentsControllerTest"
    "InventoryControllerTest"
    "InventoryTransfersControllerTest"
    "OrdersControllerTest"
    "ProductStateFeesControllerTest"
#    "StripeWebhookControllerTest"
    "UserTaxesControllerTest"
)

APP_INTEGRATION_TESTS=(
    "AfterShipComponentTest"
    "ApiV1ProductLocatorIntegrationTest"
    "LightspeedComponentTest"
    "ShippoComponentTest"
    "ShopifyIntegrationTest"
    "SquarePosComponentTest"
    "StripeIntegrationTest"
)

APP_LIB_TESTS=(
    "KlaviyoIntegrationTest"
)

APP_MODEL_TESTS=(
    "OrderFindAllForDealerOrderIndexTest"
    "OrderFindAllWithReservationsTest"
    "OrderFindForPopupTest"
    "AccessTokenTest"
    "AppModelTest"
    "B2bCartProductTest"
    "B2bCartTest"
    "B2bShippingRateTest"
    "B2bShippingZoneTest"
    "BrandStaffPermissionTest"
    "BrandStaffRoleTest"
    "BrandStaffTest"
    "BtaskTest"
    "CollectionTest"
    "ConfigurationTest"
    "CountryTaxTest"
    "CountryTest"
    "CountryValidationMethodTest"
    "CourierTest"
    "DealerOrderTest"
    "DiscountTest"
    "DiscountRuleTest"
    "DiscountUsageTest"
    "FulfillmentTest"
    "InventoryTransferProductReservationTest"
    "InventoryTransferProductTest"
    "InventoryTransferTest"
    "ManufacturerRetailerTest"
    "OrderCommentTest"
    "OrderTest"
    "PriceBasedShippingRateTest"
    "PricingTierTest"
    "ProductTitleTest"
    "ProductStateFeeTest"
    "ProductTagTest"
    "ProductTest"
    "ProductTierTest"
    "RetailerCreditPaymentTest"
    "RetailerCreditTest"
    "RetailerCreditVoucherTest"
    "ShippingZoneTest"
    "SplitPaymentReportTest"
    "StaffPermissionTest"
    "StaffTest"
    "StateTest"
    "StoreTest"
    "StripeUserCapabilityTest"
    "StripeUserTest"
    "TagTest"
    "TerritoryTest"
    "UnitBasedShippingRateTest"
    "UserCountryTaxTest"
    "UserCurrencyTest"
    "UserSettingTest"
    "UserTaxTest"
    "UserTest"
    "WarehouseProductReservationTest"
    "WarehouseProductTest"
    "WarehouseTest"
    "WeightBasedShippingPriceTest"
    "ZoneTaxOverrideCollectionTest"
)

APP_TEST_TESTS=(
    "TableCopyTestFixtureTest"
)

APP_UTILITY_TESTS=(
    "CurrencyTest"
    "PermissionsTest"
    "SupportedLanguagesTest"
)

CAKETESTS=(
    "${API_TESTS[@]}"
    "${SHOPIFY_TESTS[@]}"
    "${STRIPE_TESTS[@]}"
    "${WIDGETS_TESTS[@]}"
    "${WOOCOMERCE_TESTS[@]}"
    "${APP_CONSOLE_TESTS[@]}"
    "${APP_COMPONENT_TESTS[@]}"
    "${APP_CONTROLLER_TESTS[@]}"
    "${APP_INTEGRATION_TESTS[@]}"
    "${APP_LIB_TESTS[@]}"
    "${APP_MODEL_TESTS[@]}"
    "${APP_TEST_TESTS[@]}"
    "${APP_UTILITY_TESTS[@]}"
)
# Join with "|" as deliminator and remove final deliminator
printf -v FILTER "^%s\\\b|" "${CAKETESTS[@]}"
FILTER=${FILTER%?}

# Coverage generator does not automatically clean up old report files
rm -Rf ./tmp/test_logs/coverage-html/*

./Console/cake test --quiet app "${TESTSUITE}" --stderr --filter="${FILTER}"
