@media (min-width: 768px) {
  .form-search .combobox-container,
  .form-inline .combobox-container {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: top;
  }
  .form-search .combobox-container .input-group-addon,
  .form-inline .combobox-container .input-group-addon,
  .form-search .combobox-container .input-group-text,
  .form-inline .combobox-container .input-group-text {
    width: auto;
  }
}
.combobox-container .dropdown-toggle {
  justify-content: center;
}
.combobox-container .dropdown-toggle.custom-icon::after {
  content: none;
}
.combobox-container.combobox-selected .dropdown-toggle::after {
  content: none;
}
.combobox-container .utf-remove::after {
  content: "\00D7";
}
.combobox-container.combobox-selected .pulldown {
  display: none;
}
/* :not doesn't work in IE8 */
.combobox-container:not(.combobox-selected) .remove {
  display: none;
}
.typeahead-long {
  max-height: 300px;
  overflow-y: auto;
}
.control-group.error .combobox-container .add-on {
  color: #B94A48;
  border-color: #B94A48;
}
.control-group.error .combobox-container .caret {
  border-top-color: #B94A48;
}
.control-group.warning .combobox-container .add-on {
  color: #C09853;
  border-color: #C09853;
}
.control-group.warning .combobox-container .caret {
  border-top-color: #C09853;
}
.control-group.success .combobox-container .add-on {
  color: #468847;
  border-color: #468847;
}
.control-group.success .combobox-container .caret {
  border-top-color: #468847;
}
