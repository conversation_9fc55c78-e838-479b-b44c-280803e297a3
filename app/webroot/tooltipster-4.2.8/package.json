{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://calebjacob.com"}, "bugs": {"url": "https://github.com/calebjacob/tooltipster/issues"}, "description": "A flexible and extensible jQuery plugin for modern tooltips.", "devDependencies": {"grunt": "1.0.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-compress": "1.2.0", "grunt-contrib-concat": "1.0.1", "grunt-contrib-copy": "1.0.0", "grunt-contrib-cssmin": "1.0.1", "grunt-contrib-uglify": "1.0.1", "grunt-string-replace": "1.2.1", "grunt-umd": "3.0.0"}, "homepage": "https://github.com/calebjacob/tooltipster", "keywords": ["ecosystem:jquery", "jquery-plugin", "tooltip", "tooltips"], "license": "MIT", "main": "", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/louisameline"}], "name": "tooltipster", "peerDependencies": {"jquery": ">=1.11.0"}, "repository": {"type": "git", "url": "https://github.com/iamceege/tooltipster.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "4.2.8"}