.toggle-iphone {
  .toggle-slide {
    border-radius: 9999px;
    box-shadow: 0 0 0 1px #999;
  }

  .toggle-on, .toggle-off {
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(0,0,0,0.5);
  }

  .toggle-on {
    border-radius: 9999px 0 0 9999px;
    background: #037bda;
    box-shadow: inset 2px 2px 5px rgba(0,0,0,0.4);

    &:after {
      background: linear-gradient(to bottom,  #1189f1 0%,#3797ef 100%);
      height: 50%;
      content: '';
      margin-top: -19%;
      display: block;
      border-radius: 9999px;
      margin-left: 10%;
    }
  }

  .toggle-off {
    box-shadow: inset -2px 2px 5px rgba(0,0,0,0.4);
    border-radius: 0 9999px 9999px 0;
    color: rgb(130,130,130);
    background: #ECECEC;
    text-shadow: 0 0 1px white;

    &:after {
      background: linear-gradient(to bottom,  #fafafa 0%,#fdfdfd 100%);
      height: 50%;
      content: '';
      margin-top: -19%;
      display: block;
      margin-right: 10%;
      border-radius: 9999px;
    }
  }

  .toggle-blob {
    border-radius: 50px;
    background: linear-gradient(to bottom,  #d1d1d1 0%,#fafafa 100%);
    box-shadow: inset 0 0 0 1px rgba(0,0,0,0.6), inset 0 0 0 2px white, 0 0 3px rgba(0,0,0,0.6);
  }
}
