<?php
App::uses('AppController', 'Controller');
/**
 * ManufacturerRetailerNotes Controller
 *
 * @property BrandStaff $BrandStaff
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property ManufacturerRetailerNote $ManufacturerRetailerNote
 * @property ManufacturerRetailerSalesRep $ManufacturerRetailerSalesRep
 */
class ManufacturerRetailerNotesController extends AppController
{
    public $uses = ['BrandStaff', 'ManufacturerRetailerNote', 'ManufacturerRetailer', 'ManufacturerRetailerSalesRep'];

    public function isAuthorized()
    {
        return in_array($this->Auth->user('user_type'), User::TYPES_BRAND, true);
    }

    public function index()
    {
        $retailerId = $this->request->query('retailerId');
        $brandId = $this->resolveBrandId();

        if (!$this->salesRepHasAccess($brandId, $retailerId)) {
            return;
        }
        $page = $this->request->query('page') ?? 1;
        $pageLimit = 5;
        if ($retailerId === null) {
            $this->_ajaxErrorResponse();

            return;
        }

        $notes = $this->ManufacturerRetailerNote->findForIndex($brandId, $retailerId, $page, $pageLimit);
        $count = $this->ManufacturerRetailerNote->countForIndex($brandId, $retailerId);
        if ($notes === null) {
            $this->_ajaxErrorResponse();

            return;
        }
        $editableAuthorIds = $this->getEditableAuthorIdsForUser($this->Auth->user());

        $notes = array_map(function($note) use ($editableAuthorIds) {
            $note['isEditable'] = $this->canEdit($note['ManufacturerRetailerNote']['id'], $this->Auth->user(), $editableAuthorIds, $note['User']['id']);
            $note['isEdited'] = $note['ManufacturerRetailerNote']['created_at'] != $note['ManufacturerRetailerNote']['updated_at'];
            return $note;
        }, $notes);
        $this->set('notes', $notes);
        $this->set('count', $count);
    }

    public function view(?int $id = null)
    {
        $brandId = $this->resolveBrandId();

        if ($id === null) {
            $this->set('note', ['User' => ['company_name' => User::revertAuthParent($this->Auth->user())['company_name']]]);

            return;
        }

        if (!$this->ManufacturerRetailerNote->existsForBrand($id, $brandId)) {
            $this->_ajaxErrorResponse('Note not found.');

            return;
        }
        $note = $this->ManufacturerRetailerNote->findById($id);
        if ($note === null) {
            $this->_ajaxErrorResponse();

            return;
        }
        $this->set('note', $note);
    }

    public function create(int $retailerId, $brandId)
    {
        $connectionId = $this->ManufacturerRetailer->getConnectionId($brandId, $retailerId);
        $authorId = User::revertAuthParent($this->Auth->user())['id'];
        $authorName = User::revertAuthParent($this->Auth->user())['company_name'];
        if ($connectionId === false) {
            $this->_ajaxErrorResponse('Brand and retailer are not connected.');

            return;
        }

        $saveData = [
            'content' => $this->request->data('content'),
            'manufacturer_retailer_id' => $connectionId,
            'author_id' => $authorId,
            'author_name' => $authorName,
        ];
        if ($this->ManufacturerRetailerNote->save($saveData) === false) {
            CakeLog::error(json_encode($this->ManufacturerRetailerNote->validationErrors));
            $this->_ajaxErrorResponse();

            return;
        }
        $this->_ajaxSuccessResponse('Note created.');
    }

    public function save()
    {
        $brandId = $this->resolveBrandId();
        $retailerId = $this->request->data('retailerId');
        if ($this->request->data('content') == null) {
            $this->_ajaxErrorResponse();
        }
        if (!$this->salesRepHasAccess($brandId, $retailerId)) {
            return;
        }

        if ($this->request->data('noteId') == 0) {
            $this->create($retailerId, $brandId);

            return;
        }
        $this->edit($this->request->data('noteId'), $brandId, $retailerId);
    }

    public function edit(int $id, int $brandId, int $retailerId)
    {
        if (!$this->ManufacturerRetailerNote->existsForBrand($id, $brandId)) {
            $this->_ajaxErrorResponse('Note not found.');

            return;
        }
        if (!$this->canEdit($id, $this->Auth->user(), $this->getEditableAuthorIdsForUser($this->Auth->user()))) {
            $this->_ajaxErrorResponse('User cannot edit this note.');

            return;
        }
        if (!$this->ManufacturerRetailerNote->edit($id, $this->request->data)) {
            CakeLog::error(json_encode($this->ManufacturerRetailerNote->validationErrors));
            $this->_ajaxErrorResponse();

            return;
        }
        $this->_ajaxSuccessResponse('Note updated.');
    }

    public function delete()
    {
        $brandId = $this->resolveBrandId();
        $retailerId = $this->request->data('retailerId');
        $noteId = $this->request->data('noteId');
        if ($noteId === null) {
            $this->_ajaxErrorResponse();

            return;
        }
        if (!$this->salesRepHasAccess($brandId, $retailerId)) {
            return;
        }

        if (!$this->ManufacturerRetailerNote->existsForBrand($noteId, $brandId)) {
            $this->_ajaxErrorResponse('Note not found.');

            return;
        }

        if (!$this->canEdit($noteId, $this->Auth->user(), $this->getEditableAuthorIdsForUser($this->Auth->user()))) {
            $this->_ajaxErrorResponse('User cannot delete this note.');

            return;
        }
        if (!$this->ManufacturerRetailerNote->delete($noteId)) {
            CakeLog::error(json_encode($this->ManufacturerRetailerNote->validationErrors));
            $this->_ajaxErrorResponse();
        }
        $this->_ajaxSuccessResponse('Note deleted.');
    }

    public function fetchBrands()
    {
        if (!$this->request->query('retailerId')) {
            $this->_ajaxErrorResponse('No retailer id provided');

            return;
        }
        $retailerId = $this->request->query('retailerId');
        if ($this->Auth->user('user_type') !== User::TYPE_SALES_REP) {
            $this->_ajaxErrorResponse(sprintf('fetchBrands only available for %s', User::TYPE_SALES_REP));

            return;
        }

        $brands = $this->ManufacturerRetailer->findAllSalesRepUserRetailerNames($this->Auth->user('id'), ['User.company_name' => 'ASC'], ['Retailer.id' => $retailerId]);
        $brands = array_column($brands, 'User');
        $brands = array_map(function($brand) {
            return ['id' => $brand['id'], 'text' => $brand['company_name']];
        }, $brands);
        $this->_ajaxSuccessResponse($brands);
    }

    protected function resolveBrandId()
    {
        $brandId = $this->request->data('brandId') ?? $this->request->query('brandId');
        $userType = $this->Auth->user('user_type');
        if ($userType === User::TYPE_MANUFACTURER) {
            $brandId = $this->Auth->user('id');
        }
        if (!$brandId) {
            return false;
        }

        return $brandId;
    }

    protected function salesRepHasAccess(int $brandId, int $retailerId)
    {
        $userType = $this->Auth->user('user_type');
        if (
            $userType === User::TYPE_SALES_REP
            && !$this->ManufacturerRetailerSalesRep->isConnectedToRetailer(
                $this->Auth->user('id'),
                $retailerId,
                $brandId
            )
        ) {
            $this->_ajaxErrorResponse('Sales rep not assigned to brand retailer connection.');

            return false;
        }

        return true;
    }
    protected function canEdit($noteId, $authUser, $editableAuthorIds, $authorId = null)
    {
        $authorId = $authorId ?? $this->ManufacturerRetailerNote->field('author_id', ['id' => $noteId]);
        $authUser = User::revertAuthParent($authUser);

        return in_array($authorId, $editableAuthorIds);
    }

    protected function getEditableAuthorIdsForUser($authUser)
    {
        $editableAuthorIds = [];
        $authUser = User::revertAuthParent($authUser);

        //can always edit their own notes
        $editableAuthorIds[] = $authUser['id'];

        //brands can edit staff member notes, vice versa
        if ($authUser['user_type'] == User::TYPE_MANUFACTURER) {
            $editableAuthorIds = array_merge($editableAuthorIds, array_values($this->BrandStaff->getAllStaffIdsForBrand($authUser['id'])));
        }
        if ($authUser['user_type'] == User::TYPE_BRAND_STAFF) {
            $editableAuthorIds[] = $authUser['Parent']['id'];
        }

        return $editableAuthorIds;
    }
}
