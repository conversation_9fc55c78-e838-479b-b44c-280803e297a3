<?php
App::uses('AppController', 'Controller');

/**
 * Class BranchsController.
 *
 * @property BranchLogicComponent $BranchLogic
 * @property UserLogicComponent $UserLogic
 * @property LightspeedComponent $Lightspeed
 * @property QuickbookComponent $Quickbook
 * @property PhpExcelComponent $PhpExcel
 * @property UploadComponent $Upload
 *
 * @property User $User
 * @property AppModel $Countries
 * @property State $State
 * @property Contact $Contact
 * @property EmailTemplate $EmailTemplate
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property QuickbookCompany $QuickbookCompany
 * @property ProductRetailer $ProductRetailer
 * @property Store $Store
 * @property Contactpersons $Contactpersons
 * @property UserSetting $UserSetting
 * @property UserSchedule $UserSchedule
 */
class BranchsController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Branchs';
    /**
     * @var array
     */
    public $components = array('BranchLogic', 'UserLogic', 'Lightspeed', 'Quickbook.Quickbook', 'PhpExcel', 'Upload');
    /**
     * @var array
     */
    public $uses = array('User', 'Countries', 'State', 'Contact', 'EmailTemplate', 'ManufacturerRetailer', 'Quickbook.QuickbookCompany', 'ProductRetailer', 'Store', 'Contactpersons', 'UserSetting', 'UserSchedule');

    /**
     *
     */
    public function beforeFilter()
    {
        parent::beforeFilter();
        $isBranchAction = in_array($this->request->param('action'), ['exportInventory', 'importInventory']);
        if ($this->Auth->user('user_type') != 'Retailer' || ($this->Auth->user('Branch') != 0 && !$isBranchAction)) {
            $this->redirect('/');
        }
    }

    /**
     * List all Branches associated with current retailer
     * @URL: /branchs
     */
    public function index()
    {
        $this->set('title_for_layout', __('Locations'));

        $noRecords = 10;
        if (!empty($this->request->data['Order']['noRecords'])) {
            $noRecords = $this->request->data['Order']['noRecords'];
        }
        $this->set('noRecords', $noRecords);

        $page = 1;
        if (!empty($this->request->params['pageNo'])) {
            $page = $this->request->params['pageNo'];
        }

        $this->BranchLogic->BranchListLogic($this->Auth->user('id'), $noRecords, $page);
    }

    /**
     * Return the table structure through ajax Response
     */
    public function ajax_index()
    {
        $this->layout = '';
        if ($this->request->is('ajax')) {
            $this->BranchLogic->BranchListLogic($this->Auth->user('id'), $this->request->data['noRecords'], $this->request->data['pageNumber'], $this->request->data['countBranches']);
        }
    }

    /**
     * Create new store(Branch)
     * @url: /branchs/addBranch
     */
    public function addBranch()
    {
        $this->autoRender = false;
        $this->set('title_for_layout', __('Add new Location'));

        if ($this->request->is('post')) {
            $this->request->data['User']['address'] = $this->setFormatAddress($this->request->data['User']['address1'], $this->request->data['User']['address2']);
            if (!empty($this->request->data['User']['zipcode'])) {
                /** update latitude &  longitude **/
                $geopoints = $this->_getLnt($this->request->data['User']['address'], $this->request->data['User']['city'], $this->request->data['User']['zipcode'], $this->request->data['User']['state_id'], $this->request->data['User']['country_id']);
                $this->request->data['User']['latitude'] = $geopoints['lat'];
                $this->request->data['User']['longitude'] = $geopoints['lng'];
                /** update latitude &  longitude **/
            }

            if (!empty($this->request->data['User']['inventory_type']) && $this->request->data['User']['inventory_type'] == 'none') {
                $this->request->data['User']['inventory_type'] = 'other';
                $this->request->data['User']['otherInventory'] = 'None';
            }

            $this->request->data['User']['Branch'] = $this->Auth->user('id');
            $overrideWithMasterFields = array_intersect_key($this->Auth->user(), array_flip([
                'user_type',
                'instore',
                'shipment',
                'nonstock',
                'shipment_type',
                'shipment_option',
                'free_shipping',
            ]));
            $this->request->data['User'] = array_merge($this->request->data['User'], $overrideWithMasterFields);

            if ($this->User->addUser($this->request->data)) {
                $data = $this->User->record($this->User->id);
                if ($data['User']['inventory_type'] == 'quickbook_pos' && $data['User']['user_type'] == 'Retailer') {
                    $this->request->data['User']['quickbook_priceLevel'] = 'Price1';
                    $data['User']['inventory_apiuser'] = $this->Quickbook->apikey($data['User']['inventory_apiuser'], $this->request->data['User']['quickbook_priceLevel'], $data['User']['id']);
                    $this->User->updateUser(array('User' => array('id' => $data['User']['id'], 'inventory_apiuser' => $data['User']['inventory_apiuser'])));
                }

                //Update store
                $this->Btask->queueStoreUpdate($data['User']['id']);

                //Contact table
                if (isset($this->request->data['User']['telephone']) && !empty($this->request->data['User']['telephone'])) {
                    $this->Contact->addContact($data['User']['id'], 'company', $data['User']['id'], 'telephone', $this->request->data['User']['telephone']);
                }

                $userSettingInfo['UserSetting']['user_id'] = $data['User']['id'];
                $this->UserSetting->saveUserSetting($userSettingInfo);

                // Create contact person array
                $person = array(
                    'user_id'   => $data['User']['id'],
                    'firstname' => $this->request->data['User']['first_name'],
                    'lastname'  => $this->request->data['User']['last_name'],
                    'email'     => $this->request->data['User']['email_address']
                );

                // Create default contact person
                if ($this->Contactpersons->addContactPerson($person)) {
                    $typeUid = $this->Contactpersons->getLastInsertId();
                    $this->Contact->addContact($data['User']['id'], 'person', $typeUid, 'telephone', $this->request->data['User']['telephone']);
                }

                $this->ManufacturerRetailer->createSettingsForNewStore($data['User']['id'], $data['User']['Branch']);

                $this->UserLogic->UpdateUserCat(array(), array_keys($this->Auth->user('user_categories')), $data['User']['id'], $data['User']['user_type'], $data['User']['Branch'], false);
                $this->UserLogic->newStoreNotification($data, array('id' => $this->Auth->user('id'), 'company_name' => $this->Auth->user('company_name')));

                $this->setFlash(__('New Location has been created successfully.'), 'success');
                $this->redirect(['controller' => 'branchs', 'action' => 'index']);
            }
        }

        $defaultToMasterFields = array_intersect_key($this->Auth->user(), array_flip([
            'company_name',
            'country_id',
            'inventory_apiuser',
            'inventory_password',
            'Inventory_Emp_ID',
            'Inventory_Reg_ID',
            'Inventory_Store_ID',
            'shop_url',
        ]));
        $this->request->data['User'] = (array)$this->request->data('User') + $defaultToMasterFields;

        if (!empty($this->request->data['User']['shop_url'])) {
            $this->request->data['User']['shopify_url'] = rtrim($this->request->data['User']['shop_url'], '/');
        }

        $this->getCountryState($this->request->data['User']['country_id']);
        $this->set('title_for_submit', __('Add Location'));
        $this->getLanguages();

        return $this->render('/Users/<USER>');
    }

    /**
     * Main Retailer can able to deactivate the his branch
     */
    public function deActiveBranch()
    {
        $this->autoRender = false;
        $this->layout = '';
        $id = $this->request->param('id');
        if (
            $id
            && $this->User->checkBranchPermission($id)
            && $this->User->deActive($id)
        ) {
            $this->setFlash(__('Location moved to Offline mode.'), 'success');
        }
        $this->redirect(['controller' => 'branchs', 'action' => 'index']);
    }

    /**
     * Main Retailer can able to activate the his branch
     */
    public function ActiveBranch()
    {
        $this->autoRender = false;
        $this->layout = '';
        $id = $this->request->param('id');
        if (
            $id
            && $this->User->checkBranchPermission($id)
            && $this->User->Active($id)
        ) {
            $this->setFlash(__('Location moved to Online mode.'), 'success');
        }
        $this->redirect(['controller' => 'branchs', 'action' => 'index']);
    }

    /**
     * Update store(Branch) Details
     * @url: /editBranch/:id
     */
    public function editBranch($id = null)
    {
        $this->autoRender = false;

        $branch = $this->User->findForEditBranch((int)$id);

        if (empty($branch['User']['id']) || $branch['User']['user_type'] !== User::TYPE_RETAILER || !$branch['User']['Branch']) {
            throw new NotFoundException('Branch not found where id=' . json_encode($id));
        }
        if ($branch['User']['Branch'] !== $this->Auth->user('id')) {
            throw new ForbiddenException(json_encode(compact('branch') + ['Auth' => User::extractAuthUserLogFields($this->Auth->user())]));
        }

        if ($this->request->is(['post', 'put'])) {
            if ($this->User->saveFromEditBranch($branch, $this->request->data)) {
                return $this->_successResponse(__('Location information updated successfully.'));
            }
        } else {
            $this->request->data = $branch;
        }

        $this->set('title_for_layout', __('Edit Location'));
        $this->set('title_for_submit', __('Update Location'));
        $this->getCountryState($this->request->data['User']['country_id']);
        $this->getLanguages();

        return $this->render('/Users/<USER>');
    }

    /**
     * Update store permission (Notification and email) Details
     *
     * @param int $id
     */
    public function storePermission($id = null)
    {
        if (!$this->User->checkPermission($id, $this->Auth->user('id'))) {
            $this->redirect($this->referer());
        }

        if ($this->request->is('post')) {
            if ($this->User->addStorePermission($id, $this->request->data)) {
                $this->setFlash(__('Location permission has been updated'), 'success');
            }
        }

        $store = $this->User->findById($id, ['id', 'email_address', 'company_name', 'permission'], null, -1);
        $store['User']['permission'] = json_decode($store['User']['permission'], true);
        $this->set('store', $store);

        $this->set('title_for_layout', "{$store['User']['company_name']} " . __('Access Level'));
    }

    public function getUserSchedule()
    {
        $this->autoRender = false;
        if ($this->request->isAjax()) {
            $branch_id = $this->request->query['user_id'];
            $userschedules = $this->UserSchedule->getUserSchedule($branch_id);

            return new CakeResponse(array('body' => json_encode(array('status' => 'success', 'data' => $userschedules)), 'status' => 200));
        }
    }

    public function saveSchedule()
    {
        $this->autoRender = false;
        if ($this->request->isAjax()) {

            $branch_id = $this->request->data['user_id'];
            $instorepickup_ids = json_decode($this->request->data['instorepickup_ids'], true);
            $instorepickup_names = json_decode($this->request->data['instorepickup_names'], true);
            $instorepickup_links = json_decode($this->request->data['instorepickup_links'], true);
            $localdelivery_ids = json_decode($this->request->data['localdelivery_ids'], true);
            $localdelivery_names = json_decode($this->request->data['localdelivery_names'], true);
            $localdelivery_links = json_decode($this->request->data['localdelivery_links'], true);
            $instorepickup_delete_ids = json_decode($this->request->data['instorepickup_delete_ids'], true);
            $localdelivery_delete_ids = json_decode($this->request->data['localdelivery_delete_ids'], true);
            $schedule_data = [];

            $instoreSchedules = $this->UserSchedule->getUserInStorePickupSchedule($branch_id, $instorepickup_ids);
            $localdeliverySchedules = $this->UserSchedule->getUserLocalDeliverySchedule($branch_id, $localdelivery_ids);

            if(!empty($instorepickup_delete_ids)) {
                $this->UserSchedule->deleteAll(array('user_id' => $branch_id, 'id' => $instorepickup_delete_ids), false);
            }

            if(!empty($localdelivery_delete_ids)) {
                $this->UserSchedule->deleteAll(array('user_id' => $branch_id, 'id' => $localdelivery_delete_ids), false);
            }

            if(empty($instorepickup_names)) {
                $this->UserSchedule->deleteAll(array('user_id' => $branch_id, 'order_type' => 'In_store'), false);
            }

            if(empty($localdelivery_names)) {
                $this->UserSchedule->deleteAll(array('user_id' => $branch_id, 'order_type' => 'Local_delivery'), false);
            }

            foreach ($instorepickup_names as $key => $value) {
                $pos = -1;
                foreach ($instoreSchedules as $i => $v) {
                    $idx = array_search($instorepickup_ids[$key], array_column($v, 'id'));
                    if($idx !== false) {
                        $pos = $i;
                    }
                }
                if($pos >= 0) {
                    $schedule_data[$key] = $instoreSchedules[$pos];
                    $schedule_data[$key]['UserSchedule']['schedule_name'] = $value;
                    $schedule_data[$key]['UserSchedule']['schedule_link'] = $instorepickup_links[$key];
                }
                else {
                    $schedule_data[$key]['UserSchedule']['user_id'] = $branch_id;
                    $schedule_data[$key]['UserSchedule']['order_type'] = 'In_store';
                    $schedule_data[$key]['UserSchedule']['schedule_name'] = $value;
                    $schedule_data[$key]['UserSchedule']['schedule_link'] = $instorepickup_links[$key];
                }
                
            }

            $schedule_data_length = count($schedule_data);
            foreach ($localdelivery_names as $key => $value) {
                $pos = -1;
                foreach ($localdeliverySchedules as $i => $v) {
                    $idx = array_search($localdelivery_ids[$key], array_column($v, 'id'));
                    if($idx !== false) {
                        $pos = $i;
                    }
                }
                if($pos >= 0) {
                    $schedule_data[$schedule_data_length+$key] = $localdeliverySchedules[$pos];
                    $schedule_data[$schedule_data_length+$key]['UserSchedule']['schedule_name'] = $value;
                    $schedule_data[$schedule_data_length+$key]['UserSchedule']['schedule_link'] = $localdelivery_links[$key];
                }
                else {
                    $schedule_data[$schedule_data_length+$key]['UserSchedule']['user_id'] = $branch_id;
                    $schedule_data[$schedule_data_length+$key]['UserSchedule']['order_type'] = 'Local_delivery';
                    $schedule_data[$schedule_data_length+$key]['UserSchedule']['schedule_name'] = $value;
                    $schedule_data[$schedule_data_length+$key]['UserSchedule']['schedule_link'] = $localdelivery_links[$key];
                }
            }
            if(!empty($schedule_data)) {
                $this->UserSchedule->saveMany($schedule_data);
            }

            $this->setFlash(__('Schedule has been setup successfully'), 'success');

                return new CakeResponse(array('body' => json_encode(array('status' => 'success', 'msg' => "Schedule has been setup successfully")), 'status' => 200));
        }
    }

    public function saveTaxData($userId = null)
    {
        $this->autoRender = false;

        if (!$this->User->existsById($userId)) {
            throw new NotFoundException();
        }

        if ($this->request->is(['post', 'put'])) {
            $taxRate = $this->request->data['User'][$userId]['defaultTax'];
            $taxName = $this->request->data['User'][$userId]['defaultTaxName'];

            $user = [
                'id' => $userId,
                'defaultTax' => $taxRate,
                'defaultTaxName' => $taxName,
            ];

            if ($this->User->save($user)) {
                return $this->_successResponse('Tax data has been saved successfully');
            } else {
                return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data])), 'Failed to save tax data', true);
            }
        }
    }

    public function exportInventory($id = null)
    {
        $this->_validateBranchId($id);
        $this->autoRender = false;

        $associations = array(
            'hasOne' => array(
                'ManufacturerRetailer' => array(
                    'type' => 'INNER',
                    'foreignKey' => false,
                    'conditions' => array(
                        'ManufacturerRetailer.user_id = Product.user_id',
                        'ManufacturerRetailer.retailer_id' => $id,
                        'ManufacturerRetailer.status' => ManufacturerRetailer::STATUS_CONNECTED,
                    ),
                ),
                'Store' => array(
                    'type' => 'LEFT',
                    'foreignKey' => 'productId',
                    'conditions' => array(
                        'Store.storeId' => $id,
                    ),
                )
            )
        );
        $conditions = array(
            'COALESCE(Product.product_upc, "") !=' => "",
            'Product.sell_direct !=' => Product::SELL_DIRECT_EXCLUSIVELY,
            'Product.product_status' => Product::STATUS_ENABLED,
            'Product.deleted' => false,
        );

        $this->Product->bindModel($associations);
        if ($this->Product->find('count', ['contain' => ['ManufacturerRetailer', 'Store'], 'conditions' => $conditions])) {
            $this->PhpExcel->createWorksheet();
            $this->PhpExcel->setDefaultFont('Calibri', 12);

            $tableHeader = array(
                array('label' => __('UPC'), 'filter' => true),
                array('label' => __('QOH')),
            );
            $this->PhpExcel->addTableHeader($tableHeader, array('name' => 'Cambria', 'bold' => true));

            $this->Product->bindModel($associations);
            $this->Product->streamPagedQuery(
                array(
                    'contain' => ['ManufacturerRetailer', 'Store'],
                    'conditions' => $conditions,
                    'fields' => array(
                        'Product.product_upc',
                        'Store.inventoryCount',
                    ),
                    'order' => ['Product.sort_order ASC'],
                ),
                function($product) {
                    $this->PhpExcel->addTableRow(array(
                        $product['Product']['product_upc'],
                        (int)$product['Store']['inventoryCount'],
                    ), ['string', 'number']);
                }
            );

            $this->PhpExcel->addTableFooter();
            if ($this->request->query('format') == 'csv') {
                $filename = $this->User->field('company_name', ['id' => $id]) . ' Inventory.csv';
                $this->PhpExcel->render($filename, 'Csv');
            } else {
                $filename = $this->User->field('company_name', ['id' => $id]) . ' Inventory.xlsx';
                $this->PhpExcel->render($filename);
            }
        }
    }

    public function importInventory($id = null)
    {
        $this->_validateBranchId($id);
        $this->request->allowMethod('post');

        try {
            $filePath = $this->Upload->getTempFile($this->request->data['Store']['inventory_upload']);
            $fileName = $this->request->data['Store']['inventory_upload']['name'] ?? null;

            $tableMap = $this->PhpExcel->extractTableData($filePath, $fileName);
            $tableHeaders = array_keys(current($tableMap));

            $fullFieldMap = [
                'product_upc' => 'UPC',
                'inventoryCount' => 'QOH',
            ];

            $fieldMap = array_filter($fullFieldMap, function($columnName) use ($tableHeaders) {
                return in_array($columnName, $tableHeaders);
            });

            $missingColumns = array_values(array_diff_key($fullFieldMap, $fieldMap));
            if ($missingColumns) {
                return $this->_exceptionResponse(new BadRequestException(), 'Import file missing required columns: ' . json_encode($missingColumns));
            }

            $this->Product->bindModel(['hasOne' => [
                'ManufacturerRetailer' => [
                    'type' => 'INNER',
                    'foreignKey' => false,
                    'conditions' => [
                        'ManufacturerRetailer.user_id = Product.user_id',
                        'ManufacturerRetailer.retailer_id' => $id,
                        'ManufacturerRetailer.status' => ManufacturerRetailer::STATUS_CONNECTED,
                    ],
                ],
            ]]);
            $products = $this->Product->find('all', [
                'contain' => ['ManufacturerRetailer'],
                'conditions' => [
                    'CAST(Product.product_upc AS UNSIGNED INTEGER) IN' => array_column($tableMap, $fieldMap['product_upc']),
                    'Product.sell_direct !=' => Product::SELL_DIRECT_EXCLUSIVELY,
                    'Product.product_status' => Product::STATUS_ENABLED,
                    'Product.deleted' => false,
                ],
                'fields' => ['Product.id', 'Product.product_upc'],
            ]);
            $productUpcs = array_column(array_column($products, 'Product'), 'product_upc', 'id');

            $tableMap = array_filter($tableMap, function($rowMap) use ($fieldMap, $productUpcs) {
                $tableUpc = $rowMap[$fieldMap['product_upc']];

                return in_array($tableUpc, $productUpcs);
            });

            $storeIdsByProduct = $this->Store->find('list', [
                'recursive' => -1,
                'conditions' => ['Store.storeId' => $id],
                'fields' => ['Store.productId', 'Store.id'],
            ]);

            $stores = array_reduce($tableMap, function($list, $rowMap) use ($id, $fieldMap, $productUpcs, $storeIdsByProduct) {
                $tableUpc = $rowMap[$fieldMap['product_upc']];
                $inventoryCount = $rowMap[$fieldMap['inventoryCount']];

                $productIds = array_keys($productUpcs, $tableUpc);

                $stores = array_map(function($productId) use ($id, $inventoryCount, $storeIdsByProduct) {
                    return [
                        'id' => $storeIdsByProduct[$productId] ?? null,
                        'storeId' => $id,
                        'productId' => $productId,
                        'inventoryCount' => $inventoryCount,
                        'itemId' => '',
                        'inventoryVariantId' => '',
                    ];
                }, $productIds);

                return array_merge($list, $stores);
            }, []);

            $deletedStoreIds = array_diff_key($storeIdsByProduct, array_column($stores, 'productId', 'productId'));

            $success = (
                $this->Store->resetInventory(['Store.id' => $deletedStoreIds]) &&
                $this->Store->updateStoreInventories($stores)
            );
            if (!$success) {
                throw new InternalErrorException(json_encode(['errors' => $this->Store->validationErrors, 'data' => $this->Store->data]));
            }

            if (!$this->ProductRetailer->updateStoreInventories(['user_id' => $this->User->getMainRetailerId($id)])) {
                throw new InternalErrorException(json_encode(['errors' => $this->ProductRetailer->validationErrors, 'data' => $this->ProductRetailer->data]));
            }

            $this->setFlash(__('Inventory successfully updated'), 'success');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash(__('An error occurred uploading the file'), 'error');
        } finally {
            if (!empty($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
        }

        return $this->redirect($this->referer());
    }

    /**
     * @param int $id
     * @return int $id
     */
    protected function _validateBranchId($id)
    {
        $retailer = $this->User->record($id, ['fields' => ['id', 'Branch', 'user_type']]);
        if (empty($retailer['User']['id'])) {
            throw new NotFoundException();
        }
        if (!in_array($this->Auth->user('id'), array($retailer['User']['id'], $retailer['User']['Branch']))) {
            throw new ForbiddenException();
        }
        if ($retailer['User']['user_type'] != 'Retailer') {
            throw new BadRequestException();
        }
        return $retailer['User']['id'];
    }

}
