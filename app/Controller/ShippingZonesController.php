<?php
App::uses('AppController', 'Controller');

/**
 * ShippingZones Controller.
 *
 * @property AuthComponent $Auth
 * @property ShippoComponent $Shippo
 *
 * @property Collection $Collection
 * @property ManufacturerSalesRep $ManufacturerSalesRep
 * @property PriceBasedShippingRate $PriceBasedShippingRate
 * @property Product $Product
 * @property ShippingCarrier $ShippingCarrier
 * @property ShippingZone $ShippingZone
 * @property UnitBasedShippingRate $UnitBasedShippingRate
 * @property User $User
 * @property UserAddress $UserAddress
 * @property UserCountryTax $UserCountryTax
 * @property UserShippingCarrier $UserShippingCarrier
 * @property UserShippingCarrierRate $UserShippingCarrierRate
 * @property UserShippingPackage $UserShippingPackage
 * @property UserTax $UserTax
 * @property WeightBasedShippingPrice $WeightBasedShippingPrice
 * @property ZoneTaxOverride $ZoneTaxOverride
 */
class ShippingZonesController extends AppController
{
    public $components = [
        'Auth',
        'Shippo',
    ];

    public $uses = [
        'Collection',
        'ManufacturerSalesRep',
        'PriceBasedShippingRate',
        'Product',
        'ShippingCarrier',
        'ShippingZone',
        'UnitBasedShippingRate',
        'User',
        'UserAddress',
        'UserCountryTax',
        'UserShippingCarrier',
        'UserShippingCarrierRate',
        'UserShippingPackage',
        'UserTax',
        'WeightBasedShippingPrice',
        'ZoneTaxOverride',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $authUser = $this->Auth->user();

            $this->Permissions->assertUserIsType($authUser, [User::TYPE_MANUFACTURER, User::TYPE_RETAILER], false);
            $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_SHIPMENT_SETTINGS, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function shipping_zones()
    {
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');
        $userIsBrand = in_array($userType, User::TYPES_BRAND, true);

        $validBrandIds = ($userIsBrand) ? [$userId] : array_keys($this->User->listConnectedBrandNames($userId));

        $this->set('currencycode', $this->Auth->user('currency_code'));

        /** @var Country $C */
        $C = ClassRegistry::init(['class' => 'Country', 'alias' => 'C']);
        $countries = $C->getCountryWithStatesCountList();
        $this->set('countries', $countries);

        $distributorOptions = ($userIsBrand) ? $this->ManufacturerSalesRep->listConnectedSalesReps($userId, true) : [];
        $this->set('distributorOptions', $distributorOptions);

        $zone_option = !empty($this->request->params['option']) ? $this->request->params['option'] : ShippingZone::TYPE_CONSUMER;
        $usershippingcountries = $this->ShippingZone->getUserShippingCountries($userId, $zone_option);

        $allusercountries = array_reduce(
            $usershippingcountries,
            function($map, $value) {
                $zonecountrycodes = json_decode($value['ShippingZone']['country_codes'], true);
                $zonecountrystatecodes = json_decode($value['ShippingZone']['state_codes'], true);
                foreach ($zonecountrycodes as $countryId) {
                    $idx = array_search($countryId, array_column($zonecountrystatecodes, 'id'));
                    if ($idx !== false) {
                        if (empty($map[$countryId])) {
                            $map[$countryId] = array();
                        }
                        $map[$countryId] = array_merge($map[$countryId], $zonecountrystatecodes[$idx]['states']);
                    }
                }
                return $map;
            },
            array()
        );

        $usercarrierselectlist = array_reduce(
            $this->UserShippingCarrier->getUserCarrierSelectList($userId),
            function($map, $value) {
                $map[$value['ShippingCarrier']['name']] = $value['UserShippingCarrier']['carrier_id'];
                return $map;
            },
            array()
        );
        $this->set('usercarrierlist', $usercarrierselectlist);

        $this->set('productCategories', $this->Product->getProductTypesByUser($validBrandIds));

        $type = (string)$this->request->param('type');
        if ($type == 'remove') {
            $this->autoRender = false;

            $messageType = 'success';
            $message = 'Your Shipping Zone was removed successfully';
            if (!$this->ShippingZone->delete($this->request->param('id'))) {
                $messageType = 'error';
                $message = 'Error removing your Shipping Zone';
            } elseif ($userIsBrand) {
                $this->resetOrphanedTaxSettings($userId);
            }
            $this->setFlash($message, $messageType);
            $this->response->body(json_encode([$messageType => $message]));

            return $this->response;
        } elseif ($type == 'edit') {
            $shippingzone = $this->ShippingZone->getShippingZoneByUUID($userId, $this->request->param('id'));
            if (empty($shippingzone['ShippingZone']['id'])) {
                throw new NotFoundException(json_encode(['error' => 'Shipping Zone not found', 'conditions' => ['user_id' => $userId, 'uuid' => $this->request->param('id')]]));
            }

            $countrycodes = json_decode($shippingzone['ShippingZone']['country_codes'], true);

            $state_list = array_reduce(
                $countrycodes,
                function($map, $value) {
                    $map[$value] = $this->State->getStateList($value);
                    return $map;
                },
                array()
            );

            $zonestatecodes = !empty($shippingzone['ShippingZone']['state_codes'])
                ? json_decode($shippingzone['ShippingZone']['state_codes'], true)
                : array();
            $zonestatebycountry = array_reduce(
                $countrycodes,
                function($map, $value) use ($zonestatecodes) {
                    foreach ($zonestatecodes as $zonestatedata) {
                        if ($zonestatedata['id'] === $value) {
                            $map[$value] = $zonestatedata['states'];
                        }
                    }
                    return $map;
                },
                array()
            );

            $zoneId = $shippingzone['ShippingZone']['id'];
            $weightBasedShippingPrices = $this->WeightBasedShippingPrice->getWeightBasedShippingPrices($userId, $zoneId);
            $priceBasedShippingRates = $this->PriceBasedShippingRate->getPriceBasedShippingRates($userId, $zoneId);
            $unitBasedShippingRates = $this->UnitBasedShippingRate->getUnitBasedShippingRates($userId, $zoneId);
            $usershippingcarrierrates = ($userIsBrand) ? $this->UserShippingCarrierRate->getUserShippingCarrierRateByZone($userId, $zoneId) : [];

            $this->set('weightBasedShippingPrices', $weightBasedShippingPrices);
            $this->set('priceBasedShippingRates',$priceBasedShippingRates);
            $this->set('unitBasedShippingRates',$unitBasedShippingRates);
            $this->set('usershippingcarrierrates_data', $usershippingcarrierrates);
            $this->set('zoneid', $zoneId);
            $this->set('zonename', $shippingzone['ShippingZone']['name']);
            $this->set('zoneoption', $shippingzone['ShippingZone']['type']);
            $this->set('distributorId', $shippingzone['ShippingZone']['distributor_id']);
            $this->set('countrycodes', $countrycodes);
            $this->set('countrynames', $this->Country->getCountriesByIds($countrycodes));
            $this->set('countryflags', $this->Country->getCountryIconsByIds($countrycodes));
            $this->set('usercountries', $allusercountries);
            $this->set('statesarray', $state_list);
            $this->set('states', json_encode($state_list));
            $this->set('zonestatebycountry', $zonestatebycountry);
        } else {
            $this->set('zoneoption', (string)$this->request->param('option'));
            $this->set('zoneid', "");
            $this->set('zonename', "");
            $this->set('distributorId', null);
            $this->set('countrynames', []);
            $this->set('countryflags', []);
            $this->set('usercountries', $allusercountries);
            $this->set('statesarray', []);
            $this->set('states', json_encode([]));
            $this->set('zonestatebycountry', []);
        }
    }

    public function shipping_zone_manage()
    {
        $this->autoRender = false;

        $urlType = $this->request->param('type');
        if ($urlType !== 'save') {
            throw new BadRequestException("Url param was '{$urlType}' when 'save' was expected");
        }

        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');
        $userIsBrand = in_array($userType, User::TYPES_BRAND, true);

        $zone_id = $this->request->data['zone_id'];
        $zone_option = $this->request->data['zone_option'];
        $zone_name = $this->request->data['zone_name'];
        $distributor_id = ($userIsBrand) ? $this->request->data['distributor_id'] : null;

        $countries = json_encode((array)$this->request->data['countries']);
        $statecodes = (string)$this->request->data['states'];

        $weightrates_data = json_decode((string)$this->request->data['weights'], true);
        $deleteweightrates_data = json_decode((string)$this->request->data['deleteweights'], true);

        $pricerates_data = json_decode((string)$this->request->data['prices'], true);
        $deletepricerates_data = json_decode((string)$this->request->data['deleteprices'], true);

        $unitrates_data = json_decode((string)$this->request->data['units'], true);
        $deleteunitrates_data = json_decode((string)$this->request->data['deleteunits'], true);

        $carrier_rates_data = ($userIsBrand) ? json_decode((string)$this->request->data['carriers'], true) : [];
        $delete_carrier_rates_data = ($userIsBrand) ? json_decode((string)$this->request->data['deletecarriers'], true) : [];

        $zone_id = $this->ShippingZone->field('id', ['id' => $zone_id, 'user_id' => $userId]);
        $created = !$zone_id;

        $result = $this->ShippingZone->save(array(
            'id' => $zone_id,
            'user_id' => $userId,
            'distributor_id' => $distributor_id,
            'type' => !empty($zone_option) ? $zone_option : 'consumer',
            'name' => $zone_name,
            'country_codes' => $countries,
            'state_codes' => $statecodes,
        ));
        if (!$result) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . json_encode(['errors' => $this->ShippingZone->validationErrors, 'data' => $this->ShippingZone->data]));
            throw new InternalErrorException();
        }

        $zone_id = (int)$this->ShippingZone->id;

        $this->WeightBasedShippingPrice->saveSetFromShippingZoneForm($userId, $zone_id, $weightrates_data, $deleteweightrates_data);
        $this->PriceBasedShippingRate->saveSetFromShippingZoneForm($userId, $zone_id, $pricerates_data, $deletepricerates_data);
        $this->UnitBasedShippingRate->saveSetFromShippingZoneForm($userId, $zone_id, $unitrates_data, $deleteunitrates_data);
        if ($userIsBrand) {
            $this->UserShippingCarrierRate->saveSetFromShippingZoneForm($userId, $zone_id, $carrier_rates_data, $delete_carrier_rates_data);
            $this->ZoneTaxOverride->saveSetFromEdit($userId, $zone_id, $this->request->data('ZoneTaxOverride'));
            $this->resetOrphanedTaxSettings($userId);
        }

        if ($created) {
            $this->setFlash("Your Shipping Zone was setup successfully", 'success');
        } else {
            $this->setFlash("Your Shipping Zone was successfully updated", 'success');
        }

        $shippingzone = $this->ShippingZone->getShippingZone($userId, $zone_id);
        $this->response->body(json_encode(['id' => $shippingzone['ShippingZone']['uuid'], 'shippingzone' => $shippingzone]));
        return $this->response;
    }

    protected function resetOrphanedTaxSettings(int $userId): bool
    {
        $shippingCountryIds = $this->ShippingZone->findAllShippingCountryIds($userId);

        return (
            $this->UserCountryTax->deleteAllJoinless([
                'UserCountryTax.user_id' => $userId,
                'UserCountryTax.country_id !=' => $shippingCountryIds,
            ], false)
            && $this->UserTax->deleteAllJoinless([
                'UserTax.user_id' => $userId,
                $this->State->buildExistsSubquery([
                    'State.id' => $this->UserTax->identifier('UserTax.state_id'),
                    'State.country_id !=' => $shippingCountryIds,
                ]),
            ], false)
        );
    }

    public function tax_override_form($taxOverrideId = null)
    {
        $userId = $this->Auth->user('id');
        if ($taxOverrideId && !$this->ZoneTaxOverride->validUser($taxOverrideId, $userId)) {
            throw new ForbiddenException();
        }

        $this->set('productTitles', $this->Product->getProductTitlesByUser($userId));
        $this->set('collections', $this->Collection->getCollectionsByUser($userId, true));

        $taxOverride = ['ZoneTaxOverride' => (array)$this->request->data('ZoneTaxOverride')];
        if (!empty($taxOverrideId)) {
            // Fill in any missing fields with existing record
            $taxOverride['ZoneTaxOverride']['id'] = $taxOverrideId;
            $existing = $this->ZoneTaxOverride->findForEdit($taxOverrideId);
            $taxOverride['ZoneTaxOverride'] += $existing['ZoneTaxOverride'];
        }

        $this->set('title_for_layout', 'Add Tax Override');
        if (!empty($taxOverride['ZoneTaxOverride']['key'])) {
            $this->set('title_for_layout', 'Edit Tax Override');
        }

        $this->set('taxOverride', $taxOverride);
    }

    public function ajax_tax_override_table($shippingZoneId = null)
    {
        $userId = (int)$this->Auth->user('id');
        if ($shippingZoneId && !$this->ShippingZone->validUser($shippingZoneId, $userId)) {
            throw new ForbiddenException();
        }

        $this->set('taxOverrides', $this->ZoneTaxOverride->findForIndex($shippingZoneId));
    }

    public function ajax_tax_override_row($taxOverrideId = null)
    {
        $userId = $this->Auth->user('id');
        if ($taxOverrideId && !$this->ZoneTaxOverride->validUser($taxOverrideId, $userId)) {
            throw new ForbiddenException();
        }

        $taxOverride = ['ZoneTaxOverride' => (array)$this->request->data('ZoneTaxOverride')];
        if (!empty($taxOverrideId)) {
            // Fill in any missing fields with existing record
            $taxOverride['ZoneTaxOverride']['id'] = $taxOverrideId;
            $existing = $this->ZoneTaxOverride->findForEdit($taxOverrideId);
            $taxOverride['ZoneTaxOverride'] += $existing['ZoneTaxOverride'];
        }

        if (empty($taxOverride['ZoneTaxOverride']['key'])) {
            $taxOverride['ZoneTaxOverride']['key'] = generate_key([
                $taxOverride['ZoneTaxOverride']['name'],
                $taxOverride['ZoneTaxOverride']['is_shipping_tax'],
            ]);
        }

        $this->set('taxOverride', $taxOverride);
        $this->render('/Elements/ShippingZones/ajax_tax_override_row');
    }

    public function ajax_shipment_origin()
    {
        $this->autoRender = false;
        if ($this->request->is('ajax')) {
            $userAddress = $this->request->data['UserAddress'];
            $geopoints = $this->_getLnt($userAddress['address1'], $userAddress['city'], $userAddress['zipcode'], $userAddress['state_id'], $userAddress['country_id']);
            $userAddress['latitude'] = $geopoints['lat'];
            $userAddress['longitude'] = $geopoints['lng'];
            $userAddressModel = $this->UserAddress->getAddress($this->Auth->user('id'));
            if($userAddressModel) {
                $userAddressModel['UserAddress']['address1'] = $userAddress['address1'];
                $userAddressModel['UserAddress']['address2'] = $userAddress['address2'];
                $userAddressModel['UserAddress']['city'] = $userAddress['city'];
                $userAddressModel['UserAddress']['zipcode'] = $userAddress['zipcode'];
                $userAddressModel['UserAddress']['state_id'] = $userAddress['state_id'];
                $userAddressModel['UserAddress']['country_id'] =  $userAddress['country_id'];
                $userAddressModel['UserAddress']['latitude'] =  $userAddress['latitude'];
                $userAddressModel['UserAddress']['longitude'] =  $userAddress['longitude'];
                $this->UserAddress->save($userAddressModel['UserAddress']);
            }
            else {
                $userAddress['user_id'] = $this->Auth->user('id');
                $this->UserAddress->save($userAddress);
            }

            $this->setFlash("Your Shipping Origin was changed successfully", 'success');
            echo new CakeResponse(array('body' => json_encode(array('success' => 'Your Shipping Origin was changed successfully')), 'status' => 200));
        }
    }

    public function shipping_carriers()
    {
        $carrier_data = $this->ShippingCarrier->listActiveCarriersByName();

        $user_carrier_parameter_values = $this->UserShippingCarrier->listUserCarriersByCarrierName($this->Auth->user('id'));
        $user_carriers = array_column($user_carrier_parameter_values, 'carrier_id');

        $this->set('carrier_account_parameters', $carrier_data);
        $this->set('user_carriers', $user_carriers);
        $this->set('user_carrier_parameter_values', $user_carrier_parameter_values);
    }

    public function ajax_user_carrier()
    {
        $this->autoRender = false;
        if ($this->request->isAll(['post', 'ajax'])) {
            $userId = $this->Auth->user('id');
            $carrierName = $this->request->data['carrier_id'];

            $carrierId = $this->ShippingCarrier->findIdByName($carrierName);
            $existingUserCarrier = $this->UserShippingCarrier->getUserCarrier($userId, $carrierId);
            if (isset($existingUserCarrier['UserShippingCarrier'])) {
                $existingUserCarrier = $existingUserCarrier['UserShippingCarrier'];
            }

            $action = $this->request->params['type'];
            if ($action == 'save') {
                $carrier_parameters = $this->request->data['carrier_parameters'];
                $parameters = json_decode($carrier_parameters, true);

                $shippoAccount = $this->Shippo->findCarrierAccountByAccountId(
                    $parameters['parameter1'], ['carrier' => $carrierName]
                );

                if ($shippoAccount) {
                    if ($shippoAccount['is_shippo_account']) {
                        // Override the input account id with Shippo's account id
                        $parameters['parameter1'] = $shippoAccount['account_id'];
                        $carrier_parameters = json_encode($parameters);
                    }

                    $response = $this->Shippo->updateCarrierAccount(
                        $shippoAccount['object_id'], $carrierName, $parameters
                    );
                } else {
                    $response = $this->Shippo->createCarrierAccount($carrierName, $parameters);
                }
                if (!isset($response['object_id'])) {
                    $this->setFlash("An error occurred updating the carrier account", 'success');
                    $this->response->body(json_encode(['status' => 'error', 'msg' => "An error occurred updating the carrier account"]));
                    return $this->response;
                }

                $userCarrier = array(
                    'user_id' => $userId,
                    'carrier_id' => $carrierId,
                    'object_id' => $response['object_id'],
                    'object_owner' => $response['object_owner'],
                    'account_id' => $parameters['parameter1'],
                    'parameters' => $carrier_parameters,
                    'is_active' => $response['active'],
                    'is_test' => $response['test'],
                    'is_connected' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                );

                if (!empty($existingUserCarrier)) {
                    $userCarrier['id'] = $existingUserCarrier['id'];
                    $userCarrier['created_at'] = $existingUserCarrier['created_at'];
                }

                $this->UserShippingCarrier->save($userCarrier);

                $this->setFlash("Carrier has been activated successfully", 'success');
                $this->response->body(json_encode(['status' => 'success', 'msg' => "Carrier has been activated successfully"]));
                return $this->response;
            } elseif ($action == 'delete') {
                $delete_status = $this->UserShippingCarrier->delete($existingUserCarrier['id']);
                if ($delete_status) {
                    $this->UserShippingCarrierRate->deleteUserCarrierRates($userId, $existingUserCarrier['carrier_id']);
                }

                $this->setFlash("Carrier has been deactivated", 'success');
                $this->response->body(json_encode(['status' => 'error', 'msg' => "Carrier has been deactivated"]));
                return $this->response;
            }
        }
    }

    public function ajax_user_package()
    {
        $this->autoRender = false;

        $userId = (int)$this->Auth->user('id');
        $action = (string)$this->request->param('type');

        if ($this->request->is('ajax')) {
            $package_id = $this->request->data['package_id']
                ? (int)$this->UserShippingPackage->fieldByConditions('id', ['id' => $this->request->data['package_id'], 'user_id' => $userId])
                : null;

            if ($action === 'save') {
                $this->request->allowMethod('post', 'put');

                $created = !$package_id;
                $isDefault = (bool)$this->request->data['is_default'];

                $data = [
                    'id' => $package_id,
                    'user_id' => $userId,
                    'name' => $this->request->data['name'],
                    'type' => $this->request->data['type'],
                    'length' => $this->request->data['length'],
                    'width' => $this->request->data['width'],
                    'height' => $this->request->data['height'],
                    'dimension_unit' => $this->request->data['dimension_unit'],
                    'weight' => $this->request->data['weight'],
                    'weight_unit' => $this->request->data['weight_unit'],
                    'is_default' => $isDefault,
                ];
                if (!$this->UserShippingPackage->save($data)) {
                    $message = null;
                    $this->setFlash($this->_buildFlashMessage('error', $message), 'error');

                    //TODO Error response after implementing a front end error handler
                    $this->response->body(json_encode(['status' => 'success', 'msg' => $message]));

                    return $this->response;
                }
                $package_id = (int)$this->UserShippingPackage->id;

                if ($isDefault) {
                    $this->UserShippingPackage->resetUserPackageDefaultStatus($userId, $package_id);
                }

                $message = ($created)
                    ? __('Package has been created successfully')
                    : __('Package has been updated successfully');
                $this->setFlash($this->_buildFlashMessage('success', $message), 'success');
                $this->response->body(json_encode(['status' => 'success', 'msg' => $message]));

                return $this->response;
            } elseif ($action === 'delete') {
                $this->request->allowMethod('post', 'delete');

                $this->UserShippingPackage->delete($package_id);

                $message = __('Package has been removed');
                $this->setFlash($this->_buildFlashMessage('success', $message), 'success');
                $this->response->body(json_encode(['status' => 'success', 'msg' => $message]));

                return $this->response;
            }

        }
    }

    /**
     * Save, edit, delete weight based shipping price data's in WeightBasedShippingPrice table
     */
    public function weightBasedShippingPrice()
    {
        $this->autoRender = false;
        $weightBasedShippingPriceInfo = array();
        if ($this->request->is('ajax')) {
            if (!empty($this->request->params['id'])) {
                $weightBasedShippingPriceInfo['id'] = $this->request->params['id'];
                $type = $this->request->param('type');
                if ($type === 'retrieve') {
                    $weightBasedShippingPrice = $this->WeightBasedShippingPrice->getWeightBasedShippingPrice($weightBasedShippingPriceInfo['id']);
                    if(!empty($weightBasedShippingPrice)) {
                        return new CakeResponse(array('body' => json_encode(array('editSuccess' => 'success', 'data' => $weightBasedShippingPrice)), 'status' => 200));
                    } else {
                        $message = "Please try again";
                        return new CakeResponse(array('body' => json_encode(array('success' => 'error', 'message' => $message)), 'status' => 200));
                    }
                }
                if ($type === 'remove') {
                    $weightBasedShippingPrice = $this->WeightBasedShippingPrice->delete($weightBasedShippingPriceInfo['id']);
                    if($weightBasedShippingPrice) {
                        $returnResponse = "success";
                        $message = __('Shipment Settings updated successfully');
                    } else {
                        $returnResponse = "error";
                        $message = "Please try again";
                    }
                }
                if ($type === 'save') {
                    $weightBasedShippingPriceInfo['user_id'] = $this->Auth->user('id');
                    $weightBasedShippingPriceInfo = $this->request->data;
                    if ($this->WeightBasedShippingPrice->updateWeightBasedShippingPrice($weightBasedShippingPriceInfo)) {
                        $returnResponse = "success";
                        $message = __('Shipment Settings updated successfully');
                    } else {
                        $returnResponse = "error";
                        $message = "Please try again";
                    }
                }
            } else {
                $weightBasedShippingPriceInfo['user_id'] = $this->Auth->user('id');
                $weightBasedShippingPriceInfo = $this->request->data;
                if ($this->WeightBasedShippingPrice->addWeightBasedShippingPrice($weightBasedShippingPriceInfo)) {
                    $returnResponse = "success";
                    $message = __('Shipment Settings updated successfully');
                } else {
                    $returnResponse = "error";
                    $message = "Please try again";
                }
            }
            if($returnResponse == "success") {
                $this->setFlash($message, 'success');
                return new CakeResponse(array('body' => json_encode(array('success' => 'success', 'message' => $message)), 'status' => 200));
            }
            if($returnResponse == "error") {
                $this->setFlash($message, 'success');
                return new CakeResponse(array('body' => json_encode(array('success' => 'error', 'message' => $message)), 'status' => 200));
            }
        }
    }

}
