<?php

use Stripe\Exception\ApiErrorException;

App::uses('App<PERSON><PERSON>roller', 'Controller');

/**
 * Class BankAccountsController
 *
 * @property StripeComponent $Stripe
 *
 * @property StripeUser $StripeUser
 */
class BankAccountsController extends AppController
{
    /**
     * @var array
     */
    public $components = [
        'Stripe.Stripe',
    ];

    /**
     * @var array
     */
    public $uses = [
        'StripeUser',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_RETAILER);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    /**
     * Add a stripe bank account to a customer
     * @param int $brandId brand id who who is destination for payment methods
     * @return CakeResponse|null|void
     * @throws CakeSessionException
     * @throws InvalidArgumentException
     * @throws CakeException
     * @throws MissingTableException
     * @throws MissingDatasourceException
     * @throws Exception
     */
    public function add(int $brandId)
    {
        if ($this->request->is('post')) {
            $stripeConnectedAccountId = $this->StripeUser->getAccountId($brandId);
            $retailerId = (int)$this->Auth->user('id');
            $tokenId = (string)$this->request->data('bank_account_add.stripeToken');

            try {
                $retailer = $this->User->findForStripeConnectedCustomerCreation($retailerId);
                $address = $this->User->findB2bShipToUserOrderAddress($brandId, $retailerId);
                $stripeCustomerId = $this->Stripe->createB2bCustomerIfNotExists($brandId, (array)$retailer['User'], $address, $stripeConnectedAccountId);

                $this->Stripe->updateCustomerPAD($stripeConnectedAccountId, $stripeCustomerId, $tokenId);

                return $this->_successResponse('Account Added.');
            } catch (\Stripe\Exception\ApiErrorException $e) {
                CakeLog::error($e);

                return $this->_exceptionResponse(new BadRequestException($e->getMessage()), $e->getMessage());
            }
        }

        $countryOptions = ['US', 'CA'];
        $countryOptions = array_combine($countryOptions, $countryOptions);

        $this->set('countryOptions', $countryOptions);
    }


    /**
     * Get a token for a SetupIntent
     * @param int $brandId brand id who who is destination for payment methods
     * @return CakeResponse|null 
     * @throws CakeSessionException 
     * @throws InvalidArgumentException 
     * @throws MissingTableException 
     * @throws MissingDatasourceException 
     * @throws CakeException 
     * @throws \Stripe\Exception\ApiErrorException
     * @throws Exception 
     */
    public function get_setup_token(int $brandId)
    {
        $this->autoRender = false;
        $retailerId = (int)$this->Auth->user('id');
        $stripeConnectedAccountId = $this->StripeUser->getAccountId($brandId);

        $country = $this->request->query('country');

        $retailer = $this->User->findForStripeConnectedCustomerCreation($retailerId);
        $address = $this->User->findB2bShipToUserOrderAddress($brandId, $retailerId);
        $stripeCustomerId = $this->Stripe->createB2bCustomerIfNotExists($brandId, (array)$retailer['User'], $address, $stripeConnectedAccountId);

        if ($country === 'CA') {
            $intent = $this->Stripe->getPadIntentCa($stripeConnectedAccountId, $stripeCustomerId);
        } elseif ($country === 'US') {
            $intent = $this->Stripe->getPadIntentUs($stripeConnectedAccountId, $stripeCustomerId);
        } else {
            return $this->_exceptionResponse(
                new BadRequestException('Unrecognized Country: ' . json_encode($country)),
                'Unrecognized Country',
                true
            );
        }

        $this->response->body(json_encode([
            'client_secret' => $intent->client_secret,
            'billing_email' => $retailer['User']['email_address'],
            'billing_name' => $retailer['User']['company_name'],
        ]));

        return $this->response;
    }

    /**
     * detach a payment method from a its attached customer
     * @param int $brandId brand id who who is destination for payment methods
     * @return CakeResponse|null 
     * @throws ApiErrorException 
     * @throws ApiErrorException 
     * @throws InvalidArgumentException 
     * @throws CakeException 
     */
    public function remove(int $brandId)
    {
        $this->autoRender = false;
        $this->request->allowMethod('post', 'delete');
        $paymentMethodId = $this->request->data('paymentMethodId');
        if (!(bool)$paymentMethodId){
            return $this->_exceptionResponse(
                new BadRequestException('Missing paymentMethodId'),
                'Missing paymentMethodId',
                true
            );
        }
        $stripeConnectedAccountId = $this->StripeUser->getAccountId($brandId);
        if($this->Stripe->detachPaymentMethod($stripeConnectedAccountId, $paymentMethodId)){
            $this->response->body(json_encode([
                'success' => true,
                'message' => 'Bank account removed.'
            ]));
        } else {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'An error occurred removing the account.'
            ]));
        }
        return $this->response;
    }
}
