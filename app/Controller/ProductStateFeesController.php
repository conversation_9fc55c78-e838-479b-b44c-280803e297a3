<?php
App::uses('AppController', 'Controller');
App::uses('User', 'Model');
App::uses('Permissions', 'Utility');
App::uses('ProductSellDirect', 'Utility');
App::uses('ProductStatus', 'Utility');

/**
 * ProductStateFees Controller
 *
 * @property PhpExcelComponent $PhpExcel
 * @property SpoutComponent $Spout
 * @property UploadComponent $Upload
 *
 * @property ProductStateFee $ProductStateFee
 * @property Product $Product
 * @property Country $Country
 * @property State $State
 * @property User $User
 * @property UserSetting $UserSetting
 */
class ProductStateFeesController extends AppController
{
    public $components = [
        'PhpExcel',
        'Spout',
        'Upload',
    ];

    public $uses = [
        'ProductStateFee',
        'Product',
        'Country',
        'State',
        'User',
        'UserSetting',
    ];

    public $importHeaders = [
        'Product.product_sku' => 'Part #',
    ];

    /**
     * Country codes where fees are saved at the state level.
     *
     * Declared public so that it can be overridden by tests.
     *
     * @var string[]
     */
    public array $stateFeeCountryCodes = ['US', 'CA'];

    public static function getFeeAmountHeader($stateAlias)
    {
        return $stateAlias;
    }

    public static function getB2bFeeAmountHeader($stateAlias)
    {
        return ($stateAlias . ' B2B');
    }

    public static function getIsTaxableHeader($stateAlias)
    {
        return ($stateAlias . ' TAXABLE');
    }

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        if (!$this->Product->exists(['Product.id' => $this->request->param('fee_product_id'), 'Product.user_id' => $this->Auth->user('id')])) {
            // Show the 404 page for an invalid URL
            throw new NotFoundException(json_encode(['Auth' => User::extractAuthUserLogFields($this->Auth->user())]));
        }

        return true;
    }

    public function import($feeProductId = null)
    {
        $userId = $this->Auth->user('id');

        if ($this->request->is('post')) {
            $filePath = $this->Upload->getTempFile($this->request->data('ProductStateFee.upload'));
            $fileName = $this->request->data('ProductStateFee.upload.name');

            $tableMap = $this->Spout->extractTableData($filePath, $fileName);
            $tableHeaders = array_keys(current($tableMap));

            $importHeaders = array_filter($this->importHeaders, function($columnName) use ($tableHeaders) {
                return in_array($columnName, $tableHeaders);
            });
            if (!isset($importHeaders['Product.product_sku'])) {
                return $this->_exceptionResponse(new BadRequestException(), 'Import file missing required column: ' . json_encode($this->importHeaders['Product.product_sku']));
            }

            $skus = array_column($tableMap, $importHeaders['Product.product_sku']);

            $duplicateSkus = array_unique(array_diff_assoc($skus, array_unique($skus)));
            if ($duplicateSkus) {
                return $this->_exceptionResponse(new BadRequestException(), "Duplicate {$importHeaders['Product.product_sku']} found in import file: " . implode(', ', $duplicateSkus));
            }

            $productIdsBySku = $this->Product->find('list', [
                'recursive' => -1,
                'conditions' => ['Product.product_sku' => $skus] + $this->_getProductConditions($feeProductId, $userId),
                'fields' => ['Product.product_sku', 'Product.id'],
            ]);
            $tableMap = array_filter($tableMap, function($row) use ($importHeaders, $productIdsBySku) {
                return !empty($productIdsBySku[$row[$importHeaders['Product.product_sku']]]);
            });
            if (!$tableMap) {
                return $this->_exceptionResponse(new BadRequestException(), 'Import file did not have any valid products');
            }

            $selectedCountries = $this->Country->findAllShippingCountriesById($userId);
            if (!$selectedCountries) {
                return $this->_exceptionResponse(new NotFoundException(), __('No eCommerce shipping countries selected. Make sure they are configured and try again.'));
            }
            $countryIds = array_keys($selectedCountries);

            $stateAliasesByCountryIdAndStateId = $this->State->listAliasesByCountryIdAndStateId([
                'State.country_id' => $countryIds,
                'State.country_code' => $this->stateFeeCountryCodes,
            ]);

            $countryIdByAlias = array_column($selectedCountries, 'id', 'country_code');
            $countryIdByAlias = array_intersect_key($countryIdByAlias, current($tableMap));

            $stateByAlias = [];
            foreach ($stateAliasesByCountryIdAndStateId as $countryId => $stateAliasById) {
                $stateByAlias += array_map(fn(int $stateId): array => [
                    'id' => $stateId,
                    'country_id' => $countryId,
                ], array_flip($stateAliasById));
            }
            $stateByAlias = array_intersect_key($stateByAlias, current($tableMap));

            // If a state and country have the same alias (eg. IM or VA), prefer the country mapping.
            $stateByAlias = array_diff_key($stateByAlias, $countryIdByAlias);

            if (!$stateByAlias && !$countryIdByAlias) {
                return $this->_exceptionResponse(new BadRequestException(), 'Import file did not have any valid states');
            }

            $existingRecords = (array)$this->ProductStateFee->find('all', [
                'conditions' => [
                    'ProductStateFee.fee_product_id' => $feeProductId,
                    'ProductStateFee.product_id' => $productIdsBySku,
                    'OR' => [
                        'ProductStateFee.country_id' => array_values($countryIdByAlias),
                        'ProductStateFee.state_id' => array_column($stateByAlias, 'id'),
                    ],
                ],
                'fields' => ['id', 'product_id', 'country_id', 'state_id'],
            ]);
            $productCountryFeeIdMap = Hash::combine(
                $existingRecords,
                '{n}.ProductStateFee.country_id',
                '{n}.ProductStateFee.id',
                '{n}.ProductStateFee.product_id'
            );
            $productStateFeeIdMap = Hash::combine(
                $existingRecords,
                '{n}.ProductStateFee.state_id',
                '{n}.ProductStateFee.id',
                '{n}.ProductStateFee.product_id'
            );

            $saveMany = array_reduce($tableMap, function($list, $row) use ($feeProductId, $importHeaders, $productIdsBySku, $countryIdByAlias, $stateByAlias, $productCountryFeeIdMap, $productStateFeeIdMap) {
                $productId = $productIdsBySku[$row[$importHeaders['Product.product_sku']]];
                foreach ($stateByAlias as $stateAlias => $state) {
                    $stateId = $state['id'];
                    $stateCountryId = $state['country_id'];

                    $productStateFee = $this->_buildFeeRecordFromImportRow($stateAlias, $row);

                    if ($productStateFee) {
                        $keys = [
                            'id' => $productStateFeeIdMap[$productId][$stateId] ?? null,
                            'product_id' => $productId,
                            'country_id' => $stateCountryId,
                            'state_id' => $stateId,
                            'fee_product_id' => $feeProductId,
                        ];

                        $list[] = $keys + $productStateFee;
                    }
                }

                foreach ($countryIdByAlias as $countryAlias => $countryId) {
                    $productCountryFee = $this->_buildFeeRecordFromImportRow($countryAlias, $row);

                    if ($productCountryFee) {
                        $keys = [
                            'id' => $productCountryFeeIdMap[$productId][$countryId] ?? null,
                            'product_id' => $productId,
                            'country_id' => $countryId,
                            'state_id' => null,
                            'fee_product_id' => $feeProductId,
                        ];

                        $list[] = $keys + $productCountryFee;
                    }
                }

                return $list;
            }, []);

            if (!$saveMany) {
                return $this->_exceptionResponse(new BadRequestException(), 'Import file did not provide any fee values');
            }

            $success = $this->ProductStateFee->doInTransaction(function() use ($userId, $feeProductId, $saveMany) {
                $deleteConditions = [
                    'ProductStateFee.fee_product_id' => $feeProductId,
                    'ProductStateFee.id !=' => array_filter(array_column($saveMany, 'id')),
                ];

                return $this->ProductStateFee->deleteAllJoinless($deleteConditions, false) &&
                       $this->ProductStateFee->saveMany($saveMany);
            });
            if ($success) {
                return $this->_successResponse();
            }

            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->ProductStateFee->validationErrors, 'data' => $this->ProductStateFee->data])), null, true);
        }
    }

    private function _buildFeeRecordFromImportRow(string $columnAlias, array $row): array
    {
        $record = [];

        $feeAmountHeader = static::getFeeAmountHeader($columnAlias);
        if (is_numeric($row[$feeAmountHeader] ?? '') && $row[$feeAmountHeader] > 0) {
            $record['fee_amount'] = $row[$feeAmountHeader];
        }
        $b2bFeeAmountHeader = static::getB2bFeeAmountHeader($columnAlias);
        if (is_numeric($row[$b2bFeeAmountHeader] ?? '') && $row[$b2bFeeAmountHeader] > 0) {
            $record['b2b_fee_amount'] = $row[$b2bFeeAmountHeader];
        }

        // Require at least one fee amount to save the record
        if (!$record) {
            return [];
        }

        $record['fee_amount'] = $record['fee_amount'] ?? 0;
        $record['b2b_fee_amount'] = $record['b2b_fee_amount'] ?? 0;

        $isTaxableHeader = static::getIsTaxableHeader($columnAlias);
        if (array_key_exists($isTaxableHeader, $row)) {
            $record['is_taxable'] = filter_var($row[$isTaxableHeader], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
        }

        return $record;
    }

    public function export($feeProductId = null)
    {
        $this->autoRender = false;

        $userId = $this->Auth->user('id');

        $productConditions = $this->_getProductConditions($feeProductId, $userId);

        if (!$this->Product->exists($productConditions)) {
            return $this->_exceptionResponse(new NotFoundException(), 'No active products found. Make sure they are configured and try again.');
        }

        $selectedCountries = $this->Country->findAllShippingCountriesById($userId);
        if (!$selectedCountries) {
            return $this->_exceptionResponse(new NotFoundException(), __('No eCommerce shipping countries selected. Make sure they are configured and try again.'));
        }
        $countryIds = array_keys($selectedCountries);

        $stateAliasesByCountryIdAndStateId = $this->State->listAliasesByCountryIdAndStateId([
            'State.country_id' => $countryIds,
            'State.country_code' => $this->stateFeeCountryCodes,
        ]);

        $filteredStates = [];
        $filteredCountries = [];
        foreach ($selectedCountries as $countryId => $country) {
            $stateAliasesById = $stateAliasesByCountryIdAndStateId[$countryId] ?? [];
            if ($stateAliasesById) {
                $filteredStates += $stateAliasesById;
            } else {
                $filteredCountries += [$countryId => $country['country_code']];
            }
        }

        $tableModel = [
            $this->PhpExcel->newExportColumn($this->importHeaders['Product.product_sku'], function($row) {
                return $row['Product']['product_sku'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Item Description'), function($row) {
                return $row['Product']['product_title'];
            }),
        ];

        foreach ($filteredStates as $stateId => $stateAlias) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn(static::getFeeAmountHeader($stateAlias), function($row) use ($stateId) {
                    return $row['FeeByStateId'][$stateId]['fee_amount'] ?? null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(static::getB2bFeeAmountHeader($stateAlias), function($row) use ($stateId) {
                    return $row['FeeByStateId'][$stateId]['b2b_fee_amount'] ?? null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(static::getIsTaxableHeader($stateAlias), function($row) use ($stateId) {
                    return ($row['FeeByStateId'][$stateId]['is_taxable'] ?? true) ? 'Yes' : 'No';
                }, ['filter' => true]),
            ]);
        }

        foreach ($filteredCountries as $countryId => $countryAlias) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn(static::getFeeAmountHeader($countryAlias), function($row) use ($countryId) {
                    return $row['FeeByCountryId'][$countryId]['fee_amount'] ?? null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(static::getB2bFeeAmountHeader($countryAlias), function($row) use ($countryId) {
                    return $row['FeeByCountryId'][$countryId]['b2b_fee_amount'] ?? null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(static::getIsTaxableHeader($countryAlias), function($row) use ($countryId) {
                    return ($row['FeeByCountryId'][$countryId]['is_taxable'] ?? true) ? 'Yes' : 'No';
                }, ['filter' => true]),
            ]);
        }
        $fileName = $this->Auth->user('company_name') . ' Product Fees.xlsx';

        $this->Spout->doWithOpenWriter($fileName, function() use ($feeProductId, $productConditions, $tableModel) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));

            $this->Product->hasMany('ProductStateFee', ['conditions' => ['ProductStateFee.fee_product_id' => $feeProductId]]);
            $this->Product->streamPagedQuery([
                'contain' => [
                    'ProductStateFee' => [
                        'fields' => ['id', 'product_id', 'country_id', 'state_id', 'fee_amount', 'b2b_fee_amount', 'is_taxable'],
                        'order' => false,
                    ],
                ],
                'conditions' => $productConditions,
                'fields' => ['Product.id', 'Product.product_sku', 'Product.product_title'],
                'order' => ['Product.product_sku' => 'ASC'],
            ], function($row) use ($tableModel) {
                $row['FeeByStateId'] = Hash::combine($row['ProductStateFee'], '{n}.state_id', '{n}');
                $row['FeeByCountryId'] = Hash::combine($row['ProductStateFee'], '{n}.country_id', '{n}');
                $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $row));
            });
            $this->Product->unbindModel(['hasMany' => ['ProductStateFee']], false);
       });
    }

    private function _getProductConditions($feeProductId, $userId): array
    {
        return $this->Product->getConditionsForActiveProducts([
            'Product.id !=' => $feeProductId,
            'Product.user_id' => $userId,
            'Product.sell_direct !=' => ProductSellDirect::EXCLUSIVELY,
        ]);
    }
}
