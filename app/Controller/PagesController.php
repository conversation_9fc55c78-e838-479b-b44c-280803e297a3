<?php
/**
 * Static content controller.
 *
 * This file will render views from views/pages/
 *
 * CakePHP(tm) : Rapid Development Framework (http://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (http://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (http://cakefoundation.org)
 * @link          http://cakephp.org CakePHP(tm) Project
 * @package       app.Controller
 * @since         CakePHP(tm) v 0.2.9
 * @license       http://www.opensource.org/licenses/mit-license.php MIT License
 */

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppController', 'Controller');
App::uses('User', 'Model');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');

/**
 * Static content controller
 *
 * Override this controller by placing a copy in controllers directory of an application
 *
 * @package       app.Controller
 * @link http://book.cakephp.org/2.0/en/controllers/pages-controller.html
 *
 * @property AuthComponent $Auth
 * @property CurrencyComponent $Currency
 * @property OrderLogicComponent $OrderLogic
 * @property UserLogicComponent $UserLogic
 * @property StripeComponent $Stripe
 * @property UploadComponent $Upload
 *
 * @property Page $Page
 * @property User $User
 * @property EmailTemplate $EmailTemplate
 * @property Cron $Cron
 * @property Order $Order
 * @property AppModel $Masspay
 * @property Configuration $Configuration
 * @property Category $Category
 * @property Administrator $Administrator
 * @property Contact $Contact
 * @property State $State
 * @property Country $Country
 * @property ManufacturerSalesRep $ManufacturerSalesRep
 */
class PagesController extends AppController
{

    /**
     * This controller does not use a model
     *
     * @var array
     */
    var $name = 'Pages';

    public $components = [
        'Auth',
        'Currency',
        'OrderLogic',
        'UserLogic',
        'Stripe.Stripe',
        'Upload',
    ];

    /**
     * @var array
     */
    public $uses = [
        'Page',
        'User',
        'EmailTemplate',
        'Cron',
        'Order',
        'Masspay',
        'Configuration',
        'Category',
        'Administrator',
        'Contact',
        'State',
        'Country',
        'ManufacturerSalesRep',
    ];

    /**
     *
     */
    public function beforeFilter()
    {
        $this->Auth->allow('view');
        parent::beforeFilter();
    }

    /**
     * Common View page for all cms pages
     */
    public function view()
    {
        $page = $this->Page->findByPageSlug($this->request->param('url'));
        if (empty($page['Page']['id'])) {
            throw new NotFoundException();
        }
        if (!$this->Auth->user() && !$page['Page']['public']) {
            return $this->redirect('/', 403);
        }

        if (isset($this->request->query['popup'])) {
            $this->response->body($page['Page']['page_content']);
            return $this->response;
        }

        $this->set('title_for_layout', $page['Page']['page_title']);
        $this->set('page', $page);
    }

    /**
     * Create New CMS page using manage pages tab
     */
    function admin_add()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Add Page');

        $destination = WWW_ROOT . 'files/';
        if (!empty($this->request->data)) {
            $this->setFlash("Page has been created successfully!", 'success');
            if (!empty($this->request->data['Page']['id'])) {
                $this->Page->id = $this->request->data['Page']['id'];
                $this->setFlash("Page has been updated successfully!", 'success');
            }
            $this->request->data['Page']['page_slug'] = generate_slug($this->request->data['Page']['page_title']);

            $this->Page->save($this->request->data);
            $this->redirect(ADMIN_PATH . "pages/list");
        }

        $uuid = $this->request->param('uuid');
        if ($uuid) {
            $this->set('title_for_layout', 'Edit Page');
            $this->request->data = $this->Page->findByUuid($uuid);
            $pages = $this->Page->getPages($this->request->data['Page']['id']);
        } else {
            $pages = $this->Page->getPages();
        }
        $this->set('pages', $pages);
        $this->set('destination', $destination);
    }

    /**
     * List all CMS page on manage page table
     */
    public function admin_list()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Manage Pages');

        $cond = "";

        if (!empty($this->request->data) && empty($this->request->data['Page']['page_title'])) {
            $this->Session->delete('search_parent_title');
        }

        $search_parent_title = $this->Session->read('search_parent_title');
        if (!empty($this->request->data['Page']['page_title'])) {
            $cond .= " AND Page.page_title LIKE '%" . $this->request->data['Page']['page_title'] . "%' ";
            $this->Session->write('search_parent_title', $this->request->data['Page']['page_title']);
        } elseif (!empty($search_parent_title)) {
            $this->request->data['Page']['page_title'] = $search_parent_title;
            $cond .= " AND Page.page_title LIKE '%" . $this->request->data['Page']['page_title'] . "%' ";
        }
        $this->Session->write('Page.pickSession', 'set');
        $this->Page->searchPages($cond);
    }

    /**
     * List all CMS page on manage page table(ajax_response)
     */
    public function admin_ajax_lists()
    {
        $this->autoRender = false;
        $this->layout = 'ajax';

        $this->_getHistory('Page');

        $page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1; // get the requested page
        $limit = isset($_REQUEST['rows']) ? $_REQUEST['rows'] : 20; // get how many rows we want to have into the grid
        $sidx = isset($_REQUEST['sidx']) ? $_REQUEST['sidx'] : ""; // get index row - i.e. user click to sort
        $sord = isset($_REQUEST['sord']) ? $_REQUEST['sord'] : "asc"; // get the direction
        $totalrows = isset($_REQUEST['totalrows']) ? $_REQUEST['totalrows'] : false;

        $wh = '';
        if (isset($this->request->query['filters'])) {
            $wh = $this->Jqgrid->filterOptions($this->request->query['filters']);
        }
        if ($totalrows) {
            $limit = $totalrows;
        }
        if (!$sidx) $sidx = 1;
        $result = $this->Page->query($this->Session->read('page_list_count') . $wh);
        $count = $result[0][0]['Count'];
        if ($count > 0) {
            $total_pages = ceil($count / $limit);
        } else {
            $total_pages = 0;
        }
        $responce = new stdClass();
        if ($page > $total_pages) $page = $total_pages;
        $start = $limit * $page - $limit; // do not put $limit*($page - 1)
        if ($start < 0) $start = 0;
        $res_data = $this->Page->query($this->Session->read('page_list_query') . $wh . " ORDER BY " . $sidx . " " . $sord . " LIMIT " . (int)$start . " , " . (int)$limit);
        $responce->page = $page;
        $responce->total = $total_pages;
        $responce->records = $count;
        $i = 0;
        //$confirm_msg = "Are you sure? you want to delete this Page ?";
        foreach ($res_data as $row) {
            $rowdata = array();
            $rowdata[] = $row['Page']['page_title'];
            $rowdata[] = date(DATE_FORMAT, strtotime($row['Page']['created']));
            $rowdata[] = "<a href='" . ADMIN_PATH . "pages/edit/" . $row['Page']['uuid'] . "' title='Edit'> <i class='fas fa-edit'></i> </a> | <a href='javascript:;' onClick='deletePage(\"" . $row['Page']['uuid'] . "\");' title='Delete'> <i class='fas fa-trash'></i> </a>";
            $responce->rows[$i]['cell'] = $rowdata;
            $i++;
        }
        return new CakeResponse(array('body' => json_encode($responce), 'status' => 200));
        exit;
    }

    /**
     * Sub function to manage session information
     * @param $part
     */
    public function _getHistory($part)
    {
        if ($this->Session->read("$part.pickSession") == 'set') {
            $_REQUEST['page'] = $this->Session->read("$part.newpage");
            $_REQUEST['rows'] = $this->Session->read("$part.rows");
            $_REQUEST['sidx'] = $this->Session->read("$part.sidx");
            $_REQUEST['sord'] = $this->Session->read("$part.sord");
            $this->Session->write("$part.pickSession", '');
        } else {
            $this->Session->write("$part.newpage", $_REQUEST['page']);
            $this->Session->write("$part.rows", $_REQUEST['rows']);
            $this->Session->write("$part.sidx", $_REQUEST['sidx']);
            $this->Session->write("$part.sord", $_REQUEST['sord']);
        }
    }

    /**
     * Delete Selected CMS page using manage pages tab
     * @param string $uuid
     */
    function admin_delete_page($uuid = "")
    {
        if (!empty($uuid)) {
            $page_details = $this->Page->findByUuid($uuid, array("id"));
            if (!empty($page_details['Page']['id']))
                $this->Page->delete($page_details['Page']['id']);
            $this->setFlash("Page has been deleted successfully!", 'success');
            $this->redirect(ADMIN_PATH . "pages/list");
        }
    }

    /**
     * List all users on shipearly on Brands and retailers tab
     */
    function admin_userlist()
    {
        $this->layout = "admin";

        $userType = $this->request->param('filter');
        if ($userType === User::TYPE_RETAILER) {
            $this->set('title_for_layout', 'Stores/Retailers');
        } elseif ($userType === User::TYPE_STAFF) {
            $this->set('title_for_layout', 'Store Associates');
        } elseif ($userType === User::TYPE_SALES_REP) {
            $this->set('title_for_layout', 'Sales Reps / Distributors');
        } else {
            $this->set('title_for_layout', 'Brands/Manufacturers');
        }

        $this->Session->write('search_email', $this->request->data('User.search') ?: '');
        $this->Session->write('search_unappuser', $this->request->data('User.status') ?: '');
        $this->Session->write('User.pickSession', 'set');

        $this->set('colModel', $this->_getUserListColumns($userType));
    }

    /**
     * List all users on shipearly on Brands and retailers tab(ajax_response)
     */
    public function admin_ajax_userlists()
    {
        $this->autoRender = false;
        $this->layout = 'ajax';

        $this->_getHistory('User');

        $page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1; // get the requested page
        $limit = isset($_REQUEST['rows']) ? $_REQUEST['rows'] : 20; // get how many rows we want to have into the grid
        $sidx = isset($_REQUEST['sidx']) ? $_REQUEST['sidx'] : "User.created"; // get index row - i.e. user click to sort
        $sord = isset($_REQUEST['sord']) ? $_REQUEST['sord'] : "DESC"; // get the direction
        $totalrows = isset($_REQUEST['totalrows']) ? $_REQUEST['totalrows'] : 0;
        if ($totalrows) {
            $limit = $totalrows;
        }

        $search = $this->Session->read('search_email');
        $unappuser = $this->Session->read('search_unappuser');

        $userType = $this->request->param('filter');

        $conditions = array('User.user_type' => $userType);
        if (!empty($search)) {
            $conditions[] = $this->User->buildSearchCondition($search);
        }
        if (!empty($unappuser)) {
            $conditions['User.status'] = 'Register';
        }

        $count = (int)$this->User->find('count', ['recursive' => -1, 'conditions' => $conditions]);

        $total_pages = ($count > 0) ? ceil($count / $limit) : 0;
        if ($page > $total_pages) $page = $total_pages;

        $start = $limit * $page - $limit; // do not put $limit*($page - 1)
        if ($start < 0) $start = 0;

        $res_data = $this->User->findForAdminUserList($conditions, $sidx, $sord, $limit, $start);

        $rows = $this->_getUserListRows($res_data, $userType);

        $this->response->body(json_encode([
            'page' => $page,
            'total' => $total_pages,
            'records' => $count,
            'rows' => $rows
        ]));
        return $this->response;
    }

    private function _getUserListColumns($userType)
    {
        if ($userType === User::TYPE_RETAILER) {
            $colModel = [
                ['name' => 'Company Name', 'sortable' => true, 'index' => 'User.company_name', 'width' => 90, 'formatter' => 'showlink'],
                ['name' => 'E-mail', 'sortable' => true, 'index' => 'User.email_address', 'width' => 90],
                ['name' => '# of Orders | Fulfillment', 'sortable' => false, 'width' => 120],
                ['name' => 'Type', 'sortable' => true, 'index' => 'User.user_type', 'width' => 50],
                ['name' => 'Inventory Software', 'sortable' => true, 'index' => 'User.inventory_type', 'width' => 90],
                ['name' => 'Status', 'sortable' => true, 'index' => 'User.setup_status', 'width' => 60],
            ];
        } elseif ($userType === User::TYPE_STAFF) {
            $colModel = [
                ['name' => 'Associate Name', 'sortable' => true, 'index' => 'User.company_name', 'width' => 90, 'formatter' => 'showlink'],
                ['name' => 'E-mail', 'sortable' => true, 'index' => 'User.email_address', 'width' => 90],
                ['name' => 'Company Name', 'sortable' => true, 'index' => 'MasterRetailer.company_name', 'width' => 90, 'formatter' => 'showlink'],
                ['name' => '# of Orders', 'sortable' => true, 'index' => 'Order.no_of_orders', 'width' => 90],
                ['name' => 'Commission', 'sortable' => true, 'index' => 'Order.total_sales_commission', 'width' => 90],
            ];
        } elseif ($userType === User::TYPE_SALES_REP) {
            $colModel = [
                ['name' => 'Company Name', 'sortable' => true, 'index' => 'User.company_name', 'width' => 90, 'formatter' => 'showlink'],
                ['name' => 'E-mail', 'sortable' => true, 'index' => 'User.email_address', 'width' => 90],
            ];
        } else {
            $colModel = [
                ['name' => 'Company Name', 'sortable' => true, 'index' => 'User.company_name', 'width' => 90, 'formatter' => 'showlink'],
                ['name' => 'E-mail', 'sortable' => true, 'index' => 'User.email_address', 'width' => 90],
                ['name' => 'Retailer Count', 'sortable' => false, 'width' => 90],
                ['name' => 'Sales Rep Count', 'sortable' => true, 'index' => 'SalesRepCount.count', 'width' => 90],
                ['name' => 'eCommerce Software', 'sortable' => true, 'index' => 'User.site_type', 'width' => 90],
                ['name' => 'Sell Direct', 'sortable' => true, 'index' => 'User.admin_sell_direct', 'width' => 90],
            ];
        }
        $colModel[] = ['name' => 'Last Login', 'sortable' => true, 'index' => 'User.last_login', 'width' => 90];
        $colModel[] = ['name' => 'Action', 'sortable' => false, 'width' => 50];
        return $colModel;
    }

    private function _getUserListRows($users, $userType)
    {
        return array_map(function($user) use ($userType) {
            $cells = array(
                "<a href=" . ADMIN_PATH . "contact/{$user['User']['id']}>{$user['User']['company_name']}</a>",
                $user['User']['email_address'],
            );
            if ($userType === User::TYPE_RETAILER) {
                $cells[] = $this->_getRetailerRatingCell($user['User']['id']);
                $cells[] = ($user['User']['Branch'] == 0) ? 'Retailer' : 'Store';

                if (empty($user['User']['inventory_type'])) {
                    $user['User']['inventory_type'] = "None";
                } elseif ($user['User']['inventory_type'] == 'other') {
                    $user['User']['inventory_type'] .= " ({$user['User']['otherInventory']})";
                }
                $cells[] = $user['User']['inventory_type'];
                $cells[] = ($user['User']['setup_status']) ? 'Completed' : 'Pending';

                if ($user['User']['Branch'] != 0) {
                    $user['User']['company_code'] = $user['MasterRetailer']['company_code'];
                }
            } elseif ($userType === User::TYPE_STAFF) {
                $cells[] = "<a href=" . ADMIN_PATH . "contact/{$user['MasterRetailer']['id']}>{$user['MasterRetailer']['company_name']}</a>";
                $cells[] = $user['Order']['no_of_orders'];
                $cells[] = $user['Order']['total_sales_commission'];
            } elseif ($userType === User::TYPE_SALES_REP) {
            } else {
                $cells[] = $this->OrderLogic->getRetailersCount($user['User']['id']);
                $cells[] = (int)$user['SalesRepCount']['count'];
                $cells[] = $user['User']['site_type'];
                $cells[] = $this->_getUserSellDirectCell($user);
            }
            $cells[] = $user['User']['last_login'] ? format_datetime($user['User']['last_login'], DATE_FORMAT) : 'Never';
            $cells[] = $this->_getUserActionCell($user);

            return ['cell' => $cells];
        }, $users);
    }

    /**
     * @param $userId
     * @return string
     */
    protected function _getRetailerRatingCell($userId)
    {
        $rating = $this->OrderLogic->getRetailerRating($userId);
        $totalOrderCount = $this->OrderLogic->getRetailerOrdersCount($userId);
        return $totalOrderCount . ' | ' . $rating . ' %';
    }

    /**
     * @param $user
     * @return string
     */
    protected function _getUserSellDirectCell($user)
    {
        if ($user['User']['status'] != 'Active' || !in_array($user['User']['site_type'], [UserSiteType::SHOPIFY, UserSiteType::MAGENTO], true)) {
            return '';
        }

        if ($user['User']['admin_sell_direct'] == 1) {
            $selldirect = 0;
            $iconClass = 'far fa-check-square';
        } else {
            $selldirect = 1;
            $iconClass = 'far fa-square';
        }
        return "<a href=\"javascript:;\" title=\"Sell Direct Option\" onClick=\"sellDirectOption('{$user['User']['uuid']}', '{$user['User']['user_type']}', '{$selldirect}');\"><i class=\"{$iconClass}\"></i></a>";
    }

    /**
     * @param $user
     * @return string
     */
    protected function _getUserActionCell($user)
    {
        $editUrl = BASE_PATH . "admin/useredit/{$user['User']['user_type']}/{$user['User']['uuid']}";
        $editLink = "<a href=\"{$editUrl}\" title=\"Edit\"><i class=\"fas fa-edit\"></i></a>";
        $rejectLink = "<a href=\"javascript:void(0);\" title=\"Reject\" onClick=\"rejectUser('{$user['User']['uuid']}');\"><i class=\"fas fa-trash\"></i></a>";
        $approveLink = "<a href=\"javascript:void(0);\" title=\"Approve\" onClick=\"approveUser('{$user['User']['id']}', '{$user['User']['uuid']}', '{$user['User']['user_type']}');\"><i class=\"fas fa-check-square\"></i></a>";

        $actionLinks = [$editLink, $approveLink, $rejectLink];
        if (in_array($user['User']['status'], ['Active', 'Approve'])) {
            $actionLinks = [$editLink, $rejectLink];
        } elseif ($user['User']['status'] == 'Reject') {
            $actionLinks = [$editLink, $approveLink];
        }

        return implode(' | ', $actionLinks);
    }

    /**
     * Approve/Reject or update user details though contact page
     */
    public function admin_userapp()
    {
        $this->autoRender = false;

        $action = $this->request->params['act'];
        if (empty($action)) {
            return $this->_exceptionResponse(new BadRequestException("No value for 'act' provided"), null, true);
        }

        $uuid = $this->request->params['id'];
        $user = $this->User->findByUuid($uuid, ['id', 'status'], null, -1);
        if (empty($user['User']['id'])) {
            return $this->_exceptionResponse(new NotFoundException(json_encode(['User' => ['uuid' => $uuid]])), null, true);
        }

        $userId = $user['User']['id'];
        $contactUrl = ADMIN_PATH . "contact/" . $userId;

        $companycode = urldecode($this->request->param('companycode'));

        if ($action == 'companycode') {
            if (!$this->User->updateUser(['User' => ['id' => $userId, 'company_code' => $companycode]])) {
                $message = $this->User->validationErrors['company_code'][0];
                return $this->_exceptionResponse(new InternalErrorException($message), $message, true, $contactUrl);
            }
            return $this->_successResponse("User company code has been updated", $contactUrl);
        }

        if ($action == $user['User']['status'] || ($action == 'Approve' && $user['User']['status'] == 'Active')) {
            return $this->_exceptionResponse(new BadRequestException(), "This user's status has already been set to '{$action}'");
        }
        $save = array('User' => array('id' => $userId, 'status' => $action));

        if (!empty($companycode)) {
            $save['User']['company_code'] = $companycode;
        }

        $revenuemodel = urldecode($this->request->param('revenuemodel'));
        if (!empty($revenuemodel)) {
            $save['User']['revenue_model'] = $revenuemodel;
        }

        if (isset($this->request->params['store_associate_pin'])) {
            $save['User']['store_associate_pin'] = $this->request->params['store_associate_pin'];
        }
        if ($action == 'Reject') {
            $save['User']['store_associate_pin'] = '';
        }

        if (!$this->User->save($save)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode($this->User->validationErrors)),
                implode('<br />', array_column($this->User->validationErrors, 0)),
                true
            );
        }

        $message = "The user's status has been set to '{$action}'";
        if ($action == 'Approve') {
            $message = "The user has been approved successfully!";
            $this->UserLogic->activationMail($userId);
        } elseif ($action == 'Reject') {
            $message = "The user has been rejected successfully!";
        }
        return $this->_successResponse($message);
    }

    /**
     * Suspend user account though contact page
     */
    public function admin_suspend()
    {
        $this->autoRender = false;
        $id = $this->request->param('id');

        if (!empty($id)) {
            $userid = $this->User->findForAdminEditStatus((string)$id);
            if ($userid['User']['status'] == 'Suspend') {
                $this->setFlash("User has been already in Suspend state", 'error');
            } else {
                if ($this->User->suspend($userid['User']['id'])) {
                    $this->setFlash("User has been Suspended successfully!", 'success');
                }
            }
        }
        $this->redirect($this->referer());
    }

    /**
     * Activate user account though contact page
     */
    public function admin_active()
    {
        $this->autoRender = false;
        $id = $this->request->param('id');

        if (!empty($id)) {
            $userid = $this->User->findForAdminEditStatus((string)$id);
            if ($userid['User']['status'] == 'Active') {
                $this->setFlash("User has been already in Active state", 'error');
            } else {
                if ($this->User->directActive($userid['User']['id'])) {
                    $this->setFlash("User has been successfully moved to Active status!", 'success');
                }
            }
        }
        $this->redirect($this->referer());
    }

    /**
     * List all orders on manage orders tab
     */
    public function admin_orders()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Manage Orders');
        $cond = "";
        $orderKey = $this->Session->read('orderKey');
        $date = $this->Session->read('date');
        if ($this->request->is('post')) {
            if (isset($this->request->data) && empty($this->request->data) || empty($this->request->data['Order']['search'])) {
                $this->Session->delete('orderKey');
                $orderKey = '';
            } elseif (!empty($this->request->data) && !empty($this->request->data['Order']['search'])) {
                $orderKey = $this->request->data['Order']['search'];
                $this->Session->write('orderKey', $orderKey);
            }

            if (isset($this->request->data) && empty($this->request->data) || empty($this->request->data['Order']['date'])) {
                $this->Session->delete('date');
                $date = '';
            } elseif (!empty($this->request->data) && !empty($this->request->data['Order']['date'])) {
                $date = json_decode($this->request->data['Order']['date'], true);
                $this->Session->write('date', $date);
            }
        }  else {
            $this->Session->delete('orderKey');
            $orderKey = '';
            $this->Session->delete('date');
            $date = '';
       }
        $this->set('date', json_encode($date));

        if (!empty($orderKey)) {
            $cond .= " AND (SHOR.orderID LIKE '%" . $orderKey . "%' OR SHOR.order_status LIKE '%" . $orderKey . "%' OR SHOR.total_price LIKE '%" . $orderKey . "%') ";
        }

        if (!empty($date)) {
           // print_r($date);die();
            $date['start'] = date("Y-m-d H:i:s", strtotime($date['start']));
            $date['end'] = date("Y-m-d H:i:s", strtotime($date['end']. "+1 days"));
            if($date['start'] == $date['end']) {
                $date['end'] = date("Y-m-d", strtotime($date['end'])).' 23:59:59';
            }
            $cond .= " AND SHOR.created_at BETWEEN '" . $date['start'] . "' AND '" . $date['end'] . "' ";
        }

        $this->set('orderKey', $orderKey);
        $this->Session->delete('order_list_count');
        $this->Session->delete('order_list_query');
        $this->Session->write('Order.pickSession', 'set');
        $this->Page->searchOrders($cond);
    }

    /**
     * List all orders on manage orders tab (ajax response)
     */
    public function admin_ajax_orders()
    {
        $this->autoRender = false;
        $this->layout = 'ajax';

        $this->_getHistory('Order');

        $page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1; // get the requested page
        $limit = isset($_REQUEST['rows']) ? $_REQUEST['rows'] : 20; // get how many rows we want to have into the grid
        $sidx = isset($_REQUEST['sidx']) ? $_REQUEST['sidx'] : 'orderID'; // get index row - i.e. user click to sort
        $sord = isset($_REQUEST['sord']) ? $_REQUEST['sord'] : 'DESC'; // get the direction
        $totalrows = isset($_REQUEST['totalrows']) ? $_REQUEST['totalrows'] : 0;

        if ($totalrows) {
            $limit = $totalrows;
        }

        $result = $this->User->query($this->Session->read('order_list_count'));
        $count = $result[0][0]['Count'];
        $total_pages = ($count > 0 && $limit > 0) ? ceil($count / $limit) : 0;
        if ($page > $total_pages) $page = $total_pages;

        $offset = $limit * $page - $limit; // do not put $limit*($page - 1)
        if ($offset < 0) $offset = 0;

        $orderBy = $sidx . ' ' . $sord;

        $res_data = $this->User->query($this->Session->read('order_list_query') . " ORDER BY " . $orderBy . " LIMIT " . (int)$offset . " , " . (int)$limit);

        $responce = new stdClass();
        $responce->page = $page;
        $responce->total = $total_pages;
        $responce->records = $count;

        $responce->rows = array_map(function($row) {
            $orderUrl = ADMIN_PATH . "orders/view/" . str_replace('#', '', $row['SHOR']['orderID']);
            $brandUrl = ADMIN_PATH . "contact/" . $row['SHOR']['user_id'];
            $retailerUrl = ADMIN_PATH . "contact/" . $row['SHOR']['retailer_id'];

            $brandName = $this->User->field('company_name', ['id' => $row['SHOR']['user_id']]);
            $retailerName = 'Direct Order';
            if ($row['SHOR']['retailer_id']) {
                $retailerName = $this->User->field('company_name', ['id' => $row['SHOR']['retailer_id']]);
            }

            return array('cell' => array(
                "<a href=\"{$orderUrl}\">{$row['SHOR']['orderID']}</a>",
                "<a href=\"{$brandUrl}\">{$brandName}</a>",
                "<a href=\"{$retailerUrl}\">{$retailerName}</a>",
                OrderStatus::getLabel($row['SHOR']['order_status']),
                OrderPaymentStatus::getLabel((int)$row['SHOR']['payment_status']),
                Order::getFulfillType($row['SHOR']['order_type'], $row['SHOR']['subType']),
                $this->Currency->formatCurrency($row['SHOR']['total_price'], $row['SHOR']['currency_code']),
                date(DATE_FORMAT, strtotime($row['SHOR']['created_at'])),
            ));
        }, $res_data);

        $this->response->body(json_encode($responce));
        return $this->response;
    }

    public function admin_dealerorders()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Dealer Orders');
        
        $orderKey = $this->Session->read('orderKey');
        $date = $this->Session->read('date');
        
        if ($this->request->is('post')) {
            if (isset($this->request->data) && empty($this->request->data) || empty($this->request->data['Order']['search'])) {
                $this->Session->delete('orderKey');
                $orderKey = '';
            } elseif (!empty($this->request->data) && !empty($this->request->data['Order']['search'])) {
                $orderKey = $this->request->data['Order']['search'];
                $this->Session->write('orderKey', $orderKey);
            }
            
            if (isset($this->request->data) && empty($this->request->data) || empty($this->request->data['Order']['date'])) {
                $this->Session->delete('date');
                $date = '';
            } elseif (!empty($this->request->data) && !empty($this->request->data['Order']['date'])) {
                $date = $this->request->data['Order']['date'];
                $this->Session->write('date', $date);
            }
        } else {
            $this->Session->delete('orderKey');
            $orderKey = '';
            $this->Session->delete('date');
            $date = '';
        }
        
        $this->set('date', $date);
        $this->set('orderKey', $orderKey);
    }

    public function admin_ajax_dealerorders()
    {
        $this->autoRender = false;
        $this->layout = 'ajax';

        $page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1; // get the requested page
        $limit = isset($_REQUEST['rows']) ? $_REQUEST['rows'] : 20; // get how many rows we want to have into the grid
        $sidx = isset($_REQUEST['sidx']) ? $_REQUEST['sidx'] : 'created_at'; // get index row - i.e. user click to sort
        $sord = isset($_REQUEST['sord']) ? $_REQUEST['sord'] : "ASC"; // get the direction

        $orderKey = isset($_REQUEST['orderKey']) ? $_REQUEST['orderKey'] : '';
        $date = isset($_REQUEST['date']) ? json_decode($_REQUEST['date'], true) : ''; 

        // Using a less selective method of querying for 'Order.is_dealerorder = true'
        $conditions = array("COALESCE(Order.dealer_qty_ordered, '') !=" => '');

        if (!empty($orderKey)) {
            $conditions[] = $this->Order->buildSearchCondition($orderKey);
        }
        if (!empty($date)) {
            $conditions['Order.created_at BETWEEN ? AND ?'] = $this->Order->convertDateRange($date['start'], $date['end']);
        }

        $count = $this->Order->find('count', array('recursive' => -1,'conditions' => $conditions));

        $total_pages = ($count > 0 && $limit > 0) ? ceil($count / $limit) : 0;

        if ($page > $total_pages) {
            $page = $total_pages;
        }
        
        $start = $limit * $page - $limit; // do not put $limit*($page - 1)
        if ($start < 0) {
            $start = 0;
        }

        $this->Order->bindModel([
            'belongsTo' => [
                'Brand' => [
                    'className' => 'User',
                    'foreignKey' => 'user_id'
                ],
                'Retailer' => [
                    'className' => 'User',
                    'foreignKey' => 'retailer_id'
                ]
            ]
        ], false);
        $dealerOrderData = $this->Order->find('all', [
            'contain' => ['Brand', 'Retailer'],
            'fields' => [
                'Order.id',
                'Order.user_id',
                'Order.retailer_id',
                'Brand.company_name',
                'Retailer.company_name',
                'Order.order_status',
                'Order.orderID',
                'Order.created_at',
                'Order.shipped_date',
                'Order.currency_code',
                'Order.dealer_qty_ordered'
            ],
            'conditions' => $conditions,
            'order' => $sidx . " " . $sord,
            'limit' => (int)$limit, 
            'offset' => (int)$start
        ]);
        $this->Order->unbindModel([
            'belongsTo' => ['Brand', 'Retailer']
        ], false);

        $response = new stdClass();
        $response->page = $page;
        $response->total = $total_pages;
        $response->records = $count;

        $response->rows = array_map(function($row) {
            $orderUrl = ADMIN_PATH . "orders/dealer/view/" . str_replace('#', '', $row['Order']['orderID']);
            $brandUrl = ADMIN_PATH . "contact/" . $row['Order']['user_id'];
            $retailerUrl = ADMIN_PATH . "contact/" . $row['Order']['retailer_id'];

            $shippedDate = !empty($row['Order']['shipped_date']) ? date(DATE_FORMAT, strtotime($row['Order']['shipped_date'])) : null;

            $totalPrice = 0;
            $dealer_qty_ordered = json_decode($row['Order']['dealer_qty_ordered'], true);
            if (!empty($dealer_qty_ordered['products'])) {
                foreach ($dealer_qty_ordered['products'] as $product) {
                    if (!empty($product['dealer_price'])) {
                        $productPrice = $product['dealer_price'] * $product['quantity'];
                        $totalPrice += $productPrice + (($dealer_qty_ordered['b2b_tax'] / 100) * $productPrice);
                    }
                }
            }
            if (isset($dealer_qty_ordered['shipping_amount']) && !empty($dealer_qty_ordered['shipping_amount'])) {
                $totalPrice += $dealer_qty_ordered['shipping_amount'];
            }
            if(!empty($dealer_qty_ordered['b2b_tax']) && isset($dealer_qty_ordered['b2b_shipping_tax_option']) && $dealer_qty_ordered['b2b_shipping_tax_option']) {
                $totalPrice += $dealer_qty_ordered['shipping_amount'] * $dealer_qty_ordered['b2b_tax'] / 100;
            }

            return array(
                'id' => $row['Order']['id'],
                'cell' => array(
                    "<a href=\"{$orderUrl}\">{$row['Order']['orderID']}</a>",
                    "<a href=\"{$brandUrl}\">{$row['Brand']['company_name']}</a>",
                    "<a href=\"{$retailerUrl}\">{$row['Retailer']['company_name']}</a>",
                    OrderStatus::getLabel($row['Order']['order_status']),
                    date(DATE_FORMAT, strtotime($row['Order']['created_at'])),
                    $shippedDate,
                    $row['Order']['currency_code'],
                    $this->Currency->formatAmount($totalPrice, $row['Order']['currency_code'])
                )
            );
        }, $dealerOrderData);

        $this->response->body(json_encode($response));
        return $this->response;
    }

    /**
     * Email templates list in superadmin
     */
    public function admin_emails()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Manage Email templates');
        $cond = "";
        $this->Session->delete('email_list_count');
        $this->Session->write('Email.pickSession', 'set');
        $this->Page->searchEmails($cond);
    }

    /**
     * Email templates list in superadmin(ajax_response)
     */
    public function admin_ajax_emails()
    {
        $this->autoRender = false;
        $this->layout = 'ajax';

        $this->_getHistory('Email');

        $page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1; // get the requested page
        $limit = isset($_REQUEST['rows']) ? $_REQUEST['rows'] : 20; // get how many rows we want to have into the grid
        $sidx = isset($_REQUEST['sidx']) ? $_REQUEST['sidx'] : ""; // get index row - i.e. user click to sort
        $sord = isset($_REQUEST['sord']) ? $_REQUEST['sord'] : "asc"; // get the direction
        $totalrows = isset($_REQUEST['totalrows']) ? $_REQUEST['totalrows'] : false;

        $wh = '';
        if (isset($this->request->query['filters'])) {
            $wh = $this->Jqgrid->filterOptions($this->request->query['filters']);
        }
        if ($totalrows) {
            $limit = $totalrows;
        }
        if (!$sidx) $sidx = 1;
        $result = $this->User->query($this->Session->read('email_list_count') . $wh);
        $count = $result[0][0]['Count'];
        if ($count > 0) {
            $total_pages = ceil($count / $limit);
        } else {
            $total_pages = 0;
        }
        $responce = new stdClass();
        if ($page > $total_pages) $page = $total_pages;
        $start = $limit * $page - $limit; // do not put $limit*($page - 1)
        if ($start < 0) $start = 0;
        $res_data = $this->User->query($this->Session->read('email_list_query') . $wh . " ORDER BY " . $sidx . " " . $sord . " LIMIT " . (int)$start . " , " . (int)$limit);
        $responce->page = $page;
        $responce->total = $total_pages;
        $responce->records = $count;
        $i = 0;
        foreach ($res_data as $row) {
            $rowdata = array();
            $rowdata[] = $row['EmailTemplate']['id'];
            $rowdata[] = "<a href='" . ADMIN_PATH . "pages/emailedit/" . $row['EmailTemplate']['id'] . "' title='Edit' style = 'color:#335a7f; font-weight:bold;'>" . $row['EmailTemplate']['template_name'] . "</a>";
            // $rowdata[] = $row['EmailTemplate']['template_name'];
            $rowdata[] = $row['EmailTemplate']['description'];
            // $rowdata[] = "<a href='" . ADMIN_PATH . "pages/emailedit/" . $row['EmailTemplate']['id'] . "' title='Edit'> <i class=fas fa-edit></i> </a>";
            $responce->rows[$i]['cell'] = $rowdata;
            $i++;
        }
        return new CakeResponse(array('body' => json_encode($responce), 'status' => 200));
    }

    public function admin_emailedit($id = null)
    {
        $language = $this->request->query('lang') ?? 'eng';
        $this->EmailTemplate->locale = $language;

        $data = $this->EmailTemplate->findForAdminSettings($id, [
            'EmailTemplate.id',
            'EmailTemplate.template_name',
            'EmailTemplate.alias',
            'EmailTemplate.description',
            'EmailTemplate.subject',
            'EmailTemplate.content',
            'EmailTemplate.bcc_admin',
        ]);

        if (empty($data['EmailTemplate']['id'])) {
            throw new NotFoundException();
        }

        if ($this->request->is(['post', 'put'])) {
            if ($this->EmailTemplate->saveForAdminSettings($id, $this->request->data)) {
                return $this->_successResponse();
            }
        } else {
            $this->request->data = $data;
        }

        $this->layout = 'admin';
        $this->set('title_for_layout', 'Edit Email Template - ' . $data['EmailTemplate']['template_name']);
    }

    /**
     * Manage Settings tab in super admin
     */
    public function admin_configuration()
    {
        $this->layout = 'admin';
    }

    /**
     * Manage Settings tab in super admin(ajax_response)
     */
    public function admin_ajax_configuration()
    {
        $this->autoRender = false;
        $this->response->body(json_encode(array(
            'rows' => array_map(
                function($value) {
                    return array(
                        'cell' => array(
                            $value['Configuration']['id'],
                            $value['Configuration']['name'],
                            $value['Configuration']['value'],
                        )
                    );
                },
                $this->Configuration->getAllConstantsAsRecords()
            )
        )));
        return $this->response;
    }

    /**
     * Edit Shipearly Settings in super admin
     */
    public function admin_editconfiguration()
    {
        $this->layout = 'admin';
        if ($this->request->is('post')) {
            $this->Configuration->updateConfiguration($this->request->data);
            return $this->_successResponse('Settings has been updated successfully!', ADMIN_PATH . 'setting');
        }
        $this->set('data', $this->Configuration->getAllConstantsAsRecords());
    }

    /**
     * Area of Interest tab in super admin
     */
    public function admin_interestedarea()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Manage Area of Interest');
        $cond = "";
        $this->Session->delete('category_list_count');
        $this->Session->write('Category.pickSession', 'set');
        $this->Page->searchCategories($cond);
    }

    /**
     * Area of Interest tab in super admin (ajax_response)
     */

    public function admin_ajax_interestedarea()
    {
        $this->autoRender = false;
        $this->layout = 'ajax';

        $this->_getHistory('Category');

        $page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1; // get the requested page
        $limit = isset($_REQUEST['rows']) ? $_REQUEST['rows'] : 20; // get how many rows we want to have into the grid
        $sidx = isset($_REQUEST['sidx']) ? $_REQUEST['sidx'] : ""; // get index row - i.e. user click to sort
        $sord = isset($_REQUEST['sord']) ? $_REQUEST['sord'] : "asc"; // get the direction
        $totalrows = isset($_REQUEST['totalrows']) ? $_REQUEST['totalrows'] : false;

        $wh = '';
        if (isset($this->request->query['filters'])) {
            $wh = $this->Jqgrid->filterOptions($this->request->query['filters']);
        }
        if ($totalrows) {
            $limit = $totalrows;
        }
        if (!$sidx)
            $sidx = 1;
        $result = $this->User->query($this->Session->read('category_list_count') . $wh);
        $count = $result[0][0]['Count'];
        if ($count > 0) {
            $total_pages = ceil($count / $limit);
        } else {
            $total_pages = 0;
        }
        $responce = new stdClass();
        if ($page > $total_pages)
            $page = $total_pages;
        $start = $limit * $page - $limit; // do not put $limit*($page - 1)
        if ($start < 0)
            $start = 0;
        $res_data = $this->User->query($this->Session->read('category_list_query') . $wh . " ORDER BY " . $sidx . " " . $sord . " LIMIT " . (int)$start . " , " . (int)$limit);
        $responce->page = $page;
        $responce->total = $total_pages;
        $responce->records = $count;
        $i = 0;
        foreach ($res_data as $row) {
            $rowdata = array();
            $rowdata[] = $row['Category']['id'];
            $rowdata[] = $row['Category']['category_name'];
            $rowdata[] = "<a href='" . ADMIN_PATH . $row['Category']['id'] . "/areaedit" . "' title='Edit'> <i class='fas fa-edit'></i> </a>";
            $responce->rows[$i]['cell'] = $rowdata;
            $i++;
        }
        return new CakeResponse(array('body' => json_encode($responce), 'status' => 200));
    }

    /**
     * Edit already created area of Interest
     */
    public function admin_interestedareaedit()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Edit Area of Interest');
        if (!empty($this->request->data)) {
            if (!empty($this->request->data['Category']['id'])) {
                $this->Category->id = $this->request->data['Category']['id'];
                $this->Category->category_name = $this->request->data['Category']['category_name'];
                $this->setFlash("Area of Interest has been updated successfully!", 'success');
            }
            $this->Category->addCategory($this->Category);
            $this->redirect(ADMIN_PATH . "pages/interestedarea");
        }
        $id = $this->request->param('id');
        if ($id) {
            $this->request->data['Category']['id'] = $id;
            $this->request->data['Category']['category_name'] = $this->Category->getCategoryById($id)[$id];
        }
    }

    /**
     * Add new area of Interest
     */
    public function admin_interestedareaadd()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Add Area of Interest');
        if (!empty($this->request->data)) {
            if (!empty($this->request->data['Category']['category_name'])) {
                $catName = $this->Category->checkCategory($this->request->data['Category']['category_name']);
                if (!empty($catName)) {
                    $this->setFlash("Area of Interest Already Exists!", 'success');
                } else {
                    $this->Category->category_name = $this->request->data['Category']['category_name'];
                    $this->setFlash("Area of Interest has been Added successfully!", 'success');
                }
            }
            $this->Category->addCategory($this->Category);
            $this->redirect(ADMIN_PATH . "pages/interestedarea");
        }
    }

    /**
     * Brand stripe coupon code popup on admin contact page
     */
    public function admin_stripeCoupon()
    {
        $this->autoRender = false;
        $id = $this->request->param('id');
        $couponcode = $this->request->param('couponcode');
        $userid = $this->User->findByUuid($id, array("id", 'coupon_code'), null, -1);
        if ($this->Stripe->validateCoupon($couponcode)) {
            if ($this->User->updateCouponCode($userid['User']['id'], $couponcode)) {
                $this->setFlash("Coupon code updated and it will apply at the time of activation", 'success');
            }
        } else {
            $this->setFlash("Coupon code not valid. Please check stripe account and update again", 'error');
        }
        $this->redirect($this->referer());
    }

    public function admin_associateRevenueModel()
    {
        $this->autoRender = false;
        if (!empty($this->request->data['User'])) {
            $fields = array_intersect_key($this->request->data['User'], array_flip(['store_associate_default_amount']));
            if ($this->User->updateAll($fields, ['User.uuid' => $this->request->param('id')])) {
                $this->setFlash("Commission fee updated successfully", 'success');
            }
        }
        $this->redirect($this->referer());
    }

    /**
     * Brand sell direct option on admin contact page
     */
    public function admin_sellDirectOption()
    {
        $this->autoRender = false;
        $id = $this->request->param('id');
        $selldirect = $this->request->param('selldirect');
        $userid = $this->User->findByUuid($id, array("id", 'admin_sell_direct'), null, -1);
        if ($this->User->updateSellDirectOption($userid['User']['id'], $selldirect)) {
            $this->setFlash("Sell Direct Option updated successfully", 'success');
        }
        $this->redirect($this->referer());
    }

    /**
     * Super admin change password page
     */
    public function admin_resetPassword()
    {
        $this->layout = 'admin';
        $this->set('title_for_layout', 'Reset password');
        if ($this->request->is(['post', 'put'])) {
            $oldPassword = $this->request->data['Administrator']['old_password'];
            $newPassword = $this->request->data['Administrator']['new_password'];
            $this->request->data = [
                'Administrator' => [
                    'username' => $this->Auth->user('username'),
                    'password' => $oldPassword,
                ],
            ];

            $identified = (bool)$this->Auth->identify($this->request, $this->response);
            unset($this->request->data['Administrator']['password']);

            if (!$identified) {
                $message = __('The old password you entered is incorrect. Please try again.');

                return $this->_exceptionResponse(new UnauthorizedException($message), $message);
            }

            $data = [
                'id' => $this->Auth->user('id'),
                'password' => User::passwordHasher()->hash($newPassword),
            ];
            if ($this->Administrator->save($data)) {
                return $this->_successResponse(__('Your account has been updated successfully'));
            }
        }
    }

    /**
     * Editing user account though edit page
     */
    public function admin_useredit()
    {
        $this->layout = "admin";
        $this->set('title_for_layout', 'Edit User Profile');
        $id = $this->request->param('id');
        $type = $this->request->param('type');

        $this->User->bindModel(['belongsTo' => ['Country', 'State']], false);
        $user = (array)$this->User->find('first', [
            'contain' => [
                'Country' => ['fields' => ['id']],
                'State' => ['fields' => ['id']],
            ],
            'conditions' => ['User.uuid' => $id],
            'fields' => [],
        ]);
        $this->User->unbindModel(['belongsTo' => ['Country', 'State']], false);

        if (empty($user['User']['id'])) {
            throw new NotFoundException();
        }

        $userId = (int)$user['User']['id'];
        $userType = (string)$user['User']['user_type'];

        if ($this->request->is(['post', 'put'])) {
            $this->request->data['User'] = ['id' => $userId] + $this->request->data['User'];
            $this->request->data['User']['address'] = $this->User->getCombinedAddressField($this->request->data['User']['address1'], $this->request->data['User']['address2']);

            $state = $this->State->findWithCountryName($this->request->data['User']['state_id']);
            $geopoints = findGeocode(
                $this->request->data['User']['address1'],
                $this->request->data['User']['city'],
                $this->request->data['User']['zipcode'],
                $state['State']['state_name'],
                $state['State']['country_code']
            );
            $this->request->data['User']['latitude'] = $geopoints['lat'] ?? null;
            $this->request->data['User']['longitude'] = $geopoints['lng'] ?? null;

            $telephone = (string)($this->request->data['User']['contact_medium']['telephone'] ?? '');
            unset($this->request->data['User']['contact_medium']);

            if ($this->User->save($this->request->data['User'])) {
                if ($telephone) {
                    $this->Contact->addContact($userId, 'company', $userId, 'telephone', $telephone);
                }

                return $this->_successResponse('User profile has been updated successfully', ADMIN_PATH . 'users/' . $userType);
            }

            $this->setFlash($this->_buildFlashMessage('error'), 'error');
        }

        $user['User']['telephone'] = $this->Contact->getCompanyTelephone($userId);

        $countryId = (int)$user['Country']['id'] ?: null;
        $stateId = (int)$user['State']['id'] ?: null;

        $user['User']['country'] = $countryId;
        $user['User']['state'] = $stateId;

        $this->set('user', $user);
        $this->set('country', $this->Country->getCountryList());
        $this->set('states', $this->State->getStateList($countryId));
    }

    public function admin_edit_interface()
    {
        $this->layout = 'admin';

        if ($this->request->is(['post', 'put'])) {
            try {
                $upload = (array)$this->request->data['UserInterface']['logo_upload'];

                $newFileUrl = $this->Upload->replaceFileInWebroot(
                    SHIPEARLY_LOGO_URL,
                    $upload,
                    '/files/admin/logo',
                    $upload['name']
                );
                $this->Configuration->updateValue('SHIPEARLY_LOGO_URL', $newFileUrl);

                return $this->_successResponse();
            } catch (Exception $e) {
                return $this->_exceptionResponse(new InternalErrorException(), $e->getMessage(), $e);
            }
        }

        $this->set('SHIPEARLY_LOGO_URL', defined('SHIPEARLY_LOGO_URL') ? trim(SHIPEARLY_LOGO_URL) : '');
    }

    private function _deleteOtherLogoFiles($exclude)
    {
        $exclude = str_replace(BASE_PATH, WWW_ROOT, $exclude);
        foreach (array_filter((array)glob(WWW_ROOT . 'files/admin/logo/*')) as $file) {
            if (is_file($file) && $file !== $exclude) {
                unlink($file);
            }
        };
    }

}
