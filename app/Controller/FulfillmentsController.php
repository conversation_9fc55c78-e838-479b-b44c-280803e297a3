<?php
App::uses('AppController', 'Controller');
App::uses('EcommercePlacementException', 'Error');
App::uses('UserFriendlyException', 'Error');
App::uses('OrderType', 'Utility');

/**
 * Fulfillments Controller.
 *
 * Fulfillment endpoints accessed via
 *
 * - /orders/:order_id/fulfillments
 * - /dealerorders/:dealer_order_id/fulfillments
 *
 * @property FulfillmentLogicComponent $FulfillmentLogic
 * @property NotificationLogicComponent $NotificationLogic
 * @property OrderLogicComponent $OrderLogic
 * @property ShopifyComponent $Shopify
 *
 * @property Fulfillment $Fulfillment
 * @property OrderProduct $OrderProduct
 */
class FulfillmentsController extends AppController
{
    public $components = [
        'FulfillmentLogic',
        'NotificationLogic',
        'OrderLogic',
        'Shopify.Shopify',
    ];

    public $uses = [
        'Fulfillment',
        'OrderProduct',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            // Configured in routes.php
            $dealerOrderId = $this->request->param('dealer_order_id');
            $orderId = ($dealerOrderId)
                ? $this->Fulfillment->DealerOrder->field('order_id', ['DealerOrder.id' => $dealerOrderId])
                : $this->request->param('order_id');

            $authUser = (array)$this->Auth->user();
            if($authUser['user_type'] == User::TYPE_SALES_REP && $this->Order->isOrderDistributor($authUser, $orderId)){
                return true;
            }
            $validUserTypes = [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF];
            if ($this->Order->isShipFromStoreShipmentStatus($orderId)) {
                $validUserTypes[] = User::TYPE_RETAILER;
            }
            $this->Permissions->assertUserIsType($authUser, $validUserTypes);
            $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);

            $this->OrderLogic->isAuthorizedForOrder($orderId);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function add($orderId = null)
    {
        if ($this->request->is('post')) {
            if ($this->request->param('dealer_order_id')) {
                return $this->_add_post_dealer_order($orderId);
            } elseif ($this->Order->isShipFromStoreShipmentStatus($orderId)) {
                return $this->_add_post_shipfromstore_order($orderId);
            } else {
                return $this->_add_post_order($orderId);
            }
        }

        return $this->_add_get($orderId);
    }

    /**
     * @param $orderId
     * @return CakeResponse|null
     */
    private function _add_get($orderId): ?CakeResponse
    {
        $userType = (string)$this->Auth->user('user_type');
        $isDealerOrder = (bool)$this->request->param('dealer_order_id');

        $orderAlias = ($isDealerOrder) ? 'DealerOrder' : 'Order';
        $orderProductAlias = ($isDealerOrder) ? 'DealerOrderProduct' : 'OrderProduct';
        $orderProductKey = ($isDealerOrder) ? 'dealer_order_product_id' : 'order_product_id';

        $order = ($isDealerOrder)
            ? $this->Fulfillment->DealerOrder->findForFulfillment($orderId)
            : $this->Fulfillment->Order->findForFulfillment($orderId);
        $orderProducts = $order[$orderProductAlias];

        $isShipFromStoreShipmentStatus = $this->Order->isShipFromStoreShipmentStatus($order['Order']['id']);

        $warehouseOptions = ($userType === User::TYPE_SALES_REP)
            ? $this->Fulfillment->Warehouse->listValidSalesRepWarehouses($this->Auth->user('id'), $order['Order']['user_id'], $order['Order']['retailer_id'])
            : $this->Fulfillment->Warehouse->listValidUserWarehouses($order['Order']['user_id'], $order['Order']['retailer_id']);

        $warehouseSourceIdMap = (array)$this->Fulfillment->Warehouse->find('list', [
            'conditions' => ['Warehouse.id' => array_keys($warehouseOptions)],
            'fields' => ['id', 'source_id'],
        ]);
        $_warehouseOptions = [];
        foreach ($warehouseOptions as $warehouseId => $warehouseName) {
            $_warehouseOptions[$warehouseId] = [
                'value' => $warehouseId,
                'name' => $warehouseName,
                'data-source-id' => $warehouseSourceIdMap[$warehouseId] ?? null,
            ];
        }
        $warehouseOptions = $_warehouseOptions;

        // Form defaults
        $dominantWarehouseId = (!$isShipFromStoreShipmentStatus)
            ? $this->OrderProduct->getDominantWarehouseId($orderProducts) ?: key($warehouseOptions)
            : null;
        $this->request->data['Fulfillment']['warehouse_id'] = $dominantWarehouseId;
        $this->request->data['FulfillmentProduct'] = array_map(function($item) use ($orderProductKey, $dominantWarehouseId) {
            return [
                $orderProductKey => $item['id'],
                'quantity' => ($dominantWarehouseId === null || $item['warehouse_id'] === $dominantWarehouseId)
                    ? $item['remaining_quantity']
                    : 0,
            ];
        }, Hash::combine($orderProducts, '{n}.id', '{n}'));

        $this->set('isDealerOrder', $isDealerOrder);
        $this->set('isCreditOrder', ($order['Order']['payment_method'] ?? '') === OrderPaymentMethod::CREDIT);
        $this->set('isShipFromStoreShipmentStatus', $isShipFromStoreShipmentStatus);
        $this->set('orderAlias', $orderAlias);
        $this->set('orderProductAlias', $orderProductAlias);
        $this->set('orderProductKey', $orderProductKey);
        $this->set('order', $order);
        $this->set('customerAddress', $this->_formatOrderCustomerAddress($order));
        $this->set('warehouseOptions', $warehouseOptions);
        $this->set('courierOptions', $this->Fulfillment->Courier->listNamesById());

        return null;
    }

    /**
     * @param $orderId
     * @return CakeResponse|null
     * @throws Exception
     */
    private function _add_post_order($orderId): ?CakeResponse
    {
        $order = $this->Fulfillment->Order->findForFulfillmentPost($orderId);

        $userType = (string)$this->Auth->user('user_type');
        if ($userType === User::TYPE_SALES_REP) {
            // isAuthorized() asserts that a sales rep is the order's distributor
            $distributorId = $this->Auth->user('id');
            $selectedWarehouseId = $this->request->data['Fulfillment']['warehouse_id'];

            if (!$this->Fulfillment->Warehouse->isDistributorAssignedToWarehouse($distributorId, $selectedWarehouseId)) {
                return $this->_exceptionResponse(new BadRequestException(), __('Selected distributor is not assigned to selected warehouse.'), false);
            }
        }

        if (!$this->Fulfillment->createOrderFulfillment($orderId, $this->request->data)) {
            return $this->_formErrorResponse();
        }
        $fulfillmentId = $this->Fulfillment->id;

        if (
            $order['Order']['source_id'] &&
            !empty($this->request->data['Fulfillment']['place_ecommerce_fulfillment'])
        ) {
            try {
                $this->FulfillmentLogic->place_ecommerce_fulfillment($fulfillmentId);
            } catch (EcommercePlacementException $e) {
                CakeLog::debug($e->getMessage());
                $this->setFlash($e->getMessage(), 'error');
            } catch (Exception $e) {
                CakeLog::error($e);
                $this->setFlash('An error occurred attempting to create the fulfillment in ' . $order['User']['site_type'], 'error');
            }
        }

        try {
            $this->FulfillmentLogic->afterConsumerFulfillment((int)$orderId, $fulfillmentId);
        } catch (UserFriendlyException $e) {
            CakeLog::debug($e->getMessage());
            $this->setFlash($e->getMessage(), 'error');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash('An error occurred attempting to update the order\'s fulfillment status', 'error');
        }
        $this->NotificationLogic->orderFulfillmentEmail($fulfillmentId);

        $fulfillmentName = $this->Fulfillment->field('name', ['Fulfillment.id' => $fulfillmentId]);
        $flashMessage = "Fulfillment {$fulfillmentName} has been added to the order";

        return $this->_successResponse($flashMessage, ['controller' => 'orders', 'action' => 'invoice', 'id' => $orderId]);
    }

    /**
     * Perform the Ship from Store consumer shipment workflow from the fulfillment form UI.
     *
     * Fulfillment records currently originate from the brand but this action originates from the retailer.
     *
     * @param $orderId
     * @return CakeResponse|null
     * @see OrdersController::dealerOrderShipmentTracking
     */
    //TODO Convert to fulfillment records after ensuring the model is compatible.
    private function _add_post_shipfromstore_order($orderId): ?CakeResponse
    {
        try {
            if (!$this->request->data['Fulfillment']['courier_id']) {
                $this->request->data['Fulfillment'] = array_merge($this->request->data['Fulfillment'], [
                    'courier_id' => null,
                    'tracking_number' => null,
                    'tracking_url' => null,
                ]);
            } elseif (!is_numeric($this->request->data['Fulfillment']['courier_id'])) {
                $this->request->data['Fulfillment'] = array_merge($this->request->data['Fulfillment'], [
                    'courier_id' => null,
                ]);
            }

            $courierId = (int)$this->request->data['Fulfillment']['courier_id'];
            $trackingNumber = (string)$this->request->data['Fulfillment']['tracking_number'];
            $trackingUrl = (string)$this->request->data['Fulfillment']['tracking_url'];

            if ($courierId && $trackingNumber) {
                $trackingResponse = $this->FulfillmentLogic->trackOrder($orderId, $courierId, $trackingNumber);
                if (!$trackingUrl) {
                    $trackingUrl = is_array($trackingResponse) ? ($trackingResponse['courier_tracking_link'] ?? null) : null;
                }
            }

            $this->Order->markShippedToConsumer($orderId);
            $this->NotificationLogic->sendShipmentTrackingEmailsForOrder($orderId, $trackingUrl, false);

            return $this->_successResponse('Tracking info added to the order.', ['controller' => 'orders', 'action' => 'invoice', 'id' => $orderId]);
        } catch (UserFriendlyException $e) {
            return $this->_exceptionResponse(new InternalErrorException($e->getMessage()), $e->getMessage());
        } catch (Exception $e) {
            return $this->_exceptionResponse(new InternalErrorException($e->getMessage()), 'Failed to add tracking info to the order', $e);
        }
    }

    /**
     * @param $dealerOrderId
     * @return CakeResponse|null
     * @throws Exception
     */
    private function _add_post_dealer_order($dealerOrderId): ?CakeResponse
    {
        $dealerOrder = $this->Fulfillment->DealerOrder->findForFulfillmentPost($dealerOrderId);

        $userType = (string)$this->Auth->user('user_type');
        if ($userType === User::TYPE_SALES_REP) {
            // isAuthorized() asserts that a sales rep is the order's distributor
            $distributorId = $this->Auth->user('id');
            $selectedWarehouseId = $this->request->data['Fulfillment']['warehouse_id'];

            if (!$this->Fulfillment->Warehouse->isDistributorAssignedToWarehouse($distributorId, $selectedWarehouseId)) {
                return $this->_exceptionResponse(new BadRequestException(), __('Selected distributor is not assigned to selected warehouse.'), false);
            }
        }

        if (!$this->Fulfillment->createDealerOrderFulfillment($dealerOrderId, $this->request->data)) {
            return $this->_formErrorResponse();
        }
        $fulfillmentId = $this->Fulfillment->id;

        if (
            $dealerOrder['DealerOrder']['source_id'] &&
            !empty($this->request->data['Fulfillment']['place_ecommerce_fulfillment'])
        ) {
            try {
                $this->FulfillmentLogic->place_ecommerce_fulfillment($fulfillmentId);
            } catch (EcommercePlacementException $e) {
                CakeLog::debug($e->getMessage());
                $this->setFlash($e->getMessage(), 'error');
            } catch (Exception $e) {
                CakeLog::error($e);
                $this->setFlash('An error occurred attempting to create the fulfillment in ' . $dealerOrder['Order']['User']['site_type'], 'error');
            }
        }

        try {
            $this->FulfillmentLogic->afterDealerFulfillment((int)$dealerOrderId, (int)$fulfillmentId, (bool)!empty($this->request->data['Fulfillment']['create_retailer_credit']));
        } catch (UserFriendlyException $e) {
            CakeLog::debug($e->getMessage());
            $this->setFlash($e->getMessage(), 'error');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash('An error occurred attempting to update the order\'s fulfillment status', 'error');
        }

        $fulfillment = $this->Fulfillment->find('first', [
            'contain' => [
                'DealerOrder' => ['fields' => ['id', 'fulfillment_status']],
            ],
            'conditions' => ['Fulfillment.id' => $fulfillmentId],
            'fields' => ['Fulfillment.id', 'Fulfillment.dealer_order_id', 'Fulfillment.name'],
        ]);
        $flashMessage = ($fulfillment['DealerOrder']['fulfillment_status'] === FulfillmentStatus::FULFILLED)
            ? 'Delivery date sent to customer via email notification'
            : "Fulfillment {$fulfillment['Fulfillment']['name']} has been added to the order";

        return $this->_successResponse($flashMessage, ['controller' => 'orders', 'action' => 'edit', 'id' => $dealerOrder['DealerOrder']['order_id']]);
    }

    private function _formErrorResponse(): ?CakeResponse
    {
        $presentableErrors = array_intersect_key($this->Fulfillment->validationErrors, array_flip([
            'warehouse_id',
            'courier_id',
            'tracking_name',
            'tracking_url',
            'FulfillmentProduct',
        ]));
        $log = json_encode(['errors' => $this->Fulfillment->validationErrors, 'data' => $this->Fulfillment->data]);

        return ($presentableErrors)
            ? $this->_validationErrorFlashResponse($presentableErrors, $log)
            : $this->_exceptionResponse(new InternalErrorException($log), null, true);
    }
}
