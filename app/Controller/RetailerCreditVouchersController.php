<?php

App::uses('AppController', 'Controller');
App::uses('CreditVoucherLine', 'Lib/RetailerCredit');

/**
 * RetailerCreditVouchers Controller
 *
 * @property IndexQueryHandlerComponent $IndexQueryHandler
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property RetailerCreditVoucher $RetailerCreditVoucher
 */
class RetailerCreditVouchersController extends AppController
{
    public $components = [
        'IndexQueryHandler' => [
            'defaultQuery' => [
                'sort' => 'RetailerCreditVoucher.created_at',
                'direction' => 'DESC',
                'limit' => '10',
                'page' => '1',
            ],
        ],
    ];

    public $uses = [
        'ManufacturerRetailer',
        'RetailerCreditVoucher',
    ];

    public function index($retailerId)
    {
        list($brandId, $retailerId) = $this->_extractUserIds($this->request);
        $this->request->query = $this->IndexQueryHandler->extractAllParams($this->request->query);
        $limit = $this->request->query('limit') ?: 10;
        $page = $this->request->query('page') ?: 1;
        $sortField = $this->request->query('sort');
        $sortDirection = $this->request->query('direction');

        $this->set('currencyCode', $this->getCurrencyCodeForIndex($brandId, $retailerId));
        $this->set('title_for_layout', $this->getTitleForIndex($brandId, $retailerId));

        $this->set('totals', $this->RetailerCreditVoucher->findTotals($brandId, $retailerId));
        $vouchers = $this->RetailerCreditVoucher->getVouchersForIndex($brandId, $retailerId, $limit, $page, $sortField, $sortDirection);
        $vouchers = $this->getCreditVoucherLineObjects($vouchers);
        $this->set('vouchers', $vouchers);

        $this->set('creditCount', $this->RetailerCreditVoucher->getCountForIndex($brandId, $retailerId));
        $this->set('limit', $limit);
        $this->set('page', $page);
        $this->set('sortField', $sortField);
        $this->set('sortOrder', $sortDirection);
        $this->set('brandId', $brandId);

        // permission settings
        $this->set('canCreateVoucher', $this->userCanCreateVoucher());
    }

    public function create()
    {
        if (!$this->userCanCreateVoucher()) {
            throw new ForbiddenException(json_encode($this->Auth->user()));
        }

        list($brandId, $retailerId) = $this->_extractUserIds($this->request);

        if ($this->request->is('post')) {
            $this->createPost($brandId, $retailerId);
        }
    }

    protected function createPost($brandId, $retailerId)
    {
        $authUser = User::revertAuthParent($this->Auth->user());
        $this->request->data['RetailerCreditVoucher']['user_id'] = $brandId;
        $this->request->data['RetailerCreditVoucher']['retailer_id'] = $retailerId;
        $this->request->data['RetailerCreditVoucher']['created_by'] = Hash::get($authUser, 'id');
        if (!$this->RetailerCreditVoucher->save($this->request->data)) {
            $errors = $this->RetailerCreditVoucher->validationErrors;

            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $errors, 'data' => $this->RetailerCreditVoucher->data])),
                !empty($errors['value'])
                    ? implode('<br />', $errors['value'])
                    : 'The voucher could not be saved. Please, try again.',
                true
            );
        }

        return $this->_successResponse('The voucher has been saved.');
    }

    /**
     * @param CakeRequest $request
     * @return array [$brandId, $retailerId]
     * @throws BadRequestException
     */
    protected function _extractUserIds(CakeRequest $request): array
    {
        $brandId = $request->param('user_id');
        $retailerId = $this->Auth->user('id');
        if ($this->Auth->user('user_type') == User::TYPE_MANUFACTURER) {
            $brandId = $this->Auth->user('id');
            $retailerId = $request->param('retailer_id');
        } elseif ($this->Auth->user('user_type') == User::TYPE_SALES_REP) {
            $retailerId = $request->param('retailer_id');
        }

        if (!$this->ManufacturerRetailer->validateBrandAndRetailerIds((int)$brandId, (int)$retailerId)) {
            $users = $this->User->findAllById([$brandId, $retailerId], ['id', 'email_address', 'user_type', 'Branch'], null, null, null, -1);

            throw new ForbiddenException('URL provided an invalid user id ' . json_encode($users));
        }

        return [$brandId, $retailerId];
    }

    protected function getTitleForIndex($brand_id, $retailer_id)
    {
        $title = '';
        $retailerName = false;
        $brandName = false;
        if ($this->Auth->user('user_type') == User::TYPE_MANUFACTURER) {
            $retailerName = $this->User->field('company_name', ['id' => $retailer_id]);
        } elseif ($this->Auth->user('user_type') == User::TYPE_SALES_REP) {
            $retailerName = $this->User->field('company_name', ['id' => $retailer_id]);
            $brandName = $this->User->field('company_name', ['id' => $brand_id]);
        } elseif ($this->Auth->user('user_type') == User::TYPE_RETAILER) {
            $brandName = $this->User->field('company_name', ['id' => $brand_id]);
        }

        if ($brandName && $retailerName) {
            return "{$brandName} - {$retailerName}";
        }

        return $brandName ?: $retailerName;
    }

    protected function userCanCreateVoucher()
    {
        $authUser = User::revertAuthParent($this->Auth->user());
        $userType = Hash::get($authUser, 'user_type');

        if (in_array($userType, [User::TYPE_MANUFACTURER])) {
            return true;
        }

        return false;
    }

    protected function getCreditVoucherLineObjects(array $vouchers)
    {
        return array_map(function($voucher) {
            return CreditVoucherLine::createFromVoucher($this->RetailerCreditVoucher, $voucher);
        }, $vouchers);
    }
    protected function getCurrencyCodeForIndex($brandId, $retailerId)
    {
        return $this->ManufacturerRetailer->getCurrencyCodeForCredits((int)$brandId, (int)$retailerId);
    }
}
