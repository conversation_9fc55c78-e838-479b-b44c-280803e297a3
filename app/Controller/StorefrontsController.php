<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('App<PERSON><PERSON>roller', 'Controller');
App::uses('B2bCartType', 'Utility');

/**
 * Class StorefrontsController
 * 
 * @property Storefront $Storefront
 * @property StorefrontSlide $StorefrontSlide
 * @property UploadComponent $Upload
 * @property Discount $Discount
 * @property Collection $Collection
 * @property Collection $Menu
 */
class StorefrontsController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Storefronts';

    public $uses = [
        'Storefront',
        'StorefrontSlide',
        'Discount',
        'Collection',
        'Menu',
    ];

    public $components = ['Upload'];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF]);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_STOREFRONTS, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function index($setId = null)
    {
        $this->set('title_for_layout', 'Storefronts');

        $userId = (int)$this->Auth->user('id');
        $selectedSetId = $this->request->query['set_id'] ?? $setId;

        $setsOptions = (array)$this->Storefront->find('list', [
            'conditions' => ['Storefront.user_id' => $userId],
            'fields' => ['id', 'name'],
            'order' => ['Storefront.name' => 'ASC']
        ]);
        $selectedSetId = $selectedSetId ?: array_key_first($setsOptions);

        $storefrontSet = (array)$this->Storefront->find('first', [
            'conditions' => [
                'Storefront.user_id' => $userId,
                'Storefront.id' => $selectedSetId
            ],
            'fields' => ['id', 'menu_id', 'menu_label_color', 'menu_label_font_size']
        ]);
        $selectedSetId = (int)($storefrontSet['Storefront']['id'] ?? 0) ?: null;

        $storefrontData = [];
        $storefrontMenuId = null;
        $storefrontMenuLabelColor = '';
        $storefrontMenuLabelFontSize = '';
        $selectedMenu = [];
        if ($selectedSetId) {
            $storefrontData = (array)$this->StorefrontSlide->find('all', [
                'conditions' => [
                    'StorefrontSlide.user_id' => $userId,
                    'StorefrontSlide.storefront_id' => $selectedSetId
                ],
                'fields' => [
                    'id',
                    'user_id',
                    'slide_number',
                    'slide_name',
                    'image',
                    'image_position',
                    'overlay_opacity',
                    'text_color',
                    'text_alignment',
                    'heading',
                    'button_label',
                    'redirect_type',
                    'redirect_url',
                ],
                'order' => ['StorefrontSlide.slide_number' => 'ASC'],
            ]);
            foreach ($storefrontData as &$item) {
                $id = (int)$item['StorefrontSlide']['id'];
                $translations = $this->StorefrontSlide->recordWithAllTranslations($id, [
                    'fields' => ['id', 'slide_name', 'heading', 'button_label']
                ]);
                foreach ($translations as $locale => $trans) {
                    $item['StorefrontSlide'][$locale] = $trans;
                }
            }

            $storefrontMenuId = (int)$storefrontSet['Storefront']['menu_id'] ?: null;
            $storefrontMenuLabelColor = (string)$storefrontSet['Storefront']['menu_label_color'];
            $storefrontMenuLabelFontSize = (string)$storefrontSet['Storefront']['menu_label_font_size'];

            $selectedMenu = (array)$this->Menu->find('first', [
                'conditions' => [
                    'Menu.user_id' => $userId,
                    'Menu.id' => $storefrontMenuId,
                ],
                'contain' => [
                    'MenuItem' => [
                        'fields' => ['id', 'menu_id', 'label', 'url', 'menu_item_order'],
                        'order' => ['MenuItem.menu_item_order' => 'ASC']
                    ],
                ],
                'fields' => ['id', 'user_id', 'name'],
            ]);
        }

        $menuOptions = (array)$this->Menu->find('list', [
            'conditions' => ['Menu.user_id' => $userId],
            'fields' => ['id', 'name'],
            'order' => ['Menu.name' => 'ASC'],
        ]);
        $discountTypes = $this->Discount->getAvailableB2bDiscounts(null, $userId);
        $discountTypeOptions = array_filter(array_map(
            fn(string $type): string => B2bCartType::getLabels($type),
            Hash::combine($discountTypes, '{n}.Discount.b2b_discount_type', '{n}.Discount.b2b_discount_type')
        ));
        $collectionOptionsByUser = $this->Collection->getCollectionsOptionsByUser($userId);
        $productOptions = $this->Product->getProductTitlesGroupByTitleId($userId);

        $this->set([
            'storefront' => $storefrontData,
            'discountTypeOptions' => $discountTypeOptions,
            'productOptions' => $productOptions,
            'collectionOptionsByUser' => $collectionOptionsByUser,
            'retailerId' => $this->User->getMainRetailerId($userId),
            'setsOptions' => $setsOptions,
            'selectedSetId' => $selectedSetId,
            'selectedMenu' => $selectedMenu,
            'menuOptions' => $menuOptions,
            'storefrontMenuId' => $storefrontMenuId,
            'storefrontMenuLabelColor' => $storefrontMenuLabelColor,
            'storefrontMenuLabelFontSize' => $storefrontMenuLabelFontSize,
        ]);

        $this->render('index');
    }

    public function createSet()
    {
        $this->autoRender = false;

        if ($this->request->is('post')) {
            $userId = $this->Auth->user('id');
            $name = $this->request->data('name');

            $this->Storefront->create();
            $setData = [
                'user_id' => $userId,
                'name' => $name
            ];

            if ($this->Storefront->save($setData)) {
                $newSetId = $this->Storefront->getLastInsertId();
                $this->response->body(json_encode(['success' => true, 'set_id' => $newSetId]));
            } else {
                $this->response->body(json_encode(['success' => false, 'message' => 'Failed to save set.']));
            }
        } else {
            $this->response->body(json_encode(['success' => false, 'message' => 'Invalid request.']));
        }

        return $this->response;
    }

    public function updateSetName()
    {
        $this->autoRender = false;

        if ($this->request->is('post')) {
            $userId = $this->Auth->user('id');
            $setId = $this->request->data('set_id');
            $newSetName = $this->request->data('name');

            $id = (int)$this->Storefront->fieldByConditions('id', [
                'Storefront.id' => $setId,
                'Storefront.user_id' => $userId,
            ]);
            if (!$id) {
                $this->response->body(json_encode(['success' => false, 'message' => 'Storefront set not found.']));
                return $this->response;
            }

            if ($this->Storefront->save(['id' => $id, 'name' => $newSetName])) {
                $this->response->body(json_encode(['success' => true, 'message' => 'Storefront set name updated.']));
            } else {
                $this->response->body(json_encode(['success' => false, 'message' => 'Failed to update storefront set name.']));
            }
        } else {
            $this->response->body(json_encode(['success' => false, 'message' => 'Invalid request.']));
        }

        return $this->response;
    }

    public function updateMenuId()
    {
        $this->autoRender = false;

        if ($this->request->is('post')) {
            $userId = $this->Auth->user('id');
            $setId = $this->request->data('set_id');
            $menuId = $this->request->data('menu_id');
            $labelColor = $this->request->data('menu_label_color');
            $fontSize = $this->request->data('menu_label_font_size');

            $id = (int)$this->Storefront->fieldByConditions('id', [
                'Storefront.id' => $setId,
                'Storefront.user_id' => $userId,
            ]);

            if (!$id) {
                $this->response->body(json_encode(['success' => false, 'message' => 'Storefront set not found.']));
                return $this->response;
            }

            $data = [
                'id' => $id,
                'menu_id' => $menuId,
                'menu_label_color' => $labelColor,
                'menu_label_font_size' => $fontSize,
            ];

            if ($this->Storefront->save($data)) {
                $this->response->body(json_encode(['success' => true, 'message' => 'Header settings updated.']));
            } else {
                $this->response->body(json_encode(['success' => false, 'message' => 'Failed to update header settings.']));
            }
        } else {
            $this->response->body(json_encode(['success' => false, 'message' => 'Invalid request.']));
        }

        return $this->response;
    }

    public function save()
    {
        $this->autoRender = false;

        if ($this->request->is('post')) {
            $userId = $this->Auth->user('id');
            $selectedSetId = $this->request->data['StorefrontSlide']['storefront_id'];

            $slides = $this->request->data['StorefrontSlide']['slides']
                ?? [$this->request->data['StorefrontSlide']];
            unset($this->request->data['StorefrontSlide']);

            foreach ($slides as $index => $slideData) {
                if (empty($slideData)) {
                    continue;
                }

                $slideData['storefront_id'] = $selectedSetId;

                $language = $slideData['locale'] ?? SupportedLanguages::DEFAULT_LOCALE;

                $existing = null;
                if (isset($slideData['id'])) {
                    $existing = $this->StorefrontSlide->find('first', [
                        'conditions' => [
                            'StorefrontSlide.user_id' => $userId,
                            'StorefrontSlide.storefront_id' => $selectedSetId,
                            'StorefrontSlide.id' => $slideData['id'],
                        ],
                        'fields' => ['id', 'image', 'slide_number'],
                    ]);
                }

                if (!$existing) {
                    $maxSlide = $this->StorefrontSlide->find('first', [
                        'conditions' => [
                            'StorefrontSlide.user_id' => $userId,
                            'StorefrontSlide.storefront_id' => $selectedSetId,
                        ],
                        'fields' => ['MAX(StorefrontSlide.slide_number) AS max_slide'],
                    ]);
                    $slideNumber = (int)($maxSlide[0]['max_slide'] ?? 0) + 1;
                } else {
                    $slideNumber = $existing['StorefrontSlide']['slide_number'];
                }

                $file = (array)($slideData['slide_image'] ?? []);
                $sanitizedFileName = !empty($file['name']) ? preg_replace('/[^a-zA-Z0-9-_\.]/', '_', basename($file['name'])) : null;

                if (!empty($sanitizedFileName)) {
                    $imageUrl = $this->Upload->replaceFileInUserHash(
                        !empty($existing['StorefrontSlide']['image']) ? $existing['StorefrontSlide']['image'] : '',
                        $file,
                        $userId,
                        'storefronts',
                        $sanitizedFileName
                    );
                    $slideData['image'] = $imageUrl;
                } elseif (!empty($slideData['hidden_slide_image'])) {
                    $slideData['image'] = $slideData['hidden_slide_image'];
                }

                if (isset($slideData['redirect_url']) && $slideData['redirect_url'] === 'promotion') {
                    $slideData['redirect_url'] = BASE_PATH . 'promotions';
                }

                unset($slideData['slide_image'], $slideData['hidden_slide_image'], $slideData['current_slide_index']);

                $this->StorefrontSlide->locale = $language;

                if (!empty($existing)) {
                    $this->StorefrontSlide->id = $existing['StorefrontSlide']['id'];
                } else {
                    $this->StorefrontSlide->create();
                }

                $slideData['user_id'] = $userId;
                $slideData['slide_number'] = $slideNumber;

                if ($this->StorefrontSlide->save($slideData)) {
                    $this->response->type('json');
                    $this->response->body(json_encode([
                        'success' => true,
                        'message' => 'Slide saved successfully'
                    ]));
                    return $this->response;
                } else {
                    $this->response->type('json');
                    $this->response->body(json_encode([
                        'success' => false,
                        'message' => 'Could not save slide'
                    ]));
                    return $this->response;
                }
            }
            $this->response->type('json');
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'No slide data to save'
            ]));
            return $this->response;
        }

        $this->response->type('json');
        $this->response->body(json_encode([
            'success' => false,
            'message' => 'Invalid request method'
        ]));
        return $this->response;
    }

    public function deleteStorefront()
    {
        $this->autoRender = false;
        $this->response->type('json');

        if ($this->request->is('post')) {
            $userId = $this->Auth->user('id');
            $setId = $this->request->data('set_id');

            if (!$setId) {
                $this->response->body(json_encode(['success' => false, 'message' => 'Invalid storefront set ID.']));
                return $this->response;
            }

            if (!$this->Storefront->exists(['Storefront.id' => $setId, 'Storefront.user_id' => $userId])) {
                $this->response->body(json_encode(['success' => false, 'message' => 'Storefront set not found or does not belong to you.']));
                return $this->response;
            }

            if ($this->Storefront->delete($setId)) {
                $nextSetId = (int)$this->Storefront->fieldByConditions('id', ['Storefront.user_id' => $userId], [
                    'order' => ['Storefront.id' => 'ASC'],
                ]) ?: null;

                $this->response->body(json_encode(['success' => true, 'message' => 'Storefront set deleted successfully.', 'next_set_id' => $nextSetId]));
            } else {
                $this->response->body(json_encode(['success' => false, 'message' => 'Failed to delete storefront set.']));
            }
        } else {
            $this->response->body(json_encode(['success' => false, 'message' => 'Invalid request.']));
        }

        return $this->response;
    }

    public function deleteSlide()
    {
        $this->autoRender = false;
        $this->response->type('json');

        if (!$this->request->is('post')) {
            return json_encode([
                'success' => false,
                'message' => 'Invalid request method.'
            ]);
        }

        $userId = $this->Auth->user('id');
        $slideId = $this->request->data('slide_id');

        if (!$slideId) {
            return json_encode([
                'success' => false,
                'message' => 'Slide ID is required.'
            ]);
        }

        if (!$this->StorefrontSlide->exists(['StorefrontSlide.id' => $slideId, 'StorefrontSlide.user_id' => $userId])) {
            return json_encode([
                'success' => false,
                'message' => 'Slide not found.'
            ]);
        }

        if ($this->StorefrontSlide->delete($slideId)) {
            return json_encode([
                'success' => true,
                'message' => 'Slide deleted successfully.'
            ]);
        }

        return json_encode([
            'success' => false,
            'message' => 'Failed to delete slide.'
        ]);
    }

    public function update_slide_order()
    {
        $this->request->allowMethod(['post']);
        $slideOrder = $this->request->data('order');

        if (empty($slideOrder) || !is_array($slideOrder)) {
            $this->set([
                'success' => false,
                'message' => __('Invalid slide order.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $userId = $this->Auth->user('id');

        $countValidSlides = (int)$this->StorefrontSlide->find('count', [
            'conditions' => ['StorefrontSlide.user_id' => $userId, 'StorefrontSlide.id' => $slideOrder],
        ]);
        if ($countValidSlides < count($slideOrder)) {
            $this->set([
                'success' => false,
                'message' => __('Some slides do not belong to you.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $saveData = [];
        foreach ($slideOrder as $index => $id) {
            $saveData[] = [
                'id' => $id,
                'slide_number' => $index + 1
            ];
        }

        if ($this->StorefrontSlide->saveMany($saveData)) {
            $this->set([
                'success' => true,
                'message' => __('Slide order updated successfully.'),
                '_serialize' => ['success', 'message']
            ]);
        } else {
            $this->set([
                'success' => false,
                'message' => __('Could not save new slide order.'),
                '_serialize' => ['success', 'message']
            ]);
        }
    }

}
