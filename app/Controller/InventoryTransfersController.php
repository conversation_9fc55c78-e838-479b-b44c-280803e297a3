<?php
App::uses('AppController', 'Controller');
App::uses('UserFriendlyException', 'Error');
App::uses('InventoryTransferStatus', 'Utility');

/**
 * InventoryTransfers Controller.
 *
 * @property IndexQueryHandlerComponent $IndexQueryHandler
 * @property PhpExcelComponent $PhpExcel
 * @property SpoutComponent $Spout
 * @property UploadComponent $Upload
 *
 * @property InventoryTransfer $InventoryTransfer
 * @property InventoryTransferProduct $InventoryTransferProduct
 * @property Warehouse $Warehouse
 */
class InventoryTransfersController extends AppController
{
    public $components = [
        'IndexQueryHandler' => [
            'defaultQuery' => [
                'sort' => 'InventoryTransfer.expected_arrival_date',
                'direction' => 'ASC',
                'limit' => '50',
                'search' => '',
                'status' => InventoryTransferStatus::PENDING,
                'destination' => '',
            ],
        ],
        'PhpExcel',
        'Spout',
        'Upload',
    ];

    public $uses = [
        'InventoryTransfer',
        'InventoryTransferProduct',
        'Warehouse',
    ];

    public $importHeaders = [
        'Product.product_sku' => 'Part #',
        'InventoryTransferProduct.quantity' => 'Qty',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $authUser = $this->Auth->user();

            $this->Permissions->assertUserIsType($authUser, [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF]);
            $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_INVENTORY, Permissions::LEVEL_EDIT);

            if (array_key_exists('id', $this->request->params)) {
                $id = $this->request->params['id'];

                $record = $this->InventoryTransfer->findForAuthorization($id);

                if (empty($record['InventoryTransfer']['id'])) {
                    throw new NotFoundException('InventoryTransfer not found where id=' . json_encode($id));
                }

                if ($authUser['id'] !== $record['InventoryTransfer']['user_id']) {
                    throw new ForbiddenException(json_encode($record + ['Auth' => User::extractAuthUserLogFields($authUser)]));
                }
            }
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function index()
    {
        $userId = (int)$this->Auth->user('id');

        $this->request->data = $this->IndexQueryHandler->extractAllParams($this->request->query);
        $this->request->query = $this->IndexQueryHandler->extractModifiedParams($this->request->query);

        $conditions = [
            'InventoryTransfer.user_id' => $userId,
        ];
        if ($this->request->data['search']) {
            $conditions[] = $this->InventoryTransfer->buildSearchCondition($this->request->data['search']);
        }
        if ($this->request->data['status']) {
            $conditions['InventoryTransfer.status'] = $this->request->data['status'];
        }
        if ($this->request->data['destination']) {
            $conditions['InventoryTransfer.destination_warehouse_id'] = $this->request->data['destination'];
        }

        $sort = $this->request->data['sort'];
        $direction = $this->request->data['direction'];
        $limit = (int)$this->request->data['limit'];
        $page = (int)$this->request->data['page'];

        $order = [];
        if ($sort) {
            $order[$sort] = $direction;
        }

        $this->set('limit', $limit);
        $this->set('page', $page);
        $this->set('count', $this->InventoryTransfer->countAllForIndex($conditions));
        $this->set('records', $this->InventoryTransfer->findAllForIndex($conditions, $order, $limit, $page));
        $this->set('warehouseOptions', $this->Warehouse->listValidUserWarehouses($userId));
    }

    public function add()
    {
        if ($this->request->is('post')) {
            return $this->_post_edit_form(null);
        }

        return $this->_get_edit_form(null);
    }

    public function edit(int $id)
    {
        if ($this->request->is(['post', 'put'])) {
            return $this->_post_edit_form($id);
        }

        return $this->_get_edit_form($id);
    }

    private function _get_edit_form(?int $id): ?CakeResponse
    {
        $userId = (int)$this->Auth->user('id');

        $this->request->data = $this->InventoryTransfer->findForEdit((int)$id);
        $this->set('warehouseOptions', $this->Warehouse->listValidUserWarehouses($userId));

        return $this->render('edit');
    }

    private function _post_edit_form(?int $id): ?CakeResponse
    {
        $userId = (int)$this->Auth->user('id');

        $created = (!$id);
        $importFile = (array)($this->request->data('InventoryTransfer.import_products') ?: ['error' => UPLOAD_ERR_NO_FILE]);

        if ($created || $importFile['error'] !== UPLOAD_ERR_NO_FILE) {
            try {
                $warehouseId = $this->request->data('InventoryTransfer.destination_warehouse_id')
                    // Fetch existing if form input was disabled
                    ?: $this->InventoryTransfer->field('destination_warehouse_id', ['InventoryTransfer.id' => $id])
                    ?: null;

                $this->request->data['InventoryTransferProduct'] = $this->_import_products($id, $userId, $warehouseId, $importFile);

                unset($this->request->data['InventoryTransfer']['import_products']);
            } catch (UserFriendlyException $e) {
                CakeLog::debug($e->getMessage());

                return $this->_exceptionResponse(new BadRequestException($e->getMessage()), $e->getMessage());
            } catch (Exception $e) {
                CakeLog::error($e);

                return $this->_exceptionResponse(new InternalErrorException($e->getMessage()), 'An error occurred uploading the file');
            }
        }

        if (!$this->InventoryTransfer->saveFromEdit($id, $userId, $this->request->data)) {
            return $this->_validationErrorResponse($this->InventoryTransfer->validationErrors, $this->InventoryTransfer->data);
        }

        return $this->_successResponse(sprintf('Transfer %s successfully %s',
            $this->InventoryTransfer->field('name', ['InventoryTransfer.id' => $this->InventoryTransfer->id]),
            ($created ? 'created' : 'updated')
        ));
    }

    public function delete(int $id)
    {
        $this->request->allowMethod('post', 'delete');

        $record = $this->InventoryTransfer->findForDelete($id);

        if ($record['InventoryTransfer']['status'] !== InventoryTransferStatus::PENDING) {
            $message = sprintf('Cannot delete a transfer in %s status', InventoryTransferStatus::getLabel($record['InventoryTransfer']['status']));
            CakeLog::debug($message);

            return $this->_exceptionResponse(new BadRequestException($message), $message);
        }

        if (!$this->InventoryTransfer->delete($id)) {
            $message = 'Failed to delete transfer';
            $Auth = User::extractAuthUserLogFields($this->Auth->user());

            $log = compact('message') + $record + compact('Auth');

            return $this->_exceptionResponse(new InternalErrorException(json_encode($log)), null, true);
        }

        return $this->_successResponse("Transfer {$record['InventoryTransfer']['name']} has been deleted");
    }

    public function export_products(?int $id = null)
    {
        $userId = (int)$this->Auth->user('id');

        if (!$this->InventoryTransferProduct->hasExportData($userId)) {
            return $this->_exceptionResponse(new NotFoundException(), 'No active products found. Make sure they are configured and try again.');
        }

        $tableModel = [
            $this->PhpExcel->newExportColumn($this->importHeaders['Product.product_sku'], function($row) {
                return $row['Product']['product_sku'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn('Name', function($row) {
                return $row['Product']['product_title'];
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['InventoryTransferProduct.quantity'], function($row) {
                return (int)($row['InventoryTransferProduct']['quantity'] ?? null);
            }),
        ];

        $fileName = sprintf('%s %s %s.xlsx',
            $this->Auth->user('company_name'),
            'Inventory Transfer',
            ltrim($this->InventoryTransfer->field('name', ['InventoryTransfer.id' => $id], false) ?: 'NEW', '#')
        );

        $this->Spout->doWithOpenWriter($fileName, function() use ($id, $userId, $tableModel) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));

            $this->InventoryTransferProduct->streamExportData($id, $userId, function($record) use ($tableModel) {
                $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $record));
            });
        });

        return $this->response;
    }

    /**
     * @param int|null $id
     * @param int $userId
     * @param int $warehouseId
     * @param array $file
     * @return array
     * @throws UserFriendlyException
     * @throws \Box\Spout\Common\Exception\IOException
     * @throws \Box\Spout\Reader\Exception\ReaderNotOpenedException
     */
    protected function _import_products(?int $id, int $userId, int $warehouseId, array $file): array
    {
        try {
            $filePath = $this->Upload->getTempFile($file);
            $fileName = $file['name'] ?? null;

            $tableMap = $this->Spout->extractTableData($filePath, $fileName);
            $tableHeaders = array_keys(current($tableMap));

            $importHeaders = array_intersect($this->importHeaders, $tableHeaders);
            if (!isset($importHeaders['Product.product_sku'])) {
                throw new UserFriendlyException('Import file missing required column: ' . json_encode($this->importHeaders['Product.product_sku']));
            }

            $skus = array_column($tableMap, $importHeaders['Product.product_sku']);

            $duplicateSkus = array_unique(array_diff_assoc($skus, array_unique($skus)));
            if ($duplicateSkus) {
                throw new UserFriendlyException("Duplicate {$importHeaders['Product.product_sku']} found in import file: " . implode(', ', $duplicateSkus));
            }

            $productBySku = $this->InventoryTransferProduct->listImportedProductsBySku($id, $userId, $warehouseId, $skus);

            $tableMap = array_filter($tableMap, function($row) use ($importHeaders, $productBySku) {
                $sku = $row[$importHeaders['Product.product_sku']];

                return array_key_exists($sku, $productBySku);
            });
            if (!$tableMap) {
                throw new UserFriendlyException("No products found for the {$importHeaders['Product.product_sku']} column in the import file.");
            }

            return array_reduce($tableMap, function($list, $row) use ($importHeaders, $productBySku) {
                $sku = $row[$importHeaders['Product.product_sku']];
                $product = $productBySku[$sku];

                $keys = [
                    'id' => $product['InventoryTransferProduct']['id'] ?? null,
                    'product_id' => $product['Product']['id'],
                ];

                $record = [];

                foreach ($importHeaders as $field => $columnName) {
                    $value = $row[$columnName];
                    if ($field === 'Product.product_sku') {
                        continue;
                    }

                    $model = 'InventoryTransferProduct';
                    if (strpos($field, '.') !== false) {
                        list($model, $field) = explode('.', $field, 2);
                    }

                    if ($model === 'InventoryTransferProduct') {
                        $record[$field] = $value;
                    } else {
                        $record[$model][$field] = $value;
                    }
                }

                if ($record) {
                    $list[] = $keys + $record;
                }

                return $list;
            }, []);
        } finally {
            if (!empty($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }

    private function _validationErrorResponse(array $validationErrors, array $data): ?CakeResponse
    {
        $presentableErrors = array_intersect_key($validationErrors, array_flip([
            'destination_warehouse_id',
            'status',
            'expected_arrival_date',
            'reference',
            'InventoryTransferProduct',
        ]));
        $presentableErrors['InventoryTransferProduct'] = array_map(
            function($item) {
                return array_intersect_key($item, array_flip([
                    'product_id',
                    'quantity',
                    'accepted_quantity',
                ]));
            },
            $presentableErrors['InventoryTransferProduct'] ?? []
        );

        $presentableErrors = array_unique(array_filter(Hash::flatten($presentableErrors)));

        $log = json_encode([
            'errors' => $validationErrors,
            'data' => $data,
            'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
        ]);

        return ($presentableErrors)
            ? $this->_validationErrorFlashResponse($presentableErrors, $log)
            : $this->_exceptionResponse(new BadRequestException($log), null, true);
    }
}
