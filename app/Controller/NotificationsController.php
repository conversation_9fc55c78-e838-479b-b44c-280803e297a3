<?php
App::uses('AppController', 'Controller');

/**
 * Class NotificationsController
 *
 * @property Notification $Notification
 * @property Order $Order
 * @property OrderRefund $OrderRefund
 */
class NotificationsController extends AppController
{
    /**
     * @var array
     */
    public $components = array();
    /**
     * @var array
     */
    public $uses = array('Notification', 'Order', 'OrderRefund');

    /**
     * get all user notification based on filter
     */
    public function getUsersNotifications()
    {
        $type = NULL;
        $userId = $this->Auth->user('id');
        $this->set('title_for_layout', 'Notifications');

        //filter notifications based on users
        if (isset($this->request->data['userId']) && !empty($this->request->data['userId'])) {
            $userId = $this->request->data['userId'];
        }

        //filter notifications based on notification type
        if (isset($this->request->data['type']) && !empty($this->request->data['type'])) {
            $type = $this->request->data['type'];
        }

        //filter notifications based on type
        if (isset($this->request->params['filter']) && !empty($this->request->params['filter'])) {
            if ($this->request->params['filter'] == 'comment') {
                $type = $this->Notification->messageTypes;
            } elseif ($this->request->params['filter'] == 'order') {
                $type = $this->Notification->orderTypes;
            }
        }

        $noRecords = 10;
        if (!empty($this->request->data['noRecords'])) {
            $noRecords = $this->request->data['noRecords'];
        }

        $page = 1;
        if (!empty($this->request->params['pageNo'])) {
            $page = $this->request->params['pageNo'];
        }
        $count_not = $this->Notification->getUsersNotificationsCount($userId, $type);
        $paging = paging($page, $noRecords, $count_not);
        $not = $this->Notification->getUsersNotifications($userId, $type, '', $noRecords, $paging['offset']);
        $this->set('notifications', $not);
        $this->set('paging', $paging);
        $this->set('noRecords', $noRecords);
        $this->set('count_not', $count_not);
        $this->set('userId', $userId);
        $this->set('type', $this->request->params['filter']);

        $ids = $this->_mapUnreadNotificationIds($not);
        if (count($ids)) {
            $this->Notification->makeRead($ids, $userId);
        }
    }

    /**
     * Get all messages on activities page (ajax_response)
     */
    public function ajax_not()
    {
        $this->layout = '';
        if ($this->request->is('ajax')) {
            $page = 1;
            if (!empty($this->request->data['pageNumber'])) $page = $this->request->data['pageNumber'];
            $this->set('type', $this->request->data['type']);

            //filter notifications based on type
            if (isset($this->request->data['type']) && !empty($this->request->data['type'])) {
                if ($this->request->data['type'] == 'comment') {
                    $this->request->data['type'] = $this->Notification->messageTypes;
                } elseif ($this->request->data['type'] == 'order') {
                    $this->request->data['type'] = $this->Notification->orderTypes;
                }
            }

            $paging = paging($page, $this->request->data['noRecords'], $this->request->data['count_not']);
            $not = $this->Notification->getUsersNotifications($this->request->data['userId'], $this->request->data['type'], '', $this->request->data['noRecords'], $paging['offset']);
            $this->set('notifications', $not);
            $ids = $this->_mapUnreadNotificationIds($not);
            if (count($ids)) {
                $this->Notification->makeRead($ids, $this->request->data['userId']);
            }
        }
    }

    /**
     * list last 10 message on notification popup (ajax_response)
     */
    public function ajax_popup()
    {
        $this->layout = 'ajax';
        $this->autoRender = false;
        if ($this->request->is('ajax')) {
            $userId = $this->Auth->user('id');
            $type = false;
            $filter = $this->request->param('filter');
            if ($filter === 'notification') {
                $type = $this->Notification->orderTypes;
                $this->set('type', 'order');
            } elseif ($filter === 'comment') {
                $type = $this->Notification->messageTypes;
                $this->set('type', 'comment');
            }
            $not = $this->Notification->getUsersNotifications($userId, $type, '', 5);
            $this->set('notifications', $not);
            $count_not = $this->Notification->getUsersNotificationsCount($userId, $type);
            if ($count_not > 5) {
                $this->set('pop_footer', true);
            }
            $this->render('ajax_not');
            $ids = $this->_mapUnreadNotificationIds($not);
            if (count($ids)) {
                $this->Notification->makeRead($ids, $userId);
            }
        }
    }

    /**
     * this function run on every 30 sec
     * and update head notification comments
     */
    public function getheadernot()
    {
        $this->autoRender = false;
        $userId = $this->Auth->user('id');
        $this->response->body(json_encode([
            'notification' => $this->Notification->getHeaderNotificationCount($userId),
            'comment' => $this->Notification->getHeaderMessageCount($userId),
        ]));
        return $this->response;
    }

    /**
     * @param array $notifications
     * @return array
     */
    protected function _mapUnreadNotificationIds($notifications)
    {
        $nid = function($a) {
            return ($a['Notification']['isRead'] != 1) ? $a['Notification']['id'] : null;
        };
        return array_map($nid, array_filter($notifications, $nid));
    }

}
