<?php
App::uses('AppController', 'Controller');
App::uses('DiscountOptions', 'Utility/Discount');
App::uses('DiscountUsageLimitOptions', 'Utility/Discount');

/**
 * Class DiscountsController
 *
 * @property PhpExcelComponent $PhpExcel
 * @property ShopifyComponent $Shopify
 *
 * @property Order $Order
 * @property User $User
 * @property UserSetting $UserSetting
 * @property PricingTier $PricingTier
 * @property Product $Product
 * @property CreditTerm $CreditTerm
 * @property Discount $Discount
 * @property DiscountRule $DiscountRule
 * @property DiscountUsage $DiscountUsage
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Collection $Collection
 * @property Tag $Tag
 */
class DiscountsController extends AppController
{
    /**
     * @var string
     */
    public $name = 'Discounts';

    /**
     * @var array
     */
    public $components = array('PhpExcel', 'Shopify.Shopify');

    public $uses = [
        'Order',
        'User',
        'UserSetting',
        'PricingTier',
        'Product',
        'Discount',
        'DiscountRule',
        'DiscountUsage',
        'ManufacturerRetailer',
        'Tag',
        'Collection',
        'CreditTerm',
    ];

    public $helpers = array('Discount');

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
            if ($this->request->is(['post', 'put', 'delete'])) {
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_DISCOUNTS, Permissions::LEVEL_EDIT);
            }
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function index()
    {
        $this->set('title_for_layout', 'Discounts');
        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_DISCOUNTS, Permissions::LEVEL_EDIT));
        $this->set('discountStatus', '');
        $this->set('discountSearch', '');
        $this->set('count_discounts', (int)$this->Discount->find('count', [
            'recursive' => -1,
            'conditions' => ['user_id' => $this->Auth->user('id')]
        ]));
        $this->set('rowsPerPage', 10);
    }

    /**
     * Ajax response for fetching the discounts table.
     */
    public function ajax_discounts_index()
    {
        if (!$this->request->is('ajax')) {
            return new CakeResponse(array('body' => 'Not an ajax request', 'status' => 200));
        }
        $this->layout = '';

        $conditions = array(
            'Discount.user_id' => $this->Auth->user('id')
        );

        $discountSearch = '';
        if (!empty($this->request->query['discountSearch'])) {
            $discountSearch = $this->request->query['discountSearch'];
            $conditions[] = $this->Discount->buildSearchCondition($discountSearch);
        }

        $sortOrder = 'Discount.created_at DESC';
        if (!empty($this->request->query['sortField'])) {
            $sortOrder = $this->request->query['sortField'];

            if (!empty($this->request->query['sortOrder'])) {
                $sortOrder .= " " . $this->request->query['sortOrder'];
            }
        }

        $discountStatus = (string)$this->request->query('discount_status');
        if ($discountStatus === 'Active') {
            $conditions['Discount.is_active'] = true;
        } elseif ($discountStatus === 'InActive') {
            $conditions['Discount.is_active'] = false;
        }

        $discountType = (string)$this->request->query('discount_type');
        if ($discountType) {
            $discountTypeConditions = [
                'DiscountRule.discount_id' => $this->Discount->primaryKeyIdentifier(),
                'DiscountRule.is_buy_x_get_y' => ($discountType === DiscountOptions::BUY_X_GET_Y),
            ];
            if ($discountType !== DiscountOptions::BUY_X_GET_Y) {
                $discountTypeConditions['DiscountRule.option'] = $discountType;
            }
            $conditions[] = $this->DiscountRule->buildExistsSubquery($discountTypeConditions);
        }

        $totalDiscountsCount = (int)$this->Discount->find('count', ['recursive' => -1, 'conditions' => $conditions]);

        $pageNum = 1;
        if (!empty($this->request->query['pageNo'])) {
            $pageNum = $this->request->query['pageNo'];
        }
        $rowsPerPage = 10;
        if (!empty($this->request->query['noRecords'])) {
            $rowsPerPage = $this->request->query['noRecords'];
        }
        $paging = paging($pageNum, $rowsPerPage, $totalDiscountsCount);

        $discounts = (array)$this->Discount->find('all', [
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => [
                'id',
                'uuid',
                'code',
                'description',
                'is_b2b_discount',
                'start_date',
                'end_date',
                'usage_limit_option',
                'usage_limit_quantity',
                'is_enabled',
                'is_automatic',
                'usage_count',
            ],
            'order' => $sortOrder,
            'limit' => $paging['rowsPerPage'],
            'offset' => $paging['offset']
        ]);

        // Output to view
        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_DISCOUNTS, Permissions::LEVEL_EDIT));
        $this->set('paging', $paging);
        $this->set('count_discounts', $totalDiscountsCount);
        $this->set('discounts', $discounts);
        $this->set('exportLink', $this->_buildExportLink($discountType, $discountStatus, $discountSearch));
        $this->set('currencycode', $this->Auth->user('currency_code'));
    }

    public function manage_discounts(?string $token = null)
    {
        $userId = (int)$this->Auth->user('id');
        if ($token && !$this->Discount->exists(['Discount.uuid' => $token, 'Discount.user_id' => $userId])) {
            throw new NotFoundException(__('Invalid discount code'));
        }

        $discount = ($token) ? $this->Discount->findManageDiscountsForm($token) : [];

        if ($this->request->is(['post', 'put'])) {
            if ($this->Discount->saveManageDiscountsForm($userId, $this->request->data)) {
                return $this->_successResponse(__('The discount code has been saved.'), ['action' => 'index']);
            }

            CakeLog::warning(new BadRequestException(json_encode(['errors' => $this->Discount->validationErrors, 'data' => $this->Discount->data])));
            $this->setFlash($this->_appendDebugOutput(__('The discount code could not be saved. Please, try again.')), 'error');
        } else {
            $this->request->data = $discount;
        }

        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_DISCOUNTS, Permissions::LEVEL_EDIT));

        $this->set('productCategories', $this->Product->getProductTypesByUser($userId));
        $this->set('productTitles', $this->Product->getProductTitlesByUser($userId));
        $this->set('productVariants', $this->Product->getProductVariantsByUser($userId));
        $this->set('productCollections', $this->Collection->getCollectionsByUser($userId));
        $this->set('productTags', $this->Tag->getTagsByUser($userId));

        $this->set('tiersList', $this->PricingTier->getSelectOptions($userId));
        $this->set('storesList', $this->User->listConnectedRetailerNames($userId, true));
        $this->set('creditTerms', $this->CreditTerm->getAllCreditTermOptions($userId));

        $this->set('discount', $discount);
    }

    public function delete(int $id): ?CakeResponse
    {
        $userId = (int)$this->Auth->user('id');
        if (!$this->Discount->exists(['Discount.id' => $id, 'Discount.user_id' => $userId])) {
            throw new NotFoundException(__('Invalid discount code'));
        }
        $this->request->allowMethod('post', 'delete');

        if (!$this->Discount->delete($id)) {
            return $this->_exceptionResponse(null, __('The discount code could not be deleted. Please, try again.'));
        }

        return $this->_successResponse(__('The discount code has been deleted.'), ['action' => 'index']);
    }

    public function setStatus($id, $option = null)
    {
        $this->autoRender = false;
        $this->request->allowMethod('post', 'put');

        if (!$this->Discount->exists(['Discount.id' => $id])) {
            return $this->_exceptionResponse(new NotFoundException(), null, true);
        }

        $is_enabled = ($option === 'Enable');

        $this->Discount->save(['id' => $id, 'is_enabled' => $is_enabled]);

        return $this->_successResponse('The discount code has been ' . ($is_enabled ? 'enabled' : 'disabled'));
    }

    /**
     * Export order details based on current filter
     */
    public function export()
    {
        $this->autoRender = false;

        $conditions = [
            'Discount.user_id' => $this->Auth->user('id'),
        ];

        $discountType = (string)$this->request->param('type');
        if ($discountType && $discountType !== 'all') {
            $discountTypeConditions = [
                'DiscountRule.discount_id' => $this->Discount->primaryKeyIdentifier(),
                'DiscountRule.is_buy_x_get_y' => ($discountType === DiscountOptions::BUY_X_GET_Y),
            ];
            if ($discountType !== DiscountOptions::BUY_X_GET_Y) {
                $discountTypeConditions['DiscountRule.option'] = $discountType;
            }
            $conditions[] = $this->DiscountRule->buildExistsSubquery($discountTypeConditions);
        }

        $discountStatus = (string)$this->request->param('status');
        if ($discountStatus === 'Active') {
            $conditions['Discount.is_active'] = true;
        } elseif ($discountStatus === 'InActive') {
            $conditions['Discount.is_active'] = false;
        }

        $discountSearch = (string)$this->request->param('search');
        if ($discountSearch) {
            $conditions[] = $this->Discount->buildSearchCondition($discountSearch);
        }

        if ($this->Discount->exists($conditions)) {
            $this->PhpExcel->createWorksheet();
            $this->PhpExcel->setDefaultFont('Calibri', 12);

            // define table cells
            $table = array(
                array('label' => __('Discount ID'), 'width' => 'auto', 'filter' => true),
                array('label' => __('Name'), 'width' => 'auto', 'filter' => true),
                array('label' => __('Code'), 'width' => 'auto', 'filter' => true),
                array('label' => __('Used'), 'width' => 'auto', 'filter' => true),
                array('label' => __('Start'), 'width' => 'auto', 'filter' => false),
                array('label' => __('End'), 'width' => 'auto', 'filter' => false),
                array('label' => __('Type'), 'width' => 'auto', 'filter' => false),
                array('label' => __('Status'), 'width' => 'auto', 'filter' => true)
            );

            // heading
            $this->PhpExcel->addTableHeader($table, array('name' => 'Cambria', 'bold' => true));

            $originalVirtualFields = $this->Discount->virtualFields;

            $isBuyXGetYSubquery = $this->DiscountRule->buildExistsSubquery([
                'DiscountRule.discount_id' => $this->Discount->primaryKeyIdentifier(),
                'DiscountRule.is_buy_x_get_y' => true,
            ]);
            $optionSubquery = $this->DiscountRule->buildSubquery([
                'conditions' => [
                    'DiscountRule.discount_id' => $this->Discount->primaryKeyIdentifier(),
                    'DiscountRule.is_buy_x_get_y' => false,
                ],
                'fields' => ['option'],
                'limit' => 1,
            ]);
            $this->Discount->virtualFields['option'] = "IF({$isBuyXGetYSubquery}, 'buy_x_get_y', {$optionSubquery})";

            $hasAvailableUsesSql = $this->Discount->conditions([
                'Discount.end_date >=' => $this->date('Y-m-d'),
                'OR' => [
                    'Discount.usage_limit_option' => DiscountUsageLimitOptions::UNLIMITED,
                    'Discount.usage_count < Discount.usage_limit_quantity',
                ],
            ]);
            $usageTextSql = "IF({$this->Discount->conditions(['Discount.usage_limit_option' => DiscountUsageLimitOptions::LIMITED])}, CONCAT(Discount.usage_count, '/', Discount.usage_limit_quantity), Discount.usage_count)";
            $this->Discount->virtualFields['used'] = "IF({$hasAvailableUsesSql}, {$usageTextSql}, 'Expired')";

            $this->Discount->streamPagedQuery([
                'recursive' => -1,
                'conditions' => $conditions,
                'fields' => [
                    'id',
                    'code',
                    'start_date',
                    'end_date',
                    'is_active',
                    'name',
                    'option',
                    'used',
                ],
            ], function(array $discount): void {
                $this->PhpExcel->addTableRow([
                    $discount['Discount']['id'],
                    $discount['Discount']['name'],
                    $discount['Discount']['code'],
                    $discount['Discount']['used'],
                    format_datetime($discount['Discount']['start_date'], DATE_FORMAT),
                    format_datetime($discount['Discount']['end_date'], DATE_FORMAT),
                    Inflector::humanize($discount['Discount']['option']),
                    $discount['Discount']['is_active'] ? 'Active' : 'InActive',
                ]);
            });

            $this->Discount->virtualFields = $originalVirtualFields;

            $this->PhpExcel->addTableFooter();
            $filename = sprintf('%s %s %s.xlsx',
                $this->Auth->user('company_name'),
                'ShipEarly Discounts',
                $this->date('Y-m-d')
            );
            $this->PhpExcel->render($filename);
        }else {
            $this->redirect($this->referer());
        }
    }

    /**
     * Generates a link for exporting the discounts table to a spreadsheet.
     * @param string $discountStatus
     * @param string $discountType
     * @param string $discountSearch
     * @return string $exportLink
     */
    public function _buildExportLink($discountType = '', $discountStatus = 'all', $discountSearch = '')
    {
        if (empty($discountType)) {
            $discountType = 'all';
        }
        if (empty($discountStatus)) {
            $discountStatus = 'all';
        }

        return Router::url(['controller' => 'discounts', 'action' => 'export', (string)$discountType, (string)$discountStatus, (string)$discountSearch]);
    }

    public function add_discount_rule()
    {
        $userId = (int)$this->Auth->user('id');

        $this->set('key', $this->request->query('key'));
        $this->set('productCategories', $this->Product->getProductTypesByUser($userId));
        $this->set('productTitles', $this->Product->getProductTitlesByUser($userId));
        $this->set('productVariants', $this->Product->getProductVariantsByUser($userId));
        $this->set('productCollections', $this->Collection->getCollectionsByUser($userId));
        $this->set('productTags', $this->Tag->getTagsByUser($userId));

        return $this->render('/Elements/Discounts/manage_discounts/discount_rule_card');
    }

    public function add_auto_add_y_row()
    {
        $userId = (int)$this->Auth->user('id');

        $this->set('ruleKey', $this->request->query('ruleKey'));
        $this->set('key', $this->request->query('key'));
        $this->set('productVariants', $this->Product->getProductVariantsByUser($userId));

        return $this->render('/Elements/Discounts/manage_discounts/discount_rule_card/auto_add_y_row');
    }
}
