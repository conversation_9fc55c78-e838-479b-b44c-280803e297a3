<?php
App::uses('App<PERSON><PERSON>roller', 'Controller');
/**
 * OrderCustomerMessages Controller
 *
 * @property OrderCustomerMessage $OrderCustomerMessage
 */
class OrderCustomerMessagesController extends AppController
{
    public function tooltip($orderId)
    {
        $orderCustomerMessage = current($this->OrderCustomerMessage->findAllForOrderTimeline($orderId));
        if (!$orderCustomerMessage) {
            throw new NotFoundException();
        }
        $this->set('orderCustomerMessage', $orderCustomerMessage);
    }
}
