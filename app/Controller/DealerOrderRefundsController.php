<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppController', 'Controller');
App::uses('OrderRefundException', 'Error');
App::uses('EcommercePlacementException', 'Error');
App::uses('UserFriendlyException', 'Error');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');

/**
 * DealerOrderRefunds Controller.
 *
 * @property FulfillmentLogicComponent $FulfillmentLogic
 * @property OrderLogicComponent $OrderLogic
 * @property StripeComponent $Stripe
 * @property ShopifyComponent $Shopify
 *
 * @property DealerOrderRefund $DealerOrderRefund
 * @property DealerOrder $DealerOrder
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property OrderRefund $OrderRefund
 * @property LegacyRetailerCredit $LegacyRetailerCredit
 * @property StripeUser $StripeUser
 */
class DealerOrderRefundsController extends AppController
{
    public $components = [
        'FulfillmentLogic',
        'OrderLogic',
        'Stripe.Stripe',
        'Shopify.Shopify',
    ];

    public $uses = [
        'DealerOrderRefund',
        'DealerOrder',
        'Order',
        'OrderProduct',
        'OrderRefund',
        'LegacyRetailerCredit',
        'StripeUser',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            // Configured in routes.php
            $orderId = $this->request->param('order_id');

            $this->OrderLogic->isAuthorizedForOrder($orderId);

            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function refund($orderId = null)
    {
        if ($this->request->is('post')) {
            try {
                $response = $this->perform_refund($orderId, $this->request->data);
            } catch (OrderRefundException $e) {
                $response = json_encode(['status' => 'error', 'message' => $e->getMessage()]);
                CakeLog::debug($response);
            } catch (Exception $e) {
                CakeLog::error($e);
                $response = json_encode(['status' => 'error', 'message' => 'There was an issue processing the refund']);
            }
            $this->response->body($response);
            return $this->response;
        }

        $orderInfo = $this->DealerOrder->findForRefundPopup($orderId);
        if (empty($orderInfo['Order']['id'])) {
            throw new NotFoundException(json_encode(['Order' => ['id' => $orderId]]));
        }
        $this->set('order', $orderInfo);

        $this->set('balanceBefore', $orderInfo['DealerOrder']['adjusted_total']);
        $this->set('balanceAfter', $orderInfo['DealerOrder']['adjusted_total']);
    }

    /**
     * @param int $orderId
     * @param array $request
     * @return string
     * @throws OrderRefundException
     */
    private function perform_refund($orderId, $request)
    {
        $request['DealerOrderRefundProduct'] = array_filter($request['DealerOrderRefundProduct'], function($refundProduct) {
            return $refundProduct['quantity'] != 0;
        });

        $existing = $this->DealerOrder->findForRefund($orderId);

        $save = $this->_build_save_data($request, $existing);
        $this->_validate_save_data($save, $existing);

        $save['DealerOrderRefund']['transaction_id'] = ($existing['Order']['order_type'] === Order::TYPE_WHOLESALE)
            ? $this->_handle_wholesale_refund($orderId, $existing, $save)
            : $this->_handle_consumer_refund($orderId, $existing, $save);

        if (!$this->DealerOrderRefund->saveAssociated($save)) {
            throw new InternalErrorException(json_encode(['errors' => $this->DealerOrderRefund->validationErrors, 'data' => $this->DealerOrderRefund->data]));
        }
        $id = $this->DealerOrderRefund->id;

        if ($existing['DealerOrder']['source_id'] && !empty($request['DealerOrderRefund']['place_ecommerce_refund'])) {
            try {
                $source_id = $this->_place_ecommerce_refund($existing, $save);
                if (!$this->DealerOrderRefund->save(compact('id', 'source_id'))) {
                    throw new InternalErrorException(json_encode(['errors' => $this->DealerOrderRefund->validationErrors, 'data' => $this->DealerOrderRefund->data]));
                }
            } catch (EcommercePlacementException $e) {
                CakeLog::debug($e->getMessage());
                $this->setFlash($e->getMessage(), 'error');
            } catch (Exception $e) {
                CakeLog::error($e);
                $this->setFlash('An error occurred while attempting to link the e-commerce refund to this order', 'error');
            }
        }

        try {
            $this->FulfillmentLogic->afterDealerRefund((int)$existing['DealerOrder']['id'], (int)$id);
        } catch (UserFriendlyException $e) {
            CakeLog::debug($e->getMessage());
            $this->setFlash($e->getMessage(), 'error');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash('An error occurred attempting to update the order\'s fulfillment status', 'error');
        }

        $this->setFlash($this->_appendDebugOutput('Refund has been successfully processed'), 'success');
        return json_encode(['status' => 'ok']);
    }

    private function _build_save_data(array $data, array $existing): array
    {
        $unitTaxes = array_column($existing['DealerOrderProduct'], 'unit_tax', 'id');
        $productTax = array_sum(array_map(
            function($refundProduct) use ($unitTaxes) {
                return $unitTaxes[$refundProduct['dealer_order_product_id']] * (int)$refundProduct['quantity'];
            },
            $data['DealerOrderRefundProduct']
        ));
        $shippingTax = (float)$data['DealerOrderRefund']['shipping_portion'] * $existing['DealerOrder']['shipping_tax_rate'];
        return [
            'DealerOrderRefund' => [
                'dealer_order_id' => $existing['DealerOrder']['id'],
                'transaction_id' => null,
                'source_id' => null,
                'amount' => $data['DealerOrderRefund']['amount'],
                'shipping_portion' => $data['DealerOrderRefund']['shipping_portion'],
                'tax_portion' => format_number($productTax + $shippingTax),
                'reason' => $data['DealerOrderRefund']['reason'] ?: null,
            ],
            'DealerOrderRefundProduct' => array_values(array_map(
                function($refundProduct) {
                    return [
                        'dealer_order_product_id' => $refundProduct['dealer_order_product_id'],
                        'quantity' => $refundProduct['quantity'],
                    ];
                }, $data['DealerOrderRefundProduct']
            )),
        ];
    }

    private function _validate_save_data(array $save, array $existing): bool
    {
        if (!$this->DealerOrderRefund->validateAssociated($save)) {
            throw new OrderRefundException(implode('<br />', array_unique(Hash::flatten($this->DealerOrderRefund->validationErrors))));
        }

        if (!$this->_hasSplitPaymentBalance($save['DealerOrderRefund']['amount'], $existing)) {
            $link = sprintf('<a href="mailto:%s" class="link">%s</a>', SUPPORT_EMAIL, SUPPORT_EMAIL);

            throw new OrderRefundException(__('Insufficient balance to perform refund, contact %s.', $link));
        }

        return true;
    }

    private function _hasSplitPaymentBalance($refundAmount, array $dealerOrder): bool
    {
        $refundEndDate = $this->_getReportPeriodEndDate();
        $shipmentEndDate = $this->_getReportPeriodEndDate($dealerOrder['DealerOrder']['shipment_date']);

        return !(
            $refundEndDate > $shipmentEndDate
            && $this->DealerOrder->isSplitPaymentReportOrder($dealerOrder)
            && $refundAmount > $this->DealerOrder->getSplitpaymentBalance($dealerOrder['Order']['user_id'], $dealerOrder['Order']['distributor_id'], $dealerOrder['Order']['currency_code'], $refundEndDate)
        );
    }

    private function _getReportPeriodEndDate(?string $date = null): string
    {
        $time = $date ? strtotime($date, $this->time()) : $this->time();

        return ($this->date('d', $time) <= '15') ? $this->date('Y-m-15', $time) : $this->date('Y-m-t', $time);
    }

    private function _handle_wholesale_refund($orderId, array $existing, array $save)
    {
        $orderUpdate = [];
        $transactionId = null;

        $refundQuantities = array_column($save['DealerOrderRefundProduct'], 'quantity', 'dealer_order_product_id');
        $nextQuantities = array_map(
            function($existingItem) use ($refundQuantities) {
                return $existingItem['adjusted_quantity'] - ($refundQuantities[$existingItem['id']] ?? 0);
            },
            $existing['DealerOrderProduct']
        );
        if (
            $save['DealerOrderRefund']['amount'] == $existing['DealerOrder']['adjusted_total']
            || !array_has_any($nextQuantities, fn($quantity) => $quantity > 0)
        ) {
            if ($existing['Order']['payment_status'] != OrderPaymentStatus::PAID) {
                $orderUpdate['order_status'] = OrderStatus::VOIDED;
                $orderUpdate['payment_status'] = OrderPaymentStatus::VOIDED;
            } else {
                $orderUpdate['order_status'] = OrderStatus::REFUNDED;
            }
        }

        if ($existing['Order']['payment_method'] === OrderPaymentMethod::STRIPE) {
            if (!$existing['Order']['transactionID']) {
                throw new InternalErrorException('Stripe charge not found for order ' . json_encode($existing['Order']['orderID']));
            }
            $charge_id = $existing['Order']['transactionID'];
            $stripe_account = $this->StripeUser->getOrderAccountId($orderId, $existing['Order']);

            $refundAmount = ($save['DealerOrderRefund']['amount'] < $existing['DealerOrder']['adjusted_total'])
                ? $save['DealerOrderRefund']['amount']
                : null; // Full refund

            if (($orderUpdate['payment_status'] ?? null) === OrderPaymentStatus::VOIDED) {
                $this->Stripe->cancelUnconfirmedPayment($stripe_account, $charge_id);
            } else {
                $transactionId = $this->Stripe->createRefund($stripe_account, $charge_id, $refundAmount)->id;
            }

            $orderUpdate['stripe_fees'] = ($existing['Order']['payment_status'] == OrderPaymentStatus::PAID)
                ? $this->Stripe->retrieveStripeFees($stripe_account, $charge_id)
                : $this->Stripe->estimateStripeFees($existing['DealerOrder']['adjusted_total'] - $save['DealerOrderRefund']['amount']);
        } elseif ($existing['Order']['payment_method'] === OrderPaymentMethod::CREDIT) {
            // Do nothing; Because credits are created at fulfillment, The refunded value may or may not be recorded so we ask users to do it manually.
        } elseif ($existing['Order']['payment_method'] === OrderPaymentMethod::EXTERNAL) {
            // Do nothing; payment is managed externally
        } else {
            throw new InternalErrorException('Unknown payment method for wholesale order ' . json_encode(array_intersect_key($existing['Order'], array_flip(['id', 'user_id', 'orderID', 'payment_method']))));
        }

        if ($orderUpdate) {
            if (!$this->Order->save(['id' => $orderId] + $orderUpdate)) {
                throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
            }
        }

        return $transactionId;
    }

    private function _handle_consumer_refund($orderId, array $existing, array $save)
    {
        if ($existing['Order']['payment_method'] === OrderPaymentMethod::STRIPE) {
            if ($existing['DealerOrder']['is_split_payment']) {
                if (!$existing['Order']['transactionID']) {
                    throw new InternalErrorException('Stripe charge not found for order ' . json_encode($existing['Order']['orderID']));
                }
                $charge_id = $existing['Order']['transactionID'];
                $stripe_account = $this->StripeUser->getOrderAccountId($orderId, $existing['Order']);
                $charge = $this->Stripe->retrieveCharge($stripe_account, $charge_id, ['expand' => ['balance_transaction']]);

                $feeRefund = $this->Stripe->createFeeRefund($charge->application_fee, [
                    'amount' => round($save['DealerOrderRefund']['amount'] * 100),
                ]);

                return $feeRefund->id;
            } else {
                return null;
            }
        } else {
            throw new InternalErrorException('Unknown payment method for consumer order ' . json_encode(array_intersect_key($existing['Order'], array_flip(['id', 'user_id', 'orderID', 'payment_method']))));
        }
    }

    /**
     * @param array $existing
     * @param array $save
     * @return string
     * @throws EcommercePlacementException
     * @throws ShopifyApiException
     */
    private function _place_ecommerce_refund(array $existing, array $save): string
    {
        $siteType = $existing['Order']['User']['site_type'];

        $responseId = null;
        switch ($siteType) {
            case UserSiteType::SHIPEARLY:
                // Do nothing
                return '';
            case UserSiteType::SHOPIFY:
                $response = $this->Shopify->createDealerOrderRefund($save, $existing);
                $responseId = $response['id'] ?? null;
                break;
            case UserSiteType::WOOCOMMERCE:
            case UserSiteType::MAGENTO:
                //TODO Support other platforms; then present checkbox in view
                throw new EcommercePlacementException("Unable to place the refund in {$siteType}: platform not supported");
            default:
                throw new BadRequestException(json_encode(['message' => 'Unknown site_type', 'User' => mask_secret_fields($existing['Order']['User'], ['api_key', 'secret_key'])]));
        }
        if (!$responseId) {
            throw new EcommercePlacementException("Failed to place the refund in {$siteType}");
        }

        return (string)$responseId;
    }
}
