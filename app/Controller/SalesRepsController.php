<?php
App::uses('AppController', 'Controller');

/**
 * SalesReps Controller
 *
 * @property User $User
 * @property UserSetting $UserSetting
 * @property Contact $Contact
 * @property Contactpersons $Contactpersons
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property ManufacturerRetailerSalesRep $ManufacturerRetailerSalesRep
 * @property ManufacturerSalesRep $ManufacturerSalesRep
 * @property UserLogicComponent $UserLogic
 */
class SalesRepsController extends AppController
{
    /**
     * @var array
     */
    public $components = ['UserLogic'];

    /**
     * @var array
     */
    public $uses = [
        'User',
        'UserSetting',
        'Contact',
        'Contactpersons',
        'ManufacturerRetailer',
        'ManufacturerRetailerSalesRep',
        'ManufacturerSalesRep',
    ];

    public function isAuthorized()
    {
        return (
            $this->Auth->user('user_type') === User::TYPE_MANUFACTURER &&
            parent::isAuthorized()
        );
    }

    public function index()
    {
        $this->set('salesReps', $this->ManufacturerSalesRep->findAllForSalesRepIndex($this->Auth->user('id')));
    }

    public function sales_rep_search_form()
    {
    }

    public function sales_rep_form()
    {
        $email = $this->request->query('email_address');

        $this->_bindSalesRepAssociations();
        $user = $this->User->find('first', [
            'contain' => [
                'Contactpersons', 
                'Contact',
                'ManufacturerSalesRep'
            ],
            'conditions' => [
                'User.email_address' => $email,
            ],
            'fields' => [
                'User.id',
                'User.user_type',
                'User.email_address',
                'User.company_name',
                'User.is_distributor',
                'User.descriptor',
                'User.first_name',
                'User.last_name',
                'User.telephone',
                'ManufacturerSalesRep.id',
            ],
        ]);

        $userExists = !empty($user['User']['id']);

        if ($userExists && $user['User']['user_type'] !== 'SalesRep') {
            $this->response->body('This email has already been taken');

            return $this->response;
        }

        if ($userExists && !empty($user['ManufacturerSalesRep']['id'])) {
            $this->response->body('This sales rep has already been added');

            return $this->response;
        }

        $user['User']['email_address'] = $email;
        $this->request->data = $user;
    }

    /*
     * Add a sales rep. User will be created if email
     *
     *
     * @param int $userId
     * @return bool
     */
    public function add()
    {
        $this->request->allowMethod('post');

        $userId = $this->Auth->user('id');
        $email = $this->request->data['User']['email_address'];

        $existingUser = $this->User->find('first',
        [
            'recursive' => -1,
            'conditions' => ['email_address' => $email],
            'fields' => ['id','user_type'],
        ]);

        // here we check for existing users. sales reps are valid, others user_types are not.
        if (!empty($existingUser)) {
            if ($existingUser['User']['user_type'] != User::TYPE_SALES_REP) {
                return $this->_exceptionResponse(new BadRequestException('Email: ' . $email . ' is already in use'), true);
            }
            $salesRepId = $existingUser['User']['id'];
        } else {
            $salesRepId = null;
        }

        // if we cannot find existing sales rep, create a new User account.
        if (!$salesRepId) {
            $salesRepId = $this->ManufacturerSalesRep->createSalesRep($userId, $this->request->data['User']);
            if (empty($salesRepId)) {
                $validationErrors = implode('<br />', Hash::extract($this->ManufacturerSalesRep->validationErrors, '{s}.{n}'));

                return $this->_exceptionResponse(
                    new BadRequestException(json_encode(['errors' => $this->ManufacturerSalesRep->validationErrors, 'data' => $this->ManufacturerSalesRep->data])),
                    $validationErrors ?: null,
                    true
                );
            }

            // send activation email for new account.
            $this->UserLogic->activationMail($salesRepId);

        // if sales rep already exists, link it to the manufacturer.
        } else {
            if (!$this->ManufacturerSalesRep->addSalesRep($userId, $salesRepId, $this->request->data['User'])) {
                $validationErrors = implode('<br />', Hash::extract($this->ManufacturerSalesRep->validationErrors, '{s}.{n}'));

                return $this->_exceptionResponse(
                    new BadRequestException(json_encode(['errors' => $this->ManufacturerSalesRep->validationErrors, 'data' => $this->ManufacturerSalesRep->data])),
                    $validationErrors ?: null,
                    true
                );
            }
        }

        return $this->_successResponse("'{$email}' has been added to your sales reps.");
    }

    public function edit($salesRepId = null)
    {
        $userId = $this->Auth->user('id');

        $existing = $this->ManufacturerSalesRep->findForSalesRepView($userId, $salesRepId);
        if (empty($existing['ManufacturerSalesRep']['id'])) {
            throw new NotFoundException(json_encode([
                'message' => 'Sales rep not found',
                'sales_rep_id' => $salesRepId,
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type'])),
            ]));
        }
        $this->request->allowMethod('post', 'put');

        $data = [];
        foreach (['SalesRep', 'ManufacturerSalesRep', 'Contactpersons', 'Contact'] as $assoc) {
            $data[$assoc] = ['id' => $existing[$assoc]['id']] + (array)$this->request->data("{$assoc}.$salesRepId");
        }
        if (!$this->ManufacturerSalesRep->editSalesRep($data)) {
            $validationErrors = implode('<br />', Hash::extract($this->ManufacturerSalesRep->validationErrors, '{s}.{n}'));

            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['errors' => $this->ManufacturerSalesRep->validationErrors, 'data' => $this->ManufacturerSalesRep->data])),
                $validationErrors ?: null,
                true
            );
        }

        $is_distributor = (
            $this->request->data[$salesRepId]['ManufacturerSalesRep']['is_distributor'] ??
            $existing['ManufacturerSalesRep']['is_distributor']
        );
        if ($is_distributor !== $existing['ManufacturerSalesRep']['is_distributor']) {
            $this->_clearSalesRepAssignments($userId, $salesRepId);
        }

        return $this->_successResponse();
    }

    public function remove($id = null)
    {
        $this->request->allowMethod('post', 'delete');

        $userId = $this->Auth->user('id');
        $email = $this->User->field('email_address', ['id' => $id]);

        $success = $this->ManufacturerSalesRep->deleteAll([
            'ManufacturerSalesRep.user_id' => $userId,
            'ManufacturerSalesRep.sales_rep_id' => $id,
        ], false);
        if (!$success) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(compact('userId', 'id', 'email'))),
                "'{$email}' could not be removed from your sales reps. Please, try again.",
                true
            );
        }

        $this->_clearSalesRepAssignments($userId, $id);

        return $this->_successResponse("'{$email}' has been removed from your sales reps.");
    }

    public function delete($id = null)
    {
        $this->request->allowMethod('post', 'delete');

        $salesRep = $this->User->find('first', [
            'recursive' - 1,
            'conditions' => ['id' => $id],
            'fields' => ['id', 'email_address', 'user_type'],
        ]);
        if (empty($salesRep['User']['id'])) {
            return $this->_exceptionResponse(
                new NotFoundException(json_encode(['sales_rep_id' => $id])),
                'Sales rep not found',
                true
            );
        }
        if ($salesRep['User']['user_type'] !== 'SalesRep') {
            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['SalesRep' => $salesRep['User']])),
                'Sales rep not found',
                true
            );
        }

        $email = $salesRep['User']['email_address'];

        if (!$this->User->delete($id)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(compact('id', 'email'))),
                "Unable to remove '{$email}' from sales reps. Please, try again.",
                true
            );
        }

        return $this->_successResponse("'{$email}' has been removed from sales reps.");
    }

    private function _bindSalesRepAssociations()
    {
        $this->User->virtualFields += [
            'is_distributor' => 'ManufacturerSalesRep.is_distributor',
            'descriptor' => 'ManufacturerSalesRep.descriptor',
            'first_name' => 'Contactpersons.firstname',
            'last_name' => 'Contactpersons.lastname',
            'telephone' => 'Contact.value',
        ];

        return $this->User->bindModel(['hasOne' => [
            'Contactpersons',
            'Contact' => [
                'conditions' => [
                    'Contact.type' => 'company',
                    'Contact.contact_medium' => 'telephone',
                ],
            ],
            'ManufacturerSalesRep' => [
                'foreignKey' => 'sales_rep_id',
                'conditions' => [
                    'ManufacturerSalesRep.user_id' => $this->Auth->user('id'),
                ],
            ],
        ]]);
    }

    private function _clearSalesRepAssignments($userId, $salesRepId): bool
    {
        return (
            $this->ManufacturerRetailer->updateAllJoinless(['ManufacturerRetailer.distributor_id' => null], [
                'ManufacturerRetailer.user_id' => $userId,
                'ManufacturerRetailer.distributor_id' => $salesRepId,
            ]) &&
            $this->ManufacturerRetailerSalesRep->deleteAllByUserIdAndSalesRepId((int)$userId, (int)$salesRepId)
        );
    }
}
