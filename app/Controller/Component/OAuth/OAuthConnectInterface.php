<?php

namespace ShipEarlyApp\Controller\Component\OAuth;

/**
 * OAuth flow methods for a 3rd party API integration.
 */
interface OAuthConnectInterface
{
    /**
     * Get the OAuth authorize URL for the API.
     *
     * @param string $stateToken
     * @return string The URL to redirect the user to
     */
    public function getAuthorizeUrl(string $stateToken): string;

    /**
     * Get OAuth access token data from the API.
     *
     * @param string $code The authorization code
     * @return array The access token data
     */
    public function getAccessToken(string $code): array;

    /**
     * Get an account ID from OAuth access token response data.
     *
     * @param array $accessTokenData The access token data
     * @return string The account ID
     */
    public function getAccessTokenAccountId(array $accessTokenData): string;

    /**
     * @param array $data The state data
     * @return string A new state token
     */
    public function createOAuthState(array $data): string;

    /**
     * @param string $token The state token
     * @return array The state data
     */
    public function retrieveOAuthState(string $token): array;
}
