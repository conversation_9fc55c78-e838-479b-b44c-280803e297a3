<?php

namespace ShipEarlyApp\Controller\Component\OAuth;

use Cache;
use Cake\Utility\Security;

/**
 * OAuth State Trait.
 *
 * Helper methods for associating data with OAuth state CSRF tokens.
 */
trait OAuthStateTrait
{
    /**
     * @param array $data The state data
     * @return string A new state token
     */
    public function createOAuthState(array $data): string
    {
        $token = $this->generateOAuthStateToken();

        $data += [
            'expiry_time' => $this->time() + (60 * 60),
        ];
        Cache::write($this->getOAuthStateCacheKey($token), $data, 'short');

        return $token;
    }

    /**
     * @param string $token The state token
     * @return array The state data
     */
    public function retrieveOAuthState(string $token): array
    {
        $data = (array)(Cache::read($this->getOAuthStateCacheKey($token), 'short') ?: []);

        if (!isset($data['expiry_time']) || $data['expiry_time'] < $this->time()) {
            return [];
        }

        return $data;
    }

    /**
     * Returns the cache key for storing OAuth state data.
     *
     * Can be overridden by the implementing class for more distinct cache keys.
     *
     * @param string $token The state token
     * @return string The cache key
     */
    protected function getOAuthStateCacheKey(string $token): string
    {
        return 'oauth_state_' . $token;
    }

    /**
     * Creates a secure random string to act as a state token.
     *
     * Can be mocked by tests for predictable results.
     *
     * @return string The new state token
     */
    protected function generateOAuthStateToken(): string
    {
        return Security::randomString();
    }
}
