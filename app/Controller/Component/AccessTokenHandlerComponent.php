<?php

/**
 * Access Token Handler Component.
 *
 * Wraps AccessToken model operations with Cookie token operations.
 *
 * @property CookieComponent $Cookie
 *
 * @property AccessToken $AccessToken
 */
class AccessTokenHandlerComponent extends AppComponent
{
    const DEFAULT_WAITING_EXPIRES = '60 seconds';
    const DEFAULT_ACCESS_EXPIRES = '15 minutes';

    public $components = [
        'Cookie',
    ];

    public $uses = [
        'AccessToken',
    ];

    /**
     * Name used for cookie key.
     *
     * @var string
     */
    public $cookieName = 'access_token';

    /**
     * Expire time in seconds or a DateTime string for tokens that are waiting.
     *
     * Must be long enough to not expire between polling requests.
     *
     * @var int|string
     */
    public $waitingExpires = self::DEFAULT_WAITING_EXPIRES;

    /**
     * Expire time in seconds or a DateTime string for tokens in checkout.
     *
     * Approximate time before a checkout is considered abandoned.
     *
     * @var int|string
     */
    public $accessExpires = self::DEFAULT_ACCESS_EXPIRES;

    /**
     * The number of tokens that can be granted access before waiting.
     *
     * Set null to grant unlimited access.
     *
     * @var int|null
     */
    public $accessLimit = 150;

    public function startup(Controller $controller)
    {
        parent::startup($controller);

        $this->waitingExpires = $this->_filterExpiry($this->waitingExpires, static::DEFAULT_WAITING_EXPIRES);
        $this->accessExpires = $this->_filterExpiry($this->accessExpires, static::DEFAULT_ACCESS_EXPIRES);
        $this->accessLimit = filter_var($this->accessLimit, FILTER_VALIDATE_INT, ['options' => ['default' => null, 'min_range' => 0]]);

        $this->AccessToken->accessLimit = $this->accessLimit;
    }

    /**
     * Sets an access token cookie and attempts to grant access.
     *
     * Existing tokens will have their duration refreshed.
     *
     * @return bool True if access was granted, false otherwise.
     * @throws Exception
     */
    public function requestAccess(): bool
    {
        if ($this->accessLimit === null) {
            // Bypass write operations if granting unlimited access
            return true;
        }

        $token = $this->AccessToken->requestToken($this->_readCookieToken($this->cookieName), $this->waitingExpires);

        $this->Cookie->write($this->cookieName, $token, false, $this->accessExpires);

        return (bool)$this->AccessToken->requestAccess($token, $this->accessExpires);
    }

    /**
     * Deletes the access token and cookie.
     *
     * @return bool Success
     */
    public function revokeAccess(): bool
    {
        $success = $this->AccessToken->revokeToken($this->_readCookieToken($this->cookieName));
        $this->Cookie->delete($this->cookieName);

        return $success;
    }

    /**
     * Get the number of tokens including the current one that are ahead in the waiting room.
     *
     * @return int
     */
    public function getPosition(): int
    {
        return $this->AccessToken->countWaiting($this->_readCookieToken($this->cookieName));
    }

    /**
     * Wrapper for CookieComponent::read for a type safe return value.
     *
     * @param string $key
     * @return string|null
     * @see CookieComponent::read
     */
    private function _readCookieToken(string $key): ?string
    {
        return ((string)$this->Cookie->read($key)) ?: null;
    }

    /**
     * @param int|string $expiry
     * @param string $default
     * @return string
     */
    private function _filterExpiry($expiry, string $default): string
    {
        $expiry = (string)$expiry;
        if (is_numeric($expiry)) {
            $expiry = sprintf('%d seconds', $expiry);
        }
        $isValid = (bool)strtotime($expiry);

        return ($isValid) ? $expiry : $default;
    }
}
