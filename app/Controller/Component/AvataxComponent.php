<?php

/**
 * Class AvataxComponent
 *
 * @property Btask $Btask
 * @property Country $Country
 * @property User $User
 */
class AvataxComponent extends AppComponent
{
    public $uses = [
        'Btask',
        'Country',
        'User',
    ];

    /**
     * @var string
     */
    public $serviceURL = '';
    /**
     * @var string
     */
    public $accountNumber = '';
    /**
     * @var string
     */
    public $licenseKey = '';

    /**
     * @param Controller $controller
     */
    function initialize(Controller $controller)
    {
        require_once(APP . "Vendor/AvaTax/AvaTaxClasses/AvaTax.php");
    }

    /**
     * Validate the shipping address is valid using Avatax api
     * @param $shipping
     * @return bool|string
     */
    public function validateAddress($shipping)
    {
        $this->avaTaxConfiguration();
        $addressSvc = new AddressServiceRest($this->serviceURL, $this->accountNumber, $this->licenseKey);
        $address = new Address();

        // Required Request Parameters
        $shippingLine = preg_replace('/\n/', ' ', $shipping->street);
        $address->setLine1($shippingLine);
        $address->setCity($shipping->city);
        $address->setRegion($shipping->region);
        $address->setPostalCode($shipping->postcode);
        $address->setCountry($shipping->country_id);

        // Optional Request Parameters
        /*if(isset($shippingLine[1])) {
            $address->setLine2($shippingLine[1]);
        }
        if(isset($shippingLine[2])) {
            $address->setLine3($shippingLine[2]);
        }*/

        $validateRequest = new ValidateRequest();
        $validateRequest->setAddress($address);
        $validateResult = $addressSvc->Validate($validateRequest);

        if ($validateResult->getResultCode() != SeverityLevel::$Success) {
            foreach ($validateResult->getMessages() as $message) {
                $error[] = $message->getSummary();
            }
        }

        if (isset($error)) {
            return json_encode($error);
        }
        return true;
    }

    /**
     * Basic avatax configurations
     */
    public function avaTaxConfiguration()
    {
        $this->serviceURL = AVALARA_SERVICEURL;
        $this->accountNumber = AVALARA_ACCOUNTNUMBER;
        $this->licenseKey = AVALARA_LICENSEKEY;
    }

    /**
     * Get only shipping tax based on given source and destination
     * @param $customerID
     * @param $currencyCode
     * @param $proInfo
     * @param $retailer
     * @param $customer
     * @param $shippingAmount
     * @return string
     */
    public function getShippingTax($customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount)
    {
        return $this->Tax("TaxCheck", FALSE, $customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount, true);
    }

    /**
     * Commmon tax calculation function to communicate avatax api
     * @param string $DocCode
     * @param bool $commit
     * @param string $CustomerCode
     * @param $CurrencyCode
     * @param $itemDetails
     * @param $retailerInfo
     * @param $customerInfo
     * @param $shippingAmount
     * @param $onlyShipping
     * @return string
     */
    public function Tax($DocCode = "TaxCheck", $commit = FALSE, $CustomerCode = '', $CurrencyCode, $itemDetails, $retailerInfo, $customerInfo, $shippingAmount, $onlyShipping)
    {
        $this->avaTaxConfiguration();
        $taxSvc = new TaxServiceRest($this->serviceURL, $this->accountNumber, $this->licenseKey);
        $getTaxRequest = new GetTaxRequest();

        // Document Level Elements
        // Required Request Parameters

        if (empty($CustomerCode)) {
            $CustomerCode = "Guest";
        }

        $getTaxRequest->setCustomerCode($CustomerCode);
        $getTaxRequest->setDocDate(getCurrentDate());

        // Best Practice Request Parameters
        $getTaxRequest->setCompanyCode($retailerInfo['company_code']);
        $getTaxRequest->setClient("Shipearly Cakephp");
        $getTaxRequest->setDocCode($DocCode);
        $getTaxRequest->setDetailLevel(DetailLevel::$Tax);
        $getTaxRequest->setCommit($commit);
        if ($commit) {
            $getTaxRequest->setDocType(DocumentType::$SalesInvoice);
        } else {
            $getTaxRequest->setDocType(DocumentType::$SalesOrder);
        }

        // Situational Request Parameters
        // $getTaxRequest->setCustomerUsageType("G");
        // $getTaxRequest->setExemptionNo("12345");
        // $getTaxRequest->setDiscount(50);
        // $taxOverride = new TaxOverride();
        // $taxOverride->setTaxOverrideType("TaxDate");
        // $taxOverride->setReason("Adjustment for return");
        // $taxOverride->setTaxDate("2013-07-01");
        // $taxOverride->setTaxAmount("0");
        // $getTaxRequest->setTaxOverride($taxOverride);

        // Optional Request Parameters
        //$getTaxRequest->setPurchaseOrderNo("PO123456");
        //$getTaxRequest->setReferenceCode("ref123456");
        //$//getTaxRequest->setPosLaneCode("09");
        $getTaxRequest->setCurrencyCode($CurrencyCode);

        // Address Data
        $addresses = $this->_getAddress($retailerInfo, $customerInfo);
        $getTaxRequest->setAddresses($addresses);

        // Line Data
        $lines = $this->_getLines($onlyShipping, $itemDetails, $shippingAmount);

        CakeLog::debug(json_encode($lines), 'avatax');

        $getTaxRequest->setLines($lines);

        #pr($getTaxRequest);
        $getTaxResult = @$taxSvc->getTax($getTaxRequest);
        #pr($getTaxResult);
        #exit;

        if ($getTaxResult->getResultCode() != SeverityLevel::$Success) {
            foreach ($getTaxResult->getMessages() as $message) {
                $error[] = $message->getSummary();
            }
        }
        if (isset($error)) {
            return json_encode(array('status' => 'error', 'message' => $error));
        }

        CakeLog::debug(json_encode($getTaxResult), 'avatax');

        $shippingTax = 0;
        foreach ($getTaxResult->getTaxLines() as $taxLine) {
            if ('02-FR' == (string)$taxLine->getLineNo()) {
                $shippingTax = $taxLine->getTax();
            }
        }

        $tax = $getTaxResult->getTotalTax();
        return json_encode(array('status' => 'success', 'tax' => $tax, 'shippingTax' => $shippingTax));

        //return json_encode(array('status' => 'success', 'tax' => $shippingTax));
    }

    /**
     * Sub function to format address inputs for avatax api
     * @param $origin
     * @param $destination
     * @return array
     */
    public function _getAddress($origin, $destination)
    {
        CakeLog::debug(json_encode(compact('origin', 'destination')), 'avatax');

        if (is_numeric($destination['country_id'])) {
            $destination['country_id'] = $this->Country->getCountryCode($destination['country_id']);
        }
        $destination['country_id'] = strtolower($destination['country_id']);

        $origin['address'] = str_replace('_,_', ' ', $origin['address']);

        $addresses = array();
        $addresses[] = $this->_setAddress("01", $origin['address'], $origin['city'], $origin['state_id'], $origin['country_id'], $origin['zipcode']);
        $addresses[] = $this->_setAddress("02", $destination['Line1'], $destination['city'], $destination['region'], $destination['country_id'], $destination['postcode']);
        return $addresses;
    }

    /**
     * Sub function to set address inputs for avatax api
     * @param $addressCode
     * @param $line1
     * @param $city
     * @param $region
     * @param $country
     * @param $postalCode
     * @return Address
     */
    public function _setAddress($addressCode, $line1, $city, $region, $country, $postalCode)
    {
        $address = new Address();
        $address->setAddressCode($addressCode);
        $address->setLine1($line1);
        $address->setCity($city);
        $address->setRegion($region);
        $address->setCountry($country);
        $address->setPostalCode($postalCode);
        return $address;
    }

    /**
     * Sub function to set products list on product lines
     * @param $onlyShipping
     * @param $itemDetails
     * @param $shippingAmount
     * @return array
     */
    public function _getLines($onlyShipping, $itemDetails, $shippingAmount)
    {
        $lines = array();
        $lines[] = $this->_setShippingLine($shippingAmount);
        if (!$onlyShipping) {
            $i = 1;
            foreach ($itemDetails as $key => $value) {
                $value = (array)$value;
                $lines[] = $this->_setProductLine($i, $key, $value['qty'], $value['unformatedprice'], $value['taxcode']);
                $i++;
            }
        }
        return $lines;
    }

    /**
     * Sub function to set Freight on product lines
     * @param $shippingAmount
     * @return Line
     */
    public function _setShippingLine($shippingAmount)
    {
        $line = new Line();
        $line->setLineNo("02-FR");
        $line->setItemCode("FREIGHT");
        $line->setQty(1);
        $line->setAmount($shippingAmount);
        $line->setOriginCode("01");
        $line->setDestinationCode("02");
        $line->setTaxCode("FR");
        //$line->setDescription("Shipping Charge");
        return $line;
    }

    /**
     * Sub function to set products list on product lines
     * @param $i
     * @param $id
     * @param $qty
     * @param $amount
     * @param string $taxcode
     * @return Line
     */
    public function _setProductLine($i, $id, $qty, $amount, $taxcode = '')
    {
        $line = new Line();
        $line->setLineNo($i);
        $line->setItemCode($id);
        $line->setQty($qty);
        $line->setAmount($amount);
        $line->setOriginCode("01");
        $line->setDestinationCode("02");
        if (!empty($taxcode)) {
            $line->setTaxCode($taxcode);
        }
        //$line->setDescription("Size 10 Green Running Shoe");
        return $line;
    }

    /**
     * Calculate total tax(shipping and product tax) using avatax api
     * @param $customerID
     * @param $currencyCode
     * @param $proInfo
     * @param $retailer
     * @param $customer
     * @param $shippingAmount
     * @return string
     */
    public function getTotalTax($customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount)
    {
        return $this->Tax("TaxCheck", FALSE, $customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount, false);
    }

    /**
     * Make a committed request to avatax api with only fright product code
     * @param $orderId
     * @param $customerID
     * @param $currencyCode
     * @param $proInfo
     * @param $retailer
     * @param $customer
     * @param $shippingAmount
     * @return string
     */
    public function setShippingTax($orderId, $customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount)
    {
        return $this->Tax($orderId, TRUE, $customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount, true);
    }

    /**
     * Make a committed request to avatax api with all product code
     * @param $orderId
     * @param $customerID
     * @param $currencyCode
     * @param $proInfo
     * @param $retailer
     * @param $customer
     * @param $shippingAmount
     * @param bool $queue
     * @return bool
     */
    public function setTotalTax($orderId, $customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount, $queue = true)
    {
        if ($queue) {
            return $this->Btask->queueTask(Btask::TYPE_AVALARA, func_get_args());
        }
        try {
            $status = $this->Tax($orderId, TRUE, $customerID, $currencyCode, $proInfo, $retailer, $customer, $shippingAmount, false);
            $error = json_decode($status);
            if ($error->status == 'error') {
                return false;
            }
            return true;
        } catch (Exception $e) {
            CakeLog::error(strval($e));
            return false;
        }
    }

    /**
     * @param $currencyCode
     * @param $retailer_id
     * @param $shipping
     * @param $orderID
     * @param $logs
     * @param $products
     * @return bool
     */
    public function setTotalTaxFromEcommerce($currencyCode, $retailer_id, $shipping, $orderID, $logs, $products): bool
    {
        try {
            $retailer = $this->User->getRetailerForAvatax((int)$retailer_id);

            $customerInfo = [
                'Line1' => $shipping['address'],
                'Line2' => $shipping['address2'],
                'city' => $shipping['city'],
                'region' => $shipping['regionName'],
                'postcode' => $shipping['PostalCode'],
                'country_id' => $shipping['countryName'],
            ];

            return $this->setTotalTax(
                $orderID,
                $logs['customerID'],
                $currencyCode,
                $products,
                $retailer,
                $customerInfo,
                $logs['shippingAmount']
            );
        } catch (Exception $e) {
            CakeLog::error($e);

            return false;
        }
    }
}
