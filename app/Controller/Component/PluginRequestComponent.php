<?php
App::uses('AppComponent', 'Controller/Component');

abstract class PluginRequestComponent extends AppComponent
{
    protected function _requestAction($url, array $data): array
    {
        // Temporarily clear AuthComponent to prevent WsController from calling syncUserSession
        $auth = $this->controller->Auth->user();
        $originalSessionKey = AuthComponent::$sessionKey;

        try {
            AuthComponent::$sessionKey = 'Auth.User';
            $this->controller->Auth->logout();

            return (array)json_decode($this->requestAction($url, compact('data')), true);
        } finally {
            AuthComponent::$sessionKey = $originalSessionKey;
            $this->controller->Auth->login($auth);
        }
    }
}
