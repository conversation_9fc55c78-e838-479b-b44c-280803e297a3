<?php

use ShipEarlyApp\Lib\Globals\AppGlobalMethodsTrait;

App::uses('Component', 'Controller');
App::uses('AppController', 'Controller');
App::uses('CurlException', 'Error');

/**
 * Class AppComponent.
 *
 * Allows creating components that manage their own models and components
 * making it easier to define reusable controller methods.
 */
abstract class AppComponent extends Component
{
    use AppGlobalMethodsTrait;

    /**
     * An array containing the class names of models loaded by this component.
     *
     * @var array
     */
    public $uses = array();

    /**
     * The controller that this component belongs to.
     *
     * @var AppController
     */
    protected $controller = null;

    public function __construct(ComponentCollection $collection, $settings = array())
    {
        $parent = get_parent_class($this);
        if (!empty($this->components)) {
            $this->_mergeVars(['components'], $parent, true);
        }
        if (!empty($this->uses)) {
            $this->_mergeVars(['uses'], $parent, false);
        }

        parent::__construct($collection, $settings);

        $this->controller = $collection->getController();

        // Load components now instead of lazily so that events will propagate
        foreach ($this->_componentMap as $name => $properties) {
            $this->{$name} = $collection->load($properties['class'], $properties['settings']);
        }
    }

    public function __isset($name)
    {
        if (is_array($this->uses)) {
            foreach ($this->uses as $modelClass) {
                list($plugin, $class) = pluginSplit($modelClass, true);
                if ($name === $class) {
                    return $this->loadModel($modelClass);
                }
            }
        }
        return false;
    }

    /**
     * Loads and instantiates models required by this component.
     *
     * @param string $modelClass Name of model class to load
     * @param int|string $id Initial ID the instanced model class should have
     * @return bool True if the model was found
     * @throws MissingModelException if the model class cannot be found.
     * @see Controller::loadModel
     */
    protected function loadModel($modelClass = null, $id = null)
    {
        $this->uses = ($this->uses) ? (array)$this->uses : array();
        if (!in_array($modelClass, $this->uses, true)) {
            $this->uses[] = $modelClass;
        }

        list($plugin, $modelClass) = pluginSplit($modelClass, true);

        $this->{$modelClass} = ClassRegistry::init(array(
            'class' => $plugin . $modelClass, 'alias' => $modelClass, 'id' => $id
        ));
        if (!$this->{$modelClass}) {
            throw new MissingModelException($modelClass);
        }
        return true;
    }

    /**
     * @param resource $curl
     * @return bool|string
     * @throws CurlException
     */
    protected function _curlExec($curl)
    {
        $response = curl_exec($curl);
        $errno = curl_errno($curl);
        $error = curl_error($curl);

        curl_close($curl);

        if ($errno) {
            throw new CurlException($error, $errno);
        }

        return $response;
    }
}
