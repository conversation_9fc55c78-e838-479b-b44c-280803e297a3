<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('Validation', 'Utility');

/**
 * IndexQueryHandler Component.
 */
class IndexQueryHandlerComponent extends AppComponent
{
    const DEFAULT_QUERY = [
        'sort' => '',
        'direction' => '',
        'limit' => 10,
        'page' => 1,
        'search' => '',
    ];

    public $defaultQuery = [];

    public function getDefaultQuery(): array
    {
        $query = $this->_processQuery($this->defaultQuery);
        $defaults = static::DEFAULT_QUERY;

        return array_merge($defaults, $query);
    }

    public function extractAllParams(array $query): array
    {
        $query = $this->_processQuery($query);
        $defaults = $this->getDefaultQuery();

        return array_merge($defaults, $query);
    }

    public function extractModifiedParams(array $query): array
    {
        $query = $this->_processQuery($query);
        $defaults = $this->getDefaultQuery();

        $query = array_udiff_assoc($query, $defaults, fn($a, $b) => $a <=> $b);

        // Keep sort and direction if either of them is modified
        if (!empty($query['sort']) || !empty($query['direction'])) {
            $query += array_intersect_key($defaults, array_flip(['sort', 'direction']));
        }

        // Merge to preserve default key order
        return array_merge(array_intersect_key($defaults, $query), $query);
    }

    private function _processQuery(array $query): array
    {
        $query = $this->_filterValidValues($query);

        $query = $this->_processSortOrder($query);
        $query = $this->_processPagination($query);

        return $query;
    }

    private function _filterValidValues(array $query): array
    {
        return array_filter($query, function($value) {
            return (!$value || is_array($value) || Validation::notBlank($value));
        });
    }

    private function _processSortOrder(array $query): array
    {
        if (!empty($query['sort'])) {
            $direction = strtoupper($query['direction'] ?? 'ASC');
            if (!in_array($direction, ['ASC', 'DESC'], true)) {
                $direction = 'ASC';
            }
            $query['direction'] = $direction;
        } else {
            $query = array_diff_key($query, array_flip(['sort', 'direction']));
        }

        return $query;
    }

    private function _processPagination(array $query): array
    {
        foreach (['limit', 'page'] as $field) {
            if (isset($query[$field])) {
                $query[$field] = (int)max((int)$query[$field], 1);
            }
        }

        return $query;
    }
}
