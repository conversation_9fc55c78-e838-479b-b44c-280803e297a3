<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('AppComponent', 'Controller/Component');
App::uses('AuthComponent', 'Controller/Component');
App::uses('User', 'Model');
App::uses('CakeRequest', 'Network');

/**
 * Translate Component.
 *
 * Extract TranslateBehavior arguments from requests.
 */
class TranslateComponent extends AppComponent
{
    public function getPreferredLanguage(): string
    {
        return (
            $this->getAuthUserLocale()
            ?: $this->getBrowserLanguage()
        );
    }

    public function getBrowserLanguage(): string
    {
        return (
            $this->getBrowserLocale(SupportedLanguages::getLocalesSet())
            ?: SupportedLanguages::DEFAULT_LOCALE
        );
    }

    protected function getAuthUserLocale(): ?string
    {
        $lang = (string)(User::revertAuthParent(AuthComponent::user())['language_code'] ?? '');
        if ($lang) {
            return SupportedLanguages::toLocale($lang);
        }

        return null;
    }

    protected function getBrowserLocale(?array $whitelist = null): ?string
    {
        foreach (CakeRequest::acceptLanguage() as $lang) {
            $locale = SupportedLanguages::toLocale((string)$lang);
            if ($whitelist === null || in_array($locale, $whitelist, true)) {
                return $locale;
            }
        }

        return null;
    }

    public function getHtmlLang(string $language): string
    {
        return (
            SupportedLanguages::toShortLang($language)
            ?: SupportedLanguages::DEFAULT_LANG
        );
    }
}
