<?php
App::uses('AppComponent', 'Controller/Component');

/**
 * Class BranchLogicComponent.
 *
 * @property User $User
 */
class BranchLogicComponent extends AppComponent
{
    public $uses = [
        'User',
    ];

    /**
     * Branch list logic for locations tab
     * @param $retailerId
     * @param $noRecords
     * @param $page
     * @param string $count
     * @internal param $orderStatus
     * @internal param $fullfilltype
     */
    public function BranchListLogic($retailerId, $noRecords, $page, $count = '')
    {
        if (empty($count)) {
            $countBranches = $this->User->getBranchListCount($retailerId);
            $this->controller->set('countBranches', $countBranches);
        } else {
            $countBranches = $count;
        }

        $paging = paging($page, $noRecords, $countBranches);
        $this->controller->set('paging', $paging);

        $branchs = $this->User->getBranchList($retailerId, $paging['offset'], $noRecords);
        $this->controller->set('branchs', $branchs);
    }
}
