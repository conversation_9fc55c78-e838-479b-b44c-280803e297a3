<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('Permissions', 'Utility');
App::uses('User', 'Model');

/**
 * Permissions Component.
 * 
 * @property User $User
 */
class PermissionsComponent extends AppComponent
{
    public $uses = [
        'User',
    ];
    /**
     * Block access to users of the wrong type by throwing a ForbiddenException.
     *
     * @param array $authUser User fields from User::findAuthUser.
     * @param string|string[] $validUserType A user_type from the set of User::TYPE_* constants.
     * @param bool $allowBranch Whether or not retail branch locations are also permitted access.
     * @throws ForbiddenException If the user is not one of the expected types for the request.
     * @see User::findAuthUser
     */
    public function assertUserIsType(array $authUser, $validUserType, bool $allowBranch = true)
    {
        $isAuthorized = in_array($authUser['user_type'], (array)$validUserType, true);
        if ($allowBranch === false) {
            $isAuthorized = $isAuthorized && !$authUser['Branch'];
        }
        if (!$isAuthorized) {
            throw new ForbiddenException(json_encode(['Auth' => User::extractAuthUserLogFields($authUser)]));
        }
    }

    /**
     * Block access to users with the wrong ecommerce site type by throwing a ForbiddenException.
     *
     * @param array $authUser User fields from User::findAuthUser.
     * @param string[] $validSiteTypes Ecommerce site types from the set of UserSiteType::* constants.
     * @throws ForbiddenException If the user's ecommerce site type is not allowed for the request.
     * @see User::findAuthUser
     */
    public function assertUserHasSiteType(array $authUser, array $validSiteTypes)
    {
        if (!in_array($authUser['site_type'], $validSiteTypes, true)) {
            throw new ForbiddenException(json_encode(['Auth' => User::extractAuthUserLogFields($authUser, ['site_type'])]));
        }
    }

    /**
     * Block access to users without the required permission level by throwing a ForbiddenException.
     *
     * @param array $authUser User fields from User::findAuthUser.
     * @param string $name Permission name required for access from the set of Permissions::NAME_* constants.
     * @param int $level Permission level required for access from the set of Permissions::LEVEL_* constants.
     * @throws ForbiddenException If the user does not have permission for the request.
     * @see User::findAuthUser
     */
    public function assertUserHasPermission(array $authUser, string $name, int $level = Permissions::LEVEL_VIEW)
    {
        if (!$this->userHasPermission($authUser, $name, $level)) {
            throw new ForbiddenException(json_encode($this->getUserPermissionLogAttributes($authUser, $name, $level)));
        }
    }

    /**
     * @param array $authUser User fields from User::findAuthUser.
     * @param string $name Permission name from the set of Permissions::NAME_* constants.
     * @param int $minimumLevel Permission level from the set of Permissions::LEVEL_* constants.
     * @return bool True if $authUser has the required permission level, false otherwise.
     * @see User::findAuthUser
     */
    public function userHasPermission(array $authUser, string $name, int $minimumLevel = Permissions::LEVEL_VIEW): bool
    {
        return Permissions::userHasPermission($authUser, $name, $minimumLevel);
    }

    /**
     * @param array $authUser User fields from User::findAuthUser.
     * @param string $name Permission name from the set of Permissions::NAME_* constants.
     * @param int $requiredLevel Permission level from the set of Permissions::LEVEL_* constants.
     * @return array Log attributes.
     * @see User::findAuthUser
     */
    public function getUserPermissionLogAttributes(array $authUser, string $name, int $requiredLevel = Permissions::LEVEL_VIEW): array
    {
        return [
            'Permission' => [
                'name' => $name,
                'level' => Permissions::extractUserPermissionLevel($authUser, $name),
                'required_level' => $requiredLevel,
            ],
            'Auth' => User::extractAuthUserLogFields($authUser),
        ];
    }

    public function userHasB2bCartPermission(array $authUser)
    {
        return $this->User->hasB2bCartPermission($authUser['id']) 
            && $this->userHasPermission($authUser, Permissions::NAME_B2B_ORDERS);
    }
}
