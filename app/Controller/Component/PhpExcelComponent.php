<?php
App::uses('Component', 'Controller');

/**
 * Component for working with PHPExcel class.
 *
 * PHPExcel upgraded to PhpSpreadsheet.
 *
 * @package PhpExcel
 * <AUTHOR>
 * <AUTHOR>
 *
 * @property SpoutComponent $Spout
 */
class PhpExcelComponent extends Component
{
    public $components = [
        'Spout',
    ];

    /**
     * Instance of PHPExcel class
     *
     * @var \PhpOffice\PhpSpreadsheet\Spreadsheet
     */
    protected $_xls;

    /**
     * Pointer to current row
     *
     * @var int
     */
    protected $_row = 1;

    /**
     * Internal table params
     *
     * @var array
     */
    protected $_tableParams;

    /**
     * Number of rows
     *
     * @var int
     */
    protected $_maxRow = 0;

    /**
     * Number of cols
     *
     * @var int
     */
    protected $_maxCol = 0;

    /**
     * @var int
     */
    protected $_maxCellIndex = 0;

    /**
     * Create new worksheet or load it from existing file
     *
     * @return $this for method chaining
     */
    public function createWorksheet()
    {
        $this->_xls = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $this->_row = 1;

        return $this;
    }

    /**
     * Read a spreadsheet file and return its contents as a table map.
     *
     * @param string $file Path of the file to open
     * @param string|null $fileName Actual file name with ext for determining file type
     * @return array Cell values mapped by headers for each row
     * @throws \Box\Spout\Common\Exception\IOException
     * @throws \Box\Spout\Reader\Exception\ReaderNotOpenedException
     * @deprecated Use SpoutComponent::extractTableData instead
     */
    public function extractTableData(string $file, string $fileName = null): array
    {
        return $this->Spout->extractTableData($file, $fileName);
    }

    /**
     * Create new worksheet from existing file
     *
     * @param string $file path to excel file to load
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function loadWorksheet($file)
    {
        $this->_xls = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
        $this->setActiveSheet(0);
        $this->_row = 1;

        return $this;
    }

    /**
     * Add sheet
     *
     * @param string $name
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function addSheet($name)
    {
        $index = $this->_xls->getSheetCount();
        $this->_xls->createSheet($index)
            ->setTitle($name);

        $this->setActiveSheet($index);

        return $this;
    }

    /**
     * Set active sheet
     *
     * @param int $sheet
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function setActiveSheet($sheet)
    {
        $this->_maxRow = $this->_xls->setActiveSheetIndex($sheet)->getHighestRow();
        $this->_maxCol = $this->_xls->setActiveSheetIndex($sheet)->getHighestColumn();
        $this->_maxCellIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($this->_maxCol);
        $this->_row = 1;

        return $this;
    }

    /**
     * Set worksheet name
     *
     * @param string $name name
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function setSheetName($name)
    {
        $this->_xls->getActiveSheet()->setTitle($name);

        return $this;
    }

    /**
     * Overloaded __call
     * Move call to PHPExcel instance
     *
     * @param string function name
     * @param array arguments
     * @return mixed the return value of the call
     */
    public function __call($name, $arguments)
    {
        return call_user_func_array(array($this->_xls, $name), $arguments);
    }

    /**
     * Set default font
     *
     * @param string $name font name
     * @param int $size font size
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function setDefaultFont($name, $size)
    {
        $this->_xls->getDefaultStyle()->getFont()->setName($name);
        $this->_xls->getDefaultStyle()->getFont()->setSize($size);

        return $this;
    }

    /**
     * Set row pointer
     *
     * @param int $row number of row
     * @return $this for method chaining
     */
    public function setRow($row)
    {
        $this->_row = (int)$row;

        return $this;
    }

    public function newExportColumn(string $label, callable $valueCallback, array $params = []): array
    {
        return compact('label', 'valueCallback') + $params;
    }

    public function processExportColumns(array $columns, ...$rowArgs): array
    {
        return array_map(function($column) use ($rowArgs) {
            return call_user_func($column['valueCallback'], ...$rowArgs);
        }, $columns);
    }

    /**
     * Start table - insert table header and set table params
     *
     * @param array $data data with format:
     *   label   -   table heading
     *   width   -   numeric (leave empty for "auto" width)
     *   filter  -   true to set excel filter for column
     *   wrap    -   true to wrap text in column
     * @param array $params table parameters with format:
     *   offset  -   column offset (numeric or text)
     *   font    -   font name of the header text
     *   size    -   font size of the header text
     *   bold    -   true for bold header text
     *   italic  -   true for italic header text
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function addTableHeader($data, $params = array())
    {
        // offset
        $offset = 1;
        if (isset($params['offset']))
            $offset = is_numeric($params['offset']) ? (int)$params['offset'] : \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($params['offset']);

        // font name
        if (isset($params['font']))
            $this->_xls->getActiveSheet()->getStyle($this->_row)->getFont()->setName($params['font']);

        // font size
        if (isset($params['size']))
            $this->_xls->getActiveSheet()->getStyle($this->_row)->getFont()->setSize($params['size']);

        // bold
        if (isset($params['bold']))
            $this->_xls->getActiveSheet()->getStyle($this->_row)->getFont()->setBold($params['bold']);

        // italic
        if (isset($params['italic']))
            $this->_xls->getActiveSheet()->getStyle($this->_row)->getFont()->setItalic($params['italic']);

        // set internal params that need to be processed after data are inserted
        $this->_tableParams = array(
            'header_row' => $this->_row,
            'offset' => $offset,
            'row_count' => 0,
            'auto_width' => array(),
            'filter' => array(),
            'wrap' => array()
        );

        foreach ($data as $d) {
            // set label
            $this->_xls->getActiveSheet()->setCellValueByColumnAndRow($offset, $this->_row, $d['label']);

            // set width
            if (isset($d['width']) && is_numeric($d['width']))
                $this->_xls->getActiveSheet()->getColumnDimensionByColumn($offset)->setWidth((float)$d['width']);
            else
                $this->_tableParams['auto_width'][] = $offset;

            // filter
            if (isset($d['filter']) && $d['filter'])
                $this->_tableParams['filter'][] = $offset;

            // wrap
            if (isset($d['wrap']) && $d['wrap'])
                $this->_tableParams['wrap'][] = $offset;

            $offset++;
        }
        $this->_row++;

        return $this;
    }

    /**
     * Write array of data to current row
     *
     * @param array $data
     * @param array $data_types
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function addTableRow(array $data, $data_types = array())
    {
        $offset = $this->_tableParams['offset'];
        foreach ($data as $k => $d) {
            $cell_dataType = \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING;
            if (!empty($data_types[$k])) {
                $cell_dataType = $data_types[$k];

                if ($cell_dataType == 'string') {
                    $cell_dataType = \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING;
                } elseif ($cell_dataType == 'number') {
                    $cell_dataType = \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_NUMERIC;
                }
            }
            $this->_xls->getActiveSheet()->setCellValueExplicitByColumnAndRow($offset++, $this->_row, $d, $cell_dataType);
        }
        $this->_row++;
        $this->_tableParams['row_count']++;

        return $this;
    }

    /**
     * End table - set params and styles that required data to be inserted first
     *
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function addTableFooter()
    {
        // auto width
        foreach ($this->_tableParams['auto_width'] as $col)
            $this->_xls->getActiveSheet()->getColumnDimensionByColumn($col)->setAutoSize(true);

        // filter (has to be set for whole range)
        if (count($this->_tableParams['filter']))
            $this->_xls->getActiveSheet()->setAutoFilter(\PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($this->_tableParams['filter'][0]) . ($this->_tableParams['header_row']) . ':' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($this->_tableParams['filter'][count($this->_tableParams['filter']) - 1]) . ($this->_tableParams['header_row'] + $this->_tableParams['row_count']));

        // wrap
        foreach ($this->_tableParams['wrap'] as $col)
            $this->_xls->getActiveSheet()->getStyle(\PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col) . ($this->_tableParams['header_row'] + 1) . ':' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col) . ($this->_tableParams['header_row'] + $this->_tableParams['row_count']))->getAlignment()->setWrapText(true);

        return $this;
    }

    /**
     * Write array of data to current row starting from column defined by offset
     *
     * @param array $data
     * @param int $offset
     * @return $this for method chaining
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function addData($data, $offset = 1)
    {
        // solve textual representation
        if (!is_numeric($offset))
            $offset = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($offset);

        foreach ($data as $d)
            $this->_xls->getActiveSheet()->setCellValueByColumnAndRow($offset++, $this->_row, $d);

        $this->_row++;

        return $this;
    }

    /**
     * Get array of data from current row
     *
     * @param int $max
     * @return array row contents
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function getTableData($max = 100)
    {
        if ($this->_row > $this->_maxRow)
            return false;

        $data = array();

        for ($col = 0; $col < $max; $col++)
            $data[] = $this->_xls->getActiveSheet()->getCellByColumnAndRow($col, $this->_row)->getValue();

        $this->_row++;

        return $data;
    }

    /**
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function getSheetData()
    {
        $data = [];
        for ($row = 0; $row < $this->_maxRow; ++$row) {
            $data[$row] = [];
            for ($col = 0; $col < $this->_maxCellIndex; ++$col) {
                $data[$row][$col] = $this->_xls->getActiveSheet()->getCellByColumnAndRow($col + 1, $row + 1)->getValue();
            }
        }
        return $data;
    }

    /**
     * Get writer
     *
     * @param $writer
     * @return \PhpOffice\PhpSpreadsheet\Writer\IWriter
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function getWriter($writer = 'Xlsx')
    {
        return \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($this->_xls, $writer);
    }

    /**
     * Save to a file
     *
     * @param string $file path to file
     * @param string $writer
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function save($file, $writer = 'Xlsx')
    {
        $objWriter = $this->getWriter($writer);
        return $objWriter->save($file);
    }

    /**
     * Output file to browser
     *
     * @param string $filename
     * @param string $writer
     * @return void exit on this call
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($filename = 'export.xlsx', $writer = 'Xlsx')
    {
        // remove all output
        ob_end_clean();

        // headers
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // writer
        $objWriter = $this->getWriter($writer);
        $this->_saveViaTempFile($objWriter);

        exit;
    }

    /**
     * @param \PhpOffice\PhpSpreadsheet\Writer\IWriter $objWriter
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    protected function _saveViaTempFile($objWriter)
    {
        $dir = TMP . 'phpexcel' . DS;
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }

        $filePath = $dir . rand(0, getrandmax()) . rand(0, getrandmax()) . '.tmp';

        $objWriter->save($filePath);
        readfile($filePath);
        unlink($filePath);
        exit;
    }

}
