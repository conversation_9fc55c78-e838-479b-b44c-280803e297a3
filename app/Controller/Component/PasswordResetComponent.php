<?php

use ShipEarlyApp\Controller\Component\OAuth\OAuthStateTrait;
use ShipEarlyApp\Lib\Globals\AppGlobalMethodsTrait;

App::uses('Component', 'Controller');

class PasswordResetComponent extends Component
{
    use AppGlobalMethodsTrait;
    // Reusing OAuth token functions because password reset tokens are functionally identical.
    use OAuthStateTrait {
        createOAuthState as public createTokenState;
        retrieveOAuthState as public retrieveTokenState;
    }

    protected function getOAuthStateCacheKey(string $token): string
    {
        return 'password_reset_' . $token;
    }
}
