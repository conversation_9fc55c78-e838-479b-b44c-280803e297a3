<?php

/**
 * Class UspsComponent.
 *
 * @property AppController|WsController $controller
 */
class UspsComponent extends Component
{
    /**
     * @var string
     */
    public $uspsURL = 'http://production.shippingapis.com/ShippingAPITest.dll';

    /**
     * @var string
     */
    public $apiKey;

    public $cacheConfig = 'medium';

    public function startup(Controller $controller)
    {
        $this->controller = $controller;
        $this->apiKey = USPS_API_USERNAME;
    }

    /**
     * @param string $country_code
     * @return bool
     */
    public function validationIsAllowed($country_code)
    {
        return (strtoupper($country_code) === 'US') && !empty($this->apiKey);
    }

    /**
     * @param string $address1
     * @param string $city
     * @param string $country_code
     * @param string $state_code
     * @param string $zip_code
     * @return array
     */
    public function validateAddress($address1, $city, $country_code, $state_code, $zip_code)
    {
        $request = $this->_buildAddressRequest($address1, '', $city, $state_code, $zip_code);

        $response = $this->validationIsAllowed($country_code)
            ? $this->_validate($request)
            : [];

        return $this->_processAddressResponse($response, $request);
    }

    /**
     * @param array $request
     * @return array
     */
    protected function _validate(array $request): array
    {
        try {
            CakeLog::debug(json_encode(compact('request')));

            $from_cache = true;
            $response = Cache::remember(
                $this->cacheKey($request['AddressValidateRequest']['Address']),
                function() use ($request, &$from_cache) {
                    $from_cache = false;

                    $requestXml = Xml::fromArray($request)->asXML();
                    $requestXml = str_replace(['<?xml version="1.0" encoding="UTF-8"?>', "\n"], '', $requestXml);

                    $requestUrl = $this->uspsURL . '?' . http_build_query(['API' => 'Verify', 'XML' => $requestXml]);
                    CakeLog::debug(json_encode(compact('requestUrl')));

                    $responseXml = $this->_curl($requestUrl);

                    return Xml::toArray(Xml::build($responseXml));
                },
                $this->cacheConfig
            );

            CakeLog::debug(json_encode(compact('from_cache', 'response')));

            return (array)$response;
        } catch (Exception $e) {
            CakeLog::error($e);

            return [];
        }
    }

    /**
     * @param string $url
     * @return string|mixed
     * @throws Exception
     */
    protected function _curl($url)
    {
        $ch = curl_init($url);

        curl_setopt_array($ch, [
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_HTTPGET => 1,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_SSL_VERIFYPEER => false,
        ]);

        $output = curl_exec($ch);
        $errno = curl_errno($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($errno) {
            throw new Exception("cURL Error ({$errno}): {$error}");
        }

        return $output;
    }

    /**
     * @param array $response
     * @param array $request
     * @return array
     */
    protected function _processAddressResponse(array $response, array $request): array
    {
        if (empty($response['AddressValidateResponse']['Address'])) {
            return [
                'status' => 'ERROR',
                'message' => 'Address could not be validated',
                'fieldErrors' => json_decode('{}'),
            ];
        }
        $responseAddress = $response['AddressValidateResponse']['Address'];

        if (isset($responseAddress['Error'])) {
            return [
                'status' => 'INVALID',
                'message' => $responseAddress['Error']['Description'],
                'fieldErrors' => json_decode('{}'),
            ];
        }

        $requestAddress = $request['AddressValidateRequest']['Address'];
        $diffAddressFields = $this->_diffAddressFields($responseAddress, $requestAddress);
        if ($diffAddressFields) {
            //TODO more specific response using field(s) that do not match
            return [
                'status' => 'INVALID',
                'message' => isset($responseAddress['ReturnText'])
                    ? 'Incorrect Address Please Validate'
                    : 'Zip Code does not match address',
                'fieldErrors' => json_decode('{}'),
            ];
        }

        return [
            'status' => 'VALID',
            'message' => 'Address is valid',
            'fieldErrors' => json_decode('{}'),
        ];
    }

    private function _diffAddressFields(array $response, array $request): array
    {
        $evaluatedKeys = [
            'State',
            'Zip5',
        ];
        if ($request['Zip4']) {
            $evaluatedKeys[] = 'Zip4';
        }

        $response = array_intersect_key($response, array_flip($evaluatedKeys));

        return array_diff($response, $request);
    }

    private function _buildAddressRequest(string $address1, string $address2, string $city, string $state_code, string $zip_code): array
    {
        list($zip5, $zip4) = $this->_splitZip($zip_code);

        // Fields must be ordered in this exact sequence
        $address = [
            '@ID' => '0',
            'Address1' => $address1,
            'Address2' => $address2,
            'City' => $city,
            'State' => $state_code,
            'Zip5' => $zip5,
            'Zip4' => $zip4,
        ];

        return [
            'AddressValidateRequest' => [
                '@USERID' => $this->apiKey,
                'Address' => $address,
            ],
        ];
    }

    /**
     * @param string $zip_code
     * @return string[]
     */
    private function _splitZip(string $zip_code): array
    {
        list($zip5, $zip4) = [$zip_code, ''];
        if (strpos($zip_code, '-') !== false) {
            list($zip5, $zip4) = explode('-', $zip_code, 2);
        }

        return [$zip5, $zip4];
    }

    protected function cacheKey(array $parameters): string
    {
        $parameters = array_map('mb_strtoupper', $parameters);
        ksort($parameters);

        return strtolower(__CLASS__) . '_' . md5(json_encode($parameters));
    }
}
