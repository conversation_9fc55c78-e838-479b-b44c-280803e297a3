<?php
App::uses('ProfileSetupConditions', 'Controller/Component/UserLogic');

class BrandSetupConditions extends ProfileSetupConditions
{
    protected $showContactPerson = true;
    protected $showAreaOfInterest = true;
    protected $showConfiguration = true;
    protected $showBankConnect = true;
    protected $showSubscriptionPlan = true;
    protected $showShipmentSetting = true;
    protected $showStoreTiming = false;
    protected $showBrandAssociation = false;

    public function evaluateSetupStatus(): bool
    {
        return (
            $this->hasContactPerson() &&
            $this->hasConfiguration() &&
            $this->hasAreaOfInterest() &&
            $this->hasSubscriptionPlan() &&
            $this->hasShipmentSetting() &&
            $this->hasBankConnect()
        );
    }
}
