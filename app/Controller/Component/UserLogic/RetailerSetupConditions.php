<?php
App::uses('ProfileSetupConditions', 'Controller/Component/UserLogic');

class RetailerSetupConditions extends ProfileSetupConditions
{
    protected $showContactPerson = true;
    protected $showAreaOfInterest = true;
    protected $showConfiguration = true;
    protected $showBankConnect = false;
    protected $showSubscriptionPlan = false;
    protected $showShipmentSetting = true;
    protected $showStoreTiming = true;
    protected $showBrandAssociation = true;

    public function evaluateSetupStatus(): bool
    {
        return (
            $this->hasContactPerson() &&
            $this->hasConfiguration() &&
            $this->hasStoreTiming() &&
            $this->hasAreaOfInterest()
        );
    }
}
