<?php

/**
 * Class ProfileSetupConditions.
 *
 * Contains setup steps completed by a new user in the registration workflow.
 *
 * @package Controller/Component/UserLogic
 */
abstract class ProfileSetupConditions
{
    /** @var bool */
    protected $showContactPerson;
    /** @var bool */
    protected $showAreaOfInterest;
    /** @var bool */
    protected $showConfiguration;
    /** @var bool */
    protected $showBankConnect;
    /** @var bool */
    protected $showSubscriptionPlan;
    /** @var bool */
    protected $showShipmentSetting;
    /** @var bool */
    protected $showStoreTiming;
    /** @var bool */
    protected $showBrandAssociation;

    /** @var bool */
    private $contactPerson;
    /** @var bool */
    private $areaOfInterest;
    /** @var bool */
    private $configuration;
    /** @var bool */
    private $bankConnect;
    /** @var bool */
    private $subscriptionPlan;
    /** @var bool */
    private $shipmentSetting;
    /** @var bool */
    private $storeTiming;
    /** @var bool */
    private $brandAssociation;

    abstract public function evaluateSetupStatus(): bool;

    public function canShowContactPerson(): bool
    {
        return $this->showContactPerson;
    }

    public function setShowContactPerson(bool $showContactPerson): ProfileSetupConditions
    {
        $this->showContactPerson = $showContactPerson;
        return $this;
    }

    public function canShowAreaOfInterest(): bool
    {
        return $this->showAreaOfInterest;
    }

    public function setShowAreaOfInterest(bool $showAreaOfInterest): ProfileSetupConditions
    {
        $this->showAreaOfInterest = $showAreaOfInterest;
        return $this;
    }

    public function canShowConfiguration(): bool
    {
        return $this->showConfiguration;
    }

    public function setShowConfiguration(bool $showConfiguration): ProfileSetupConditions
    {
        $this->showConfiguration = $showConfiguration;
        return $this;
    }

    public function canShowBankConnect(): bool
    {
        return $this->showBankConnect;
    }

    public function setShowBankConnect(bool $showBankConnect): ProfileSetupConditions
    {
        $this->showBankConnect = $showBankConnect;
        return $this;
    }

    public function canShowSubscriptionPlan(): bool
    {
        return $this->showSubscriptionPlan;
    }

    public function setShowSubscriptionPlan(bool $showSubscriptionPlan): ProfileSetupConditions
    {
        $this->showSubscriptionPlan = $showSubscriptionPlan;
        return $this;
    }

    public function canShowShipmentSetting(): bool
    {
        return $this->showShipmentSetting;
    }

    public function setShowShipmentSetting(bool $showShipmentSetting): ProfileSetupConditions
    {
        $this->showShipmentSetting = $showShipmentSetting;
        return $this;
    }

    public function canShowStoreTiming(): bool
    {
        return $this->showStoreTiming;
    }

    public function setShowStoreTiming(bool $showStoreTiming): ProfileSetupConditions
    {
        $this->showStoreTiming = $showStoreTiming;
        return $this;
    }

    public function canShowBrandAssociation(): bool
    {
        return $this->showBrandAssociation;
    }

    public function setShowBrandAssociation(bool $showBrandAssociation): ProfileSetupConditions
    {
        $this->showBrandAssociation = $showBrandAssociation;
        return $this;
    }

    public function hasContactPerson(): bool
    {
        return $this->contactPerson;
    }

    public function setContactPerson(bool $contactPerson): ProfileSetupConditions
    {
        $this->contactPerson = $contactPerson;
        return $this;
    }

    public function hasAreaOfInterest(): bool
    {
        return $this->areaOfInterest;
    }

    public function setAreaOfInterest(bool $areaOfInterest): ProfileSetupConditions
    {
        $this->areaOfInterest = $areaOfInterest;
        return $this;
    }

    public function hasConfiguration(): bool
    {
        return $this->configuration;
    }

    public function setConfiguration(bool $configuration): ProfileSetupConditions
    {
        $this->configuration = $configuration;
        return $this;
    }

    public function hasBankConnect(): bool
    {
        return $this->bankConnect;
    }

    public function setBankConnect(bool $bankConnect): ProfileSetupConditions
    {
        $this->bankConnect = $bankConnect;
        return $this;
    }

    public function hasSubscriptionPlan(): bool
    {
        return $this->subscriptionPlan;
    }

    public function setSubscriptionPlan(bool $subscriptionPlan): ProfileSetupConditions
    {
        $this->subscriptionPlan = $subscriptionPlan;
        return $this;
    }

    public function hasShipmentSetting(): bool
    {
        return $this->shipmentSetting;
    }

    public function setShipmentSetting(bool $shipmentSetting): ProfileSetupConditions
    {
        $this->shipmentSetting = $shipmentSetting;
        return $this;
    }

    public function hasStoreTiming(): bool
    {
        return $this->storeTiming;
    }

    public function setStoreTiming(bool $storeTiming): ProfileSetupConditions
    {
        $this->storeTiming = $storeTiming;
        return $this;
    }

    public function hasBrandAssociation(): bool
    {
        return $this->brandAssociation;
    }

    public function setBrandAssociation(bool $brandAssociation): ProfileSetupConditions
    {
        $this->brandAssociation = $brandAssociation;
        return $this;
    }
}
