<?php
App::uses('ProfileSetupConditions', 'Controller/Component/UserLogic');

class BranchSetupConditions extends ProfileSetupConditions
{
    protected $showContactPerson = true;
    protected $showAreaOfInterest = false;
    protected $showConfiguration = true;
    protected $showBankConnect = false;
    protected $showSubscriptionPlan = false;
    protected $showShipmentSetting = false;
    protected $showStoreTiming = true;
    protected $showBrandAssociation = false;

    public function evaluateSetupStatus(): bool
    {
        return (
            $this->hasContactPerson() &&
            $this->hasConfiguration() &&
            $this->hasStoreTiming()
        );
    }
}
