<?php

use Ship<PERSON>arlyApp\Lib\Geolocation\Geolocation;
use ShipEarlyApp\Plugin\Widgets\Enum\WidgetsDeliveryMethod;

App::uses('PluginRequestComponent', 'Controller/Component');

class CheckProductComponent extends PluginRequestComponent
{
    public function checkProduct(
        string $token,
        ?string $currencyCode,
        int $retailerId,
        string $method,
        Geolocation $location,
        array $items,
        array $itemDiscounts,
        float $shippingAmount,
        float $shippingDiscount,
        ?array $discountRetailerIds = null
    ): array
    {
        /** @var State $State */
        $State = ClassRegistry::init('State');
        $stateRecord = $State->findByCountryCodeAndStateCode((string)$location->country_code, (string)$location->state_code, ['id', 'country_id']);

        $getRetailerIdsMethodToCheckProductType = [
            WidgetsDeliveryMethod::SHIP_FROM_STORE => 'shipFromStore',
            WidgetsDeliveryMethod::SELL_DIRECT => 'sellDirect',
        ];
        $type = $getRetailerIdsMethodToCheckProductType[$method] ?? $method;

        return $this->_requestAction('/ws/checkProduct', [
            'bypass_pos' => true,
            'token' => $token,
            'customer' => null,
            'items' => json_encode($items),
            'currency_code' => $currencyCode,
            'address' => json_encode([
                'address' => $location->address1,
                'city' => $location->city,
                'regionCode' => $location->state_code,
                'regionName' => $location->state_name,
                'countryCode' => $location->country_code,
                'countryName' => $location->country_name,
                'PostalCode' => $location->zipcode,
                'province' => $stateRecord['State']['id'],
                'country' => $stateRecord['State']['country_id'],
            ]),
            'shippingAmount' => $shippingAmount,
            'shippingDiscount' => $shippingDiscount,
            'retailers' => $retailerId,
            'discountRetailerIds' => $discountRetailerIds,
            'geopoints' => json_encode([
                'lat' => $location->latitude,
                'lng' => $location->longitude,
            ]),
            'type' => $type,
            'discounts' => json_encode($itemDiscounts),
        ]);
    }
}
