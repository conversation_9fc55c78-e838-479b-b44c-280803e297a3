<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('Currency', 'Utility');

/**
 * Currency Component.
 */
class CurrencyComponent extends AppComponent
{
    /**
     * Format price amount with currency symbol.
     *
     * @param float|string $amount
     * @param string $currency
     * @param bool $allowPrice
     * @param bool $removeSpan
     * @return string
     * @see \Currency::formatCurrency
     */
    public function formatCurrency($amount = 0, $currency = 'USD', $allowPrice = false, $removeSpan = false): string
    {
        return Currency::formatCurrency($amount, (string)$currency, (bool)$allowPrice, (bool)$removeSpan);
    }

    /**
     * Format price amount without currency symbol
     *
     * @param float|string $amount
     * @param string $currency
     * @param bool $allowPrice
     * @return string
     * @see \Currency::formatAmount
     */
    public function formatAmount($amount = 0, $currency = 'USD', $allowPrice = false): string
    {
        return Currency::formatAmount($amount, (string)$currency, (bool)$allowPrice);
    }

    /**
     * Looks up an array of ISO 4217 currency codes for an array of ISO 3166-1 alpha-2 country codes.
     *
     * Maintains index associations.
     *
     * @param string[] $countryCodes
     * @return string[] Currency codes
     */
    public function mapCountryCodesToCurrency(array $countryCodes): array
    {
        return Currency::mapCountryCodesToCurrency($countryCodes);
    }

    /**
     * Look up the ISO 4217 currency code for a ISO 3166-1 alpha-2 country code.
     *
     * @param string $countryCode
     * @return string|null Currency code
     */
    public function getCurrencyForCountry(string $countryCode): ?string
    {
        return Currency::getCurrencyForCountry($countryCode);
    }

    /**
     * Format a price amount as a numeric string rounded to the appropriate decimal places for its currency.
     *
     * The value is returned as a float-compatible string to avoid floating point rounding errors.
     *
     * @param float|string $amount
     * @param string $currency
     * @return string Numeric decimal string
     */
    public function formatAsDecimal($amount, string $currency): string
    {
        return Currency::formatAsDecimal($amount, $currency);
    }
}
