<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppComponent', 'Controller/Component');
App::uses('UserFriendlyException', 'Error');
App::uses('FulfillmentStatus', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderType', 'Utility');

/**
 * FulfillmentLogic Component.
 *
 * @property AfterShipComponent $AfterShip
 * @property NotificationLogicComponent $NotificationLogic
 * @property OrderLogicComponent $OrderLogic
 * @property ShopifyComponent $Shopify
 *
 * @property Courier $Courier
 * @property DealerOrder $DealerOrder
 * @property Fulfillment $Fulfillment
 * @property InventoryTransferProductReservation $InventoryTransferProductReservation
 * @property Order $Order
 * @property RetailerCredit $RetailerCredit
 */
class FulfillmentLogicComponent extends AppComponent
{
    public $components = [
        'AfterShip',
        'NotificationLogic',
        'OrderLogic',
        'Shopify.Shopify',
    ];

    public $uses = [
        'Courier',
        'DealerOrder',
        'Fulfillment',
        'InventoryTransferProductReservation',
        'Order',
        'RetailerCredit',
    ];

    /**
     * Callback after creating a consumer refund which affects fulfillment status.
     *
     * @param int $orderId
     * @param int $orderRefundId
     * @throws HttpException
     * @throws Exception
     */
    public function afterConsumerRefund(int $orderId, int $orderRefundId): void
    {
        $lastFulfillmentId = (int)$this->Fulfillment->field('id', ['Fulfillment.order_id' => $orderId], ['Fulfillment.created_at' => 'DESC']);

        $this->afterConsumerFulfillment($orderId, $lastFulfillmentId);
    }

    /**
     * Callback after creating a consumer fulfillment which affects fulfillment status.
     *
     * @param int $orderId
     * @param int $fulfillmentId
     * @throws HttpException
     * @throws Exception
     */
    public function afterConsumerFulfillment(int $orderId, int $fulfillmentId): void
    {
        $fulfillmentStatus = $this->Order->findDerivedFulfillmentStatus($orderId);

        $prevFulfillmentStatus = $this->Order->field('fulfillment_status', ['Order.id' => $orderId]);
        if (in_array($prevFulfillmentStatus, [$fulfillmentStatus, FulfillmentStatus::FULFILLED, FulfillmentStatus::CANCELLED], true)) {
            // Do nothing
            return;
        }

        if (!$this->Order->save(['id' => $orderId, 'fulfillment_status' => $fulfillmentStatus])) {
            throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
        }

        if ($fulfillmentStatus === FulfillmentStatus::FULFILLED) {
            $this->Order->markShippedToConsumer($orderId);

            try {
                $this->trackFulfillment($fulfillmentId);
            } catch (UserFriendlyException $e) {
                CakeLog::debug($e);
            } catch (Exception $e) {
                CakeLog::warning($e);
            }
        }
    }

    /**
     * Callback after creating a dealer fulfillment which affects fulfillment status.
     *
     * @param int $dealerOrderId
     * @param int $dealerOrderRefundId
     * @throws HttpException
     * @throws Exception
     */
    public function afterDealerRefund(int $dealerOrderId, int $dealerOrderRefundId): void
    {
        $lastFulfillmentId = (int)$this->Fulfillment->field('id', ['Fulfillment.dealer_order_id' => $dealerOrderId], ['Fulfillment.created_at' => 'DESC']);

        $this->afterDealerFulfillment($dealerOrderId, $lastFulfillmentId, false);
    }

    /**
     * Callback after creating a dealer refund which affects fulfillment status.
     *
     * @param int $dealerOrderId
     * @param int $fulfillmentId
     * @param bool $createRetailerCredit
     * @throws HttpException
     * @throws Exception
     */
    public function afterDealerFulfillment(int $dealerOrderId, int $fulfillmentId, bool $createRetailerCredit = true): void
    {
        $dealerOrder = (array)$this->DealerOrder->get($dealerOrderId, [
            'contain' => [
                'Order' => [
                    'fields' => [
                        'id',
                        'user_id',
                        'retailer_id',
                        'is_commission_retailer',
                        'order_type',
                        'payment_method',
                        'payment_status',
                        'total_price',
                        'total_discount',
                        'transactionID',
                        'stripe_account',
                        'shipearlyFees',
                    ],
                ],
            ],
            'fields' => ['id', 'order_id', 'fulfillment_status'],
        ]);

        $itemQuantities = array_column(
            $this->DealerOrder->DealerOrderProduct->findAllWithDerivedQuantities($dealerOrderId),
            $this->DealerOrder->DealerOrderProduct->alias
        );

        $prevFulfillmentStatus = $dealerOrder['DealerOrder']['fulfillment_status'];
        $fulfillmentStatus = FulfillmentStatus::deriveFulfillmentStatus($itemQuantities);

        if ($createRetailerCredit && $dealerOrder['Order']['payment_method'] === OrderPaymentMethod::CREDIT) {
            $this->RetailerCredit->createFromFulfillment($fulfillmentId, $dealerOrderId);
        }

        $orderId = (int)$dealerOrder['DealerOrder']['order_id'];

        $this->InventoryTransferProductReservation->reserveLineItemSet($orderId, array_map(
            function($item) {
                return array_merge($item, ['quantity' => $item['reserved_quantity']]);
            },
            $itemQuantities
        ));

        if (in_array($prevFulfillmentStatus, [$fulfillmentStatus, FulfillmentStatus::FULFILLED, FulfillmentStatus::CANCELLED], true)) {
            // Do nothing
            return;
        }

        if (!$this->DealerOrder->save(['id' => $dealerOrderId, 'fulfillment_status' => $fulfillmentStatus])) {
            throw new InternalErrorException(json_encode(['errors' => $this->DealerOrder->validationErrors, 'data' => $this->DealerOrder->data]));
        }

        if ($fulfillmentStatus === FulfillmentStatus::FULFILLED) {
            $this->DealerOrder->markShippedToDealer($dealerOrderId);
            $this->NotificationLogic->dealerOrderFulfillmentEmail($fulfillmentId);

            try {
                $this->trackFulfillment($fulfillmentId);
            } catch (UserFriendlyException $e) {
                CakeLog::debug($e);
            } catch (Exception $e) {
                CakeLog::warning($e);
            }
        }

        // Capture after fulfillment updates so that exceptions do not interrupt previous steps
        if ($fulfillmentId && $dealerOrder['Order']['payment_status'] !== OrderPaymentStatus::PAID) {
            if ($dealerOrder['Order']['payment_method'] === OrderPaymentMethod::STRIPE) {
                if (!$this->OrderLogic->captureStripeCharge($orderId, $dealerOrder['Order'])) {
                    throw new InternalErrorException(json_encode(['message' => 'Failed to save charge capture', 'errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                }
            } else {
                if (!$this->Order->updatePaymentStatus($orderId, OrderPaymentStatus::PAID)) {
                    throw new InternalErrorException(json_encode(['message' => 'Failed to save charge capture', 'data' => ['id' => $orderId, 'payment_status' => OrderPaymentStatus::PAID]]));
                }
            }
        }
    }

    /**
     * @param int $fulfillmentId
     * @return bool
     * @throws UserFriendlyException
     * @throws HttpException
     * @throws Exception
     */
    public function trackFulfillment(int $fulfillmentId): bool
    {
        $fulfillment = $this->Fulfillment->find('first', [
            'contain' => [
                'DealerOrder' => ['fields' => ['id', 'order_id']],
            ],
            'conditions' => ['Fulfillment.id' => $fulfillmentId],
            'fields' => [
                'Fulfillment.id',
                'Fulfillment.order_id',
                'Fulfillment.dealer_order_id',
                'Fulfillment.courier_id',
                'Fulfillment.tracking_number',
                'Fulfillment.tracking_url',
            ],
        ]);
        if (empty($fulfillment['Fulfillment']['id'])) {
            throw new NotFoundException('Fulfillment not found where id=' . json_encode($fulfillmentId));
        }

        $orderId = (int)($fulfillment['DealerOrder']['order_id'] ?? $fulfillment['Fulfillment']['order_id']);
        $courierId = (int)$fulfillment['Fulfillment']['courier_id'];
        $trackingNumber = (string)$fulfillment['Fulfillment']['tracking_number'];
        $trackingUrl = (string)$fulfillment['Fulfillment']['tracking_url'];

        if (!$courierId || !$trackingNumber) {
            CakeLog::debug(json_encode(['message' => 'Skipping fulfillment with no tracking info'] + $fulfillment));

            return true;
        }

        $response = $this->trackOrder($orderId, $courierId, $trackingNumber);
        if (!is_array($response)) {
            return (bool)$response;
        }

        if (!$trackingUrl) {
            $saveData = [
                'id' => $fulfillmentId,
                'tracking_url' => $response['courier_tracking_link'] ?? null,
            ];
            if (!$this->Fulfillment->save($saveData)) {
                throw new InternalErrorException(json_encode(['errors' => $this->Fulfillment->validationErrors, 'data' => $this->Fulfillment->data]));
            }
        }

        return true;
    }

    /**
     * @param int $orderId
     * @param int $courierId
     * @param string $trackingNumber
     * @return array|bool
     * @throws UserFriendlyException
     * @throws HttpException
     * @throws Exception
     */
    public function trackOrder(int $orderId, int $courierId, string $trackingNumber)
    {
        $orderType = $this->Order->field('order_type', ['Order.id' => $orderId]);
        if (!$orderType) {
            throw new NotFoundException('Order not found where order_id=' . json_encode($orderId));
        }
        $orderType = OrderType::filterOrderType($orderType);

        $slug = $this->Courier->field('slug', ['Courier.id' => $courierId]);
        if (!$slug) {
            throw new NotFoundException('Courier not found where courier_id=' . json_encode($courierId));
        }

        $after_res = [];

        if (!in_array($orderType, [OrderType::WHOLESALE, OrderType::SELL_DIRECT], true)) {
            try {
                if ($this->AfterShip->hasCouriers($trackingNumber, $slug)) {
                    $after_res = $this->AfterShip->trackOrder($orderId, $trackingNumber, $slug);
                } else {
                    throw new NotFoundException('AfterShip courier not found where: ' . json_encode(compact('slug', 'trackingNumber')));
                }
            } catch (\AfterShip\AfterShipException $e) {
                CakeLog::error($e);

                throw new UserFriendlyException($e->getMessage(), $e->getCode(), $e);
            }

            if (empty($after_res['slug'])) {
                throw new InternalErrorException('Failed to place AfterShip tracking info: ' . json_encode(compact('slug', 'trackingNumber', 'after_res')));
            }
        }

        $saveData = [
            'id' => $orderId,
            'courier' => $courierId,
            'trackingno' => $trackingNumber,
            'unique_token' => $after_res['unique_token'] ?? null,
            'last_check_point' => $after_res['LastCheckPoint'] ?? '',
        ];
        if (!$this->Order->save($saveData)) {
            throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
        }

        return $after_res ?: true;
    }
    /**
     * @param int|string $fulfillmentId
     * @throws CurlException
     * @throws HttpException
     * @throws EcommercePlacementException
     * @throws ShopifyApiException
     */
    public function place_ecommerce_fulfillment($fulfillmentId)
    {
        $fulfillment = $this->Fulfillment->findForEcommerce((int)$fulfillmentId);
        if (empty($fulfillment['Fulfillment']['id'])) {
            throw new NotFoundException('Fulfillment not found where id=' . json_encode($fulfillmentId));
        }

        $shopifyOrderId = $fulfillment['DealerOrder']['source_id'] ?? $fulfillment['Order']['source_id'];

        if (!empty($fulfillment['Fulfillment']['source_id'])) {
            throw new EcommercePlacementException("The fulfillment is already linked to /orders/{$shopifyOrderId}/fulfillments/{$fulfillment['Fulfillment']['source_id']}");
        }

        $user = $fulfillment['DealerOrder']['User'] ?? $fulfillment['Order']['User'];
        $userLog = mask_secret_fields($user, ['api_key', 'secret_key']);

        $siteType = $user['site_type'];
        $apiKey = $user['api_key'];
        $secretKey = $user['secret_key'];
        $shopUrl = $user['shop_url'];

        $responseId = null;
        switch ($siteType) {
            case UserSiteType::SHIPEARLY:
                // Do nothing
                return;
            case UserSiteType::SHOPIFY:
                if (!$fulfillment['Warehouse']['source_id']) {
                    throw new EcommercePlacementException('The fulfillment warehouse is not linked to a Shopify location');
                }

                $response = $this->Shopify->placeEcommerceFulfillment($shopifyOrderId, $apiKey, $secretKey, $shopUrl, $fulfillment);
                $responseId = $response['id'] ?? null;
                break;
            case UserSiteType::WOOCOMMERCE:
            case UserSiteType::MAGENTO:
                triggerWarning(json_encode(['message' => 'Unsupported site_type', 'User' => $userLog]));
                break;
            default:
                throw new BadRequestException(json_encode(['message' => 'Unknown site_type', 'User' => $userLog]));
        }
        if (!$responseId) {
            throw new EcommercePlacementException('Unable to place the fulfillment in platform: ' . json_encode($siteType));
        }

        if (!$this->Fulfillment->save(['id' => $fulfillmentId, 'source_id' => $responseId])) {
            throw new InternalErrorException(json_encode(['errors' => $this->Fulfillment->validationErrors, 'data' => $this->Fulfillment->data]));
        }
    }
}
