<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('CakeTime', 'Utility');
App::uses('CurlException', 'Error');
App::uses('LightspeedApiException', 'Error');
App::uses('LightspeedOauthException', 'Error');

use Ship<PERSON>arlyApp\Controller\Component\OAuth\OAuthConnectInterface;
use ShipEarlyApp\Controller\Component\OAuth\OAuthStateTrait;

/**
 * Class LightspeedComponent
 * 
 * @property Btask $Btask
 * @property Customer $Customer
 */
class LightspeedComponent extends AppComponent implements OAuthConnectInterface
{
    use OAuthStateTrait;

    const PAYMENT_TYPE_NAME = 'Stripe';

    const RESPONSE_RECORD_LIMIT = 100;

    public $uses = [
        'Btask',
        'Customer',
    ];

    /**
     * @api lightspeed APIURL
     * @var string
     */
    public $lightspeedURL = "https://api.lightspeedapp.com/API/V3/Account/";

    /**
     * @var string
     */
    public $tokenURL = "https://cloud.lightspeedapp.com/oauth/access_token.php";

    /**
     * @var MOSAPICall
     */
    public $mosapi;

    /**
     * @var array
     */
    private $new_access_token_response = array();

    /**
     * @var array
     */
    private $lastResponseHeaders = [];

    public function initialize(Controller $controller)
    {
        $this->_lightspeedInc();
    }

    /**
     * @param string $apikey
     * @param int|string $account_id
     * @param int|string $taxCategoryID
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return SimpleXMLElement
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     * @see https://developers.lightspeedhq.com/retail/endpoints/TaxCategory/#get-single-tax-category
     */
    public function getTaxCategory($apikey, $account_id, $taxCategoryID, $refresh_token = '', $token_expires_at = '')
    {
        if (!$taxCategoryID) {
            throw new LightspeedApiException(LightspeedApiException::createXmlElement(
                400,
                'Bad Request',
                'Invalid ID: ' . json_encode($taxCategoryID),
                'InvalidResourceIDError'
            ));
        }
        $url = $this->lightspeedURL . '{account_id}/TaxCategory/{id}?load_relations=["TaxCategoryClasses.TaxClass"]';
        return $this->lightResponse($apikey, $account_id, $url, $taxCategoryID, $refresh_token, $token_expires_at);
    }

    /**
     * Create a sale and customer on Lightspeed inventory system
     * Associate the order with created customer
     *
     * @param string $apikey
     * @param int|string $account_id
     * @param int|string $registerID
     * @param int|string $employeeID
     * @param int|string $shopID
     * @param array $productDetails
     * @param string[] $billing
     * @param int|string $retailerId
     * @param string $currencyCode
     * @param float|string $totalPrice
     * @param float|string $shippingAmount
     * @param bool $shipto
     * @param string[] $shipping
     * @param float|string $shippingTax
     * @param float|string $taxAmt
     * @param string $refresh_token
     * @param string $token_expires_at
     * @param bool $queue
     * @return bool Success
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    public function createSale($apikey, $account_id, $registerID, $employeeID, $shopID, $productDetails, $billing, $retailerId, $currencyCode, $totalPrice, $shippingAmount, $shipto, $shipping, $shippingTax, $taxAmt, $orderNumber, $refresh_token = '', $token_expires_at = '', $queue = true)
    {
        if ($queue) {
            return $this->Btask->queueTask(Btask::TYPE_LIGHTSPEED_ORDER, func_get_args());
        }
        $this->clearNewAccessTokenResponse($account_id);
        if ($refresh_token && CakeTime::isPast($token_expires_at)) {
            $refreshed = $this->refreshAccessToken($refresh_token, $account_id);

            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }

        $productDetails = $this->synchronizeProducts($productDetails, $apikey, $account_id, $refresh_token, $token_expires_at);
        $shipmentItemIds = $this->checkShippingItems($apikey, $account_id, $refresh_token, $token_expires_at);


        $billing = (object)$billing;
        $shipping = (object)$shipping;
        $this->mosapi = new MOSAPICall($apikey, $account_id);
        $customerID = $this->createLightspeedCustomer($billing, $retailerId);
        CakeLog::debug(json_encode(compact('customerID')));
        $xmlCreateSale = $this->_salesLine($customerID, $registerID, $employeeID, $shopID, $productDetails, $currencyCode, $totalPrice, $shippingAmount, $shipto, $shipping, $apikey, $account_id, $shippingTax, $taxAmt, $shipmentItemIds, $orderNumber);
        CakeLog::debug(json_encode(compact('xmlCreateSale')));
        $response = $this->mosapi->makeAPICall("Account.Sale", "Create", null, $xmlCreateSale);
        CakeLog::debug(json_encode(compact('response')));
        if (($response->httpCode ?? '200') !== '200') {
            throw new LightspeedApiException($response);
        }

        if($response->balance != $totalPrice)
        {
            CakeLog::warning("Lightspeed order value mismatch. Lightspeed Value: {$response->balance}. Shipearly value: {$totalPrice}");
        }

        return $this->completeSale($response->saleID, $totalPrice);
    }

    public function completeSale($saleId, $totalPrice)
    {
        $sale = [
            'completed' => true,
            'SalePayments' => [
                'SalePayment' => [
                     $this->_formatSalePayment($totalPrice)
                ]
            ]
        ];

        $xmlUpdateSale = Array2XML::createXML('Sale', $sale)->saveXML();
        CakeLog::debug(json_encode(compact('xmlUpdateSale')));
        $response = $this->mosapi->makeAPICall("Account.Sale", "Update", $saleId, $xmlUpdateSale);
        CakeLog::debug(json_encode(compact('response')));
        if (($response->httpCode ?? '200') !== '200') {
            throw new LightspeedApiException($response);
        }

        return true;
    }

    /**
     * Include the library files
     */
    private function _lightspeedInc()
    {
        if (!class_exists('MOScURL')) {
            require APP . 'Vendor/Lightspeed/MOScURL.class.php';
        }
        if (!class_exists('MOSAPICall')) {
            require APP . 'Vendor/Lightspeed/MOSAPICall.class.php';
        }
        if (!class_exists('Array2XML')) {
            require APP . 'Vendor/Lightspeed/Array2XML.php';
        }
    }

    /**
     * Return Lightspeed customer Id
     * @param $billing
     * @param $retailerId
     * @return mixed
     */
    private function createLightspeedCustomer($billing, $retailerId)
    {
        /* Check if the customer is already available based on email
         * If customer Id found just reuse
         * Else create new user and return Id
         */
        $cusId = $this->Customer->find('first', array('fields' => array('customerID', 'id'), 'conditions' => array('email_address' => $billing->email, 'user_id' => $retailerId)));
        if (!count($cusId)) {
            $customer = $this->_formatCustomerXML($billing);
            $xml = Array2XML::createXML('Customer', $customer);
            $XMLstring = $xml->saveXML();
            $customer_response_xml = $this->mosapi->makeAPICall("Account.Customer", "Create", null, $XMLstring);
            $customerId = $customer_response_xml->customerID;
            $this->Customer->syncPosBillingCustomer((int)$retailerId, (string)$customerId ?: null, (object)$billing);
        } else {
            $customerId = $cusId['Customer']['customerID'];
        }
        return $customerId;
    }

    /**
     * Format the lightspeed customer creation request
     * @param $billing
     * @return mixed
     */
    private function _formatCustomerXML($billing)
    {
        $date = new DateTime();
        $MOStimestamp = $date->format('Y-m-d\TH:i:s\+\0\0\:\0\0');

        $customer['firstName'] = $billing->firstname;
        $customer['lastName'] = $billing->lastname;
        $customer['company'] = $billing->company;
        $customer['timeStamp'] = $MOStimestamp;
        $customer['customerTypeID'] = 0;
        $customer['taxCategoryID'] = 0;
        $customer['Contact']['timeStamp'] = $MOStimestamp;
        $customer = $this->_formatContactAddress($customer, $billing);

        return $customer;
    }

    /**
     * Format the lightspeed customer creation request
     * @param $customer
     * @param $billing
     * @return mixed
     */
    private function _formatContactAddress($customer, $billing)
    {
        //Address
        $customer['Contact']['Addresses']['ContactAddress']['address1'] = $billing->street;
        $customer['Contact']['Addresses']['ContactAddress']['city'] = $billing->city;
        $customer['Contact']['Addresses']['ContactAddress']['state'] = $billing->region;
        $customer['Contact']['Addresses']['ContactAddress']['zip'] = $billing->postcode;
        $customer['Contact']['Addresses']['ContactAddress']['country'] = $billing->country_id;
        //Phone
        $customer['Contact']['Phones']['ContactPhone']['number'] = $billing->telephone;
        $customer['Contact']['Phones']['ContactPhone']['useType']['@attributes']['readonly'] = true;
        $customer['Contact']['Phones']['ContactPhone']['useType']['@value'] = 'Home';
        //Email
        $customer['Contact']['Emails']['ContactEmail']['address'] = $billing->email;
        $customer['Contact']['Emails']['ContactEmail']['useType']['@attributes']['readonly'] = true;
        $customer['Contact']['Emails']['ContactEmail']['useType']['@value'] = 'Primary';
        return $customer;
    }

    /**
     * @api Sale
     * @param $customerID
     * @param $registerID
     * @param $employeeID
     * @param $shopID
     * @param $productDetails
     * @param $currencyCode
     * @param $totalPrice
     * @param $shippingAmount
     * @param $shipto
     * @param $shipping
     * @param $apikey
     * @param $account_id
     * @param $shippingTax
     * @param $taxAmt
     * @return string
     */
    private function _salesLine($customerID, $registerID, $employeeID, $shopID, $productDetails, $currencyCode, $totalPrice, $shippingAmount, $shipto, $shipping, $apikey, $account_id, $shippingTax, $taxAmt, $shipmentItemIds, $orderNumber)
    {
        $sale = $this->_formatOrderLine($customerID, $registerID, $employeeID, $shopID, $productDetails, $currencyCode, $totalPrice, $shippingAmount, $shipto, $shipping, $apikey, $account_id, $shippingTax, $taxAmt, $shipmentItemIds, $orderNumber);
        $xml = Array2XML::createXML('Sale', $sale);
        return $xml->saveXML();
    }

    /**
     * Format the sale request
     * @param $customerID
     * @param $registerID
     * @param $employeeID
     * @param $shopID
     * @param $productDetails
     * @param $currencyCode
     * @param $totalPrice
     * @param $shippingAmount
     * @param $shipto
     * @param $shipping
     * @param $apikey
     * @param $account_id
     * @param $shippingTax
     * @param $taxAmt
     * @return mixed
     */
    private function _formatOrderLine($customerID, $registerID, $employeeID, $shopID, $productDetails, $currencyCode, $totalPrice, $shippingAmount, $shipto, $shipping, $apikey, $account_id, $shippingTax, $taxAmt, $shipmentItemIds, $orderNumber)
    {
        $date = new DateTime();
        $MOStimestamp = $date->format('Y-m-d\TH:i:s\+\0\0\:\0\0');
        $orderLine['timeStamp'] = $MOStimestamp;
        $orderLine['completed'] = false;
        $orderLine['customerID'] = $customerID;
        $orderLine['registerID'] = $registerID;
        $orderLine['employeeID'] = $employeeID;
        $orderLine['shopID'] = $shopID;
        $orderLine = $this->addSaleNotes($orderLine, $orderNumber);

        if ($shipto == true) {
            $orderLine['ShipTo'][0] = $this->_formatShipTo($shipping, $MOStimestamp);
        }


        foreach ($productDetails as $value) {
            if($value['isFeeProduct'] ?? false){
                $orderLine['SaleLines']['SaleLine'][] = $this->formatFeeSaleLine($MOStimestamp, $customerID, $employeeID, $shopID, $value['qty'], $value['isTax'], $shipto, $value['unformatedunitpricediscount'], $value['title']);
            } else {
                $orderLine['SaleLines']['SaleLine'][] = $this->formatProductSaleLine($MOStimestamp, $customerID, $employeeID, $shopID, $value['qty'], $value['isTax'], $shipto, $value['inventoryId'], $value['unformatedunitpricediscount']);
            }

        }

        if(!$shipto && $shippingAmount > 0) {
            $orderLine['SaleLines']['SaleLine'][] = $this->_formatShipSaleline($MOStimestamp, $customerID, $shipmentItemIds['shippingAmount'], $employeeID, $shopID, $shippingAmount, true);
        }
        if ($shipto) {
            $amount = 0;
            foreach ($shipmentItemIds as $key => $value) {
                if ($key == 'shippingAmount') {
                    $amount = $shippingAmount;
                } elseif ($key == 'shippingTax') {
                    $amount = $shippingTax;
                } elseif ($key == 'productTax') {
                    $amount = $taxAmt - $shippingTax;
                }
                $newsaleLine = $this->_formatShipSaleline($MOStimestamp, $customerID, $value, $employeeID, $shopID, $amount, false);
                array_push($orderLine['SaleLines']['SaleLine'], $newsaleLine);
            }
        }

        return $orderLine;
    }

    protected function addSaleNotes(array $orderLine, string $orderNumber): array
    {
        
        $orderLine['SaleNotes'] = [
            'InternalNote' => [
                'note' => "ShipEarly ID: {$orderNumber}"
            ]
        ];

        return $orderLine;
    }

    /**
     * Format shipping information
     * @param $shipping
     * @param $MOStimestamp
     * @return mixed
     */
    private function _formatShipTo($shipping, $MOStimestamp)
    {
        $shipTo['shipped'] = false;
        $shipTo['timeStamp'] = $MOStimestamp;
        $shipTo['shipNote'] = '';
        $shipTo['firstName'] = $shipping->firstname;
        $shipTo['lastName'] = $shipping->lastname;
        $shipTo['company'] = $shipping->company;
        $shipTo = $this->_formatContactAddress($shipTo, $shipping);
        return $shipTo;
    }

    /**
     * Format a sale line for a product
     * @param $MOStimestamp
     * @param $customerID
     * @param $employeeID
     * @param $shopID
     * @param $qty
     * @param $tax
     * @param $shipto
     * @param $inventoryId
     * @param $unformatedunitprice
     * @return mixed
     */
    private function formatProductSaleLine($MOStimestamp, $customerID, $employeeID, $shopID, $qty, $tax, $shipto, $inventoryId, $unformatedunitprice): array
    {
        $salesLine['createTime'] = $salesLine['timeStamp'] = $MOStimestamp;
        $salesLine['unitQuantity'] = $qty;
        $salesLine['unitPrice'] = $unformatedunitprice;
        $salesLine['isLayaway'] = false;
        $salesLine['isWorkorder'] = false;
        $salesLine['isSpecialOrder'] = false;
        if ($shipto == true) {
            $salesLine['tax'] = false;
        } else {
            $salesLine['tax'] = $tax;
        }
        $salesLine['customerID'] = $customerID;
        $salesLine['employeeID'] = $employeeID;
        $salesLine['itemID'] = $inventoryId;
        $salesLine['shopID'] = $shopID;
        return $salesLine;
    }

    /**
     * Create a sale line for a fee. 
     * @param mixed $MOStimestamp 
     * @param mixed $customerID 
     * @param mixed $employeeID 
     * @param mixed $shopID 
     * @param mixed $qty 
     * @param mixed $tax 
     * @param mixed $shipto 
     * @param mixed $unformatedunitprice 
     * @param mixed $productTitle 
     * @return array 
     */
    protected function formatFeeSaleLine($MOStimestamp, $customerID, $employeeID, $shopID, $qty, $tax, $shipto, $unformatedunitprice, $productTitle): array
    {
        $feeLine = $this->formatProductSaleLine($MOStimestamp, $customerID, $employeeID, $shopID, $qty, $tax, $shipto, null, $unformatedunitprice);
        unset($feeLine['itemID']);

        $feeLine = array_merge($feeLine, [
            'taxClassID' => 0,
            'avgCost' => $unformatedunitprice,
            'fifoCost' => $unformatedunitprice,
            'Note' => [
                'note' => $productTitle,
                'isPublic' => true,
            ]
        ]);
        return $feeLine;
    }

    /**
     * Customized Tax values are not supported in lightspeed.
     * So I create 3 custom product(Shipping Amount, Shipping Tax, Product Tax)
     * And update custom tax though created product
     *
     * @param $apikey
     * @param $account_id
     * @return mixed
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    private function checkShippingItems($apikey, $account_id, $refresh_token = '', $token_expires_at = '')
    {
        $ids = [];
        $refreshed = $this->getNewAccessTokenResponse($account_id);

        $ids = array_merge(
            $ids,
            $this->checkShippingItem('Shipping Amount', 'shippingAmount', $apikey, $account_id, $refresh_token, $token_expires_at),
            $this->checkShippingItem('Shipping Tax', 'shippingTax', $apikey, $account_id, $refresh_token, $token_expires_at),
            $this->checkShippingItem('Product Tax', 'productTax', $apikey, $account_id, $refresh_token, $token_expires_at)
        );

        $this->setNewAccessTokenResponse($account_id, $this->getNewAccessTokenResponse($account_id, $refreshed));

        return $ids;
    }

    private function checkShippingItem($description, $key, $apikey, $account_id, $refresh_token = '', $token_expires_at = '')
    {
        $id = [];
        $refreshed = $this->getNewAccessTokenResponse($account_id);
        if (!empty($refreshed['access_token'])) {
            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }
        $item = $this->fetchItemByDescription($description, $apikey, $account_id, $refresh_token, $token_expires_at);
        if (isset($item->Item->itemID)) {
            $id[$key] = (int)$item->Item->itemID;
        } else {
            $id[$key] = $this->_addNewProduct($apikey, $account_id, $description);
        }
        $this->setNewAccessTokenResponse($account_id, $this->getNewAccessTokenResponse($account_id, $refreshed));

        return $id;
    }

    public function fetchItemByDescription($description, $apikey, $account_id, $refresh_token = '', $token_expires_at = '')
    {
        $refreshed = $this->getNewAccessTokenResponse($account_id);
        if (!empty($refreshed['access_token'])) {
            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }
        $url = $this->lightspeedURL . "{account_id}/Item?description=" . urlencode($description);

        $item = $this->lightResponse($apikey, $account_id, $url, '', $refresh_token, $token_expires_at);
        $this->setNewAccessTokenResponse($account_id, $this->getNewAccessTokenResponse($account_id, $refreshed));

        return $item;
    }

    /**
     * Create new product on lightspeed
     * @param $apikey
     * @param $account_id
     * @param $item_description
     * @return SimpleXMLElement
     */
    private function _addNewProduct($apikey, $account_id, $item_description): int
    {
        $refreshed = $this->getNewAccessTokenResponse($account_id);
        if (!empty($refreshed['access_token'])) {
            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }
        $this->mosapi = new MOSAPICall($apikey, $account_id);
        $xml_create_item = "<?xml version='1.0'?><Item><description>$item_description</description><itemType>non_inventory</itemType></Item>";
        $item_response_xml = $this->mosapi->makeAPICall("Account.Item", "Create", null, $xml_create_item);
        return (int)$item_response_xml->itemID;
    }

    /**
     * Format the sale request
     * @param $MOStimestamp
     * @param $customerID
     * @param $itemID
     * @param $employeeID
     * @param $shopID
     * @param $shippingAmount
     * @return mixed
     */
    private function _formatShipSaleline($MOStimestamp, $customerID, $itemID, $employeeID, $shopID, $shippingAmount, $tax)
    {
        $salesLine['createTime'] = $salesLine['timeStamp'] = $MOStimestamp;
        $salesLine['unitQuantity'] = 1;
        $salesLine['unitPrice'] = $shippingAmount;
        $salesLine['tax'] = $tax;
        $salesLine['customerID'] = $customerID;
        $salesLine['employeeID'] = $employeeID;
        $salesLine['itemID'] = $itemID;
        $salesLine['shopID'] = $shopID;
        return $salesLine;
    }

    /**
     * Format the sale request
     * @param $amount
     * @return mixed
     */
    private function _formatSalePayment($amount): array
    {
        $SalePayment = [
            'paymentTypeID' => $this->getShipearlyPaymentTypeId(),
            'amount' => round($amount, 2),
        ];

        return $SalePayment;
    }

    protected function getShipearlyPaymentTypeId(): int
    {
        $paymentType = $this->mosapi->makeAPICall('Account.PaymentType', 'Read', null, null, "xml", "name=" . static::PAYMENT_TYPE_NAME);

        if(isset($paymentType->PaymentType->paymentTypeID)){
            return (int)$paymentType->PaymentType->paymentTypeID;
        }

        return $this->createShipearlyPaymentType();
    }

    protected function createShipearlyPaymentType(): int
    {
        $newPaymentType = Array2XML::createXML('PaymentType', [
            'name' => static::PAYMENT_TYPE_NAME,
            'requireCustomer' => false,
            'internalReserved' => false,
            'type' => 'user defined',
            'refundAsPaymentTypeID' => 0,
        ])->saveXML();

        $paymentType = $this->mosapi->makeAPICall('Account.PaymentType', 'Create', null, $newPaymentType);

        if(!isset($paymentType->paymentTypeID)){
            throw new Exception('Unable to create payment type');
        }

        return (int)$paymentType->paymentTypeID;
    }

    /**
     * Get Shop related information from lightspeed (employee, register, shop) List
     *
     * @param string $apikey
     * @param int|string $account_id
     * @param int|null $storeId
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return array
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    public function getAllList($apikey, $account_id, ?int $storeId, $refresh_token, $token_expires_at): array
    {
        $employeeList = $this->_employeeList($apikey, $account_id, $storeId, $refresh_token, $token_expires_at);

        $refreshed = $this->getNewAccessTokenResponse($account_id);
        if (!empty($refreshed['access_token'])) {
            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }

        $registerList = $this->_registerList($apikey, $account_id, $storeId, $refresh_token, $token_expires_at);

        $refreshed = $this->getNewAccessTokenResponse($account_id, $refreshed);
        if (!empty($refreshed['access_token'])) {
            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }

        $shopList = $this->_shopList($apikey, $account_id, $refresh_token, $token_expires_at);

        // Set again in case the last request cleared it
        $this->setNewAccessTokenResponse($account_id, $this->getNewAccessTokenResponse($account_id, $refreshed));

        return array(
            'Employee' => $employeeList,
            'Register' => $registerList,
            'Shop' => $shopList,
        );
    }

    /**
     * Get all employees list from lightspeed
     *
     * @param string $apikey
     * @param int|string $account_id
     * @param int|null $storeId
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return string[]
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    private function _employeeList($apikey, $account_id, ?int $storeId, $refresh_token, $token_expires_at): array
    {
        $url = $this->lightspeedURL . "{$account_id}/Employee";
        $employees = $this->lightResponse($apikey, $account_id, $url, '', $refresh_token, $token_expires_at);
        $list = array();
        foreach ($employees->Employee as $employee) {
            if ($storeId === null || (int)$employee->lastShopID === $storeId) {
                $list[(string)$employee->employeeID] = (string)$employee->firstName . ' ' . (string)$employee->lastName;
            }
        }
        return $list;
    }

    /**
     * Get all register list from lightspeed
     *
     * @param string $apikey
     * @param int|string $account_id
     * @param int|null $storeId
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return string[]
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    private function _registerList($apikey, $account_id, ?int $storeId, $refresh_token, $token_expires_at): array
    {
        $url = $this->lightspeedURL . "{$account_id}/Register";
        $registers = $this->lightResponse($apikey, $account_id, $url, '', $refresh_token, $token_expires_at);
        $list = array();
        foreach ($registers->Register as $register) {
            if ($storeId === null || (int)$register->shopID === $storeId) {
                $list[(string)$register->registerID] = (string)$register->name;
            }
        }
        return $list;
    }

    /**
     * Get all shop list from lightspeed
     *
     * @param string $apikey
     * @param int|string $account_id
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return string[]
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    private function _shopList($apikey, $account_id, $refresh_token = '', $token_expires_at = '')
    {
        $url = $this->lightspeedURL . "{account_id}/Shop";
        $shop = $this->lightResponse($apikey, $account_id, $url, '', $refresh_token, $token_expires_at);
        $list = array();
        if ($shop->count() == 1) {
            $id = (string)$shop->Shop->shopID;
            $list[$id] = (string)$shop->Shop->name;
        } else {
            foreach ($shop->Shop as $value) {
                $list["$value->shopID"] = (string)$value->name;
            }
        }
        return $list;
    }

    /**
     * @param string $apikey
     * @param int|string $account_id
     * @param int|string $shopID
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return SimpleXMLElement
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     * @see https://developers.lightspeedhq.com/retail/endpoints/Shop/#get-single-shop
     */
    public function getShop($apikey, $account_id, $shopID, $refresh_token = '', $token_expires_at = '')
    {
        if (!$shopID) {
            throw new LightspeedApiException(LightspeedApiException::createXmlElement(
                400,
                'Bad Request',
                'Invalid ID: ' . json_encode($shopID),
                'InvalidResourceIDError'
            ));
        }
        $url = $this->lightspeedURL . '{account_id}/Shop/{id}?load_relations=["PriceLevel"]';
        return $this->lightResponse($apikey, $account_id, $url, $shopID, $refresh_token, $token_expires_at);
    }

    public function archiveProduct($apikey, $account_id, $itemId, $refresh_token = '', $token_expires_at = '')
    {
        $this->clearNewAccessTokenResponse($account_id);
        if ($refresh_token && CakeTime::isPast($token_expires_at)) {
            $refreshed = $this->refreshAccessToken($refresh_token, $account_id);

            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }
        
        $this->mosapi = new MOSAPICall($apikey, $account_id);
        $response = $this->mosapi->makeAPICall("Account.Item", "Delete", $itemId);
        CakeLog::debug(json_encode(compact('response')));
        if (($response->httpCode ?? '200') !== '200') {
            throw new LightspeedApiException($response);
        }

        return $response;
    }

    public function createItem($apikey, $account_id, $item, $refresh_token = '', $token_expires_at = ''): SimpleXMLElement
    {
            $refreshed = $this->getNewAccessTokenResponse($account_id);
            if (empty($refreshed) || ($refresh_token && CakeTime::isPast($token_expires_at))) {
                $refreshed = $this->refreshAccessToken($refresh_token, $account_id);

                $apikey = $refreshed['access_token'];
                $token_expires_at = $refreshed['expires_at'];
            }

        $this->mosapi = new MOSAPICall($apikey, $account_id);
        $xml = Array2XML::createXML('Item', $item)->saveXML();
        $response = $this->mosapi->makeAPICall("Account.Item", "Create", null, $xml);
        CakeLog::debug(json_encode(compact('response')));
        if (($response->httpCode ?? '200') !== '200') {
            throw new LightspeedApiException($response);
        }
        
        $this->setNewAccessTokenResponse($account_id, $this->getNewAccessTokenResponse($account_id, $refreshed));

        return $response;

    }

    protected function synchronizeProducts($products, $apikey, $account_id, $refresh_token = '', $token_expires_at = '')
    {
        $missingProducts = $this->getMissingProducts($products, $apikey, $account_id, $refresh_token, $token_expires_at);

        foreach ($missingProducts as $key => $product) {
            $newProduct = [
                'description' => $product['title'],
                'upc' => $product['upc'],
                'tax' => true,
                'Note' => [
                    'note' => 'Automatically imported by ShipEarly',
                    'isPublic' => false,
                ]
            ];


            $response = $this->createItem($apikey, $account_id, $newProduct, $refresh_token, $token_expires_at);
            $products[(int)$response->itemID] = $products[$key];
            $products[(int)$response->itemID]['inventoryId'] = (int)$response->itemID;
            $products[(int)$response->itemID]['isTax'] = (bool)$response->tax;
            unset($products[$key]);
        }

        return $products;
    }

    protected function getMissingProducts($products, $apikey, $account_id, $refresh_token = '', $token_expires_at = '')
    {
        // products without UPCs cannot be queried and should not come up as a missing product
        $productsToSearch = array_filter($products, function($product){
            return !empty($product['upc']) || !($product['isFeeProduct'] ?? false);
        });
        $productsByUpc = Hash::combine($productsToSearch, '{n}.upc', '{n}');
        $upcs = array_keys($productsByUpc);

        $refreshed = $this->getNewAccessTokenResponse($account_id);
        if (empty($refreshed) || ($refresh_token && CakeTime::isPast($token_expires_at))) {
            $refreshed = $this->refreshAccessToken($refresh_token, $account_id);

            $apikey = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }

        $posProducts = $this->getAllProductInventoriesByUpc($apikey, $account_id, $upcs, $refresh_token, $token_expires_at);
        $this->setNewAccessTokenResponse($account_id, $this->getNewAccessTokenResponse($account_id, $refreshed));

        $upcsInPos = [];
        foreach ($posProducts->Item as $item) {
            $upc = (string) $item->upc;
            $upcsInPos[] = $upc;
        }

        $upcsInPos = array_flip($upcsInPos);

        $missingProducts = array_filter($productsToSearch, function($product) use ($upcsInPos) {
            return !isset($upcsInPos[$product['upc']]);
        });

        return $missingProducts;
    }

    /**
     * @param string $apikey
     * @param int|string $account_id
     * @param int|string $shopID
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return SimpleXMLElement
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     * @see https://developers.lightspeedhq.com/retail/endpoints/Shop/#get-single-shop
     */
    public function getShopTaxRate($apikey, $account_id, $shopID, $refresh_token = '', $token_expires_at = '')
    {
        if (!$shopID) {
            throw new LightspeedApiException(LightspeedApiException::createXmlElement(
                400,
                'Bad Request',
                'Invalid ID: ' . json_encode($shopID),
                'InvalidResourceIDError'
            ));
        }
        $url = $this->lightspeedURL . '{account_id}/Shop/{id}?load_relations=["TaxCategory"]';
        return $this->lightResponse($apikey, $account_id, $url, $shopID, $refresh_token, $token_expires_at);
    }

    /**
     * Get the inventory count for the given product
     *
     * @param string $apikey
     * @param int|string $account_id
     * @param string|string[] $upcs
     * @param string $refresh_token
     * @param string $token_expires_at
     * @return int
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    public function getInventoryCount($apikey, $account_id, $upcs, $refresh_token = '', $token_expires_at = '')
    {
        $xml = $this->getAllProductInventoriesByUpc($apikey, $account_id, $upcs, $refresh_token, $token_expires_at);
        return (int)($xml->Item->ItemShops->ItemShop[0]->qoh ?? 0);
    }

    /**
     * Get all products data based on given product Id
     *
     * @param string $apikey
     * @param int|string $account_id
     * @param string|string[] $upcs
     * @return SimpleXMLElement
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    public function getAllProductInventoriesByUpc($apikey, $account_id, $upcs, $refresh_token = '', $token_expires_at = '')
    {
        $upcs = array_filter((array)$upcs);
        if (empty($upcs)) {
            return new SimpleXMLElement('<Items count="0"/>');
        }

        $upcQuery = implode('|', array_map(
            function($upc) {
                return urlencode("upc={$upc}");
            },
            $upcs
        ));
        $url = $this->lightspeedURL . '{account_id}/Item?load_relations=["ItemShops"]&or=' . $upcQuery;

        return $this->lightResponse($apikey, $account_id, $url, '', $refresh_token, $token_expires_at);
    }

    public function getAllProductInventories($apikey, $account_id, $refresh_token = '', $token_expires_at = ''){
        $url = $this->lightspeedURL . '{account_id}/Item?load_relations=["ItemShops"]';
        return $this->lightResponse($apikey, $account_id, $url, '', $refresh_token, $token_expires_at, 0, true);
    }

    public function countAllProducts($apikey, $account_id, $refresh_token = '', $token_expires_at = ''): int
    {
        $url = $this->lightspeedURL . '{account_id}/Item?load_relations=["ItemShops"]&count=1';
        $count = (int)$this->lightResponse($apikey, $account_id, $url, '', $refresh_token, $token_expires_at)['count']->__toString();
        return $count;
    }

    public function nextPage($prevResponse, $apikey, $account_id, $refresh_token = '', $token_expires_at = ''){
        if(empty($prevResponse['next'])){
            return null;
        }
        return $this->lightResponse($apikey, $account_id, $prevResponse['next'], '', $refresh_token, $token_expires_at, 0, true);
    }

    /**
     * @param string $access_token
     * @param int|string $account_id
     * @param string $url
     * @param string $id
     * @param string $refresh_token
     * @param string $token_expires_at
     * @param int $retries Number for recursive calls
     * @param bool $paginate true to paginate results, false to receive a single result set.
     * @return SimpleXMLElement
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    public function lightResponse($access_token, $account_id, $url, $id = '', $refresh_token = '', $token_expires_at = '', $retries = 0, $paginate = false)
    {
        $method = 'GET';

        $variable_arr = [
            '{account_id}' => $account_id,
            '{id}' => $id,
        ];
        foreach ($variable_arr as $key => $value) {
            $url = str_replace($key, $value, $url);
        }

        $this->clearNewAccessTokenResponse($account_id);

        $sendRequest = function($url, $access_token, $refresh_token, $token_expires_at, $account_id, $retries, $method) {
            while (true) {
                try {
                    if ($refresh_token && CakeTime::isPast($token_expires_at)) {
                        $refreshed = $this->refreshAccessToken($refresh_token, $account_id);

                        $access_token = $refreshed['access_token'];
                        $token_expires_at = $refreshed['expires_at'];
                    }
                    $lastResponseHeaders = $this->getLastResponseHeaders($account_id);
                    $sleepSeconds = $this->_calculateSleepTime($method, $lastResponseHeaders);
                    if ($sleepSeconds > 0.0) {
                        CakeLog::info(sprintf('Approaching rate limit %s. Waiting %.1f seconds...', $lastResponseHeaders['x-ls-api-bucket-level'], $sleepSeconds));
                        $this->sleep($sleepSeconds);
                    }

                    return $this->_sendApiRequest($url, $access_token);
                } catch (LightspeedApiException $e) {
                    if ($retries <= 1 && (in_array($e->getCode(), ['429', '503', '28']) || $e->getHttpMessage() == 'Service Unavailable')) {
                        if($e->getCode() == 28){
                            $sleepSeconds = 5;
                        } else {
                            $sleepSeconds = $this->_calculateSleepTime($method, $this->getLastResponseHeaders($account_id));
                        }
                        CakeLog::warning(sprintf('%d Error on attempt %d. Retrying after %.1f seconds...', $e->getCode(), $retries, $sleepSeconds));
                        $this->sleep($sleepSeconds);

                        $retries += 1;
                    } else {
                        throw $e;
                    }
                }
            }
        };

        $fullResponse = $sendRequest($url, $access_token, $refresh_token, $token_expires_at, $account_id, $retries, $method);
        $response = $fullResponse;
        $fullResponseDom = dom_import_simplexml($fullResponse);

        //If $paginate = true, pagination is handled by the caller, otherwise collect pages into one result.
        if(!$paginate){
            while ($response['next'] != '') {
                $response = $sendRequest((string)$response['next'], $access_token, $refresh_token, $token_expires_at, $account_id, $retries, $method);
                if ($response->count() == 1) {
                    $this->appendNodes($fullResponseDom, $response->{$response->children()->getName()});
                } else {
                    foreach ($response->{$response->children()->getName()} as $item) {
                        $this->appendNodes($fullResponseDom, $item);
                    }
                }
            }
        }

        return $fullResponse;
    }

    /**
     * @param string $method HTTP request method: GET, POST, PUT, DELETE.
     * @param array $headers HTTP response headers from previous request.
     * @return float Seconds to sleep.
     * @see https://developers.lightspeedhq.com/retail/introduction/ratelimits
     */
    private function _calculateSleepTime(string $method, array $headers): float
    {
        $dateTime = DateTime::createFromFormat(DATE_RFC7231, $headers['date'] ?? '');
        $timestamp = ($dateTime) ? $dateTime->getTimestamp() : 0;
        $elapsedTime = ($timestamp > 0) ? (int)max($this->time() - $timestamp, 0) : 0;

        // Prioritize retry-after over leaky bucket
        $retryAfter = (float)($headers['retry-after'] ?? 0.0);
        if ($retryAfter > 0.0) {
            return (float)max($retryAfter - $elapsedTime, 0.0);
        }

        $requiredHeaderNames = [
            'x-ls-api-bucket-level',
            'x-ls-api-drip-rate',
        ];
        $missingHeaderNames = array_diff_key(array_flip($requiredHeaderNames), $headers);
        if ($missingHeaderNames) {
            return 0.0;
        }

        $nextDrop = 1;

        if (in_array(strtoupper($method), ['PUT', 'POST', 'DELETE'], true)) {
            $nextDrop = 10;
        }

        $bucketParts = explode('/', $headers['x-ls-api-bucket-level'], 2);
        $bucketLevel = (float)$bucketParts[0];
        $bucketLimit = (float)$bucketParts[1];
        $dripRate = (float)$headers['x-ls-api-drip-rate'];

        // Allow room for error from race conditions
        //TODO Some sort of sync delay on response headers would be better
        $toleranceBuffer = 10;

        $overflow = $bucketLevel + $nextDrop - ($bucketLimit - $toleranceBuffer);
        $dripTime = ($dripRate > 0.0) ? $overflow / $dripRate : 0.0;

        return (float)max($dripTime - $elapsedTime, 0.0);
    }

    /**
     * @param string $url
     * @param string $access_token
     * @return SimpleXMLElement
     * @throws LightspeedApiException
     */
    protected function _sendApiRequest($url, $access_token)
    {
        CakeLog::debug(json_encode(['request' => compact('url')]));

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $access_token,
                'Content-Type: application/xml',
            ),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CONNECTTIMEOUT => 30,
        ));

        $responseHeaders = [];
        curl_setopt($curl, CURLOPT_HEADERFUNCTION, function($_, $header) use (&$responseHeaders) {
            $len = strlen($header);

            if (!$responseHeaders) {
                $responseHeaders['status-line'] = trim($header);
            } else {
                $headerParts = explode(':', $header, 2);
                if (count($headerParts) === 2) {
                    $responseHeaders[strtolower(trim($headerParts[0]))] = trim($headerParts[1]);
                }
            }

            return $len;
        });

        try{
            $response = simplexml_load_string($this->_curlExec($curl));
        } catch(CurlException $e){
            CakeLog::warning($e);
            throw new LightspeedApiException($e->getMessage(), $e->getCode(), $e);
        }

        $this->setLastResponseHeaders($responseHeaders);

        if (($response->httpCode ?? '200') !== '200') {
            throw new LightspeedApiException($response);
        }

        return $response;
    }

    private function setLastResponseHeaders(array $responseHeaders): void
    {
        if (empty($responseHeaders['x-ls-acct-id'])) {
            return;
        }

        $account_id = $responseHeaders['x-ls-acct-id'];

        $cachedHeaders = (array)Cache::read('lightspeed_response_headers', 'lightspeed');
        if ($cachedHeaders) {
            $this->lastResponseHeaders = $cachedHeaders;
        }

        $this->lastResponseHeaders[$account_id] = $responseHeaders;
        Cache::write('lightspeed_response_headers', $this->lastResponseHeaders, 'lightspeed');
    }

    private function getLastResponseHeaders($account_id): array
    {
        $cachedHeaders = (array)Cache::read('lightspeed_response_headers', 'lightspeed');
        if ($cachedHeaders) {
            $this->lastResponseHeaders = $cachedHeaders;
        }

        return (array)($this->lastResponseHeaders[$account_id] ?? []);
    }

    public function clearLastResponseHeaders($account_id = null): void
    {
        if ($account_id === null) {
            $this->lastResponseHeaders = [];
            Cache::delete('lightspeed_response_headers', 'lightspeed');
        } else {
            $cachedHeaders = (array)Cache::read('lightspeed_response_headers', 'lightspeed');
            if ($cachedHeaders) {
                $this->lastResponseHeaders = $cachedHeaders;
            }

            unset($this->lastResponseHeaders[$account_id]);
            Cache::write('lightspeed_response_headers', $this->lastResponseHeaders, 'lightspeed');
        }
    }

    protected function getOAuthStateCacheKey(string $token): string
    {
        return 'lightspeed_oauth_' . $token;
    }

    /**
     * Get the OAuth authorize URL for the POS API.
     *
     * @param string $stateToken
     * @return string The URL to redirect the user to
     * @see https://developers.lightspeedhq.com/retail/authentication/access-token/
     */
    public function getAuthorizeUrl(string $stateToken): string
    {
        $query = http_build_query([
            'response_type' => 'code',
            'client_id' => LIGHTSPEED_CLIENT_ID,
            'scope' => 'employee:all',
            'state' => $stateToken,
        ]);

        return "https://cloud.lightspeedapp.com/oauth/authorize.php?{$query}";
    }

    /**
     * Get OAuth access token data from the POS API.
     *
     * @param string $code The authorization code
     * @return array The access token data
     * @throws CurlException
     * @see http://developers.lightspeedhq.com/retail/authentication/access-token/
     */
    public function getAccessToken(string $code): array
    {
        $postFields = array(
            'client_id' => LIGHTSPEED_CLIENT_ID,
            'client_secret' => LIGHTSPEED_CLIENT_SECRET,
            'code' => $code,
            'grant_type' => 'authorization_code'
        );

        $curl = curl_init();

        curl_setopt_array($curl, array(
            // Opts from official docs
            CURLOPT_URL => $this->tokenURL,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $postFields,
            // Opts added by us
            //CURLOPT_POST => 1, // Results in 'invalid_request' error
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
        ));

        $response = $this->_curlExec($curl);

        return (array)json_decode($response, true);
    }

    /**
     * Get the POS account ID for a set of OAuth access token response data.
     *
     * @param array $accessTokenData The access token data
     * @return string The account ID
     * @throws LightspeedApiException
     */
    public function getAccessTokenAccountId(array $accessTokenData): string
    {
        $session = $this->getAccessTokenSession((string)$accessTokenData['access_token']);

        return (string)$session['systemCustomerID'];
    }

    /**
     * @param string $refreshToken
     * @param int|string $account_id Required for accessing the response from getNewAccessTokenResponse
     * @return array|null
     * @throws CurlException
     * @throws LightspeedOauthException
     * @see http://developers.lightspeedhq.com/retail/authentication/refresh-token/
     */
    public function refreshAccessToken($refreshToken, $account_id = null)
    {
        $postFields = array(
            'refresh_token' => $refreshToken,
            'client_secret' => LIGHTSPEED_CLIENT_SECRET,
            'client_id' => LIGHTSPEED_CLIENT_ID,
            'grant_type' => 'refresh_token'
        );

        $curl = curl_init();

        curl_setopt_array($curl, array(
            // Opts from official docs
            CURLOPT_URL => $this->tokenURL,
            CURLOPT_POST => sizeof($postFields),
            CURLOPT_POSTFIELDS => $postFields,
            // Opts added by us
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
        ));

        try{
            $response = json_decode($this->_curlExec($curl), true);
        } catch(CurlException $e){
            CakeLog::warning($e);
            throw new LightspeedApiException($e->getMessage(), $e->getCode(), $e);
        }

        if (!array_key_exists('access_token', $response)) {
            throw new LightspeedOauthException($response);
        }

        // Derive the date value to be saved in User.vend_access_token_expires
        $response['expires_at'] = format_datetime("+{$response['expires_in']} SECONDS");

        $this->setNewAccessTokenResponse($account_id, $response);

        return $response;
    }

    /**
     * Gives information about the current session (based on the access token), including the employee and access rights.
     *
     * @param string $access_token
     * @return array Session details for the access token
     * @throws LightspeedApiException
     * @see https://developers.lightspeedhq.com/retail/endpoints/Session/
     */
    public function getAccessTokenSession(string $access_token): array
    {
        $url = "https://api.lightspeedapp.com/API/Session";

        $response = $this->_sendApiRequest($url, $access_token);

        return (array)json_decode(json_encode($response), true);
    }

    /**
     * If a request required refreshing a user's access token, it will be saved
     * here so that the user can update their saved token.
     *
     * @param int|string $account_id
     * @param array $default_response Optionally provide a default return value if no new response is set
     * @return array Response from access token request
     * @see refreshAccessToken
     */
    public function getNewAccessTokenResponse($account_id, array $default_response = array())
    {
        return !empty($this->new_access_token_response[$account_id])
            ? $this->new_access_token_response[$account_id]
            : $default_response;
    }

    /**
     * @param int|string $account_id
     * @param array $response
     */
    private function setNewAccessTokenResponse($account_id, array $response)
    {
        if ($account_id) {
            $this->new_access_token_response[$account_id] = $response;
        }
    }

    private function clearNewAccessTokenResponse($account_id)
    {
        unset($this->new_access_token_response[$account_id]);
    }

    protected function appendNodes(DOMElement $destinationXml, SimpleXMLElement $nodesToAppend)
    {
        // appending nodes to $fullResponseDom via `appendNodes()` modifies the original
        // SimpleXMLElement that $destinationXml was built from.

        $fromDom = dom_import_simplexml($nodesToAppend);
        $destinationXml->appendChild($destinationXml->ownerDocument->importNode($fromDom, true));
    }
}
