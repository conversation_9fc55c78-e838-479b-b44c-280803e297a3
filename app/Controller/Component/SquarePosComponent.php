<?php

use Ship<PERSON>arlyApp\Controller\Component\OAuth\OAuthConnectInterface;
use ShipEarlyApp\Controller\Component\OAuth\OAuthStateTrait;
use ShipEarlyApp\Lib\Globals\AppGlobalMethodsTrait;
use Square\ApiHelper;
use Square\Environment;
use Square\Exceptions\ApiException;
use Square\Models\Address;
use Square\Models\BatchRetrieveInventoryCountsRequest;
use Square\Models\BatchRetrieveInventoryCountsResponse;
use Square\Models\CatalogObject;
use Square\Models\CatalogObjectType;
use Square\Models\CreateCustomerRequest;
use Square\Models\CreateCustomerResponse;
use Square\Models\Customer;
use Square\Models\InventoryCount;
use Square\Models\ListCatalogResponse;
use Square\Models\ListLocationsResponse;
use Square\Models\ObtainTokenRequest;
use Square\Models\ObtainTokenResponse;
use Square\Models\RenewTokenRequest;
use Square\Models\RenewTokenResponse;
use Square\Models\RetrieveCatalogObjectResponse;
use Square\Models\RevokeTokenRequest;
use Square\Models\RevokeTokenResponse;
use Square\Models\TaxInclusionType;
use Square\SquareClient;

App::uses('Component', 'Controller');

/**
 * Class SquarePosComponent.
 */
class SquarePosComponent extends Component implements OAuthConnectInterface
{
    use AppGlobalMethodsTrait, OAuthStateTrait;

    protected function getOAuthStateCacheKey(string $token): string
    {
        return 'squarepos_oauth_' . $token;
    }

    /**
     * Get the OAuth authorize URL for the POS API.
     *
     * @param string $stateToken
     * @return string The URL to redirect the user to
     * @see https://developer.squareup.com/reference/square/o-auth-api/authorize
     */
    public function getAuthorizeUrl(string $stateToken): string
    {
        return ApiHelper::cleanUrl(
            $this->newClient()->getBaseUri() . '/oauth2/authorize?' . http_build_query(array_filter([
                'client_id' => SQUARE_APPLICATION_ID,
                'scope' => implode(' ', [
                    'MERCHANT_PROFILE_READ',
                    'CUSTOMERS_READ',
                    'CUSTOMERS_WRITE',
                    'ITEMS_READ',
                    'ORDERS_READ',
                    'INVENTORY_READ',
                ]),
                'session' => 'false',
                'state' => $stateToken,
            ]), '', '&', PHP_QUERY_RFC3986)
        );
    }

    /**
     * Get OAuth access token data from the POS API.
     *
     * @param string $code The authorization code
     * @return array The access token data
     * @throws ApiException
     * @throws HttpException
     * @see https://developer.squareup.com/reference/square/o-auth-api/obtain-token
     */
    public function getAccessToken(string $code): array
    {
        $request = new ObtainTokenRequest(
            SQUARE_APPLICATION_ID,
            SQUARE_APPLICATION_SECRET,
            'authorization_code'
        );
        $request->setCode($code);

        // Last API version before refresh tokens are required for renewal
        $response = $this->newClient(['squareVersion' => '2019-02-13'])
            ->getOAuthApi()
            ->obtainToken($request);
        if ($response->isError()) {
            throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
        }
        /** @var ObtainTokenResponse $result */
        $result = $response->getResult();
        if (!$result) {
            return [];
        }

        return $this->modelToArray($result);
    }

    /**
     * Get the POS account ID for a set of OAuth access token response data.
     *
     * @param array $accessTokenData The access token data
     * @return string The account ID
     */
    public function getAccessTokenAccountId(array $accessTokenData): string
    {
        return (string)$accessTokenData['merchant_id'];
    }

    /**
     * @param string $apiPassword
     * @return array|null
     * @throws ApiException
     * @throws HttpException
     */
    public function renewAccessToken(string $apiPassword): ?array
    {
        $request = new RenewTokenRequest();
        $request->setAccessToken($apiPassword);

        $response = $this->newClient(['accessToken' => $apiPassword])
            ->getOAuthApi()
            ->renewToken(SQUARE_APPLICATION_ID, $request, 'Client ' . SQUARE_APPLICATION_SECRET);
        if ($response->isError()) {
            throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
        }
        /** @var RenewTokenResponse $result */
        $result = $response->getResult();
        if (!$result) {
            return null;
        }

        return $this->modelToArray($result);
    }

    /**
     * @param string $apiPassword
     * @return array|null
     * @throws ApiException
     * @throws HttpException
     */
    public function revokeAccessToken(string $apiPassword): ?array
    {
        $request = new RevokeTokenRequest();
        $request->setClientId(SQUARE_APPLICATION_ID);
        $request->setAccessToken($apiPassword);

        $response = $this->newClient(['accessToken' => $apiPassword])
            ->getOAuthApi()
            ->revokeToken($request, 'Client ' . SQUARE_APPLICATION_SECRET);
        if ($response->isError()) {
            throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
        }
        /** @var RevokeTokenResponse $result */
        $result = $response->getResult();
        if (!$result) {
            return null;
        }

        return $this->modelToArray($result);
    }

    /**
     * @param string $apiPassword
     * @return array
     * @throws ApiException
     * @throws HttpException
     */
    public function listLocations(string $apiPassword): array
    {
        $response = $this->newClient(['accessToken' => $apiPassword])
            ->getLocationsApi()
            ->listLocations();
        if ($response->isError()) {
            throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
        }
        /** @var ListLocationsResponse $result */
        $result = $response->getResult();

        return $this->modelToArray($result->getLocations());
    }

    /**
     * @param string $apiPassword
     * @param string $apiUser
     * @return array
     * @throws ApiException
     * @throws HttpException
     */
    public function listItems(string $apiPassword): array
    {
        $items = $this->listAllItems($apiPassword);

        return $this->modelToArray($items);
    }

    /**
     * @param string $apiPassword
     * @return CatalogObject[]
     * @throws ApiException
     * @throws HttpException
     */
    private function listAllItems(string $apiPassword): array
    {
        /** @var CatalogObject[] $items */
        $items = [];
        $cursor = null;
        $catalogueApi = $this->newClient(['accessToken' => $apiPassword])->getCatalogApi();
        do {
            $response = $catalogueApi->listCatalog($cursor, CatalogObjectType::ITEM);
            if ($response->isError()) {
                throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
            }
            /** @var ListCatalogResponse $result */
            $result = $response->getResult();

            $items = array_merge($items, $result->getObjects() ?? []);

            $cursor = $response->getCursor();
        } while ($cursor !== null);

        return $items;
    }

    /**
     * @param string $apiPassword
     * @param string $itemId
     * @return array|null
     * @throws ApiException
     * @throws HttpException
     */
    public function findItem(string $apiPassword, string $itemId): ?array
    {
        $response = $this->newClient(['accessToken' => $apiPassword])
            ->getCatalogApi()
            ->retrieveCatalogObject($itemId, true);
        if ($response->isError()) {
            throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
        }
        /** @var RetrieveCatalogObjectResponse $result */
        $result = $response->getResult();
        if (!$result) {
            return null;
        }

        return $this->_formatItem($result);
    }

    private function _formatItem(RetrieveCatalogObjectResponse $result): array
    {
        $item = $this->modelToArray($result->getObject()->getItemData());
        // implicitly filters out non tax CatalogObjects
        $fees = array_column($this->modelToArray($result->getRelatedObjects()), 'tax_data');

        $item['images'] = [];
        $item['variations'] = Hash::combine($item['variations'], '{n}.id', '{n}.item_variation_data');

        $totalTaxRate = 0.00;
        $includedTaxRate = 1.00;
        foreach ($fees as $fee) {
            if (!$fee['enabled']) {
                continue;
            }
            $totalTaxRate += $fee['percentage'] / 100;
            if ($fee['inclusion_type'] == TaxInclusionType::INCLUSIVE) {
                $includedTaxRate += $fee['percentage'];
            }
        }
        $item['total_tax_rate'] = number_format($totalTaxRate, 2, '.', '');

        foreach ($item['variations'] as $id => $variation) {
            $basePrice = $variation['price_money']['amount'];
            $basePrice /= $includedTaxRate;
            $basePrice /= 100;
            $item['variations'][$id]['price_money']['amount'] = number_format($basePrice, 2, '.', '');
            $item['variations'][$id]['price_money']['total_tax'] = number_format($basePrice * $totalTaxRate, 2, '.', '');
        }

        return $this->modelToArray($item);
    }

    /**
     * @param string $apiPassword
     * @param string $apiUser
     * @return array
     * @throws ApiException
     * @throws HttpException
     */
    public function listInventoriesByVariantId(string $apiPassword, string $apiUser): array
    {
        /** @var InventoryCount[] $inventoryCounts */
        $inventoryCounts = [];
        $cursor = null;
        $request = new BatchRetrieveInventoryCountsRequest();
        $request->setLocationIds([$apiUser]);
        $inventoryApi = $this->newClient(['accessToken' => $apiPassword])->getInventoryApi();

        do {
            $request->setCursor($cursor);
            $response = $inventoryApi->batchRetrieveInventoryCounts($request);
            if ($response->isError()) {
                throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
            }
            /** @var BatchRetrieveInventoryCountsResponse $result */
            $result = $response->getResult();

            $inventoryCounts = array_merge($inventoryCounts, $result->getCounts() ?? []);

            $cursor = $response->getCursor();
        } while ($cursor !== null);

        return Hash::combine($this->modelToArray($inventoryCounts), '{n}.catalog_object_id', '{n}.quantity');
    }

    /**
     * @param string $apiPassword
     * @param object $shipping
     * @param array $orderInfo
     * @return Customer|null
     * @throws ApiException
     * @throws HttpException
     */
    public function createCustomerFromEcommerce(string $apiPassword, object $shipping, array $orderInfo): ?Customer
    {
        $address = new Address();
        $address->setAddressLine1($orderInfo['shipping_address1']);
        $address->setAddressLine2(!empty($orderInfo['shipping_address2']) ? $orderInfo['shipping_address2'] : null);
        $address->setLocality($shipping->city);
        $address->setAdministrativeDistrictLevel1($shipping->region);
        $address->setPostalCode($shipping->postcode);
        $address->setCountry(strtoupper($shipping->country_id));

        $request = new CreateCustomerRequest();
        $request->setGivenName($shipping->firstname);
        $request->setFamilyName($shipping->lastname);
        $request->setCompanyName(!empty($shipping->company) ? $shipping->company : null);
        $request->setEmailAddress($shipping->email);
        $request->setAddress($address);
        $request->setPhoneNumber($shipping->telephone);
        $request->setReferenceId(BASE_PATH . "customer/{$orderInfo['customerID']}/orders");
        $request->setNote('ShipEarly order ' . $orderInfo['orderID']);

        return $this->createCustomer($apiPassword, $request);
    }

    /**
     * @param string $apiPassword
     * @param CreateCustomerRequest $request
     * @return Customer|null
     * @throws ApiException
     * @throws HttpException
     */
    protected function createCustomer(string $apiPassword, CreateCustomerRequest $request): ?Customer
    {
        $response = $this->newClient(['accessToken' => $apiPassword])
            ->getCustomersApi()
            ->createCustomer($request);
        if ($response->isError()) {
            throw new HttpException(json_encode($response->getErrors()), $response->getStatusCode());
        }
        /** @var CreateCustomerResponse $result */
        $result = $response->getResult();

        return $result->getCustomer();
    }

    /**
     * @param JsonSerializable|JsonSerializable[]|null $model
     * @return array
     */
    private function modelToArray($model): array
    {
        return (array)json_decode(json_encode($model), true);
    }

    protected function newClient(array $options = []): SquareClient
    {
        return new SquareClient(['environment' => $this->getEnvironment()] + $options);
    }

    /**
     * Wrapper that can be overridden for sandbox testing.
     *
     * @return string
     */
    protected function getEnvironment(): string
    {
        return Environment::PRODUCTION;
    }
}
