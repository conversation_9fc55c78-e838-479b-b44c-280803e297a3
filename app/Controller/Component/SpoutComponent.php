<?php
App::uses('Component', 'Controller');
App::uses('Configure', 'Core');
App::uses('Shim', 'Shim.Lib');

use Box\Spout\Common\Entity\Row;
use Box\Spout\Common\Exception\InvalidArgumentException;
use Box\Spout\Common\Exception\IOException;
use Box\Spout\Common\Exception\UnsupportedTypeException;
use Box\Spout\Common\Type;
use Box\Spout\Reader\Common\Creator\ReaderEntityFactory;
use Box\Spout\Reader\Common\Creator\ReaderFactory;
use Box\Spout\Reader\Exception\ReaderNotOpenedException;
use Box\Spout\Reader\ReaderInterface;
use Box\Spout\Reader\SheetInterface;
use Box\Spout\Writer\Common\Creator\Style\StyleBuilder;
use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Box\Spout\Writer\Exception\WriterAlreadyOpenedException;
use Box\Spout\Writer\Exception\WriterNotOpenedException;
use Box\Spout\Writer\WriterInterface;

/**
 * Spout Component.
 *
 * Wraps the box/spout library.
 */
class SpoutComponent extends Component
{
    const DEFAULT_READ_TYPE = Type::XLSX;
    const DEFAULT_WRITE_TYPE = Type::CSV;

    /**
     * @var WriterInterface
     */
    protected $writer;

    /**
     * Read a spreadsheet file and return its contents as a table map.
     *
     * @param string $file Path of the file to open
     * @param string|null $fileName Actual file name with ext for determining file type
     * @return array Cell values mapped by headers for each row
     * @throws IOException
     * @throws ReaderNotOpenedException
     */
    public function extractTableData(string $file, string $fileName = null): array
    {
        $tableData = $this->readSpreadsheetFile($file, $fileName);

        return $this->mapTableData($tableData);
    }

    /**
     * Read a spreadsheet file and return its contents as a 2D array.
     *
     * The spreadsheet is streamed to keep memory usage constant regardless of file size.
     *
     * @param string $file Path of the file to open
     * @param string|null $fileName Actual file name with ext for determining file type
     * @return array 2D array of cell values for each row
     * @throws IOException
     * @throws ReaderNotOpenedException
     */
    protected function readSpreadsheetFile(string $file, ?string $fileName = null): array
    {
        $tableData = [];

        $reader = $this->createReader($fileName ?: $file);
        $reader->open($file);

        try {
            /** @var SheetInterface $sheet */
            foreach ($reader->getSheetIterator() as $sheet) {
                /** @var Row $row */
                foreach ($sheet->getRowIterator() as $row) {
                    $tableData[] = array_map(
                        function($cellData) {
                            return is_string($cellData) ? trim($cellData) : $cellData;
                        },
                        $row->toArray()
                    );
                }
            }
        } finally {
            $reader->close();
        }

        return $tableData;
    }

    protected function createReader(string $fileName): ReaderInterface
    {
        try {
            return ReaderEntityFactory::createReaderFromFile($fileName);
        } catch (UnsupportedTypeException $e) {
            try {
                return ReaderFactory::createFromType(static::DEFAULT_READ_TYPE);
            } catch (UnsupportedTypeException $e) {
                // should never happen
                throw new RuntimeException($e->getMessage(), $e->getCode(), $e);
            }
        }
    }

    /**
     * Convert a 2D array of spreadsheet cells to a table map.
     *
     * @param array $tableData
     * @return array
     */
    protected function mapTableData(array $tableData): array
    {
        $tableHeaders = array_filter((array)array_shift($tableData));
        if (!$tableHeaders || !$tableData) {
            return [];
        }

        // Key by spreadsheet row number starting from 1 and offset by header row
        $firstRow = 2;
        $finalRow = $firstRow + (count($tableData) - 1);
        $tableData = array_combine(range($firstRow, $finalRow), $tableData);

        return array_map(
            function($rowData) use ($tableHeaders) {
                $rowMap = [];
                foreach ($tableHeaders as $index => $header) {
                    $rowMap[$header] = $rowData[$index] ?? '';
                }

                return $rowMap;
            },
            $tableData
        );
    }

    /**
     * @param string $fileName
     * @param callable $callback
     * @throws IOException
     * @throws WriterAlreadyOpenedException
     */
    public function doWithOpenWriter(string $fileName, callable $callback): void
    {
        $this->openWriter($fileName);

        try {
            call_user_func($callback, $this->writer);
        } finally {
            $this->closeWriter();
        }
    }

    /**
     * @param string $fileName
     * @return $this
     * @throws IOException
     * @throws WriterAlreadyOpenedException
     */
    public function openWriter(string $fileName): SpoutComponent
    {
        if ($this->isWriterOpened()) {
            throw new WriterAlreadyOpenedException('Writer instance is already open.');
        }

        $defaultStyle = (new StyleBuilder())
            ->setFontName('Calibri')
            ->setFontSize(12)
            ->build();

        $this->writer = $this
            ->createWriter($fileName)
            ->openToBrowser($fileName)
            ->setDefaultRowStyle($defaultStyle);

        return $this;
    }

    public function closeWriter(): void
    {
        if (!$this->isWriterOpened()) {
            return;
        }

        $headersSentBeforeClose = headers_sent();

        $this->writer->close();
        $this->writer = null;

        if (!$headersSentBeforeClose && headers_sent()) {
            Configure::write(Shim::MONITOR_HEADERS, false);
        }
    }

    public function isWriterOpened(): bool
    {
        return (bool)$this->writer;
    }

    protected function createWriter(string $fileName): WriterInterface
    {
        try {
            return WriterEntityFactory::createWriterFromFile($fileName);
        } catch (UnsupportedTypeException $e) {
            try {
                return WriterEntityFactory::createWriter(static::DEFAULT_WRITE_TYPE);
            } catch (UnsupportedTypeException $e) {
                // should never happen
                throw new RuntimeException($e->getMessage(), $e->getCode(), $e);
            }
        }
    }

    /**
     * @param mixed[] $cells
     * @return $this
     * @throws IOException
     * @throws WriterNotOpenedException
     */
    public function addHeaderRow(array $cells = []): SpoutComponent
    {
        $headerStyle = (new StyleBuilder())
            ->setFontName('Cambria')
            ->setFontBold()
            ->build();

        $this->getWriter()->addRow(WriterEntityFactory::createRowFromArray($cells, $headerStyle));

        return $this;
    }

    /**
     * @param mixed[] $cells
     * @return $this
     * @throws IOException
     * @throws WriterNotOpenedException
     */
    public function addRow(array $cells = []): SpoutComponent
    {
        $this->getWriter()->addRow(WriterEntityFactory::createRowFromArray($cells));

        return $this;
    }

    /**
     * @param mixed[][] $rows
     * @return $this
     * @throws IOException
     * @throws InvalidArgumentException
     * @throws WriterNotOpenedException
     */
    public function addRows(array $rows = []): SpoutComponent
    {
        $this->getWriter()->addRows(array_map([WriterEntityFactory::class, 'createRowFromArray'], $rows));

        return $this;
    }

    /**
     * @return WriterInterface
     * @throws WriterNotOpenedException
     */
    protected function getWriter(): WriterInterface
    {
        if (!$this->isWriterOpened()) {
            throw new WriterNotOpenedException('The writer must be opened before performing this action.');
        }

        return $this->writer;
    }
}
