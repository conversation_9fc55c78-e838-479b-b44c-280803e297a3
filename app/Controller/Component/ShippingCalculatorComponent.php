<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('B2bShippingRate', 'Model');
App::uses('Currency', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('PercentSource', 'Utility');

/**
 * Class ShippingCalculatorComponent
 *
 * @property ShippoComponent $Shippo
 *
 * @property B2bShippingRate $B2bShippingRate
 * @property Contact $Contact
 * @property Country $Country
 * @property Discount $Discount
 * @property PriceBasedShippingRate $PriceBasedShippingRate
 * @property Product $Product
 * @property ShippingZone $ShippingZone
 * @property State $State
 * @property User $User
 * @property UserAddress $UserAddress
 * @property UserShippingCarrierRate $UserShippingCarrierRate
 * @property UserShippingPackage $UserShippingPackage
 * @property WeightBasedShippingPrice $WeightBasedShippingPrice
 */
class ShippingCalculatorComponent extends AppComponent
{
    public $components = array('Shippo');

    public $uses = array(
        'B2bShippingRate',
        'Contact',
        'Country',
        'Discount',
        'PriceBasedShippingRate',
        'Product',
        'ShippingZone',
        'State',
        'User',
        'UserAddress',
        'UserShippingCarrierRate',
        'UserShippingPackage',
        'WeightBasedShippingPrice',
    );

    /**
     * @param int $brandId
     * @param string $currencyCode
     * @param array $address
     * @param array $products
     * @param array $discount
     * @return array<int, array<string, mixed>> List of shipping rate object arrays sorted by amount ASC
     */
    public function getRetailerShippingRates(int $brandId, string $currencyCode, array $address, array $products, array $discount): array
    {
        $retailerShippingZone = $this->ShippingZone->findShippingRatesByState($brandId, $address['country'], $address['province']);
        if (empty($retailerShippingZone['ShippingZone']['id'])) {
            $message = 'Shipping zone not found';
            CakeLog::warning(json_encode(compact('message', 'address')));

            return [];
        }
        $zoneId = $retailerShippingZone['ShippingZone']['id'];

        $retailerShippingZone = $this->_filterZoneRates($retailerShippingZone, $products);

        $shippingRates = array_map(
            function($rate) use ($currencyCode) {
                return [
                    'id' => $rate['id'],
                    'type' => $rate['type'],
                    'name' => $rate['name'],
                    'code' => generate_slug($rate['name']),
                    'amount' => $rate['amount'],
                    'discount' => $rate['discount'],
                    'currency' => $currencyCode,
                    'box_weight' => null,
                ];
            },
            $this->_mergeShippingRateSets($retailerShippingZone, $discount)
        );

        $totalProductWeight = array_sum(array_map(function($product) {
            $productWeight = convertWeightToLbs($product['Product']['weight'], $product['Product']['weight_unit']);
            return $productWeight * $product['Product']['quantity'];
        }, $products));

        $carrierRates = $this->_getCarrierShippingRates($brandId, $currencyCode, $zoneId, $address, $totalProductWeight);

        // Use carrier rates if the cheapest carrier rate is less than the cheapest ShipEarly rate
        if (
            empty($shippingRates[0]['id']) ||
            (!empty($carrierRates[0]['id']) && $carrierRates[0]['amount'] < $shippingRates[0]['amount'])
        ) {
            $shippingRates = $carrierRates;
        }


        return $shippingRates;
    }

    /**
     * @param array $shippingZoneWithRates
     * @param array $productsWithQuantity
     * @return array
     */
    private function _filterZoneRates(array $shippingZoneWithRates, array $productsWithQuantity)
    {
        $aliasGetRateValues = [
            'UnitBasedShippingRate' => function($product) {
                return $product['quantity'];
            },
            'PriceBasedShippingRate' => function($product) {
                return $product['quantity'] * $product['product_price'];
            },
            'WeightBasedShippingPrice' => function($product) {
                return $product['quantity'] * convertWeightToLbs($product['weight'], $product['weight_unit']);
            },
        ];
        foreach ($aliasGetRateValues as $alias => $getRateValue) {
            $shippingZoneWithRates[$alias] = array_map(
                function($shippingRate) use ($productsWithQuantity) {
                    $rateCategories = array_column($shippingRate['ShippingRateCategory'], 'product_category');
                    if ($rateCategories) {
                        $productsWithQuantity = $this->Product->filterTypes($productsWithQuantity, $rateCategories);
                    }
                    $shippingRate['Product'] = array_map(
                        function($product) {
                            return array_intersect_key($product, array_flip([
                                'id',
                                'productID',
                                'quantity',
                                'product_price',
                                'compare_at_price',
                                'weight',
                                'weight_unit',
                            ]));
                        },
                        array_column($productsWithQuantity, 'Product')
                    );
                    return $shippingRate;
                },
                $shippingZoneWithRates[$alias]
            );
            $shippingZoneWithRates[$alias] = array_filter($shippingZoneWithRates[$alias],
                function($shippingRate) use ($getRateValue) {
                    $totalRateValue = array_sum(array_map($getRateValue, $shippingRate['Product']));
                    return ($totalRateValue > 0 && $totalRateValue >= $shippingRate['min'] && $totalRateValue <= $shippingRate['max']);
                }
            );
        }

        if (!FEATURE_TOGGLE_CONSUMER_SHIPPING_UNIT_RATES) {
            $shippingZoneWithRates['UnitBasedShippingRate'] = [];
        }

        CakeLog::debug(json_encode(array_intersect_key($shippingZoneWithRates, array_flip(['WeightBasedShippingPrice', 'PriceBasedShippingRate']))));
        return $shippingZoneWithRates;
    }

    private function _mergeShippingRateSets(array $shippingZoneWithRates, array $discount): array
    {
        $typeAliases = [
            'unit' => 'UnitBasedShippingRate',
            'price' => 'PriceBasedShippingRate',
            'weight' => 'WeightBasedShippingPrice',
        ];

        $shippingRates = [];
        foreach ($typeAliases as $type => $alias) {
            $shippingRates = array_merge($shippingRates, array_map(
                function($rate) use ($type, $discount) {
                    if ($rate['amount_type'] === 'percent') {
                        $percentSource = ($rate['percent_source'] === PercentSource::COMPARE_AT_PRICE)
                            ? 'compare_at_price'
                            : 'product_price';
                        $totalPrice = array_sum(array_map(
                            function($product) use ($percentSource) {
                                return $product['quantity'] * ($product[$percentSource] ?? $product['product_price']);
                            },
                            $rate['Product']
                        ));
                        $rate['amount_type'] = 'flat';
                        $rate['amount'] = format_number($totalPrice * $rate['amount'] / 100);
                    }

                    $rate += ['free_shipping' => false];
                    $enableFreeFreight = false;
                    if ($discount) {
                        $enableFreeFreight = $discount['enable_free_freight'];
                    }
                    if ($rate['free_shipping'] || $enableFreeFreight) {
                        $rate['amount'] = '0.00';
                    }

                    $rate['discount'] = format_number($this->Discount->calculateShippingRateDiscount($discount, $rate));

                    // Group by sorted category sets so that each rate is only counted once for multiple categories
                    // The brand is responsible for setting up mutually exclusive rate category sets
                    $rate['group'] = generate_key(array_column($rate['ShippingRateCategory'], 'product_category'));

                    $rate['type'] = $type;
                    $rate['id'] = generate_key([$type, $rate['id']]);

                    return $rate;
                },
                $shippingZoneWithRates[$alias]
            ));
        }

        if (!empty($shippingRates)) {
            $shippingRates = sortByKey($shippingRates, 'free_shipping', 'desc');
            $shippingRates = sortByKey($shippingRates, 'amount', 'asc');
        }

        $shippingRates = $this->_combineShippingRateGroups($shippingRates);

        return $shippingRates;
    }

    private function _combineShippingRateGroups(array $sortedShippingRates): array
    {
        $groupedRates = Hash::combine($sortedShippingRates, '{n}.id', '{n}', '{n}.group');

        if (count($groupedRates) > 1) {
            // Free shipping takes precedence over summing grouped rates
            foreach ($sortedShippingRates as $rate) {
                if ($rate['amount'] <= 0.00) {
                    return [$rate];
                }
            }

            $lowestRatePerGroup = array_map('current', $groupedRates);

            $combinedAmount = array_sum(array_column($lowestRatePerGroup, 'amount'));
            $combinedDiscount = array_sum(array_column($lowestRatePerGroup, 'discount'));

            // Majority rate provides all other fields such as shippingName
            $majorityRate = array_reduce(
                $lowestRatePerGroup,
                function($selected, $rate) {
                    return (!isset($selected['amount']) || $rate['amount'] > $selected['amount'])
                        ? $rate
                        : $selected;
                },
                []
            );
            $majorityRate['amount'] = format_number($combinedAmount);
            $majorityRate['discount'] = format_number($combinedDiscount);

            $sortedShippingRates = [
                $majorityRate,
            ];
        }

        return $sortedShippingRates;
    }

    /**
     * @param int $brandId
     * @param string $currencyCode
     * @param int $zoneId
     * @param array $address
     * @param float $totalProductWeight
     * @return array carrier rates
     */
    protected function _getCarrierShippingRates($brandId, $currencyCode, $zoneId, $address, $totalProductWeight): array
    {
        $userCarriersByProvider = $this->UserShippingCarrierRate->listCarrierRatesByProvider($brandId, $zoneId);
        if (!$userCarriersByProvider) {
            // The brand is not using the carrier rates feature
            return [];
        }

        $COURIER_MAX_WEIGHT = floatval(COURIER_MAX_WEIGHT);
        if ($totalProductWeight > $COURIER_MAX_WEIGHT) {
            $message = 'Maximum weight exceeded';
            CakeLog::warning(json_encode(compact('message', 'totalProductWeight', 'COURIER_MAX_WEIGHT')));

            return [];
        }

        $toAddress = [
            'name' => trim($address['First_name'] . ' ' . $address['Last_name']),
            'company' => 'Home',
            'street1' => $address['address'],
            'city' => $address['city'],
            'state' => $address['regionCode'],
            'country' => $address['countryCode'],
            'zip' => $address['PostalCode'],
            'phone' => $address['phone'],
            'email' => $address['email'],
        ];
        $missing = array_keys(array_diff_key($toAddress, array_filter($toAddress)));
        if ($missing) {
            // Log to a lower level because this will always occur when using the payment request button
            CakeLog::notice(json_encode(['message' => 'Missing required address_to fields: ' . implode(', ', $missing), 'address_to' => $toAddress]));

            return [];
        }

        $brand = $this->User->record($brandId, ['fields' => ['email_address', 'company_name']]);
        $shippingOrigin = $this->UserAddress->findShippingOrigin($brandId);
        $fromAddress = [
            'name' => $brand['User']['company_name'],
            'street1' => $shippingOrigin['UserAddress']['address1'],
            'city' => $shippingOrigin['UserAddress']['city'],
            'state' => $this->State->getStateCode($shippingOrigin['UserAddress']['state_id']),
            'country' => $this->Country->getCountryCode($shippingOrigin['UserAddress']['country_id']),
            'zip' => $shippingOrigin['UserAddress']['zipcode'],
            'phone' => $this->Contact->getCompanyTelephone($brandId),
            'email' => $brand['User']['email_address'],
        ];
        $missing = array_keys(array_diff_key($fromAddress, array_filter($fromAddress)));
        if ($missing) {
            CakeLog::warning(json_encode(['message' => 'Missing required address_from fields: ' . implode(', ', $missing), 'address_from' => $fromAddress]));

            return [];
        }

        $package = $this->UserShippingPackage->findDefaultPackage($brandId);
        $parcel = [
            'length' => $package['UserShippingPackage']['length'],
            'width' => $package['UserShippingPackage']['width'],
            'height' => $package['UserShippingPackage']['height'],
            'distance_unit' => $package['UserShippingPackage']['dimension_unit'],
            'weight' => round($package['UserShippingPackage']['weight'] + $totalProductWeight, 4),
            'box_weight' => $package['UserShippingPackage']['weight'],
            'mass_unit' => $package['UserShippingPackage']['weight_unit'],
        ];

        $userCarrierAccounts = array_column($userCarriersByProvider, 'carrier_account');

        try {
            $carrierRates = $this->Shippo->getCarrierRates($fromAddress, $toAddress, $parcel, $userCarrierAccounts);
        } catch (Exception $e) {
            CakeLog::error($e);
            $carrierRates = [];
        }

        $carrierRates = array_filter($carrierRates, function($rate) use ($userCarriersByProvider) {
            return isset($userCarriersByProvider[$rate['provider']]);
        });
        if (!$carrierRates) {
            CakeLog::warning(json_encode(['message' => 'Carrier rates not found', 'providers' => array_keys($userCarriersByProvider)]));

            return [];
        }

        $carrierRates = array_map(
            function($rate) use ($parcel, $currencyCode, $userCarriersByProvider) {
                $amount = (float)$rate['amount'];
                $rateCurrency = (string)$rate['currency'];
                // Prefer consumer currency unless explicitly set to brand currency
                if ($rateCurrency !== $currencyCode) {
                    $amount = (float)$rate['amount_local'];
                    $rateCurrency = (string)$rate['currency_local'];
                }

                $adjustment = (array)$userCarriersByProvider[$rate['provider']]['rates'];
                $percentFee = $amount * (float)$adjustment['percentage'] / 100;
                $flatFee = (float)$adjustment['flat_fee'];
                $amount += $percentFee + $flatFee;

                return [
                    'id' => $rate['object_id'],
                    'type' => 'carrier',
                    'name' => trim($rate['provider'] . ' ' . $rate['servicelevel']['name']),
                    'code' => $rate['servicelevel']['token'],
                    'amount' => Currency::formatAsDecimal($amount, $rateCurrency),
                    'discount' => Currency::formatAsDecimal(0.00, $rateCurrency),
                    'currency' => $rateCurrency,
                    'box_weight' => $parcel['box_weight'],
                ];
            },
            $carrierRates
        );

        $carrierRates = (array)sortByKey($carrierRates, 'amount');

        $carrierRatesCount = count($carrierRates);
        $SHIPPING_CARRIER_LIMIT = intval(SHIPPING_CARRIER_LIMIT);
        $carrierRates = array_slice($carrierRates, 0, $SHIPPING_CARRIER_LIMIT);

        CakeLog::debug(json_encode(compact('carrierRatesCount', 'SHIPPING_CARRIER_LIMIT', 'carrierRates')));

        return $carrierRates;
    }

    public function isToWarehouse(int $brandId, array $address): bool
    {
        $shippingOrigin = $this->UserAddress->findShippingOrigin($brandId);

        return strcasecmp($address['PostalCode'], $shippingOrigin['UserAddress']['zipcode']) === 0;
    }

    public function getWillCallShippingRates(string $currencyCode): array
    {
        $shippingRates = [];

        $type = 'will_call';
        foreach ([1 => 'Local Pickup', 2 => 'With Assembly'] as $id => $shippingName) {
            $shippingRates[] = [
                'id' => generate_key([$type, $id]),
                'type' => $type,
                'name' => $shippingName,
                'code' => generate_slug($shippingName),
                'amount' => '0.00',
                'currency' => $currencyCode,
                'box_weight' => null,
            ];
        }

        return $shippingRates;
    }

    public function calculateB2bCartShipping(array $b2bCart): float
    {
        return $this->calculateShippingFromDealerProducts(
            (int)$b2bCart['B2bCart']['user_id'],
            (int)$b2bCart['B2bCart']['retailer_id'],
            (array)$b2bCart['B2bCartProduct']
        );
    }

    public function findB2bCartRateOptions(array $b2bCart): array
    {
        return $this->extractB2bShippingRateOptions($this->findB2bRatesFromDealerProducts(
            (int)$b2bCart['B2bCart']['user_id'],
            (int)$b2bCart['B2bCart']['retailer_id'],
            (array)$b2bCart['B2bCartProduct']
        ));
    }

    /**
     * @param int $brandId
     * @param int $retailerId
     * @param array $dealerProducts Expected fields: product_id, warehouse_id, quantity, dealer_price.
     * @return float
     */
    public function calculateShippingFromDealerProducts($brandId, $retailerId, array $dealerProducts): float
    {
        return $this->sumWarehouseGroupedRates($this->findB2bRatesFromDealerProducts((int)$brandId, (int)$retailerId, $dealerProducts));
    }

    public function calculateB2bOrderIndexShipping(array $order)
    {
        if (isset($order['Order']['dealer_qty_ordered']['shipping_amount'])) {
            return $order['Order']['dealer_qty_ordered']['shipping_amount'];
        }

        return $this->sumWarehouseGroupedRates($this->calculateB2bOrderPopupShipping($order));
    }

    public function calculateB2bOrderPopupShipping(array $order): array
    {
        if ($order['Order']['order_type'] === OrderType::WHOLESALE) {
            $dealerProducts = array_map(fn($item) => [
                'product_id' => $item['product_id'],
                'warehouse_id' => $item['warehouse_id'],
                'quantity' => $item['quantity'],
                'dealer_price' => ($item['quantity']) ? format_number($item['total_price'] / $item['quantity']) : '0.00',
            ], $order['OrderProduct']);
        } else {
            $dealerProducts = [];
            foreach ($order['Order']['dealer_qty_ordered']['products'] as $productId => $product) {
                $dealerProducts[] = [
                    'product_id' => $productId,
                    'warehouse_id' => null,
                    'quantity' => $product['quantity'],
                    'dealer_price' => $product['dealer_price'],
                ];
            }
        }

        return $this->findB2bRatesFromDealerProducts((int)$order['Order']['user_id'], (int)$order['Order']['retailer_id'], $dealerProducts);
    }

    /**
     * @param int $brandId
     * @param int $retailerId
     * @param array $dealerProducts Expected fields: product_id, warehouse_id, quantity, dealer_price.
     * @return array
     */
    protected function findB2bRatesFromDealerProducts(int $brandId, int $retailerId, array $dealerProducts): array
    {
        return array_map(
            fn(array $dealerProducts): array => $this->findGroupedB2bShippingRates($brandId, $retailerId, $dealerProducts),
            Hash::combine($dealerProducts, '{n}.product_id', '{n}', '{n}.warehouse_id')
        );
    }

    /**
     * @param int $brandId
     * @param int $retailerId
     * @param array $dealerProducts Expected fields: product_id, quantity, dealer_price.
     * @return array<string, array<string, mixed>> Rate by group
     */
    protected function findGroupedB2bShippingRates(int $brandId, int $retailerId, array $dealerProducts): array
    {
        $rates = $this->B2bShippingRate->findAllForItems($brandId, $retailerId);
        CakeLog::debug(json_encode(compact('rates')));
        if (empty($rates)) {
            return [];
        }

        $dealerProductsMap = array_filter(
            Hash::combine($dealerProducts, '{n}.product_id', '{n}'),
            function($dealerProduct) {
                return $dealerProduct['quantity'] > 0;
            }
        );

        $lineItems = array_map(
            function($product) use ($dealerProductsMap) {
                $dealerProduct = $dealerProductsMap[$product['Product']['id']];
                $product['Product']['quantity'] = $dealerProduct['quantity'];
                $product['Product']['dealer_price'] = $dealerProduct['dealer_price'];

                return $product;
            },
            $this->Product->find('all', [
                'recursive' => -1,
                'conditions' => $this->Product->getConditionsForActiveProducts([
                    'Product.id' => array_keys($dealerProductsMap),
                ]),
                'fields' => ['id', 'product_type', 'weight', 'weight_unit', 'product_name'],
            ])
        );
        CakeLog::debug(json_encode($lineItems));

        $rates = $this->_filterB2bRates($rates, $lineItems);

        $groupedRates = array_reduce(
            $rates,
            function($map, $rate) {
                $rateCategories = array_column($rate['B2bShippingRateCategory'], 'product_category');
                $rateTitles = array_column($rate['B2bShippingRateTitle'], 'product_title');
                // Group by sorted category sets so that each rate is only counted for multiple categories
                // The brand is responsible for setting up mutually exclusive rate category sets
                $categorytitleSet = implode(',', $rateCategories) . '|' . implode(',', $rateTitles);
                $map[$categorytitleSet][] = $rate['B2bShippingRate'];

                return $map;
            },
            []
        );
        CakeLog::debug(json_encode(compact('groupedRates')));

        $rateByGroup = array_map(function($rates) {
            return array_reduce($rates, function($min, $rate) {
                return (!isset($min['price']) || $rate['price'] <= $min['price']) ? $rate : $min;
            });
        }, $groupedRates);
        CakeLog::debug(json_encode(compact('rateByGroup')));

        return $rateByGroup;
    }

    private function _filterB2bRates(array $rates, array $lineItems): array
    {
        $rates = array_map(function($rate) use ($lineItems) {
            $rateCategories = array_column($rate['B2bShippingRateCategory'], 'product_category');
            if ($rateCategories) {
                $lineItems = $this->Product->filterTypes($lineItems, $rateCategories);
            }

            $rateTitles = array_column($rate['B2bShippingRateTitle'], 'product_title');
            if ($rateTitles) {
                $lineItems = array_filter($lineItems, fn($item) => in_array($item['Product']['product_name'], $rateTitles, true));
            }

            $rate += ['Product' => array_column($lineItems, 'Product')];

            if ($rate['B2bShippingRate']['type'] === B2bShippingRate::TYPE_UNIT) {
                $quantity = array_sum(array_column($rate['Product'], 'quantity'));
                $rate['B2bShippingRate']['price'] = format_number($rate['B2bShippingRate']['price'] * $quantity);
            }

            return $rate;
        }, $rates);

        return array_filter($rates, function($rate) {
            if (empty($rate['Product'])) {
                return false;
            }

            $rangeValue = null;
            if ($rate['B2bShippingRate']['type'] === B2bShippingRate::TYPE_PRICE) {
                $rangeValue = array_sum(
                    array_map(fn($item) => $item['quantity'] * $item['dealer_price'], $rate['Product'])
                );
            } elseif ($rate['B2bShippingRate']['type'] === B2bShippingRate::TYPE_UNIT) {
                $rangeValue = array_sum(
                    array_map(fn($item) => $item['quantity'], $rate['Product'])
                );
            } elseif ($rate['B2bShippingRate']['type'] === B2bShippingRate::TYPE_WEIGHT) {
                $rangeValue = array_sum(
                    array_map(fn($item) => $item['quantity'] * convertWeightToLbs($item['weight'], $item['weight_unit']), $rate['Product'])
                );
            }

            return $rangeValue !== null &&
                   $rangeValue >= $rate['B2bShippingRate']['min_range'] &&
                   ($rate['B2bShippingRate']['max_range'] === null || $rangeValue <= $rate['B2bShippingRate']['max_range']);
        });
    }

    /**
     * @param array $warehouseGroupedRates
     * @return array<int, string> [B2bShippingRate.id => B2bShippingRate.name]
     */
    public function extractB2bShippingRateOptions(array $warehouseGroupedRates): array
    {
        $shippingRateOptions = Hash::combine($warehouseGroupedRates, '{n}.{s}.id', '{n}.{s}.name');
        asort($shippingRateOptions);

        return $shippingRateOptions;
    }

    /**
     * @param array<int, array<string, array<string, mixed>>> $warehouseGroupedRates
     * @return float
     */
    public function sumWarehouseGroupedRates(array $warehouseGroupedRates): float
    {
        return $this->sumGroupedRates(Hash::extract($warehouseGroupedRates, '{n}.{s}.price'));
    }

    /**
     * @param float[] $prices
     * @return float
     */
    private function sumGroupedRates(array $prices): float
    {
        $sum = 0.00;

        foreach ($prices as $price) {
            if ($price <= 0.00) {
                return 0.00;
            }

            $sum += $price;
        }

        return $sum;
    }
}
