<?php

use TaxJar\Client;

App::uses('AppComponent', 'Controller/Component');
App::uses('Cache', 'Cache');
App::uses('CakeLog', 'Log');
App::uses('UserTax', 'Model');

/**
 * Class TaxCalculatorComponent.
 *
 * @property Country $Country
 * @property State $State
 * @property User $User
 * @property UserTax $UserTax
 */
class TaxCalculatorComponent extends AppComponent
{
    public $uses = [
        'Country',
        'State',
        'User',
        'UserTax',
    ];

    public $cacheConfig = 'medium';

    public function findRatesForLocation(int $retailerId, array $shipping): array
    {
        $stateId = (int)$shipping['province'];

        $rates = $this->UserTax->findRatesForEcommerce($retailerId, $stateId);

        $usesApi = (string)$rates['uses_api'] ?: null;
        unset($rates['uses_api']);

        if ($usesApi === UserTax::API_RATES) {
            $apiRates = $this->findApiRatesForLocation($shipping);
            if ($apiRates) {
                $combined_rate = format_number($apiRates->combined_rate, 5);
                $rates['tax_rate'] = $combined_rate;
                $rates['shipping_tax_rate'] = ($apiRates->freight_taxable) ? $combined_rate : '0.00000';
            }
        } elseif ($usesApi === UserTax::API_TAXES) {
            $apiTax = $this->findApiTaxForOrder($retailerId, $shipping);
            if ($apiTax) {
                $combined_rate = format_number($apiTax->rate, 5);
                $rates['tax_rate'] = $combined_rate;
                $rates['shipping_tax_rate'] = ($apiTax->freight_taxable) ? $combined_rate : '0.00000';
            }
        }

        return $rates;
    }

    /**
     * @param array $shipping
     * @return object|null Detailed rates for a specific location.
     */
    protected function findApiRatesForLocation(array $shipping): ?object
    {
        $parameters = $this->toApiRatesParameters($shipping);

        // Short-circuit known Bad Request parameters to reduce API usage.
        if (empty($parameters['zip'])) {
            triggerWarning(static::json_encode(['message' => 'No zip, required when country is US', 'parameters' => $parameters]));

            return null;
        }

        try {
            $from_cache = true;
            $rates = (object)Cache::remember(
                $this->cacheKey($parameters),
                function() use ($parameters, &$from_cache) {
                    $from_cache = false;
                    return $this->client()->ratesForLocation($parameters['zip'], $parameters);
                },
                $this->cacheConfig
            );
            CakeLog::info(static::json_encode(compact('from_cache', 'parameters', 'rates')));

            return $rates;
        } catch (Exception $e) {
            CakeLog::info(static::json_encode(compact('parameters')));
            CakeLog::error($e);

            return null;
        }
    }

    /**
     * @param array $shipping
     * @return object|null Detailed rates for a specific location.
     */
    protected function findApiTaxForOrder(int $retailerId, array $shipping): ?object
    {
        // Fake values because we only care about the tax rate and whether freight is taxable.
        $amount = 100000.00;
        $shippingAmount = 100.00;

        $parameters = $this->toApiTaxParameters($retailerId, $shipping, $amount, $shippingAmount);

        // Short-circuit known Bad Request parameters to reduce API usage.
        if (empty($parameters['to_zip'])) {
            triggerWarning(static::json_encode(['message' => 'No to zip, required when country is US', 'parameters' => $parameters]));

            return null;
        }
        if (empty($parameters['to_state'])) {
            triggerWarning(static::json_encode(['message' => 'No to state/province, required when country is US', 'parameters' => $parameters]));

            return null;
        }

        try {
            $from_cache = true;
            $taxes = (object)Cache::remember(
                $this->cacheKey($parameters),
                function() use ($parameters, &$from_cache) {
                    $from_cache = false;
                    return $this->client()->taxForOrder($parameters);
                },
                $this->cacheConfig
            );
            CakeLog::info(static::json_encode(compact('from_cache', 'parameters', 'taxes')));

            return $taxes;
        } catch (Exception $e) {
            CakeLog::info(static::json_encode(compact('parameters')));
            CakeLog::error($e);

            return null;
        }
    }

    protected function toApiRatesParameters(array $shipping): array
    {
        return [
            'street' => $shipping['address'],
            'city' => $shipping['city'],
            'country' => $shipping['countryCode'] ?? $this->Country->getCountryCode($shipping['country']),
            'state' => $shipping['regionCode'] ?? $this->State->getStateCode($shipping['province']),
            'zip' => $shipping['PostalCode'],
        ];
    }

    protected function toApiTaxParameters(int $retailerId, array $shipping, float $amount, float $shippingAmount): array
    {
        $shippingRetailer = $this->getRetailerShippingAddress($retailerId);
        $originAddress = ['id' => $shippingRetailer['company']] + $this->toApiRatesParameters($shippingRetailer);
        $destinationAddress = $this->toApiRatesParameters($shipping);

        return [
            'from_street' => $originAddress['street'],
            'from_city' => $originAddress['city'],
            'from_country' => $originAddress['country'],
            'from_state' => $originAddress['state'],
            'from_zip' => $originAddress['zip'],
            'to_street' => $destinationAddress['street'],
            'to_city' => $destinationAddress['city'],
            'to_country' => $destinationAddress['country'],
            'to_state' => $destinationAddress['state'],
            'to_zip' => $destinationAddress['zip'],
            'amount' => $amount,
            'shipping' => $shippingAmount,
            'nexus_addresses' => [
                $originAddress,
            ],
        ];
    }

    public function getRetailerShippingAddress(int $retailerId): array
    {
        $retailer = (array)$this->User->get($retailerId, [
            'fields' => [
                'id',
                'company_name',
                'address1',
                'address2',
                'city',
                'state_id',
                'country_id',
                'zipcode',
            ],
        ])['User'];

        return [
            'company' => $retailer['company_name'],
            'address' => $retailer['address1'],
            'city' => $retailer['city'],
            'country' => $retailer['country_id'],
            'province' => $retailer['state_id'],
            'PostalCode' => $retailer['zipcode'],
        ];
    }

    protected function cacheKey(array $parameters): string
    {
        $parameters = $this->canonicalize($parameters);

        return strtolower(__CLASS__) . '_' . md5(json_encode($parameters));
    }

    protected function canonicalize(array $parameters): array
    {
        ksort($parameters);
        foreach ($parameters as &$value) {
            if (is_array($value)) {
                $value = $this->canonicalize($value);
            } elseif (is_string($value)) {
                $value = trim(preg_replace('/\s+/', ' ', $value));
                $value = mb_strtoupper($value);
            }
        }

        return $parameters;
    }

    protected function client(): Client
    {
        static $client = null;
        if (!$client) {
            $client = Client::withApiKey(TAXJAR_API_KEY);
            if ($this->isSandbox()) {
                $client->setApiConfig('api_url', Client::SANDBOX_API_URL);
            }
        }
        return $client;
    }

    protected function isSandbox(): bool
    {
        return filter_var(TAXJAR_API_SANDBOX, FILTER_VALIDATE_BOOLEAN) !== false;
    }

    private static function json_encode($value): string
    {
        App::uses('AppLogTrait', 'Log/Engine');

        return AppLogTrait::json_encode($value);
    }
}
