<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('AppController', 'Controller');
App::uses('UserFriendlyException', 'Error');
App::uses('Product', 'Model');
App::uses('ProductTitle', 'Model');
App::uses('Tag', 'Model');
App::uses('User', 'Model');
App::uses('B2bCartType', 'Utility');
App::uses('Permissions', 'Utility');
App::uses('ProductSellDirect', 'Utility');
App::uses('ProductStatus', 'Utility');

/**
 * Class ProductsController.
 *
 * @property PhpExcelComponent $PhpExcel
 * @property StripeComponent $Stripe
 * @property OrderLogicComponent $OrderLogic
 * @property UploadComponent $Upload
 * @property SpoutComponent $Spout
 * @property ShippingCalculatorComponent $ShippingCalculator
 *
 * @property AppModel $Notifycustomer
 * @property AppModel $ProductImage
 * @property B2bCart $B2bCart
 * @property B2bShippingZone $B2bShippingZone
 * @property Category $Category
 * @property Collection $Collection
 * @property CollectionsProduct $CollectionsProduct
 * @property Contact $Contact
 * @property Discount $Discount
 * @property EmailTemplate $EmailTemplate
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Menu $Menu
 * @property Notification $Notification
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property PricingTier $PricingTier
 * @property Product $Product
 * @property ProductCategories $ProductCategories
 * @property ProductInquiries $ProductInquiries
 * @property ProductMeta $ProductMeta
 * @property ProductNonApplicableOrderType $ProductNonApplicableOrderType
 * @property ProductPrice $ProductPrice
 * @property ProductRetailer $ProductRetailer
 * @property ProductStateFee $ProductStateFee
 * @property ProductTier $ProductTier
 * @property ProductTitle $ProductTitle
 * @property ProductVariantOption $ProductVariantOption
 * @property Store $Store
 * @property Storefront $Storefront
 * @property StorefrontSlide $StorefrontSlide
 * @property StripeUser $StripeUser
 * @property Tag $Tag
 * @property User $User
 * @property UserCategories $UserCategories
 * @property UserCurrency $UserCurrency
 * @property VariantOption $VariantOption
 * @property VariantSortOrder $VariantSortOrder
 * @property Viewslog $Viewslog
 * @property Warehouse $Warehouse
 * @property WarehouseProduct $WarehouseProduct
 */
class ProductsController extends AppController
{
    /**
     * @var array
     */
    public $uses = [
        'B2bShippingZone',
        'Category',
        'Collection',
        'CollectionsProduct',
        'Contact',
        'Discount',
        'EmailTemplate',
        'ManufacturerRetailer',
        'Menu',
        'Notification',
        'Notifycustomer',
        'Order',
        'OrderProduct',
        'PricingTier',
        'Product',
        'ProductCategories',
        'ProductImage',
        'ProductInquiries',
        'ProductMeta',
        'ProductNonApplicableOrderType',
        'ProductPrice',
        'ProductRetailer',
        'ProductStateFee',
        'ProductTier',
        'ProductTitle',
        'ProductVariantOption',
        'Store',
        'Storefront',
        'StorefrontSlide',
        'StripeUser',
        'Tag',
        'User',
        'UserCategories',
        'UserCurrency',
        'VariantOption',
        'VariantSortOrder',
        'Viewslog',
        'Warehouse',
        'WarehouseProduct',
    ];
    /**
     * @var array
     */
    public $components = array('PhpExcel', 'Stripe.Stripe', 'OrderLogic', 'Upload', 'ShippingCalculator', 'Spout');

    public $importHeaders = array(
        // Required columns have a `*` appended to the name
        'ProductTitle.handle' => 'Handle*',
        'id' => 'Product ID*',
        'product_sku' => 'Part #',
        'product_upc' => 'UPC',
        'ProductTitle.title' => 'Product Title',
        'VariantOption.0.name' => 'Option1 Name',
        'VariantOption.0.value' => 'Option1 Value',
        'VariantOption.1.name' => 'Option2 Name',
        'VariantOption.1.value' => 'Option2 Value',
        'VariantOption.2.name' => 'Option3 Name',
        'VariantOption.2.value' => 'Option3 Value',
        'product_type' => 'Type',
        'Tag.{n}.name' => 'Tags',
        'product_image' => 'Image Src',
        'weight' => 'Weight',
        'weight_unit' => 'Weight Unit',
        'product_status' => 'Status',
        'store_pickup_option' => 'Store Pickup',
        'assembly_option' => 'Delivery',
        'ship_from_store' => 'Ship from Store',
        'sell_direct' => 'Direct Method',
        'sales_commission' => 'Sales Commission',
        'installation_hours' => 'Installation (hrs)',
        'is_in_stock_only' => 'In-Stock Only',
        'non_stocking' => 'Non-Stocking',
        'enable_b2b_oversell' => 'Oversell B2B',
        'b2b_min_order_quantity' => 'Min B2B Order QTY',
        'b2b_max_order_quantity' => 'Max B2B Order QTY',
        'show_on_regular_orders' => 'Show on Regular B2B Orders',
        'product_price' => 'Retail Price',
        'compare_at_price' => 'Base Price',
    );

    public function getRetailPriceHeader($currencyCode)
    {
        return __('%s Retail Price', $currencyCode);
    }

    public function getCompareAtPriceHeader($currencyCode)
    {
        return __('%s Base Price', $currencyCode);
    }

    public function getTierHeader($tierName)
    {
        return $tierName;
    }

    public function getTierShipToStoreHeader($tierName)
    {
        return __('%s STS', $tierName);
    }

    public function getTierCommissionHeader($tierName)
    {
        return __('%s Commission', $tierName);
    }

    public function getTierBasePriceHeader($tierName)
    {
        return __('%s Base Price', $tierName);
    }

    /**
     * Common route for product listing page (Brand and retailer)
     */
    public function index()
    {
        $this->set('title_for_layout', __('Products'));

        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if ($userType === User::TYPE_MANUFACTURER) {
            $noRecords = 50;
            if (!empty($this->request->data['Products']['noRecords'])) {
                $noRecords = $this->request->data['Products']['noRecords'];
            } elseif (!empty($this->request->query['perpage'])) {
                $noRecords = $this->request->query['perpage'];
            }
            $this->set('noRecords', $noRecords);

            $collectionOptions = $this->Collection->getCollectionsOptionsByUser($userId);

            $collectionIds = (array)$this->request->query('collection');
            $productTypeOptions = $this->Product->getWholesaleProductTypesByUser($userId, '', $collectionIds);

            $productTypes = array_filter((array)$this->request->query('categories'));
            $variantOptionsByName = $this->Product->getVariantOptionsByName($userId, $this->Product->_setProductTypesCondition($productTypes));

            $stockOptions = $this->Product->getStockOptions();

            $this->set('collectionOptions', $collectionOptions);
            $this->set('productTypeOptions', $productTypeOptions);
            $this->set('variantOptionsByName', $variantOptionsByName);
            $this->set('stockOptions', $stockOptions);
            // Route '/:userID/products/:pageNo'
            $page = Hash::get($this->request->params, 'pageNo', 1);
            $this->set('initialPage', $page);

            $this->_brandProduct($userId, $noRecords, $page);
        } elseif (in_array($userType, [User::TYPE_RETAILER, User::TYPE_STAFF, User::TYPE_SALES_REP])) {
            deprecationWarning("Version2.11 {$this->request->here} is deprecated. The B2B catalogue should be accessed from '/catalogue/:retailer_id' instead.");

            // Route '/:userID/products'
            if ($this->request->param('userID')) {
                $this->request->query['user_id'] = $this->User->field('id', ['uuid' => $this->request->param('userID')]);
            }

            // Redirect legacy hyperlinks from /products to /catalogue/:retailer_id
            $retailerId = $this->request->query('branch_id') ?: $this->request->query('retailer_id');
            unset($this->request->query['branch_id'], $this->request->query['retailer_id']);

            if (!$retailerId) {
                if ($userType === User::TYPE_RETAILER) {
                    $retailerId = $userId;
                } elseif ($userType === User::TYPE_STAFF) {
                    $retailerId = (int)$this->Auth->user('Branch');
                }
            }

            return $this->redirect(['controller' => 'products', 'action' => 'index_retailer', 'retailer_id' => $retailerId, '?' => $this->request->query], 301);
        } else {
            throw new ForbiddenException(json_encode(['Auth' => User::extractAuthUserLogFields($this->Auth->user())]));
        }
    }

    public function index_retailer($storeId)
    {
        $this->set('title_for_layout', __('Products'));

        // Access control
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if (
            !in_array($userType, [User::TYPE_RETAILER, User::TYPE_STAFF], true) &&
            !$this->User->hasB2bRetailerPermission($userId, $storeId)
        ) {
            throw new BadRequestException('Must provide a valid connected retailer');
        }

        $isConsumerView = $this->isConsumerView($this->request, $userType);
        $isRetailer = in_array($userType, User::TYPES_RETAILER);

        $displayToggle = $this->User->getProductDisplayToggle($userId);

        $this->request->query = array_filter($this->request->query);
        $this->request->query += [
            'display' => $displayToggle,
            'sort' => 'Product.sort_order',
            'order' => 'ASC',
            'page' => 1,
        ];

        $isGridView = $this->request->query('display') === 'grid';

        $this->request->query += [
            'show' => ($isGridView) ? 12 : 50,
        ];

        $noRecords = $this->request->query('show');
        $page = $this->request->query('page');
        $sort = $this->request->query('sort') . ' ' . $this->request->query('order');
        $search = $this->request->query('search');


        if ($this->request->query['display'] !== $displayToggle) {
            $this->User->setProductDisplayToggle($userId, $this->request->query['display']);
        }

        if ($this->request->query('location_id')) {
            $storeId = $this->request->query('location_id');
        }
        $validBrands = $this->User->listConnectedBrandNames($storeId);
        if ($userType === User::TYPE_SALES_REP) {
            $validBrands = array_intersect_key($validBrands, $this->User->listSalesRepBrandNames($userId, false, $storeId));
        }
        $validBrands += $this->User->listProductDiscoveryBrandNames($storeId);
        if ($userType === User::TYPE_MANUFACTURER) {
            $validBrands = [$userId => $this->Auth->user('company_name')];
        }

        $validBrandIds = array_keys($validBrands);

        if (empty($this->request->query['user_id'])) {
            $this->request->query['user_id'] = $this->B2bCart->getBrandIdOfCartId($this->request->query('cartId'));
        }
        if (empty($this->request->query['user_id']) || !in_array($this->request->query['user_id'], $validBrandIds)) {
            unset($this->request->query['user_id']);
        }
        $brandId = $this->request->query('user_id');
        if (count($validBrandIds) === 1) {
            $brandId = current($validBrandIds);
        }

        $existingCarts = $this->B2bCart->findAllForB2bCatalogueFilter($userId, $userType, $storeId, $brandId);

        $validCartsById = Hash::combine($existingCarts, '{n}.B2bCart.id', '{n}');
        $validCartIds = array_keys($validCartsById);
        $temporaryCart = $this->getTemporaryCart($this->request);
        if(!empty($temporaryCart) && empty($existingCarts)){
            $existingCarts[] = $temporaryCart;
        }

        if (empty($this->request->query['cartId']) || !in_array($this->request->query['cartId'], $validCartIds)) {
            unset($this->request->query['cartId']);
        }
        $cartId = $this->request->query('cartId');

        $discount = $validCartsById[$cartId]['Discount']
            ?? $temporaryCart['Discount']
            ?? [];
        $discountId = $discount['id']
            ?? null;
        $orderType = $validCartsById[$cartId]['B2bCart']['order_type']
            ?? $temporaryCart['B2bCart']['order_type']
            ?? null;

        $brandIds = !empty($brandId) ? array($brandId) : $validBrandIds;

        $storeConnection = $this->ManufacturerRetailer->getStoreConnection($brandId, $storeId, [
            'pricingtierid',
        ]);
        $collectionOptions = $this->Product->getWholesaleCollectionOptionsByUser($brandIds, $orderType, $storeConnection['pricingtierid'], $storeId);

        $collectionImages = ($brandId && $collectionOptions)
            ? $this->Collection->getCollectionsImageByUser($brandId, array_keys($collectionOptions))
            : [];

        $allCollections = $this->Collection->getCollectionsOptionsByUser($brandId, true);

        if (empty($this->request->query['collection']) || !in_array($this->request->query['collection'], array_keys($allCollections))) {
            unset($this->request->query['collection']);
        }
        $collectionIds = (array)$this->request->query('collection');

        $productTypeOptions = $this->Product->getWholesaleProductTypesByUser($brandIds, $orderType, $collectionIds, $storeId);

        if (empty($this->request->query['type']) || !in_array($this->request->query['type'], array_keys($productTypeOptions))) {
            unset($this->request->query['type']);
        }
        $types = (array)$this->request->query('type');

        $variantOptionsByName = $this->Product->getWholesaleVariantOptionsByName($brandIds, $orderType, $types, $storeId);

        $this->request->query['variants'] = $this->_filterVariantsQueryParam((array)$this->request->query('variants'), $variantOptionsByName);
        if (empty($this->request->query['variants'])) {
            unset($this->request->query['variants']);
        }
        $variants = (array)$this->request->query('variants');

        $stockOptions = $this->Product->getStockOptions();

        if (empty($this->request->query['stockOption']) || $orderType === B2bCartType::BOOKING) {
            unset($this->request->query['stockOption']);
        }
        $stockOption = $this->request->query('stockOption');

        $validWarehouses = $this->Warehouse->listValidUserWarehouses($brandId, $storeId);

        if (empty($this->request->query['warehouse_id']) || !in_array($this->request->query['warehouse_id'], array_keys($validWarehouses))) {
            $this->request->query['warehouse_id'] = $this->ManufacturerRetailer->findDefaultWarehouseId($brandId, $storeId);
        }
        $warehouseId = $this->request->query('warehouse_id');

        $count = $this->Product->countRetailerIndex($storeId, $brandIds, $collectionIds, $types, $variants, $search, $sort, $orderType, $stockOption, $warehouseId, $discount);

        $products = $this->Product->findAllForRetailerIndex($storeId, $brandIds, $collectionIds, $types, $variants, $search, $sort, $discount, $noRecords, $page, $orderType, $stockOption, $warehouseId);

        $selectedCollectionId = (int)$this->request->query('collection') ?: null;
        $selectedCollectionTitle = (string)($collectionOptions[current($collectionIds)] ?? '');
        $locations = $this->User->listRetailerLocationsForBrand($brandId, $storeId);
        if ($this->isRetailStaffUser()) {
            $locations = array_intersect_key($locations, array_flip($this->getAuthUserIds()));
        }
        $storefronts = [];
        if ($orderType === B2bCartType::REGULAR) {
            $storefrontSetOptions = $this->Storefront->findAllStorefrontSetOptionsFilteredByPricingTierId($storeConnection['pricingtierid']);
            if (!empty($storefrontSetOptions)) {
                $storefronts = (array)$this->StorefrontSlide->find('all', [
                    'conditions' => [
                        'StorefrontSlide.user_id' => $brandId,
                        'StorefrontSlide.storefront_id' => $storefrontSetOptions['Storefront']['id'],
                    ],
                    'fields' => [
                        'id',
                        'user_id',
                        'slide_number',
                        'slide_name',
                        'image',
                        'image_position',
                        'overlay_opacity',
                        'text_color',
                        'text_alignment',
                        'heading',
                        'button_label',
                        'redirect_type',
                        'redirect_url',
                        'storefront_id',
                    ],
                    'order' => ['StorefrontSlide.slide_number' => 'ASC'],
                ]);
            }
        }

        $discountTypes = $this->Discount->getAvailableB2bDiscounts($storeId, $brandId);
        $visibleProductUuids = $this->Product->getVisibleUuidsForRetailer($storeId, $brandIds);

        $this->set('visibleProductUuids', $visibleProductUuids);
        $this->set('noRecords', $noRecords);
        $this->set('selectedBrandId', $brandId);
        $this->set('existingCarts', $existingCarts);
        $this->set('cartId', $cartId);
        $this->set('discountId', $discountId);
        $this->set('orderType', $orderType);
        $this->set('collectionOptions', $allCollections);
        $this->set('collectionImages', $collectionImages);
        $this->set('selectedCollectionTitle', $selectedCollectionTitle);
        $this->set('selectedCollectionId', $selectedCollectionId);
        $this->set('productTypeOptions', $productTypeOptions);
        $this->set('variantOptionsByName', $variantOptionsByName);
        $this->set('stockOptions', $stockOptions);
        $this->set('warehouseOptions', $validWarehouses);
        $this->set('selectedWarehouseId', $warehouseId);
        $this->set('locations', $locations);
        $this->set('storefront', array_values($storefronts));
        $this->set('discountTypes', $discountTypes);
        $this->set('count_products', $count);
        $this->set('paging', paging($page, $noRecords, $count));
        $this->set('all_products', $products);
        $this->set('isConsumerView', $isConsumerView);
        $this->set('isRetailer', $isRetailer);
        $this->set('shippingData', $this->B2bShippingZone->getFreeShippingBarData($brandId, $storeConnection['pricingtierid']));
        if ($this->request->is('ajax')) {
            if ($isGridView) {
                $this->render('/Elements/Products/productlist_retailer_grid', false);
            } else {
                $this->render('/Elements/Products/productlist_retailer', false);
            }
        } else {
            $this->render('index_retailer');
        }
    }

    public function catalogue_variants($productId, $storeId)
    {
        // Access control
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        $this->Product->belongsTo('User');
        $authProduct = $this->Product->record($productId, [
            'contain' => [
                'User' => ['fields' => ['id', 'company_name']],
            ],
            'fields' => ['id', 'user_id'],
        ]);
        $this->Product->unbindModel(['belongsTo' => ['User']], false);
        if (empty($authProduct['Product']['id'])) {
            throw new NotFoundException(__('Product not found where id=%s', json_encode($productId)));
        }

        $brandId = $authProduct['User']['id'];

        if (
            (
                $userType === User::TYPE_MANUFACTURER &&
                $userId !== (int)$brandId
            ) || (
                !in_array($userType, [User::TYPE_MANUFACTURER, User::TYPE_RETAILER, User::TYPE_STAFF], true) &&
                !$this->User->hasB2bRetailerPermission($userId, $storeId)
            )
        ) {
            throw new ForbiddenException(json_encode([
                'message' => 'User does not have permission',
                'brand_id' => $brandId,
                'store_id' => $storeId,
                'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
            ]));
        }
        if (
            !$this->User->hasB2bRetailerPermission($brandId, $storeId) &&
            !array_key_exists($brandId, $this->User->listProductDiscoveryBrandNames($storeId))
        ) {
            throw new ForbiddenException(json_encode([
                'message' => 'Location does not have permission',
                'brand_id' => $brandId,
                'store_id' => $storeId,
                'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
            ]));
        }

        $this->request->query = array_filter($this->request->query);

        $existingCarts = $this->B2bCart->findAllForB2bCatalogueFilter($userId, $userType, $storeId);

        $validCartsById = Hash::combine($existingCarts, '{n}.B2bCart.id', '{n}');
        $validCartIds = array_keys($validCartsById);
        $temporaryCart = $this->getTemporaryCart($this->request);
        if(!empty($temporaryCart)){
            $existingCarts[] = $temporaryCart;
        }

        if (empty($this->request->query['cartId']) || !in_array($this->request->query['cartId'], $validCartIds)) {
            unset($this->request->query['cartId']);
        }
        $cartId = $this->request->query('cartId');

        $discount = $validCartsById[$cartId]['Discount']
            ?? $temporaryCart['Discount']
            ?? [];
        $orderType = $validCartsById[$cartId]['B2bCart']['order_type']
            ?? $temporaryCart['B2bCart']['order_type']
            ?? null;

        $storeConnection = $this->ManufacturerRetailer->getStoreConnection($brandId, $storeId, [
            'pricingtierid',
        ]);
        $collectionOptions = $this->Product->getWholesaleCollectionOptionsByUser($brandId, $orderType, $storeConnection['pricingtierid'], $storeId);
        if (empty($this->request->query['collection']) || !in_array($this->request->query['collection'], array_keys($collectionOptions))) {
            unset($this->request->query['collection']);
        }
        $collectionIds = (array)$this->request->query('collection');

        $productTypeOptions = $this->Product->getWholesaleProductTypesByUser($brandId, $orderType, $collectionIds, $storeId);
        if (empty($this->request->query['type']) || !in_array($this->request->query['type'], array_keys($productTypeOptions))) {
            unset($this->request->query['type']);
        }
        $types = (array)$this->request->query('type');

        $variantOptionsByName = $this->Product->getWholesaleVariantOptionsByName($brandId, $orderType, $types, $storeId);

        $this->request->query['variants'] = $this->_filterVariantsQueryParam((array)$this->request->query('variants'), $variantOptionsByName);
        if (empty($this->request->query['variants'])) {
            unset($this->request->query['variants']);
        }
        $variants = (array)$this->request->query('variants');

        if (empty($this->request->query['stockOption']) || $orderType === B2bCartType::BOOKING) {
            unset($this->request->query['stockOption']);
        }
        $stockOption = $this->request->query('stockOption');

        $validWarehouses = $this->Warehouse->listValidUserWarehouses($brandId, $storeId);
        if (empty($this->request->query['warehouse_id']) || !in_array($this->request->query['warehouse_id'], array_keys($validWarehouses))) {
            $this->request->query['warehouse_id'] = $this->ManufacturerRetailer->findDefaultWarehouseId($brandId, $storeId);
        }
        $warehouseId = $this->request->query('warehouse_id');

        $product = $this->Product->findForCatalogueVariants($productId, $storeId, $variants, $discount, $orderType, $stockOption, $warehouseId);

        if (!empty($product['Product']['id'])) {
            // Reproduce assignment performed in productlist_retailer.ctp
            $product['Product']['can_add_to_cart'] = ($product['Product']['can_add_to_cart'] && $this->Permissions->userHasB2bCartPermission($this->Auth->user()));
        }

        $this->set('product', $product);
        $this->set('selectedBrandId', $brandId);
        $this->set('selectedWarehouseId', $warehouseId);
        $this->set('cartId', $cartId);
        $this->set('b2bCartType', $orderType);

        if ($userType === User::TYPE_RETAILER) {
            $this->Viewslog->saveProductView($userId, $productId);
        }

        $this->render('/Elements/Products/catalogue_variants');
    }

    /**
     * Common ajax route for product listing page (Brand and retailer)
     */
    public function ajax_index()
    {
        $userType = $this->Auth->user('user_type');
        if ($userType !== 'Manufacturer') {
            throw new ForbiddenException();
        }
        $userId = (int)$this->Auth->user('id');

        $noRecords = $this->request->data['noRecords'];
        $page = $this->request->data['pageNumber'];
        $sort = $this->request->data['sortField'] . ' ' . $this->request->data['sortOrder'];
        $search = $this->request->data['search'];
        $product_status = $this->request->data['product_status'];
        $collection = $this->request->data['collection'];
        $categories = $this->request->data['categories'];
        $stock = $this->request->data['stock'];

        $variantOptionsByName = $this->Product->getVariantOptionsByName($userId);
        $this->request->data['variants'] = $this->_filterVariantsQueryParam((array)$this->request->data('variants'), $variantOptionsByName);
        if (empty($this->request->data['variants'])) {
            unset($this->request->data['variants']);
        }
        $variants = (array)$this->request->data('variants');

        $this->_manufacturerListLogic($noRecords, $page, $product_status, $collection, $categories, $variants, $stock, $sort, $search);

        $this->render('/Elements/Products/productlist', false);
    }

    /**
     * sub function for product listing page(final template)
     * @param $noRecords
     * @param $page
     * @param string $count
     * @param $options
     * @param $order
     */
    protected function _productRetailerList($noRecords, $page, $count, $options, $order)
    {
        if (empty($count)) {
            $options['fields'] = array('COUNT(DISTINCT `Product`.`id`) as Count');
            $productsQuery = $this->Product->find('first', $options);
            $count_products = 0;
            if (count($productsQuery)) {
                $count_products = $productsQuery[0]['Count'];
            }
            $this->set('count_products', $count_products);
        } else {
            $count_products = $count;
        }

        $paging = paging($page, $noRecords, $count_products);
        $this->set('paging', $paging);

        $options['order'] = $order;
        $options['offset'] = $paging['offset'];
        $options['limit'] = $noRecords;
        $options['fields'] = array('DISTINCT id', 'user_id', 'productID', 'product_title', 'product_description', 'product_status', 'product_price', 'product_image', 'product_sku', 'invoice_amount', 'min_shipping', 'no_of_views', 'no_of_orders', 'user_id' /*, 'watchCount'*/, 'uuid');

        //$this->Product->bindModel(array('belongsTo' => array('User')));
        $all_products = $this->Product->find('all', $options);
        $this->set('all_products', $all_products);
    }

    /**
     * Sub function for brand product listing page
     *
     * @param int $brandId
     * @param $noRecords
     * @param $page
     */
    protected function _brandProduct(int $brandId, $noRecords, $page)
    {
        // Top filter
        $orderStatus = '';
        if (!empty($this->request->data['Products']['product_status'])) {
            $orderStatus = $this->request->data['Products']['product_status']; //$cond['product_status']
        }
        $this->set('orderStatus', $orderStatus);

        if ($this->request->data('save') === 'true' && $this->request->data('Product')) {
            try {
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);
            } catch (ForbiddenException $e) {
                return $this->_permissionDeniedResponse($e);
            }

            if ($this->Product->saveManyFromBrandIndex($brandId, $this->request->data['Product'])) {
                return $this->_successResponse();
            }

            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->Product->validationErrors, 'data' => $this->Product->data])), null, true);
        }

        $this->_manufacturerListLogic($noRecords, $page, $orderStatus);
    }

    /**
     * @param bool $storePickupOption
     * @param bool $assemblyOption
     * @param bool $shipFromStore
     *
     * @return array
     */
    protected function _getManufacturerListFields($storePickupOption, $assemblyOption, $shipFromStore)
    {
         $fields = [
            'DISTINCT Product.id',
            'Product.user_id',
            'Product.productID',
            'Product.product_title',
            'Product.product_upc',
            'Product.product_description',
            'Product.product_status',
            'Product.product_price',
            'Product.product_image',
            'Product.product_sku',
            'Product.product_name',
            'Product.variant_options',
            'Product.invoice_amount',
            'Product.min_shipping',
            'Product.no_of_views',
            'Product.no_of_orders',
            'Product.uuid',
            'Product.sell_direct',
            'Product.sell_direct_method',
            'Product.sort_order',
        ];

        if($storePickupOption){
            $fields = array_merge($fields, ['Product.store_pickup_option']);
        }
        if($assemblyOption){
            $fields = array_merge($fields, ['Product.assembly_option']);
        }
        if($shipFromStore){
            $fields = array_merge($fields, ['Product.ship_from_store']);
        }

        return $fields;
    }

    /**
     * Sub function for brand product listing page
     *
     * @param int $noRecords
     * @param int $page
     * @param string $orderStatus
     * @param string $sort
     * @param string $search
     */
    protected function _manufacturerListLogic($noRecords, $page, $orderStatus = '', $collectionId = '', $categories = '', $variants = '', $stock = '', $sort = ['Product.sort_order ASC'], $search = '')
    {
        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT));

        $storePickupOption = (bool)$this->Auth->user('instore');
        $localDelivery = (bool)$this->Auth->user('local_delivery');
        $shipment = (bool)$this->Auth->user('shipment');
        $this->set('store_pickup_option', $storePickupOption);
        $this->set('assembly_option', $localDelivery);
        $this->set('ship_from_store', $shipment);

        $conditions['deleted'] = 0;
        $conditions['user_id'] = $this->shipearly_user['User']['id'];

        if (!empty($orderStatus)) {
            $conditions['product_status'] = $orderStatus;
        }
        if (!empty($collectionId)) {
            $conditions[] = $this->Product->_setCollectionsCondition((array)$collectionId, $conditions);
        }
        if (!empty($categories)) {
            $conditions[] = $this->Product->_setProductTypesCondition((array)$categories, $conditions);
        }
        if (!empty($search)) {
            $conditions[] = $this->Product->buildSearchCondition($search);
        }
        if (!empty($stock)) {
            $conditions[] = $this->Product->_setStockOptionCondition($stock, $conditions);
        }
        if (!empty($variants)) {
            $conditions[] = $this->Product->_setVariantOptionsCondition($variants, $conditions);
        }

        $count = (int)$this->Product->find('count', ['recursive' => -1, 'conditions' => $conditions]);
        $this->set('count_products', $count);

        $paging = paging($page, $noRecords, $count);
        $this->set('paging', $paging);

        $this->Product->virtualFields['profile'] = '(select (100 * sum(' .
            // 'case when `P`.`min_order_qty` is null or `P`.`min_order_qty` = "" then 0 else 1 end
            // +case when `P`.`invoice_amount` is null or `P`.`invoice_amount` = "" then 0 else 1 end
            // +case when `P`.`shipearly_description` or `P`.`shipearly_description` = "" is null then 0 else 1 end
            // +case when `P`.`min_shipping` is null or `P`.`min_shipping` = "" then 0 else 1 end
            'case when `P`.`product_upc` is null or `P`.`product_upc` = "" then 0 else 1 end
            +case when `P`.`product_sku` is null or `P`.`product_upc` = "" then 0 else 1 end
            )) / 3 from ' . $this->Product->tablePrefix . 'products P WHERE `Product`.`id` = `P`.`id`)';

        $this->Product->virtualFields['Order'] = '(SELECT COUNT( DISTINCT `O`.`id`) FROM `' . $this->Product->tablePrefix . 'orders` AS `O` INNER JOIN `' . $this->Product->tablePrefix . 'products` as `pro` INNER JOIN `' . $this->Product->tablePrefix . 'order_products` AS `OrderProduct` ON (`OrderProduct`.`order_id` = `O`.`id` AND `OrderProduct`.`product_id` = `pro`.`id` AND `OrderProduct`.`status` != "Cancelled") WHERE `pro`.`id` = `Product`.`id` AND `O`.`order_type` IN("In_store", "Ship_store") AND NOT (`O`.`order_status` = ("canceled")))';

        $this->Product->belongsTo('User');
        $all_products = $this->Product->find('all', [
            'contain' => ['User'],
            'conditions' => $conditions,
            'fields' => array_merge($this->_getManufacturerListFields($storePickupOption, $localDelivery, $shipment), [
                'Product.profile',
                'Product.Order',
            ]),
            'order' => $sort,
            'offset' => $paging['offset'],
            'limit' => $noRecords,
        ]);
        $this->Product->unbindModel(['belongsTo' => ['User']], false);

        $this->set('all_products', $all_products);
    }

    public function ajax_edit($id = null)
    {
        $product_details = $this->Product->findForEdit($id);
        if (empty($product_details['Product']['id'])) {
            throw new NotFoundException('Product not found where id=' . json_encode($id));
        }

        $id = (int)$product_details['Product']['id'];
        $brandId = (int)$product_details['Product']['user_id'];

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
            if ((int)$this->Auth->user('id') !== $brandId) {
                throw new ForbiddenException(json_encode(['Product' => ['user_id' => $brandId], 'Auth' => User::extractAuthUserLogFields($this->Auth->user())]));
            }
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        if ($this->request->is(['post', 'put'])) {
            try {
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);
            } catch (ForbiddenException $e) {
                return $this->_permissionDeniedResponse($e);
            }

            $previousRetailerIds = $this->ProductRetailer->listRetailerIds($id);

            if (!$this->Product->saveFromEdit($id, $this->request->data)) {
                $presentableErrors = array_intersect_key($this->Product->validationErrors, array_flip(['b2b_max_order_quantity', 'b2b_min_order_quantity']));
                if ($presentableErrors) {
                    $this->_validationErrorFlashResponse($presentableErrors);
                }

                return $this->_exceptionResponse(new BadRequestException(json_encode(['errors' => $this->Product->validationErrors, 'data' => $this->Product->data])), null, true);
            }

            $newRetailerIds = array_diff($this->ProductRetailer->listRetailerIds($id), $previousRetailerIds);
            if ($newRetailerIds) {
                $notification_msg = "<a href='" . BASE_PATH . "products/" . $product_details['Product']['uuid'] . "'>" . $product_details['Product']['product_title'] . "</a> has been added newly to your category of interest";
                $this->Notification->createManyForProductAddedToRetailerCategory($brandId, $newRetailerIds, $notification_msg);
            }

            $redirectURL = preg_replace('/\?perpage\=(\d*)/', '', $this->referer());
            $redirectUrl = $redirectURL . '?perpage=' . $this->request->data['perPage'] . $this->request->data['pageNo'];
            return $this->_successResponse('Product details have been successfully updated.', $redirectUrl);
        }

        if (!$this->request->is('ajax')) {
            return $this->redirect($this->referer());
        }

        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT));
        $this->set('product_details', $product_details);
    }

    public function export()
    {
        $this->autoRender = false;
        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);

        $userId = (int)$this->Auth->user('id');
        $defaultCurrency = (string)$this->Auth->user('currency_code');

        $conditions = $this->_productExportConditions($userId);
        if (!$this->Product->exists($conditions)) {
            return $this->_exceptionResponse(new NotFoundException(), __('No active products found. Make sure they are configured and try again.'));
        }

        $userCurrencies = $this->UserCurrency->listCurrencySet($userId, false);
        $pricingTierNames = $this->PricingTier->listNames($userId);

        $tableModel = [
            $this->PhpExcel->newExportColumn($this->importHeaders['ProductTitle.handle'], function($product) {
                return $product['ProductTitle']['handle'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['id'], function($product) {
                return $product['Product']['id'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['product_sku'], function($product) {
                return $product['Product']['product_sku'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['product_upc'], function($product) {
                return $product['Product']['product_upc'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ProductTitle.title'], function($product) {
                return $product['Product']['product_name'];
            }, ['filter' => true]),
        ];
        foreach (range(0, 2) as $i) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn($this->importHeaders["VariantOption.{$i}.name"], function($product) use ($i) {
                    return $product['VariantOption'][$i]['name'] ?? null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn($this->importHeaders["VariantOption.{$i}.value"], function($product) use ($i) {
                    return $product['VariantOption'][$i]['ProductVariantOption']['value'] ?? $product['Product']['variant_options'][$i] ?? null;
                }, ['filter' => true]),
            ]);
        }
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn($this->importHeaders['product_type'], function($product) {
                return implode(', ', Product::decodeProductType($product['Product']['product_type']));
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['Tag.{n}.name'], function($product) {
                return (string)$product['Product']['tags'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['product_image'], function($product) {
                return $product['Product']['product_image'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['weight'], function($product) {
                return $product['Product']['weight'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['weight_unit'], function($product) {
                return $product['Product']['weight_unit'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['product_status'], function($product) {
                return ProductStatus::getStatusName($product['Product']['product_status']);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['store_pickup_option'], function($product) {
                return ($product['Product']['store_pickup_option']) ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['assembly_option'], function($product) {
                return ($product['Product']['assembly_option']) ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ship_from_store'], function($product) {
                return ($product['Product']['ship_from_store']) ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['sell_direct'], function($product) {
                return $product['Product']['sell_direct_method'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['sales_commission'], function($product) {
                return $product['Product']['sales_commission'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['installation_hours'], function($product) {
                return $product['Product']['installation_hours'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['is_in_stock_only'], function($product) {
                return $product['Product']['is_in_stock_only'] ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['non_stocking'], function($product) {
                return $product['Product']['non_stocking'] ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['enable_b2b_oversell'], function($product) {
                return $product['Product']['enable_b2b_oversell'] ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['b2b_min_order_quantity'], function($product) {
                return $product['Product']['b2b_min_order_quantity'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['b2b_max_order_quantity'], function($product) {
                return $product['Product']['b2b_max_order_quantity'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['show_on_regular_orders'], function($product) {
                return $product['Product']['show_on_regular_orders'] ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['product_price'], function($product) use ($defaultCurrency) {
                $price = $product['Product']['product_price'];
                $currency = $product['Product']['currency'] ?: $defaultCurrency;

                return isset($price) ? Currency::formatAsDecimal($price, $currency) : null;
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['compare_at_price'], function($product) use ($defaultCurrency) {
                $compareAtPrice = $product['Product']['compare_at_price'];
                $currency = $product['Product']['currency'] ?: $defaultCurrency;

                return isset($compareAtPrice) ? Currency::formatAsDecimal($compareAtPrice, $currency) : null;
            }, ['filter' => true]),
        ]);
        foreach ($userCurrencies as $currency) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn($this->getRetailPriceHeader($currency), function($product) use ($currency) {
                    $price = $product['ProductPriceByCurrency'][$currency]['price'] ?? null;

                    return isset($price) ? Currency::formatAsDecimal($price, $currency) : null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn($this->getCompareAtPriceHeader($currency), function($product) use ($currency) {
                    $compareAtPrice = $product['ProductPriceByCurrency'][$currency]['compare_at_price'] ?? null;

                    return isset($compareAtPrice) ? Currency::formatAsDecimal($compareAtPrice, $currency) : null;
                }, ['filter' => true]),
            ]);
        }
        foreach ($pricingTierNames as $tierName) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn($this->getTierHeader($tierName), function($product) use ($tierName) {
                    $tierPrice = $product['PricingTierByName'][$tierName]['ProductTier']['dealer_price'] ?? null;
                    $currency = $product['PricingTierByName'][$tierName]['currencytype'] ?? null;

                    return isset($tierPrice) ? Currency::formatAsDecimal($tierPrice, (string)$currency) : null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn($this->getTierBasePriceHeader($tierName), function($product) use ($tierName) {
                    $dealerBasePrice = $product['PricingTierByName'][$tierName]['ProductTier']['dealer_base_price'] ?? null;
                    $currency = $product['PricingTierByName'][$tierName]['currencytype'] ?? null;

                    return isset($dealerBasePrice) ? Currency::formatAsDecimal($dealerBasePrice, (string)$currency) : null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn($this->getTierShipToStoreHeader($tierName), function($product) use ($tierName) {
                    $tierShipToStorePrice = $product['PricingTierByName'][$tierName]['ProductTier']['alt_nonstock_dealer_price'] ?? null;
                    $currency = $product['PricingTierByName'][$tierName]['currencytype'] ?? null;

                    return isset($tierShipToStorePrice) ? Currency::formatAsDecimal($tierShipToStorePrice, (string)$currency) : null;
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn($this->getTierCommissionHeader($tierName), function($product) use ($tierName) {
                    $commission = $product['PricingTierByName'][$tierName]['ProductTier']['commission'] ?? null;
                    $currency = $product['PricingTierByName'][$tierName]['currencytype'] ?? null;

                    return isset($commission) ? Currency::formatAsDecimal($commission, (string)$currency) : null;
                }, ['filter' => true]),
            ]);
        }

        $fileName = $this->Auth->user('company_name') . ' Products.xlsx';

        $this->Spout->doWithOpenWriter($fileName, function() use ($conditions, $tableModel) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));

            $this->Product->bindModel([
                'belongsTo' => ['ProductTitle'],
                'hasMany' => ['ProductPrice'],
                'hasAndBelongsToMany' => [
                    'PricingTier' => ['with' => 'ProductTier', 'associationForeignKey' => 'pricingtierid', 'unique' => 'keepExisting'],
                    'VariantOption' => ['with' => 'ProductVariantOption', 'unique' => 'keepExisting'],
                ],
            ], false);
            $originalVirtualFields = $this->Product->virtualFields;

            $this->Product->virtualFields['tags'] = $this->Tag->ProductTag->buildTagNamesSubquery($this->Product->primaryKeyIdentifier());
            // Redefine subquery virtual fields using values that are directly available
            $this->Product->virtualFields['product_name'] = "COALESCE({$this->ProductTitle->buildTranslatedFieldSubquery('title')}, ProductTitle.title, SUBSTRING_INDEX(Product.product_title, ' - ', 1))";
            $this->Product->virtualFields['product_title'] = "CONCAT_WS(' - ', {$this->Product->virtualFields['product_name']}, NULLIF({$this->Product->virtualFields['variant_options']}, ''))";

            $this->Product->streamPagedQuery([
                'contain' => [
                    'ProductTitle' => ['fields' => ['id', 'handle']],
                    'ProductPrice' => [
                        'fields' => ['product_id', 'currency_code', 'price', 'compare_at_price'],
                        'order' => false,
                    ],
                    'PricingTier' => [
                        'with' => ['ProductTier' => ['product_id', 'pricingtierid', 'dealer_price', 'dealer_base_price', 'alt_nonstock_dealer_price', 'commission']],
                        'fields' => ['id', 'pricingtiername', 'currencytype'],
                        'order' => false,
                    ],
                    'VariantOption' => [
                        'with' => ['ProductVariantOption' => ['value']],
                        'fields' => ['name'],
                        'order' => ['VariantOption.position' => 'ASC'],
                    ],
                ],
                'conditions' => $conditions,
                'fields' => [
                    'Product.id',
                    'Product.product_type',
                    'Product.product_sku',
                    'Product.product_upc',
                    'Product.product_price',
                    'Product.compare_at_price',
                    'Product.sales_commission',
                    'Product.currency',
                    'Product.is_in_stock_only',
                    'Product.non_stocking',
                    'Product.enable_b2b_oversell',
                    'Product.weight',
                    'Product.weight_unit',
                    'Product.installation_hours',
                    'Product.product_image',
                    'Product.product_status',
                    'Product.b2b_min_order_quantity',
                    'Product.b2b_max_order_quantity',
                    'Product.store_pickup_option',
                    'Product.assembly_option',
                    'Product.ship_from_store',
                    'Product.sell_direct_method',
                    'Product.product_name',
                    'Product.variant_options',
                    'Product.show_on_regular_orders',
                    'Product.tags',
                ],
                'order' => ['Product.sort_order' => 'ASC'],
            ], function($product) use ($tableModel) {
                $product['Product']['variant_options'] = Product::decodeVariantOptions($product['Product']['variant_options']);
                $product['ProductPriceByCurrency'] = Hash::combine($product['ProductPrice'], '{n}.currency_code', '{n}');
                $product['PricingTierByName'] = Hash::combine($product['PricingTier'], '{n}.pricingtiername', '{n}');

                $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $product));
            });

            $this->Product->virtualFields = $originalVirtualFields;
            $this->Product->unbindModel([
                'belongsTo' => ['ProductTitle'],
                'hasMany' => ['ProductPrice'],
                'hasAndBelongsToMany' => [
                    'PricingTier',
                    'VariantOption',
                ],
            ], false);
        });
    }

    /**
     * Import excel file containing values for pricing tier of brand products.
     * file format is as defined in the exported file
     * column 4 contains entries of product Ids - indexed as 3
     * column 5 to the end contains entries of pricing tier prices - indexed from 5
     */
    public function import()
    {
        $this->autoRender = false;

        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);

        $userId = (int)$this->Auth->user('id');

        $this->request->allowMethod('post', 'put');

        try {
            $filePath = $this->Upload->getTempFile($this->request->data('Product.upload'));
            $fileName = $this->request->data('Product.upload.name');

            $tableMap = $this->Spout->extractTableData($filePath, $fileName);
            if (!$tableMap) {
                throw new UserFriendlyException(__('Import file is empty'));
            }

            $tableHeaders = array_keys(current($tableMap));
            $importHeaders = array_intersect($this->importHeaders, $tableHeaders);

            $errorMessages = $this->_checkForImportFileKeyColumnErrorMessages($userId, $tableMap, $importHeaders);
            if ($errorMessages) {
                if (count($errorMessages) > 1) {
                    $errorMessage = __('Import file validation errors');
                    $errorMessage .= sprintf('<ul style="list-style-type: disc;"><li>%s</li></ul>', implode('</li><li>', $errorMessages));
                } else {
                    $errorMessage = current($errorMessages);
                }

                throw new UserFriendlyException($errorMessage);
            }

            $success = $this->_saveManyFromImportTable($userId, $tableMap, $importHeaders);
            if (!$success) {
                throw new InternalErrorException(json_encode(['errors' => $this->Product->validationErrors, 'data' => $this->Product->data]));
            }

            $this->setFlash($this->_appendDebugOutput(__('Product details successfully uploaded')), 'success');
        } catch (UserFriendlyException $e) {
            CakeLog::debug($e->getMessage());
            $this->setFlash($this->_appendDebugOutput($e->getMessage()), 'error');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash($this->_appendDebugOutput(__('An error occurred uploading the file')), 'error');
        } finally {
            if (!empty($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
        }
        $this->redirect($this->referer());
    }

    /**
     * @param int $userId
     * @param array $tableMap
     * @param array $importHeaders
     * @return string[] Error flash messages
     */
    private function _checkForImportFileKeyColumnErrorMessages(int $userId, array $tableMap, array $importHeaders): array
    {
        $messages = [];

        $requiredColumns = ['id', 'ProductTitle.handle'];
        foreach ($requiredColumns as $column) {
            if (empty($importHeaders[$column])) {
                $messages[] = __('Import file requires the %s column', json_encode($this->importHeaders[$column]));
            }
        }

        // Validations past this point assume the required columns are present
        if ($messages) {
            return $messages;
        }

        $invalidRows = array_filter($tableMap, fn($rowMap) => (
            empty($rowMap[$this->importHeaders['ProductTitle.handle']])
            && empty($rowMap[$this->importHeaders['id']])
        ));
        if ($invalidRows) {
            $messages[] = __n(
                'Import file requires a value for %s or %s in row: %s',
                'Import file requires values for %s or %s in rows: %s',
                count($invalidRows),
                json_encode($this->importHeaders['ProductTitle.handle']),
                json_encode($this->importHeaders['id']),
                implode(', ', array_keys($invalidRows))
            );
        }

        $tableIdByRow = array_filter(array_map(fn(array $rowMap): ?string => $rowMap[$this->importHeaders['id']] ?? null, $tableMap));
        if ($tableIdByRow) {
            $existingIds = $this->Product->find('list', [
                'recursive' => -1,
                'conditions' => [
                    'Product.id' => $tableIdByRow,
                    'Product.user_id' => $userId,
                    'Product.deleted' => false,
                ],
                'fields' => ['id', 'id'],
            ]);
            $invalidRows = array_diff($tableIdByRow, $existingIds);
            if ($invalidRows) {
                $messages[] = __n(
                    'Import file contains an invalid %s value in row: %s',
                    'Import file contains invalid %s values in rows: %s',
                    count($invalidRows),
                    json_encode($this->importHeaders['id']),
                    implode(', ', array_keys($invalidRows))
                );
            }
        }

        return $messages;
    }

    /**
     * @param int $userId
     * @param array $tableMap
     * @param array $importHeaders
     * @return bool Success
     * @throws UserFriendlyException
     * @throws BadRequestException
     */
    private function _saveManyFromImportTable(int $userId, array $tableMap, array $importHeaders): bool
    {
        $handleColumn = (string)($importHeaders['ProductTitle.handle'] ?? '');
        $handles = array_filter(array_column($tableMap, $handleColumn, $handleColumn));

        if ($handles) {
            if (!$this->_saveManyTitlesFromImportTable($userId, $tableMap, $importHeaders)) {
                $presentableTitleErrors = array_filter(array_map(fn($error) => array_intersect_key($error, array_flip(['handle', 'title'])), $this->ProductTitle->validationErrors));
                if ($presentableTitleErrors) {
                    throw new UserFriendlyException($this->_formatPresentableTitleErrors($presentableTitleErrors));
                }

                throw new BadRequestException(json_encode(['errors' => $this->ProductTitle->validationErrors, 'data' => $this->ProductTitle->data]));
            }
        }

        $success = $this->_saveManyProductsFromImportTable($userId, $tableMap, $importHeaders);
        if (!$success) {
            $presentableErrors = $this->Product->validationErrors;
            if ($presentableErrors) {
                // Prefix assoc error messages with tier name or currency code for better context
                $presentableErrors = array_map(function($rowErrors) {
                    foreach (['ProductPrice', 'ProductTier'] as $assoc) {
                        foreach (($rowErrors[$assoc] ?? []) as $columnKey => $fieldErrors) {
                            $rowErrors[$assoc][$columnKey] = array_map(function(array $errors) use ($columnKey): array {
                                return array_map(fn(string $error): string => ($columnKey . ': ' . $error), $errors);
                            }, $fieldErrors);
                        }
                    }

                    return $rowErrors;
                }, $presentableErrors);

                $rowsByError = $this->_listValidationKeysByErrorMessage($presentableErrors);
                $presentableErrorMessage = __('Import file validation errors');
                $presentableErrorMessage .= sprintf('<ul style="list-style-type: disc;"><li>%s</li></ul>',
                    implode('</li><li>', array_map(
                        function(string $error) use ($rowsByError): string {
                            $rows = (array)$rowsByError[$error];

                            return __xn(
                                'Import file validation errors',
                                '"%s" for row %s',
                                '"%s" for rows %s',
                                count($rows),
                                $error,
                                implode(', ', $rows)
                            );
                        },
                        array_keys($rowsByError)
                    )));

                throw new UserFriendlyException($presentableErrorMessage);
            }
        }

        return $success;
    }

    private function _saveManyProductsFromImportTable(int $userId, array $tableMap, array $importHeaders): bool
    {
        $siteType = (string)$this->User->fieldByConditions('site_type', ['id' => $userId]);

        $idColumn = (string)($importHeaders['id'] ?? '');
        $handleColumn = (string)($importHeaders['ProductTitle.handle'] ?? '');
        $imageColumn = (string)($importHeaders['product_image'] ?? '');
        $tagsColumn = (string)($importHeaders['Tag.{n}.name'] ?? '');

        $handles = array_filter(array_column($tableMap, $handleColumn, $handleColumn));

        $this->Product->hasMany('ProductNonApplicableOrderType');

        try {
            $existingById = Hash::combine(
                $this->Product->find('all', [
                    'contain' => [
                        'ProductNonApplicableOrderType' => ['fields' => ['id', 'product_id', 'order_type']],
                    ],
                    'conditions' => [
                        'Product.id' => array_filter(array_column($tableMap, $idColumn)),
                        'Product.user_id' => $userId,
                        'Product.deleted' => false,
                    ],
                    'fields' => ['id', 'product_title_id', 'product_status', 'published_at'],
                ]),
                '{n}.Product.id',
                '{n}'
            );
        } finally {
            $this->Product->unbindModel(['hasMany' => ['ProductNonApplicableOrderType']], false);
        }

        $titleIdByProductId = array_filter(array_map(fn($product) => $product['Product']['product_title_id'], $existingById));
        $existingTitles = $this->ProductTitle->findAllExistingForProductImport($userId, $handles, array_keys(array_flip($titleIdByProductId)));
        $titleIdByHandle = Hash::combine($existingTitles, '{n}.ProductTitle.handle', '{n}.ProductTitle.id');
        $titleValueById = Hash::combine($existingTitles, '{n}.ProductTitle.id', '{n}.ProductTitle.title');
        $canDeriveProductTitle = (bool)$titleValueById;

        $variantOptionIdsByTitleIdAndPosition = array_reduce($existingTitles, function($map, $record) {
            foreach ($record['VariantOption'] as $variantOption) {
                $id = $variantOption['id'];
                $titleId = $variantOption['product_title_id'];
                $position = $variantOption['position'];

                // Mapping multiple ids per position for the edge case of having duplicate positions.
                $map[$titleId][$position][$id] = $id;
            }

            return $map;
        }, []);
        $variantValueIdByProductIdAndOptionId = (array)$this->ProductVariantOption->find('list', [
            'conditions' => [
                'ProductVariantOption.product_id' => array_keys($existingById),
                'ProductVariantOption.variant_option_id' => array_values(Hash::flatten($variantOptionIdsByTitleIdAndPosition)),
            ],
            'fields' => ['variant_option_id', 'id', 'product_id'],
            'order' => false,
        ]);

        $userCurrencies = $this->UserCurrency->listCurrencySet($userId, false);
        $tierNames = $this->PricingTier->listNames($userId);

        $productPriceMap = $this->ProductPrice->find('list', [
            'recursive' => -1,
            'conditions' => [
                'ProductPrice.product_id' => array_keys($existingById),
                'ProductPrice.currency_code' => $userCurrencies,
            ],
            'fields' => ['currency_code', 'id', 'product_id'],
        ]);

        $productTierMap = $this->ProductTier->find('list', [
            'recursive' => -1,
            'conditions' => [
                'ProductTier.product_id' => array_keys($existingById),
                'ProductTier.pricingtierid' => array_keys($tierNames),
            ],
            'fields' => ['pricingtierid', 'id', 'product_id'],
        ]);

        $imageIdByProductIdAndUrl = [];
        if ($imageColumn) {
            $imageIdByProductIdAndUrl = (array)$this->ProductImage->find('list', [
                'recursive' => -1,
                'conditions' => [
                    'ProductImage.product_id' => array_keys($existingById),
                    'ProductImage.image_url' => array_filter(array_column($tableMap, $imageColumn)),
                ],
                'fields' => ['image_url', 'id', 'product_id'],
                'group' => ['product_id', 'image_url'],
            ]);
        }

        if ($tagsColumn) {
            $tableMap = $this->_convertImportedTagNamesToTagIds($userId, $tagsColumn, $tableMap);
        }

        $defaultCategoryIds = $this->UserCategories->getUserCategories($userId);
        $defaultRetailerIds = $this->UserCategories->getRetailers($defaultCategoryIds, $userId);

        //FIXME the products DB table does not auto-update created_at/updated_at columns
        $now = $this->date();

        $rows = array_keys($tableMap);
        $rows = array_combine($rows, $rows);

        $deleteData = [];
        $saveData = array_map(
            function(int $row) use (
                $tableMap,
                $userId,
                $siteType,
                $importHeaders,
                $idColumn,
                $handleColumn,
                $imageColumn,
                $tagsColumn,
                $existingById,
                $titleIdByProductId,
                $titleIdByHandle,
                $canDeriveProductTitle,
                $variantOptionIdsByTitleIdAndPosition,
                $variantValueIdByProductIdAndOptionId,
                $userCurrencies,
                $tierNames,
                $productPriceMap,
                $productTierMap,
                $imageIdByProductIdAndUrl,
                $defaultCategoryIds,
                $defaultRetailerIds,
                $now,
                &$deleteData
            ): array {
                $rowMap = (array)$tableMap[$row];

                $productId = !empty($rowMap[$idColumn]) ? (int)$rowMap[$idColumn] : null;
                $handle = (string)($rowMap[$handleColumn] ?? '') ?: null;

                $titleId = (int)($titleIdByProductId[$productId] ?? $titleIdByHandle[$handle] ?? 0) ?: null;
                $existingProduct = (array)($existingById[$productId]['Product'] ?? []);
                $productId = !empty($existingProduct['id']) ? (int)$existingProduct['id'] : null;
                $created = (!$productId);

                $nonApplicableOrderTypesDelete = [];
                $nonApplicableOrderTypesSave = [];
                $productVariantOptions = [];
                $variantVariantOptionsToDelete = [];

                $product = [];
                foreach ($importHeaders as $field => $columnName) {
                    $value = $rowMap[$columnName];
                    if ($field === 'sell_direct') {
                        $validDirectMethods = array_flip(ProductSellDirect::getImportOptions($siteType));
                        if (isset($validDirectMethods[$value])) {
                            $product[$field] = $validDirectMethods[$value];
                        }
                    } elseif ($field === 'product_status') {
                        $validStatus = array_flip(ProductStatus::getValidOptions());
                        if (isset($validStatus[$value])) {
                            $existingStatus = $existingProduct['product_status'] ?? ProductStatus::INCOMPLETE;
                            if ($existingStatus !== ProductStatus::INCOMPLETE) {
                                $product[$field] = $validStatus[$value];
                                /**
                                 * Hack to skip the query to set published_at in event callback.
                                 * @see Product::beforeValidate()
                                 */
                                $product += ['published_at' => $existingProduct['published_at']];
                            }
                        }
                    } elseif ($field === 'product_type') {
                        if (strpos($value, ',') !== false) {
                            $value = array_filter(array_map('trim', explode(',', $value)));
                            $value = (count($value) > 1) ? json_encode($value) : current($value);
                        }
                        $product[$field] = (string)$value;
                    } elseif ($field === 'show_on_regular_orders') {
                        $value = ($value !== '')
                            ? filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)
                            : null;

                        $existingNonApplicableOrderTypes = (array)($existingById[$productId]['ProductNonApplicableOrderType'] ?? []);
                        $regularOrderNonApplicableData = (array)(current(
                            array_filter($existingNonApplicableOrderTypes, function($nonApplicableType) {
                                return $nonApplicableType['order_type'] === B2bCartType::REGULAR;
                            })
                        ) ?: []);
                        $existingShowOnRegularOrders = !$regularOrderNonApplicableData;

                        if ($value !== null && $value !== $existingShowOnRegularOrders) {
                            if ($value) {
                                $nonApplicableOrderTypesDelete[] = $regularOrderNonApplicableData;
                            } else {
                                $nonApplicableOrderTypesSave[] = ['order_type' => B2bCartType::REGULAR];
                            }
                        }
                    } elseif (in_array($field, ['VariantOption.0.value', 'VariantOption.1.value', 'VariantOption.2.value'], true)) {
                        $position = (int)array_search($field, [
                            1 => 'VariantOption.0.value',
                            2 => 'VariantOption.1.value',
                            3 => 'VariantOption.2.value',
                        ], true);
                        $variantOptionIds = (array)($variantOptionIdsByTitleIdAndPosition[$titleId][$position] ?? []);
                        foreach ($variantOptionIds as $variantOptionId) {
                            $productVariantOption = [
                                'id' => $variantValueIdByProductIdAndOptionId[$productId][$variantOptionId] ?? null,
                                'variant_option_id' => $variantOptionId,
                                'value' => $value,
                            ];
                            if ($productVariantOption['value']) {
                                // Temporarily assign position for sorting
                                $productVariantOptions[] = $productVariantOption + ['position' => $position];
                            } elseif ($productVariantOption['id']) {
                                $variantVariantOptionsToDelete[] = $productVariantOption;
                            }
                        }
                    } elseif (!array_key_exists($field, $product) && $this->Product->hasField($field)) {
                        if (
                            $this->Product->getColumnType($field) === 'boolean'
                            // Special case
                            || $field === 'assembly_option'
                        ) {
                            $value = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                        }

                        $product[$field] = $value;
                    }
                }

                $sortByPositionAsc = fn($current, $next) => ((int)$current['position']) <=> ((int)$next['position']);
                usort($productVariantOptions, $sortByPositionAsc);
                $productVariantOptions = Hash::remove($productVariantOptions, '{n}.position');

                if ($product) {
                    $product = array_merge($product, [
                        'user_id' => $userId,
                        'product_title_id' => $titleId,
                        'updated_at' => $now,
                    ]);
                    if ($created) {
                        $product = array_merge($product, [
                            'created_at' => $now,
                            'no_of_retailers' => count($defaultRetailerIds),
                        ]);
                    }

                    if ($canDeriveProductTitle) {
                        // Cannot derive the full product_title because variant option columns might be absent.
                        // Instead, mark null and derive after saving the final title and variant options.
                        $product['product_title'] = null;
                    }
                }
                // Always set product id to be referenced by assoc updates
                $product = ['id' => $productId] + $product;

                $imageUrl = isset($rowMap[$imageColumn]) ? (string)$rowMap[$imageColumn] : null;
                $imageId = isset($imageIdByProductIdAndUrl[$productId][$imageUrl]) ? (int)$imageIdByProductIdAndUrl[$productId][$imageUrl] : null;

                // Only save to ProductImages if the url is new
                $productImages = [];
                if ($imageUrl && !$imageId) {
                    $productImages[] = ['image_url' => $imageUrl];
                }

                $tagIds = isset($rowMap[$tagsColumn]) ? (array)$rowMap[$tagsColumn] : null;

                $save = [
                    $this->Product->alias => $product,
                    'ProductImage' => $productImages,
                    'ProductNonApplicableOrderType' => $nonApplicableOrderTypesSave,
                    'ProductPrice' => $this->_buildProductPricesFromImportRow($rowMap, $userCurrencies, (array)($productPriceMap[$productId] ?? [])),
                    'ProductTier' => $this->_buildProductTiersFromImportRow($rowMap, $tierNames, (array)($productTierMap[$productId] ?? [])),
                    'ProductVariantOption' => $productVariantOptions,
                ];
                if (is_array($tagIds)) {
                    $save = array_merge($save, [
                        // See: https://book.cakephp.org/2.0/en/models/saving-your-data.html#saving-related-model-data-habtm
                        'Tag' => ['Tag' => $tagIds],
                    ]);
                }
                if ($created) {
                    $save = array_merge($save, [
                        'Category' => ['Category' => $defaultCategoryIds],
                        'Retailer' => ['Retailer' => $defaultRetailerIds],
                    ]);
                }

                $deleteData[$row] = [
                    'ProductNonApplicableOrderType' => $nonApplicableOrderTypesDelete,
                    'ProductVariantOption' => $variantVariantOptionsToDelete,
                ];

                return $save;
            },
            $rows
        );

        // Based on eCom sync
        $this->Product->addAssociations([
            'hasMany' => [
                'ProductImage',
                'ProductNonApplicableOrderType',
                'ProductPrice',
                'ProductTier',
                'ProductVariantOption',
            ],
            'belongsToMany' => [
                'Category' => ['with' => 'ProductCategory', 'associationForeignKey' => 'cat_id', 'unique' => 'keepExisting'],
                'Retailer' => ['className' => 'User', 'with' => 'ProductRetailer', 'unique' => 'keepExisting'],
                'Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
            ],
        ]);

        try {
            $success = $this->Product->saveManyFromProductImport($saveData);

            if ($success) {
                if ($canDeriveProductTitle) {
                    $this->_updateAllImportedProductTitles($userId, $titleValueById);
                }

                $productNonApplicableOrderTypeIdsToDelete = array_filter(Hash::extract($deleteData, '{n}.ProductNonApplicableOrderType.{n}.id'));
                $productNonApplicableOrderTypeDeleteConditions = ['ProductNonApplicableOrderType.id' => $productNonApplicableOrderTypeIdsToDelete];
                if ($productNonApplicableOrderTypeIdsToDelete && !$this->ProductNonApplicableOrderType->deleteAllJoinless($productNonApplicableOrderTypeDeleteConditions, false)) {
                    CakeLog::warning(json_encode(['message' => 'Failed to delete regular non applicable order types', 'conditions' => $productNonApplicableOrderTypeDeleteConditions]));
                }

                $productVariantOptionIdsToDelete = array_filter(Hash::extract($deleteData, '{n}.ProductVariantOption.{n}.id'));
                $productVariantOptionDeleteConditions = ['ProductVariantOption.id' => $productVariantOptionIdsToDelete];
                if ($productVariantOptionIdsToDelete && !$this->ProductVariantOption->deleteAllJoinless($productVariantOptionDeleteConditions, false)) {
                    CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned product variant options', 'conditions' => $productVariantOptionDeleteConditions]));
                }

                if (!$this->Tag->deleteAllOrphansWithUserId($userId)) {
                    CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned tags', 'conditions' => ['Tag.user_id' => $userId]]));
                }
            }

            return $success;
        } finally {
            $this->Product->unbindModel([
                'hasMany' => [
                    'ProductImage',
                    'ProductNonApplicableOrderType',
                    'ProductPrice',
                    'ProductTier',
                    'ProductVariantOption',
                ],
                'hasAndBelongsToMany' => [
                    'Category',
                    'Retailer',
                    'Tag',
                ],
            ], false);
        }
    }

    private function _buildProductPricesFromImportRow(array $rowMap, array $userCurrencies, array $productPriceIdByCurrencyCode): array
    {
        return $this->_buildProductAssocRecordsFromImportRow($rowMap, $userCurrencies, $productPriceIdByCurrencyCode, 'currency_code', [
            'price' => [$this, 'getRetailPriceHeader'],
            'compare_at_price' => [$this, 'getCompareAtPriceHeader'],
        ]);
    }

    private function _buildProductTiersFromImportRow(array $rowMap, array $tierNames, array $productTierIdByPricingTierId): array
    {
        return $this->_buildProductAssocRecordsFromImportRow($rowMap, $tierNames, $productTierIdByPricingTierId, 'pricingtierid', [
            'dealer_price' => [$this, 'getTierHeader'],
            'dealer_base_price' => [$this, 'getTierBasePriceHeader'],
            'alt_nonstock_dealer_price' => [$this, 'getTierShipToStoreHeader'],
            'commission' => [$this, 'getTierCommissionHeader'],
        ]);
    }

    /**
     * @param array<string, string> $rowMap
     * @param array<int|string, string> $columnKeyByRecordKey
     * @param array<int|string, int> $idByRecordKey
     * @param string $recordKeyName
     * @param array<string, callable> $columnNameCallbackByFieldName
     * @return array<string, array<string, mixed>> List of assoc records by unique column key
     */
    private function _buildProductAssocRecordsFromImportRow(array $rowMap, array $columnKeyByRecordKey, array $idByRecordKey, string $recordKeyName, array $columnNameCallbackByFieldName): array
    {
        $records = [];

        foreach ($columnKeyByRecordKey as $recordKey => $columnKey) {
            $id = $idByRecordKey[$recordKey] ?? null;

            $record = [];
            foreach ($columnNameCallbackByFieldName as $fieldName => $columnNameCallback) {
                $columnName = call_user_func($columnNameCallback, $columnKey);
                if (array_key_exists($columnName, $rowMap)) {
                    $record[$fieldName] = (string)$rowMap[$columnName];
                }
            }

            if (!$id) {
                // The export file has empty values if the record does not exist.
                // Treat them as unmodified by excluding them.
                $record = array_filter($record, fn(string $value): bool => $value !== '');
            }

            if ($record) {
                $recordKeys = [
                    'id' => $id,
                    $recordKeyName => $recordKey,
                ];

                $records[$columnKey] = $recordKeys + $record;
            }
        }

        return $records;
    }

    private function _saveManyTitlesFromImportTable(int $userId, array $tableMap, array $importHeaders): bool
    {
        $idColumn = (string)($importHeaders['id'] ?? '');
        $handleColumn = (string)$importHeaders['ProductTitle.handle'];
        $titleColumn = (string)($importHeaders['ProductTitle.title'] ?? '');
        $variantNameColumnByPosition = array_filter([
            1 => (string)($importHeaders['VariantOption.0.name'] ?? ''),
            2 => (string)($importHeaders['VariantOption.1.name'] ?? ''),
            3 => (string)($importHeaders['VariantOption.2.name'] ?? ''),
        ]);

        $tableMap = array_filter($tableMap, fn($rowMap) => $rowMap[$handleColumn]);
        if (!$tableMap) {
            return true;
        }

        $tableMapByHandle = array_reduce(array_keys($tableMap), function(array $map, int $row) use ($tableMap, $handleColumn): array {
            $rowMap = (array)$tableMap[$row];
            $handleKey = ProductTitle::normalizeHandle((string)$rowMap[$handleColumn]);

            $map[$handleKey][$row] = $rowMap;

            return $map;
        }, []);

        $handleByKey = array_map(function(array $tableMap) use ($handleColumn): string {
            return (string)current(array_filter(array_column($tableMap, $handleColumn, $handleColumn)));
        }, $tableMapByHandle);

        $existingTitleByHandle = array_reduce(
            $this->ProductTitle->findAllExistingForProductImport($userId, $handleByKey),
            function(array $map, array $record): array {
                $existingTitle = (array)$record['ProductTitle'];
                $handleKey = ProductTitle::normalizeHandle((string)$existingTitle['handle']);

                $map[$handleKey] = $existingTitle;

                return $map;
            },
            []
        );
        $variantOptionIdByTitleIdAndLowerCase = (array)$this->VariantOption->find('list', [
            'conditions' => [
                'VariantOption.product_title_id' => array_column($existingTitleByHandle, 'id'),
            ],
            'fields' => ['lower_case', 'id', 'product_title_id'],
            'order' => ['VariantOption.position' => 'ASC'],
        ]);

        $legacyProductById = Hash::combine(
            $this->Product->find('all', [
                'recursive' => -1,
                'conditions' => [
                    'Product.product_title_id' => null,
                    'Product.id' => array_filter(array_column($tableMap, $idColumn)),
                    'Product.user_id' => $userId,
                    'Product.deleted' => false,
                ],
                'fields' => ['id', 'source_product_id', 'productID'],
                'order' => false,
            ]),
            '{n}.Product.id',
            '{n}.Product'
        );

        // Use actual handle values as saveData keys for error reporting purposes
        $keyByHandle = array_flip($handleByKey);

        $saveMany = array_map(function(string $handleKey) use (
            $tableMapByHandle,
            $userId,
            $idColumn,
            $titleColumn,
            $handleByKey,
            $variantNameColumnByPosition,
            $existingTitleByHandle,
            $variantOptionIdByTitleIdAndLowerCase,
            $legacyProductById
        ): array {
            $tableMap = (array)$tableMapByHandle[$handleKey];
            $titleValue = (string)current(array_filter(array_column($tableMap, $titleColumn, $titleColumn)));
            $variantNameByPosition = array_map(
                fn($column) => (string)current(array_filter(array_column($tableMap, $column, $column))),
                $variantNameColumnByPosition
            );

            $existingTitle = $existingTitleByHandle[$handleKey] ?? [];
            $titleId = $existingTitle['id'] ?? null;
            $variantOptionIdByLowerCase = (array)($variantOptionIdByTitleIdAndLowerCase[$titleId] ?? []);
            $legacyProductById = array_intersect_key($legacyProductById, array_column($tableMap, $idColumn, $idColumn));

            $productTitle = [
                'id' => $titleId,
                'user_id' => $userId,
                'handle' => (string)$handleByKey[$handleKey],
            ];
            if ($titleValue) {
                $productTitle['title'] = $titleValue;
            }

            $saveData = [
                'ProductTitle' => $productTitle,
                'VariantOption' => $this->_buildVariantOptionsFromTitleImport($variantNameByPosition, $variantOptionIdByLowerCase),
            ];

            if ($legacyProductById) {
                $productTitle = array_merge($productTitle, [
                    'source_id' => $existingTitle['source_id'] ?? null,
                    'source_type' => $existingTitle['source_type'] ?? null,
                ]);
                $saveData['Product'] = array_map(
                    fn($product) => ['id' => $product['id']] + Product::deriveSourceIdsFromTitle($product, $productTitle),
                    array_values($legacyProductById)
                );
            }

            return $saveData;
        }, $keyByHandle);

        $deletedVariantOptionIds = [];
        $saveMany = array_map(function($saveData) use (&$deletedVariantOptionIds) {
            $savedVariantOptions = array_filter($saveData['VariantOption'], fn($option) => ($option['name'] ?? null) !== '');

            $deletedVariantOptions = array_diff_key($saveData['VariantOption'], $savedVariantOptions);
            $deletedVariantOptionIds += array_filter(array_column($deletedVariantOptions, 'id', 'id'));

            return array_merge($saveData, ['VariantOption' => array_values($savedVariantOptions)]);
        }, $saveMany);

        $success = $this->ProductTitle->saveManyFromProductImport($saveMany);

        if ($success) {
            $variantOptionDeleteConditions = ['VariantOption.id' => $deletedVariantOptionIds];
            if ($deletedVariantOptionIds && !$this->VariantOption->deleteAllJoinless($variantOptionDeleteConditions)) {
                CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned variant options', 'conditions' => $variantOptionDeleteConditions]));
            }
        }

        return $success;
    }

    private function _buildVariantOptionsFromTitleImport(array $variantNameByPosition, array $variantOptionIdByLowerCase): array
    {
        if (!$variantNameByPosition) {
            return [];
        }

        $numberOfVariantNames = count($variantNameByPosition);
        $numberOfIds = count($variantOptionIdByLowerCase);
        $numberOfRemainingIds = (int)max($numberOfIds - $numberOfVariantNames, 0);
        $positions = range(1, $numberOfVariantNames + $numberOfRemainingIds);

        // 1st pass to reposition any existing ids matched by name and consume them from the set of possible ids
        $variantOptions = array_map(function($position) use ($variantNameByPosition, &$variantOptionIdByLowerCase) {
            $name = isset($variantNameByPosition[$position]) ? (string)$variantNameByPosition[$position] : null;
            $id = null;
            if ($name) {
                $lowerCase = mb_strtolower($name);
                $id = $variantOptionIdByLowerCase[$lowerCase] ?? null;
                if ($id) {
                    unset($variantOptionIdByLowerCase[$lowerCase]);
                }
            }

            $variantOption = [
                'id' => $id,
                'position' => $position,
            ];
            // Let ($name === '') indicate deletion of $id
            if ($name !== null) {
                $variantOption['name'] = $name;
            }

            return $variantOption;
        }, $positions);

        // 2nd pass to reposition any remaining ids not matched by name
        return array_map(function($variantOption) use (&$variantOptionIdByLowerCase) {
            if (!$variantOption['id']) {
                $variantOption['id'] = array_shift($variantOptionIdByLowerCase) ?: null;
            }

            return $variantOption;
        }, $variantOptions);
    }

    /**
     * Decode the tags column, reassigning it to a list of tag ids to for easier processing later.
     *
     * @param int $userId
     * @param string $tagsColumn
     * @param array $tableMap
     * @return array $tableMap
     */
    private function _convertImportedTagNamesToTagIds(int $userId, string $tagsColumn, array $tableMap): array
    {
        $tableMap = array_map(fn($rowMap) => array_merge($rowMap, [
            $tagsColumn => Tag::toDistinctNameByLowerCase(explode(',', $rowMap[$tagsColumn])),
        ]), $tableMap);

        $tagNames = array_reduce(array_column($tableMap, $tagsColumn), fn($set, $tags) => ($set + $tags), []);

        if (!$this->Tag->addTagsToSet($userId, $tagNames)) {
            CakeLog::warning(json_encode(['message' => 'Failed to add new tags', 'errors' => $this->Tag->validationErrors, 'data' => $this->Tag->data]));
        }

        $tagIdsByLowerCase = $this->Tag->listIdsByLowerCaseName($userId, $tagNames);

        return array_map(fn($rowMap) => array_merge($rowMap, [
            $tagsColumn => array_values(array_intersect_key($tagIdsByLowerCase, $rowMap[$tagsColumn])),
        ]), $tableMap);
    }

    private function _updateAllImportedProductTitles(int $userId, array $titleValueById): void
    {
        $titleIds = array_keys($titleValueById);
        $productsMissingTitle = (array)$this->Product->find('all', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => $this->ProductVariantOption->buildVariantTitleJoinSubquery([
                        "{$this->ProductVariantOption->VariantOption->alias}.product_title_id" => $titleIds,
                    ]),
                    'alias' => 'ProductVariantTitle',
                    'type' => 'LEFT',
                    'conditions' => ['ProductVariantTitle.product_id' => $this->Product->primaryKeyIdentifier()],
                ],
            ],
            'conditions' => [
                'Product.product_title_id' => $titleIds,
                'Product.product_title' => null,
                'Product.user_id' => $userId,
                'Product.deleted' => false,
            ],
            'fields' => [
                'Product.id',
                'Product.product_title_id',
                'ProductVariantTitle.variant_title',
            ],
        ]);
        if (!$productsMissingTitle) {
            return;
        }

        $records = array_map(function(array $product) use ($titleValueById): array {
            $id = (int)$product['Product']['id'];
            $titleId = (int)$product['Product']['product_title_id'];
            $titleValue = (string)$titleValueById[$titleId];
            $variantTitle = (string)$product['ProductVariantTitle']['variant_title'];

            $productTitle = $titleValue . ($variantTitle ? " - {$variantTitle}" : '');

            return [
                'id' => $id,
                'product_title' => $productTitle,
            ];
        }, $productsMissingTitle);

        $this->Product->saveMulti($records);
    }

    protected function _export_translations_headers(): array
    {
        $locales = array_map('strtoupper', SupportedLanguages::getLocalesSet());

        $importHeaders = [
            'ProductTitle.handle' => $this->importHeaders['ProductTitle.handle'],
        ];

        $importHeaders['ProductTitle.title'] = $this->importHeaders['ProductTitle.title'];
        foreach ($locales as $localeKey => $localeLabel) {
            $importHeaders["titleTranslation.{$localeKey}"] = sprintf('%s %s', $localeLabel, $importHeaders['ProductTitle.title']);
        }

        $importHeaders['ProductTitle.description'] = 'Description';
        foreach ($locales as $localeKey => $localeLabel) {
            $importHeaders["descriptionTranslation.{$localeKey}"] = sprintf('%s %s', $localeLabel, $importHeaders['ProductTitle.description']);
        }

        return $importHeaders;
    }

    public function export_translations(): ?CakeResponse
    {
        $this->autoRender = false;
        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);

        $userId = (int)$this->Auth->user('id');

        $conditions = $this->_productExportConditions($userId);
        if (!$this->Product->exists($conditions)) {
            return $this->_exceptionResponse(new NotFoundException(), __('No active products found. Make sure they are configured and try again.'));
        }

        $importHeaders = $this->_export_translations_headers();

        $tableModel = [];
        foreach ($importHeaders as $field => $columnName) {
            $tableModel[] = $this->PhpExcel->newExportColumn(
                $columnName,
                fn(array $record): string => (string)Hash::get($record, $field),
                ['filter' => true]
            );
        }

        $fileName = $this->Auth->user('company_name') . ' Product Translations.xlsx';

        $this->Spout->doWithOpenWriter($fileName, function() use ($conditions, $tableModel) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));

            $translationAssocs = ['title' => 'titleTranslation', 'description' => 'descriptionTranslation'];
            $originalLocale = $this->ProductTitle->locale;
            $this->ProductTitle->bindTranslation($translationAssocs, false);
            $this->ProductTitle->locale = '';
            // Get MySQL compatible fields for translation export
            $useAnyValue = $this->supportsAnyValue();
            $fields = $this->Product->getTranslationExportFields($useAnyValue);

            $this->ProductTitle->streamPagedQuery([
                'contain' => array_fill_keys($translationAssocs, ['fields' => ['locale', 'foreign_key', 'content']]),
                'joins' => [
                    [
                        'table' => $this->Product->table,
                        'alias' => 'Product',
                        'type' => 'INNER',
                        'conditions' => [
                            'Product.product_title_id' => $this->ProductTitle->primaryKeyIdentifier(),
                        ],
                    ],
                ],
                'conditions' => $conditions,
                'fields' => $fields,
                'group' => ['ProductTitle.handle'],
                'order' => ['ProductTitle.handle' => 'ASC'],
            ], function(array $record) use ($tableModel, $translationAssocs): void {
                foreach ($translationAssocs as $alias) {
                    $record[$alias] = array_column($record[$alias], 'content', 'locale');
                }

                $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $record));
            });
            $this->ProductTitle->locale = $originalLocale;
            $this->ProductTitle->unbindTranslation($translationAssocs);
        });

        return null;
    }

    public function import_translations(): ?CakeResponse
    {
        $this->autoRender = false;
        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);
        $this->request->allowMethod('post', 'put');

        $userId = (int)$this->Auth->user('id');
        $defaultLocale = (string)Configure::read('Config.language');

        try {
            $filePath = $this->Upload->getTempFile($this->request->data('Product.upload'));
            $fileName = $this->request->data('Product.upload.name');

            $tableMap = $this->Spout->extractTableData($filePath, $fileName);
            if (!$tableMap) {
                throw new UserFriendlyException(__('Import file is empty'));
            }

            $allImportHeaders = $this->_export_translations_headers();

            $errorMessages = $this->_checkForTitleTranslationsImportKeyColumnErrorMessages($userId, $tableMap, $allImportHeaders);
            if ($errorMessages) {
                if (count($errorMessages) > 1) {
                    $errorMessage = __('Import file validation errors');
                    $errorMessage .= sprintf('<ul style="list-style-type: disc;"><li>%s</li></ul>', implode('</li><li>', $errorMessages));
                } else {
                    $errorMessage = current($errorMessages);
                }

                throw new UserFriendlyException($errorMessage);
            }

            $tableHeaders = array_keys(current($tableMap));
            $importHeaders = array_intersect($allImportHeaders, $tableHeaders);
            $handleColumn = (string)$importHeaders['ProductTitle.handle'];

            // Group by handle key to future-proof processing variant option translation rows grouped by handle
            $tableMapByKey = array_reduce(array_keys($tableMap), function(array $map, int $row) use ($tableMap, $handleColumn): array {
                $rowMap = (array)$tableMap[$row];
                $handleKey = ProductTitle::normalizeHandle((string)$rowMap[$handleColumn]);

                $map[$handleKey][$row] = $rowMap;

                return $map;
            }, []);

            $handleByKey = array_map(function(array $tableMap) use ($handleColumn): string {
                return (string)current(array_filter(array_column($tableMap, $handleColumn, $handleColumn)));
            }, $tableMapByKey);

            $translationAssocs = ['title' => 'titleTranslation', 'description' => 'descriptionTranslation'];
            $originalLocale = $this->ProductTitle->locale;
            $this->ProductTitle->bindTranslation($translationAssocs, false);
            $this->ProductTitle->locale = '';

            $existingTitles = (array)$this->ProductTitle->find('all', [
                'contain' => array_fill_keys($translationAssocs, ['fields' => ['id', 'locale', 'model', 'foreign_key', 'field']]),
                'conditions' => [
                    'ProductTitle.user_id' => $userId,
                    'ProductTitle.handle' => $handleByKey,
                ],
                'fields' => ['id', 'handle'],
            ]);
            $titleIdByKey = array_flip(
                array_map(
                    fn(string $handle): string => ProductTItle::normalizeHandle($handle),
                    Hash::combine($existingTitles, '{n}.ProductTitle.id', '{n}.ProductTitle.handle')
                )
            );
            $i18nIdMap = [];
            foreach ($existingTitles as $record) {
                foreach ($translationAssocs as $i18nAlias) {
                    foreach ((array)($record[$i18nAlias] ?? []) as $i18nRecord) {
                        $i18nIdMap[$i18nRecord['locale']][$i18nRecord['model']][$i18nRecord['foreign_key']][$i18nRecord['field']] = $i18nRecord['id'];
                    }
                }
            }

            // Use actual handle values as saveData keys for error reporting purposes
            $keyByHandle = array_flip($handleByKey);

            $saveMany = array_map(function(string $handleKey) use (
                $userId,
                $defaultLocale,
                $tableMapByKey,
                $importHeaders,
                $translationAssocs,
                $titleIdByKey,
                $i18nIdMap
            ): array {
                $tableMap = (array)$tableMapByKey[$handleKey];
                $titleId = (int)$titleIdByKey[$handleKey];

                $saveData = [
                    'ProductTitle' => ['id' => $titleId],
                ];
                foreach ($importHeaders as $fieldPath => $columnName) {
                    $value = (string)current(array_filter(array_column($tableMap, $columnName, $columnName)));
                    $saveData = Hash::insert($saveData, $fieldPath, $value);
                }
                foreach ($translationAssocs as $field => $alias) {
                    $defaultTranslation = $saveData[$alias][$defaultLocale] ?? null;
                    // `Hash::remove()` also removes the parent alias key if no translations remain
                    $saveData = Hash::remove($saveData, "{$alias}.{$defaultLocale}");
                    if ($defaultTranslation) {
                        $saveData['ProductTitle'][$field] = $defaultTranslation;
                    }
                    if (empty($saveData[$alias])) {
                        continue;
                    }

                    $saveData[$alias] = array_map(fn(string $locale): array => [
                        'id' => $i18nIdMap[$locale]['ProductTitle'][$titleId][$field] ?? null,
                        'locale' => $locale,
                        'model' => 'ProductTitle',
                        'foreign_key' => $titleId,
                        'field' => $field,
                        'content' => $saveData[$alias][$locale],
                    ], array_keys($saveData[$alias]));
                    $saveData[$alias] = array_filter($saveData[$alias], fn(array $record): bool => $record['id'] || $record['content']);
                }

                return $saveData;
            }, $keyByHandle);

            $this->ProductTitle->locale = $defaultLocale;
            $success = (bool)$this->ProductTitle->saveMany($saveMany, ['deep' => true]);

            $this->ProductTitle->unbindTranslation($translationAssocs);
            $this->ProductTitle->locale = $originalLocale;

            if (!$success) {
                $presentableTitleErrors = $this->ProductTitle->validationErrors;
                if ($presentableTitleErrors) {
                    throw new UserFriendlyException($this->_formatPresentableTitleErrors($presentableTitleErrors));
                }

                throw new BadRequestException(json_encode(['errors' => $this->ProductTitle->validationErrors, 'data' => $this->ProductTitle->data]));
            }

            $this->setFlash($this->_appendDebugOutput(__('Product details successfully uploaded')), 'success');
        } catch (UserFriendlyException $e) {
            CakeLog::debug($e->getMessage());
            $this->setFlash($this->_appendDebugOutput($e->getMessage()), 'error');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash($this->_appendDebugOutput(__('An error occurred uploading the file')), 'error');
        } finally {
            if (!empty($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
        }

        return $this->redirect($this->referer());
    }

    /**
     * @param int $userId
     * @param array $tableMap
     * @param array $allImportHeaders
     * @return string[] Error flash messages
     */
    protected function _checkForTitleTranslationsImportKeyColumnErrorMessages(int $userId, array $tableMap, array $allImportHeaders): array
    {
        $handleColumn = (string)$allImportHeaders['ProductTitle.handle'];
        if (array_has_any($tableMap, fn(array $rowMap): bool => !isset($rowMap[$handleColumn]))) {
            return [
                __('Import file requires the %s column', json_encode($handleColumn)),
            ];
        }
        // Validations past this point assume the required columns are present
        $messages = [];
        $handleByRow = array_map(fn(array $rowMap): string => (string)$rowMap[$handleColumn], $tableMap);

        $emptyByRow = array_filter($handleByRow, fn(string $handle): bool => !$handle);
        if ($emptyByRow) {
            $messages[] = __n(
                'Import file requires a value for %s in row: %s',
                'Import file requires values for %s in rows: %s',
                count($emptyByRow),
                json_encode($handleColumn),
                implode(', ', array_keys($emptyByRow))
            );
        }
        $handleByRow = array_diff_key($handleByRow, $emptyByRow);
        if (!$handleByRow) {
            return $messages;
        }

        $existingHandles = $this->ProductTitle->find('list', [
            'recursive' => -1,
            'conditions' => [
                'ProductTitle.handle' => $handleByRow,
                'ProductTitle.user_id' => $userId,
                'ProductTitle.deleted' => false,
            ],
            'fields' => ['handle', 'handle'],
        ]);
        $invalidByRow = array_diff($handleByRow, $existingHandles);
        if ($invalidByRow) {
            $messages[] = __n(
                'Import file contains an invalid %s value in row: %s',
                'Import file contains invalid %s values in rows: %s',
                count($invalidByRow),
                json_encode($handleColumn),
                implode(', ', array_keys($invalidByRow))
            );
        }

        return $messages;
    }

    protected function _formatPresentableTitleErrors(array $presentableTitleErrors): string
    {
        $handlesByError = $this->_listValidationKeysByErrorMessage($presentableTitleErrors);
        $presentableTitleErrorMessage = __('Import file validation errors for %s groups', json_encode($this->importHeaders['ProductTitle.handle']));
        $presentableTitleErrorMessage .= sprintf('<ul style="list-style-type: disc;"><li>%s</li></ul>', implode('</li><li>', array_map(
            function(string $error) use ($handlesByError): string {
                $handles = (array)$handlesByError[$error];

                return __xn(
                    'Import file validation errors',
                    '"%s" for handle %s',
                    '"%s" for handles %s',
                    count($handles),
                    $error,
                    implode(', ', $handles)
                );
            },
            array_keys($handlesByError)
        )));

        return $presentableTitleErrorMessage;
    }

    /**
     * admin product preview page
     */
    public function admin_view($uuid = null)
    {
        $this->layout = 'admin';
        $this->view($uuid);
    }

    public function admin_delete_incomplete($userId)
    {
        $this->autoRender = false;
        $this->request->allowMethod('post', 'delete');

        $conditions = array(
            'Product.user_id' => $userId,
            'Product.product_status' => array(Product::STATUS_INCOMPLETE, Product::STATUS_DISABLED, Product::STATUS_DELETED),
            'OrderProduct.id' => null,
            'DealerOrderProduct.id' => null,
        );

        $this->Product->bindModel(['hasOne' => ['OrderProduct', 'DealerOrderProduct']], false);

        $fullCount = (int)$this->Product->find('count', ['contain' => ['OrderProduct', 'DealerOrderProduct'], 'conditions' => $conditions]);

        $productIds = $this->Product->find('all', [
            'contain' => ['OrderProduct', 'DealerOrderProduct'],
            'conditions' => $conditions,
            'fields' => array('Product.id', 'OrderProduct.id'),
            'limit' => 100,
        ]);
        $productIds = Hash::extract($productIds, '{n}.Product.id');

        $this->Product->unbindModel(['hasOne' => ['OrderProduct', 'DealerOrderProduct']], false);

        if (!$this->Product->deleteAllByIds($productIds)) {
            $message = 'Failed to delete disabled products and associations ' . json_encode(compact('userId', 'productIds'));
            return $this->_exceptionResponse(new InternalErrorException($message), $message, true);
        }

        $count = count($productIds);
        return $this->_successResponse("{$count} out of {$fullCount} disabled products have been deleted");
    }

    /**
     * Product preview page
     *
     * @param string $uuid
     * @return CakeResponse|null
     */
    public function view($uuid = null)
    {
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if (empty($this->request->query['branch_id'])) {
            unset($this->request->query['branch_id']);

            if ($userType === User::TYPE_RETAILER) {
                $this->request->query['branch_id'] = $userId;
            } elseif ($userType === User::TYPE_STAFF) {
                $this->request->query['branch_id'] = (int)$this->Auth->user('Branch');
            }
        }
        $retailerId = ((int)$this->request->query('branch_id')) ?: null;
        $isConsumerView = $this->isConsumerView($this->request, $userType);
        $isRetailer = in_array($userType, User::TYPES_RETAILER);

        $product = $this->_validateViewModel(
            $this->Product->getViewModel($uuid, $retailerId)
        );

        $brandId = $product['Product']['user_id'];

        $validWarehouses = $this->Warehouse->listValidUserWarehouses($brandId, $retailerId);
        if (empty($this->request->query['warehouseId']) || !in_array($this->request->query['warehouseId'], array_keys($validWarehouses))) {
            if ($userType === User::TYPE_MANUFACTURER) {
                // Brands can select the empty option to designate 'All' warehouses
                unset($this->request->query['warehouseId']);
            } else {
                $this->request->query['warehouseId'] = $this->ManufacturerRetailer->findDefaultWarehouseId($brandId, $retailerId);
            }
        }
        $warehouseId = $this->request->query('warehouseId');

        $existingCarts = $this->B2bCart->findAllForB2bCatalogueFilter($userId, $userType, $retailerId, $brandId);

        $validCartsById = Hash::combine($existingCarts, '{n}.B2bCart.id', '{n}');
        $validCartIds = array_keys($validCartsById);
        $temporaryCart = $this->getTemporaryCart($this->request);
        if(!empty($temporaryCart)){
            $existingCarts[] = $temporaryCart;
        }
        $this->set('existingCarts', $existingCarts);

        if (empty($this->request->query['cartId']) || !in_array($this->request->query['cartId'], $validCartIds)) {
            unset($this->request->query['cartId']);
        }
        $cartId = $this->request->query('cartId');

        $discount = $validCartsById[$cartId]['Discount']
            ?? $temporaryCart['Discount']
            ?? [];
        $discountId = $discount['id']
            ?? null;
        $orderType = $validCartsById[$cartId]['B2bCart']['order_type']
            ?? $temporaryCart['B2bCart']['order_type']
            ?? null;
        $this->set('orderType', $orderType);

        $product = $this->Product->getViewVariants($product, $retailerId, $warehouseId, $discount, $orderType, $userType);
        $product_details = $this->Product->findForEdit($product['Product']['id']);
        $collectionOptionsByProduct = $this->Product->getCollectionOptionsByProduct($product['Product']['id']);
        $collectionOptionsByUser = $this->Collection->getCollectionsOptionsByUser($brandId);
        $this->set('product', $product);
        $this->set('productTags', $product_details['Tag']);
        $this->set('productTypes', $this->Product->getProductTypesByUser($userId));
        $this->set('tagOptions', $this->Tag->getTagsByUser($userId));
        $this->set('productCategories', $this->UserCategories->listCategoryNames($brandId));
        $this->set('selectedCategories', $this->ProductCategories->listIdByCatIds($product['Product']['id']));
        $this->set('collectionOptionsByProduct', $collectionOptionsByProduct);
        $this->set('collectionOptionsByUser', $collectionOptionsByUser);
        $this->set('warehouseList', $validWarehouses);
        $this->set('cartId', $cartId);
        $this->set('b2bCartType', $orderType);
        $this->set('discountId', $discountId);
        $this->set('existingCarts', $existingCarts);
        $this->set('isConsumerView', $isConsumerView);
        $this->set('isRetailer', $isRetailer);

        if ($userType === User::TYPE_RETAILER) {
            $this->Viewslog->saveProductView($userId, $product['Product']['id']);
        }
    }

    public function collections()
    {
        $originalVirtualFields = $this->Collection->virtualFields;
        $this->Collection->virtualFields['product_count'] = 'COALESCE(CollectionsProductCount.count, 0)';
        $search = $this->request->query('search') ?? '';
        $sortField = $this->request->query('sortField') ?? 'collection_order';
        $sortOrder = $this->request->query('sortOrder') ?? 'ASC';
        $pageNumber = $this->request->query('pageNumber') ?? 1;
        $limit = 10;
        $conditions = [
            'Collection.user_id' => $this->Auth->user('id'),
        ];

        if (!empty($search)) {
            $conditions['Collection.title LIKE'] = '%' . $search . '%';
        }

        $collections = $this->Collection->find('all', [
            'joins' => [
                [
                    'table' => $this->CollectionsProduct->buildSubquery([
                        'joins' => [
                            [
                                'table' => 'products',
                                'alias' => 'Product',
                                'type' => 'INNER',
                                'conditions' => [
                                    'Product.id' => $this->CollectionsProduct->identifier('CollectionsProduct.product_id'),
                                ],
                            ],
                        ],
                        'conditions' => [
                            'Product.user_id' => $this->Auth->user('id'),
                            'Product.deleted' => false,
                        ],
                        'fields' => ['CollectionsProduct.collection_id', 'COUNT(DISTINCT `Product`.`product_title_id`) AS `count`'],
                        'group' => ['CollectionsProduct.collection_id'],
                    ]),
                    'alias' => 'CollectionsProductCount',
                    'type' => 'LEFT',
                    'conditions' => [
                        'CollectionsProductCount.collection_id' => $this->Collection->primaryKeyIdentifier(),
                    ],
                ],
            ],
            'conditions' => $conditions,
            'fields' => ['id', 'title', 'product_count', 'image_url', 'is_on_store_locator', 'is_on_b2b'],
            'order' => [$sortField => $sortOrder],
            'limit' => $limit,
            'page' => $pageNumber,
        ]);

        $totalCount = $this->Collection->find('count', [
            'conditions' => $conditions,
        ]);

        $paging = [
            'total' => $totalCount,
            'current' => $pageNumber,
            'limit' => $limit,
        ];

        $this->Collection->virtualFields = $originalVirtualFields;
        $this->set(compact('collections', 'paging', 'search'));
    }

    public function uploadCollectionImage($id)
    {
        if ($this->request->is('post')) {
            $collection = $this->Collection->record($id, [
                'contain' => [
                    'User' => ['fields' => ['id', 'uuid']],
                ],
                'fields' => ['id', 'user_id', 'image_url'],
            ]);
            if (empty($collection['Collection']['id'])) {
                throw new NotFoundException('Collection not found.');
            }
            if ($collection['Collection']['user_id'] !== $this->Auth->user('id')) {
                throw new ForbiddenException('You do not have permission to upload an image for this collection.');
            }
            $file = (array)$this->request->data['collection_image'];
            $sanitizedFileName = !empty($file['name']) ? preg_replace('/[^a-zA-Z0-9-_\.]/', '_', basename($file['name'])) : null;
            if(!empty($sanitizedFileName)) {
                $imageUrl = $this->Upload->replaceFileInUserHash(
                    $collection['Collection']['image_url'],
                    $file,
                    $collection['User']['uuid'],
                    'collections',
                    $sanitizedFileName
                );
                if (!$this->Collection->save(['id' => $id, 'image_url' => $imageUrl])) {
                    throw new InternalErrorException(json_encode([
                        'message' => 'Failed to save collection image',
                        'errors' => $this->Collection->validationErrors,
                        'data' => $this->Collection->data,
                    ]));
                }
            }
            // Return success response
            $this->set('response', [
                'imageUrl' => $imageUrl,
                'fileName' => $sanitizedFileName
            ]);
            $this->set('_serialize', ['response']);
            return $this->response;
        }
    }

    public function product_collections($id)
    {
        $collection = $this->Collection->recordWithAllTranslations($id, [
            'fields' => ['id', 'user_id', 'title', 'image_url'],
        ]);

        if (!$collection) {
            throw new NotFoundException(__('Collection not found'));
        }
        if ($collection['Collection']['user_id'] !== $this->Auth->user('id')) {
            throw new ForbiddenException(__('You do not have permission to view this collection'));
        }
        $search = $this->request->query('search') ?? '';
        $sortField = $this->request->query('sortField') ?? 'CollectionsProduct.product_order';
        $sortOrder = $this->request->query('sortOrder') ?? 'ASC';
        $pageNumber = $this->request->query('pageNumber') ?? 1;
        $limit = 10;

        // Get MySQL-compatible query configuration from model
        $useAnyValue = $this->supportsAnyValue();
        $queryOptions = [
            'order' => [$sortField => $sortOrder],
            'limit' => $limit,
            'page' => $pageNumber,
        ];

        $products = $this->Collection->CollectionsProduct->find('all', 
            $this->Product->getCollectionProductQuery($id, $queryOptions, $useAnyValue)
        );

        $totalCount = $this->Collection->CollectionsProduct->find('count', 
            $this->Product->getCollectionProductCountQuery($id, $useAnyValue)
        );
        $paging = [
            'total' => $totalCount,
            'current' => $pageNumber,
            'limit' => $limit,
        ];

        $this->set(compact('collection', 'products', 'paging', 'search'));
    }

    public function addcollection()
    {
        if ($this->request->is('post')) {
            $collectionName = $this->request->data['Collection']['title'];
            $userId = $this->Auth->user('id');
            $collectionHandle = generate_slug($collectionName);
            $data = [
                'user_id' => $userId,
                'title' => $collectionName,
                'handle' => $collectionHandle,
            ];

            $this->Collection->create();
            if ($this->Collection->save($data)) {
                return $this->_successResponse();
            }

            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->Collection->validationErrors, 'data' => $this->Collection->data])), null, true);
        }
    }

    public function ajax_add_collection_product_search()
    {
        $term = $this->request->query('term');
        $excludes = (array)$this->request->query('exclude');
        $products = $this->Product->findForCollectionProductSearch($this->Auth->user('id'), $term, $excludes);

        $formattedProducts = array_map(
            function($product) {
                $autocompleteFields = [
                    'category' => null,
                    'label' => $product['Product']['product_name'],
                    'value' => $product['Product']['product_name'],
                ];

                return $autocompleteFields + $product;
            },
            $products
        );
        $this->set(compact('formattedProducts'));
        $this->set('_serialize', 'formattedProducts');
    }

    public function ajax_add_collection_product($collectionId, $productId)
    {
        $userId = (int)$this->Auth->user('id');

        $collectionExists = $this->Collection->exists([
            'Collection.id' => $collectionId,
            'Collection.user_id' => $userId,
        ]);
        if (!$collectionExists) {
            throw new NotFoundException(__('Collection not found'));
        }

        $product = $this->Product->find('first', [
            'recursive' => -1,
            'conditions' => ['Product.id' => $productId],
            'fields' => ['Product.id', 'Product.product_title_id'],
        ]);
        if (empty($product)) {
            throw new NotFoundException(__('Product not found'));
        }

        $productTitleId = $product['Product']['product_title_id'];
        if (empty($productTitleId)) {
            throw new NotFoundException(__('Product title not found'));
        }

        $allProducts = $this->Product->find('all', [
            'recursive' => -1,
            'conditions' => ['Product.product_title_id' => $productTitleId],
            'fields' => ['id'],
        ]);
        $allProductIds = Hash::extract($allProducts, '{n}.Product.id');

        $existingIdByProductId = (array)$this->CollectionsProduct->find('list', [
            'conditions' => ['CollectionsProduct.collection_id' => $collectionId],
            'fields' => ['product_id', 'id'],
        ]);

        $collectionProductsData = [];
        foreach ($allProductIds as $variantId) {
            $collectionProductsData[] = [
                'id' => $existingIdByProductId[$variantId] ?? null,
                'collection_id' => $collectionId,
                'product_id' => $variantId,
            ];
        }
        $success = (bool)$this->CollectionsProduct->saveMany($collectionProductsData);

        if (!$success) {
            $this->set([
                'success' => false,
                'message' => __('Some variants could not be added to the collection.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }
        $this->set([
            'success' => true,
            'message' => __('All variants added to collection successfully.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function ajax_browse_products($collectionId = null)
    {
        $userId = $this->Auth->user('id');

        $alreadySelectedProductIds = $this->CollectionsProduct->find('list', [
            'conditions' => ['collection_id' => $collectionId],
            'fields' => ['product_id', 'product_id'],
            'recursive' => -1
        ]);

        // Check MySQL version for GROUP BY compatibility
        $useAnyValue = $this->supportsAnyValue();
        $fields = $this->Product->getBrowseProductFields($useAnyValue);
        $order = $this->Product->getBrowseProductOrder($useAnyValue);

        $products = $this->Product->find('all', [
            'recursive' => -1,
            'conditions' => [
                'Product.user_id' => $userId,
                'Product.deleted' => false,
                'Product.product_title_id !=' => null,
            ],
            'fields' => $fields,
            'order' => $order,
            'group' => ['Product.product_title_id'],
        ]);
        $this->set(compact('products', 'collectionId', 'alreadySelectedProductIds'));
    }

    public function ajax_add_collection_products()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;
        $this->response->type('json');

        $data = json_decode($this->request->input(), true);
        $collectionId = (int)($data['collection_id'] ?? 0);
        $productIds = (array)($data['product_ids'] ?? []);
        $userId = (int)$this->Auth->user('id');

        if (!$collectionId) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Invalid collection ID.'
            ]));
            return $this->response;
        }

        $collectionExists = $this->Collection->exists([
            'Collection.id' => $collectionId,
            'Collection.user_id' => $userId,
        ]);

        if (!$collectionExists) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Collection not found.'
            ]));
            return $this->response;
        }

        $existingProductIds = $this->CollectionsProduct->find('list', [
            'conditions' => ['collection_id' => $collectionId],
            'fields' => ['product_id', 'product_id'],
            'recursive' => -1
        ]);

        $added = [];
        foreach ($productIds as $productId) {
            if (!in_array($productId, $existingProductIds)) {
                $this->CollectionsProduct->create();
                if ($this->CollectionsProduct->save([
                    'collection_id' => $collectionId,
                    'product_id' => $productId
                ])) {
                    $added[] = $productId;
                }
            }
        }

        $toDelete = array_diff($existingProductIds, $productIds);
        if (!empty($toDelete)) {
            $this->CollectionsProduct->deleteAllJoinless([
                'collection_id' => $collectionId,
                'product_id' => $toDelete
            ], false);
        }

        $this->response->body(json_encode([
            'success' => true,
            'message' => 'Collection updated successfully.',
            'added' => $added,
            'removed' => array_values($toDelete)
        ]));

        return $this->response;
    }

    public function ajax_remove_collection_product()
    {
        $collectionId = $this->request->data('collection_id');
        $productId = $this->request->data('product_id');

        $product = $this->Product->find('first', [
            'recursive' => -1,
            'conditions' => ['Product.id' => $productId],
            'fields' => ['Product.product_title_id'],
        ]);

        if (empty($product)) {
            throw new NotFoundException(__('Product not found'));
        }

        $productTitleId = $product['Product']['product_title_id'];
        if (empty($productTitleId)) {
            throw new NotFoundException(__('Product title not found'));
        }

        $allProducts = $this->Product->find('all', [
            'recursive' => -1,
            'conditions' => ['Product.product_title_id' => $productTitleId],
            'fields' => ['id'],
        ]);
        $allProductIds = Hash::extract($allProducts, '{n}.Product.id');

        $result = $this->CollectionsProduct->deleteAllJoinless([
            'collection_id' => $collectionId,
            'product_id' => $allProductIds
        ]);

        $this->set([
            'success' => $result,
            '_serialize' => ['success']
        ]);
    }

    public function ajax_remove_collection()
    {
        $collectionId = $this->request->data('collection_id');

        if (!$this->Collection->existsById($collectionId)) {
            throw new NotFoundException(__('Collection not found'));
        }

        $result = $this->Collection->delete($collectionId);

        $this->set([
            'success' => $result,
            '_serialize' => ['success']
        ]);
    }

    public function update_collection_product_order($collectionId = null)
    {
        $this->request->allowMethod(['post']);

        $productOrder = $this->request->data('order');

        $collection = $this->Collection->record($collectionId, [
            'fields' => ['id', 'user_id'],
        ]);
        if (!$collection) {
            throw new NotFoundException(__('Collection not found'));
        }
        if ($collection['Collection']['user_id'] !== $this->Auth->user('id')) {
            throw new ForbiddenException(__('You do not have permission to edit this collection'));

        }
        $success = $this->CollectionsProduct->doInTransaction(function() use ($collectionId, $productOrder): bool {
            foreach ($productOrder as $index => $productId) {
                $result = $this->CollectionsProduct->updateAllJoinless(
                    ['product_order' => $index + 1],
                    ['product_id' => $productId, 'collection_id' => $collectionId]
                );
                if (!$result) {
                    return false;
                }
            }
            return true;
        });
        if ($success) {
            $this->set([
                'success' => true,
                'message' => __('Product order updated successfully.'),
                '_serialize' => ['success', 'message']
            ]);

        } else {
            $this->set([
                'success' => false,
                'message' => __('Failed to update some product orders. Please try again.'),
                '_serialize' => ['success', 'message']
            ]);
        }
    }

    public function update_collection_order()
    {
        $this->request->allowMethod(['post']);

        $collectionOrder = $this->request->data('order');

        $validCollectionIds = array_keys($this->Collection->getCollectionsOptionsByUser($this->Auth->user('id')));
        $invalidCollectionIds = array_diff($collectionOrder, $validCollectionIds);
        if ($invalidCollectionIds) {
            $this->set([
                'success' => false,
                'message' => __('Some collections were not found. Please try again.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $saveData = [];
        foreach ($collectionOrder as $index => $collectionId) {
            $saveData[] = [
                'id' => $collectionId,
                'collection_order' => ($index + 1),
            ];
        }
        if (!$this->Collection->saveMany($saveData)) {
            $this->set([
                'success' => false,
                'message' => __('Failed to update some collection orders. Please try again.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $this->set([
            'success' => true,
            'message' => __('Collection order updated successfully.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function updateCollectionsTitle()
    {
        $this->request->allowMethod(['post']);

        $collectionId = $this->request->data('id');
        $newTitle = $this->request->data('title');

        $language = $this->request->data('locale') ?? 'eng';
        $this->Collection->locale = $language;

        if (empty($collectionId) || empty($newTitle)) {
            $this->set([
                'success' => false,
                'message' => __('Invalid input data.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $collection = $this->Collection->get($collectionId);

        $collection['Collection']['title'] = $newTitle;
        $success = $this->Collection->save($collection);

        $this->set([
            'success' => $success,
            'message' => $success ? __('Collection title updated successfully.') : __('Failed to update title. Please try again.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function updateMainImage($newMainImageId)
    {
        if ($this->request->is('post')) {
            $newMainImage = $this->ProductImage->record($newMainImageId, [
                'fields' => ['id', 'product_id', 'image_url'],
            ]);
            if (!$newMainImage) {
                $this->set([
                    'success' => false,
                    'message' => __('Image not found.'),
                    '_serialize' => ['success', 'message']
                ]);
                return;
            }

            $productId = $newMainImage['ProductImage']['product_id'];
            if (!$this->Product->existsById($productId)) {
                $this->set([
                    'success' => false,
                    'message' => __('Product not found.'),
                    '_serialize' => ['success', 'message']
                ]);
                return;
            }
            $product = ['id' => $productId, 'product_image' => $newMainImage['ProductImage']['image_url']];

            if ($this->Product->save($product)) {
                $validImageIds = (array)$this->ProductImage->find('list', [
                    'recursive' => -1,
                    'conditions' => ['ProductImage.product_id' => $productId],
                    'fields' => ['id'],
                ]);
                $newOrder = json_decode($this->request->data('image_order'), true);
                $data = [];
                foreach ($newOrder as $index => $imageData) {
                    if (isset($imageData['id']) && isset($imageData['order']) && isset($validImageIds[$imageData['id']])) {
                        $data[] = [
                            'id' => $imageData['id'],
                            'image_order' => $imageData['order'],
                        ];
                    } else {
                        CakeLog::warning(json_encode(['message' => 'Missing id or order for image', 'imageData' => $imageData]));
                    }
                }
                if (!$this->ProductImage->saveMany($data)) {
                    CakeLog::warning(json_encode(['message' => 'Failed to save order for images', 'error' => $this->ProductImage->validationErrors, 'data' => $this->ProductImage->data]));
                }

                $this->set([
                    'success' => true,
                    'message' => __('Main image updated successfully.'),
                    '_serialize' => ['success', 'message']
                ]);
            } else {
                $this->set([
                    'success' => false,
                    'message' => __('Failed to update main image.'),
                    '_serialize' => ['success', 'message']
                ]);
            }
        }
    }

    public function updateStoreLocatorCheckbox()
    {
        $this->request->allowMethod(['post']);

        $collectionId = $this->request->data('id');
        $isOnStoreLocator = $this->request->data('is_on_store_locator');

        if (empty($collectionId)) {
            $this->set([
                'success' => false,
                'message' => __('Missing category ID.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        if (!$this->Collection->existsById($collectionId)) {
            $this->set([
                'success' => false,
                'message' => __('Collection not found.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $success = $this->Collection->save([
            'id' => $collectionId,
            'is_on_store_locator' => $isOnStoreLocator,
        ]);

        $this->set([
            'success' => $success,
            'message' => $success
                ? __('Store Locator setting updated.')
                : __('Failed to update setting.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function updateB2bCheckbox()
    {
        $this->request->allowMethod(['post']);

        $collectionId = $this->request->data('id');
        $isOnB2b = $this->request->data('is_on_b2b');

        if (empty($collectionId)) {
            $this->set([
                'success' => false,
                'message' => __('Missing category ID.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        if (!$this->Collection->existsById($collectionId)) {
            $this->set([
                'success' => false,
                'message' => __('Collection not found.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $success = $this->Collection->save([
            'id' => $collectionId,
            'is_on_b2b' => $isOnB2b,
        ]);

        $this->set([
            'success' => $success,
            'message' => $success
                ? __('B2B setting updated.')
                : __('Failed to update setting.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function deleteProductImage()
    {
        if ($this->request->is('post')) {
            $imageId = $this->request->data('image_id');

            $productImage = $this->ProductImage->find('first', [
                'recursive' => -1,
                'conditions' => ['ProductImage.id' => $imageId],
                'fields' => ['ProductImage.image_url', 'ProductImage.product_id']
            ]);

            if (empty($productImage)) {
                $this->set([
                    'success' => false,
                    'message' => __('Image not found.'),
                    '_serialize' => ['success', 'message']
                ]);
                return;
            }

            $imageUrl = $productImage['ProductImage']['image_url'];
            $productID = $productImage['ProductImage']['product_id'];

            $productTitleId = $this->Product->field('product_title_id', ['id' => $productID]);
            $allProducts = $this->Product->find('all', [
                'recursive' => -1,
                'conditions' => ['Product.product_title_id' => $productTitleId],
                'fields' => ['id'],
            ]);
            $allProductIds = Hash::extract($allProducts, '{n}.Product.id');

            $deleted = $this->ProductImage->deleteAllJoinless([
                'ProductImage.image_url' => $imageUrl,
                'ProductImage.product_id' => $allProductIds,
            ]);
            if ($deleted) {
                $this->Upload->deleteFromWebroot($imageUrl);
                $this->set([
                    'success' => true,
                    'message' => __('Image deleted successfully.'),
                    '_serialize' => ['success', 'message']
                ]);
            } else {
                $this->set([
                    'success' => false,
                    'message' => __('Failed to delete image.'),
                    '_serialize' => ['success', 'message']
                ]);
            }
            $this->redirect($this->referer());
        }
    }

    public function updateProduct()
    {
        if ($this->request->is('post')) {
            $productTitleId = $this->Product->fieldByConditions('product_title_id', ['Product.id' => $this->request->data('product_ID')]);
            $newProductTitle = $this->request->data('product_title');
            $newProductDescription = $this->request->data('product_description');
            $newProductType = $this->request->data('product_type');
            $newVendorName = $this->request->data('company_name');
            $newProductCategories = $this->request->data('product_cat');
            $newTags = $this->request->data('tags');
            $newCollections = $this->request->data('collections');
            $newSKU = $this->request->data('sku');
            $newUPC = $this->request->data('upc');
            $newVariantImage = $this->request->data('image');
            $productIds = $this->request->data('product_id');
            $newMainImageId = $this->request->data('new_main_image_id');
            $language = $this->request->data('locale') ?? SupportedLanguages::DEFAULT_LOCALE;

            $productSKUAndUPCAndImageData = [];
            for ($i = 0; $i < count($productIds); $i++) {
                $productId = $productIds[$i];
                $upc = $newUPC[$i];
                $sku = $newSKU[$i];
                $variantImage = $newVariantImage[$i];

                $productSKUAndUPCAndImageData[$productId] = [
                    'sku' => $sku,
                    'upc' => $upc,
                    'image' => $variantImage,
                ];
            }

            $productTitle = $this->ProductTitle->record($productTitleId, [
                'contain' => [
                    'User' => ['fields' => ['id', 'uuid', 'company_name']],
                ],
                'fields' => ['id', 'user_id', 'title', 'description'],
            ]);
            if (empty($productTitle)) {
                $this->set(['success' => false, 'message' => __('Product title not found.'), '_serialize' => ['success', 'message']]);
                return;
            }

            $defaultLocale = $this->ProductTitle->locale;
            $userId = (int)$productTitle['ProductTitle']['user_id'];

            try {
                $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);
                if ((int)$this->Auth->user('id') !== $userId) {
                    throw new ForbiddenException(json_encode(['Product' => ['user_id' => $userId], 'Auth' => User::extractAuthUserLogFields($this->Auth->user())]));
                }
            } catch (ForbiddenException $e) {
                return $this->_permissionDeniedResponse($e);
            }

            if (is_array($newProductType)) {
                $newProductType = (count($newProductType) > 1) ? json_encode($newProductType) : (string)current($newProductType);
            }

            $allProductIds = array_keys($this->Product->find('list', [
                'recursive' => -1,
                'conditions' => ['Product.product_title_id' => $productTitleId],
                'fields' => ['id', 'id'],
            ]));

            $imageFile = isset($_FILES['product_image']) ? $_FILES['product_image'] : null;
            if ($imageFile && $imageFile['error'] === UPLOAD_ERR_OK) {
                $imageUrl = $this->Upload->uploadToUserHash(
                    $imageFile,
                    $productTitle['User']['uuid'],
                    'products',
                    str_replace('.', '_', uniqid('product_', true))
                );

                $lastImageOrder = (int)$this->ProductImage->fieldByConditions('image_order', ['ProductImage.product_id' => $allProductIds], [
                    'order' => ['ProductImage.image_order' => 'DESC'],
                ]);
                $nextImageOrder = $lastImageOrder + 1;

                $data = [];
                foreach ($allProductIds as $id) {
                    $data[] = [
                        'id' => null,
                        'product_id' => $id,
                        'image_url' => $imageUrl,
                        'image_order' => $nextImageOrder,
                    ];
                }
                if (!$this->ProductImage->saveMany($data)) {
                    CakeLog::warning(json_encode(['message' => 'Failed to save product image.', 'errors' => $this->ProductImage->validationErrors, 'data' => $this->ProductImage->data]));
                    $this->set(['success' => false, 'message' => __('Failed to save product image.'), '_serialize' => ['success', 'message']]);
                    return;
                }
            }

            $validCategoryIds = $this->UserCategories->getUserCategories($userId);
            $newProductCategories = array_values(array_intersect($newProductCategories, $validCategoryIds));
            $retailerIds = $this->UserCategories->getRetailers($newProductCategories, $userId);

            $productData = [
                'product_type' => trim($newProductType),
                'vendor' => !empty($newVendorName) ? $newVendorName : $productTitle['User']['company_name'],
                'no_of_retailers' => count($retailerIds),
                'Category' => ['Category' => $newProductCategories],
                'Tag' => ['Tag' => $newTags ? array_values($this->Tag->listIdsByNameWithNewTags($userId, $newTags)) : []],
                'Retailer' => ['Retailer' => $retailerIds],
                'Collection' => ['Collection' => $newCollections ? array_values($this->Collection->getCollectionsIdsByTitle($userId, $newCollections)) : []],
            ];

            $saveAssociated = [
                'ProductTitle' => [
                    'id' => $productTitleId,
                    'title' => $newProductTitle,
                    'description' => $newProductDescription,
                ],
                'Product' => array_map(function(int $id) use ($productData, $productSKUAndUPCAndImageData): array {
                    $productData = ['id' => $id] + $productData;
                    if (isset($productSKUAndUPCAndImageData[$id])) {
                        $productData = array_merge($productData, [
                            'product_sku' => $productSKUAndUPCAndImageData[$id]['sku'],
                            'product_upc' => $productSKUAndUPCAndImageData[$id]['upc'],
                            'product_image' => $productSKUAndUPCAndImageData[$id]['image'],
                        ]);
                    }

                    return $productData;
                }, $allProductIds),
            ];

            $this->Product->bindModel([
                'hasAndBelongsToMany' => [
                    'Category' => ['with' => 'ProductCategory', 'associationForeignKey' => 'cat_id', 'unique' => 'keepExisting'],
                    'Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
                    'Retailer' => ['className' => 'User', 'with' => 'ProductRetailer', 'unique' => 'keepExisting'],
                    'Collection' => ['with' => 'CollectionsProduct', 'associationForeignKey' => 'collection_id', 'unique' => 'keepExisting'],
                ],
            ], false);

            $this->ProductTitle->locale = $language;
            $success = $this->ProductTitle->saveAssociated($saveAssociated, ['deep' => true]);

            $this->Product->unbindModel([
                'hasAndBelongsToMany' => [
                    'Category',
                    'Tag',
                    'Retailer',
                    'Collection',
                ],
            ], false);

            if (!$success) {
                CakeLog::warning(json_encode(['message' => 'Failed to save product', 'errors' => $this->ProductTitle->validationErrors, 'data' => $this->ProductTitle->data]));
                $this->set(['success' => false, 'message' => __('Failed to update product.'), '_serialize' => ['success', 'message']]);
                return;
            }

            if ($defaultLocale && $defaultLocale !== $this->ProductTitle->locale) {
                // Resave to the default locale to fix the current locale overwriting it
                $this->ProductTitle->locale = $defaultLocale;
                if (!$this->ProductTitle->save(array_intersect_key($productTitle['ProductTitle'], $saveAssociated['ProductTitle']))) {
                    CakeLog::warning(json_encode(['message' => 'Failed to save product title in default locale', 'errors' => $this->ProductTitle->validationErrors, 'data' => $this->ProductTitle->data]));
                }
            }

            if ($newMainImageId) {
                $this->updateMainImage($newMainImageId);
            }

            $this->set(['success' => true, 'message' => __('The product has been updated.'), '_serialize' => ['success', 'message']]);
            $this->redirect($this->referer());
        }
    }

    /**
     * Redirect if the product cannot be viewed.
     *
     * @param $product
     * @return array The product model.
     */
    protected function _validateViewModel($product)
    {
        if (empty($product['Product']['id'])) {
            throw new NotFoundException();
        }

        $filterProduct = function($product) {
            return !$product['deleted'] && (
                $product['user_id'] == $this->Auth->user('id') ||
                ($product['product_status'] === ProductStatus::ENABLED && !$product['is_fee_product'])
            );
        };

        if (!$filterProduct($product['Product'])) {
            $this->setFlash("{$product['Product']['product_title']} is in incomplete status. See other products from the brand.", 'error');
            $this->redirect($this->referer());
        }

        return $product;
    }

    public function watch_products()
    {
        $this->set('title_for_layout', __('Products'));

        $noRecords = 9;
        $this->set('noRecords', $noRecords);

        $this->_productRetailerList(
            $noRecords,
            1,
            '',
            array(
                'recursive' => -1,
                'conditions' => array(
                    'Product.id' => $this->WatchProduct->getWatchProductIds($this->_watchCondition()),
                    'Product.deleted' => false,
                    'Product.product_status' => Product::STATUS_ENABLED,
                ),
            ),
            'published_at DESC'
        );
    }

    public function ajax_watch_products()
    {
        $this->layout = '';

        $this->_productRetailerList(
            $this->request->data['noRecords'],
            $this->request->data['pageNumber'],
            $this->request->data['count_products'],
            array(
                'recursive' => -1,
                'conditions' => array(
                    'Product.id' => $this->WatchProduct->getWatchProductIds($this->_watchCondition()),
                    'Product.deleted' => false,
                    'Product.product_status' => Product::STATUS_ENABLED,
                )
            ),
            'published_at DESC'
        );
    }

    public function watch_product()
    {
        $uuid = $this->request->param('uuid');

        if ($this->request->is('POST') && $this->Auth->user('user_type') == 'Retailer') {
            $success = false;
            $message = "Error adding this product to your watch list";

            $product = $this->Product->find('first', [
            'recursive' => -1,
            'conditions' => ['Product.uuid' => $uuid],
            'fields' => ['id', 'user_id'],
        ]);
            if (!empty($product['Product']['id'])) {
                $watch = array(
                    'product_id' => $product['Product']['id'],
                    'user_id' => $product['Product']['user_id'],
                    'retailer_id' => $this->Auth->user('id'),
                );
                if (!$this->WatchProduct->exists($watch)) {
                    $success = $this->WatchProduct->addWatch($watch['product_id'], $watch['user_id'], $watch['retailer_id']);
                    if ($success) {
                        $message = "This product has been added to your watch list";
                    }
                } else {
                    $success = true;
                    $message = "This product has already been added to your watch list";
                }
            }

            $this->setFlash($message, array(), 'flash', ($success) ? 'success' : 'error');
        }

        $this->redirect($this->referer(['action' => $uuid], true));
    }

    /**
     * Add retailers for specific product by brand
     */
    public function addretailers()
    {
        if (isset($this->request->data) && !empty($this->request->data)) {
            if (isset($this->request->data['removed']) && !empty($this->request->data['removed']))
                $this->ProductRetailer->deleteAll(array('user_id' => $this->request->data['removed'], 'product_id' => $this->request->params['productID']));
            if (isset($this->request->data['added']) && !empty($this->request->data['added'])) {
                foreach ($this->request->data['added'] as $value) {
                    $this->ProductRetailer->create();
                    $this->ProductRetailer->save(array('created' => date('Y-m-d H:i:s'), 'user_id' => $value, 'product_id' => $this->request->params['productID']));
                }
            }
            echo 'success';
            exit;
        }

        $this->set('title_for_layout', 'Add Retailers');
        $result = $this->ManufacturerRetailer->find("all", array(
            'conditions' => array(
                'user_id' => $this->Auth->user('id')
            )
        ));

        $proRel = $manRel = array();
        foreach ($result as $val) {
            $rel = $this->ProductRetailer->find('first', array('conditions' => array('user_id' => $val['ManufacturerRetailer']['retailer_id'], 'product_id' => $this->request->params['productID'])));
            if (count($rel) == 1) {
                $proRel[] = $this->User->findById($rel['ProductRetailer']['user_id']);
            } else {
                $manRel[] = $this->User->findById($val['ManufacturerRetailer']['retailer_id']);
            }
        }
        $this->set('proRel', $proRel);
        $this->set('manRel', $manRel);
    }

    /**
     * add media related url
     * Based on the url, Product informations are collected
     */
    public function newmedia()
    {
        $product_detail = array();
        if ($this->request->params['mediaID']) {
            $this->set('title_for_layout', 'Edit media');
            $media = $this->ProductMeta->find('first', array('conditions' => array('meta_key' => 'media', 'id' => $this->request->params['mediaID']), 'fields' => array('meta_value', 'product_id')));
            if (count($media) != 0) {
                $product_detail = $this->Product->getProductInfo($media['ProductMeta']['product_id'], array('user_id', 'product_sku', 'uuid'));
                if ($product_detail['Product']['user_id'] != $this->shipearly_user['User']['id']) {
                    $this->redirect($this->referer());
                }
                $this->set('media', json_decode($media['ProductMeta']['meta_value']));
            } else {
                $this->redirect($this->referer());
            }
        } else {
            $product_detail = $this->Product->getProductInfo($this->request->params['productID'], array('user_id', 'product_sku', 'uuid'));
            if ($product_detail['Product']['user_id'] != $this->shipearly_user['User']['id']) {
                $this->redirect($this->referer());
            }
            $this->set('title_for_layout', 'Add media');
        }

        if (!empty($this->request->data)) {
            if (empty($this->request->data['ProductMeta']['media_title'])) {
                $this->setFlash("Media title can't be empty", 'error');
            } else {
                $media = json_encode(array('title' => $this->request->data['ProductMeta']['media_title'], 'content' => $this->request->data['ProductMeta']['media_content']));
                if ($this->request->params['mediaID']) {
                    if ($this->ProductMeta->addProductMeta(array('id' => $this->request->params['mediaID'], 'meta_key' => 'media', 'meta_value' => $media), $product_detail['Product']['uuid']))
                        $this->setFlash("Product details have been successfully updated.", 'success');
                } else {
                    if ($this->ProductMeta->addProductMeta(array('product_id' => $this->request->data['ProductMeta']['id'], 'meta_key' => 'media', 'meta_value' => $media), $product_detail['Product']['uuid']))
                        $this->setFlash("Product details have been successfully updated.", 'success');
                }
                $this->redirect(BASE_PATH . "products/" . $product_detail['Product']['uuid']);
            }
        }
        $this->set('product_details', $this->request->params['productID']);
    }

    /**
     * media preview page
     */
    public function viewmedia()
    {
        $this->set('title_for_layout', 'View media info');
        $media = $this->ProductMeta->find('first', array('conditions' => array('meta_key' => 'media', 'id' => $this->request->params['mediaID']), 'fields' => array('meta_value', 'product_id')));
        if (count($media) != 0) {
            $this->set('id', $this->request->params['mediaID']);
            $uid = $this->Product->getProductInfo($media['ProductMeta']['product_id'], array('user_id', 'product_sku', 'product_title', 'id', 'uuid'));
            $this->set('uid', $uid);
            $this->set('media', json_decode($media['ProductMeta']['meta_value']));
        } else {
            $this->redirect($this->referer());
        }
    }

    /**
     * List all media on single page (see all)
     */
    public function allmedia()
    {
        $this->set('title_for_layout', 'media info');
        $media = $this->ProductMeta->find('all', array('conditions' => array('meta_key' => 'media', 'product_id' => $this->request->params['productID']), 'fields' => array('meta_value', 'product_id', 'id'), 'order' => 'id DESC'));
        $uid = $this->Product->getProductInfo($this->request->params['productID'], array('user_id', 'product_sku', 'product_title', 'id', 'uuid'));
        $this->set('uid', $uid);
        $this->set('product_details', $media);
    }

    /**
     * Product enquiry popup
     * input content send to the product manufacture through mail
     */
    public function enquiry()
    {
        $this->set('title_for_layout', 'Product Enquiry');
        $this->set('product_details', $this->request->params['productID']);
        if (!empty($this->request->data['Productquery']['comment']) && $this->request->is('post')) {
            $uid = $this->Product->getProductInfo($this->request->data['Productquery']['id'], array('id', 'user_id', 'product_sku', 'no_of_inquiries', 'product_title', 'uuid'));
            if ($this->ProductInquiries->save(array('product_id' => $this->request->data['Productquery']['id'], 'user_id' => $uid['Product']['user_id'], 'retailer_id' => $this->Auth->user('id'), 'comment' => $this->request->data['Productquery']['comment'], 'created' => date('Y-m-d H:i:s'), 'status' => 1))) {

                //product inquiry
                $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Product Inquiry');
                $variables = array();
                $variables['{user_name}'] = $uid['Product']['user_id'];
                $variables['{site_name}'] = SITE_NAME;
                $variables['{content}'] = $this->request->data['Productquery']['comment'];
                $variables['{retailer_name}'] = "<a href='" . BASE_PATH . "contact/" . $this->Auth->user('id') . "'>" . $this->Auth->user('company_name') . "</a>";
                $variables['{product_name}'] = "<a href='" . BASE_PATH . "products/" . $uid['Product']['uuid'] . "'>" . $uid['Product']['product_title'] . "</a>";
                $this->sendEmail($emailTemplateArr, $variables, trim($this->User->getEmail($uid['Product']['user_id'])));

                $notification_msg = "<a href='" . BASE_PATH . "contact/" . $this->Auth->user('id') . "'>" . $this->Auth->user('company_name') . "</a> has inquired about the product <a href='" . BASE_PATH . "products/" . $uid['Product']['uuid'] . "'>" . $uid['Product']['product_title'] . "</a>";
                $this->Notification->createNotification($this->Auth->user('id'), $uid['Product']['user_id'], Notification::TYPE_PRODUCT_INQUIRY, '', $notification_msg);
                $this->setFlash("Product enquiry details sent to Manufacturer successfully.", 'success');
                $this->Product->updateProductInquiries($uid['Product']['id']);
                $this->redirect(BASE_PATH . "products/" . $uid['Product']['uuid']);
            }
        }
    }

    /**
     *
     */
    public function deletemedia()
    {
        if ($this->request->params['mediaID']) {
            $media = $this->ProductMeta->find('first', array('conditions' => array('meta_key' => 'media', 'id' => $this->request->params['mediaID']), 'fields' => array('product_id')));
            if (count($media) != 0) {
                $product_detail = $this->Product->getProductInfo($media['ProductMeta']['product_id'], array('uuid'));
                if ($this->ProductMeta->deleteProductMeta($this->request->params['mediaID'], $product_detail['Product']['uuid'])) {
                    $this->setFlash("Product details have been successfully updated.", 'success');
                    $this->redirect(BASE_PATH . "products/" . $product_detail['Product']['uuid']);
                }
            }
        }
    }

    public function productTitleSortExport()
    {
        $this->autoRender = false;
        $fileName = sprintf(
            '%s %s %s.xlsx',
            $this->Auth->user('company_name'),
            'Product Titles',
            date('Y-m-d')
        );
        $tableModel = [
            $this->PhpExcel->newExportColumn(__('Product ID'), function ($product) {
                return $product['Product']['source_product_id'];
            }),
            $this->PhpExcel->newExportColumn(__('Product Title'), function ($product) {
                return $product['Product']['product_name'];
            }),
            $this->PhpExcel->newExportColumn(__('Product Categories'), function ($product) {
                $categories = Product::decodeProductType($product['Product']['product_type']);
                asort($categories);
                return implode(', ', $categories);
            }),
        ];
        $this->Spout->doWithOpenWriter($fileName, function () use ($tableModel) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));

            $this->Product->streamPagedQuery(
                $this->Product->getProductTitleSortExportQuery($this->Auth->user('id')),
                function ($product) use ($tableModel) {
                    return $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $product));
                }
            );
        });
    }

    public function productTitleSortImport()
    {
        $this->autoRender = false;

        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);

        $userId = $this->Auth->user('id');

        $this->request->allowMethod('post', 'put');

        $importHeaders = [
            'source_product_id' => 'Product ID',
            'product_title' => 'Product Title',
        ];

        try {
            $filePath = $this->Upload->getTempFile($this->request->data('Product.upload'));
            $fileName = $this->request->data('Product.upload.name');

            $tableData = $this->Spout->extractTableData($filePath, $fileName);
            $tableHeaders = array_keys(current($tableData));

            $importHeaders = array_filter($importHeaders, function($columnName) use ($tableHeaders) {
                return in_array($columnName, $tableHeaders);
            });
            if (!isset($importHeaders['source_product_id'])) {
                return $this->_exceptionResponse(new BadRequestException(), 'Import file missing required column: ' . json_encode($importHeaders['source_product_id']));
            }

            $success = $this->Product->saveProductTitleSortOrderImport(
                $userId,
                $tableData,
                $importHeaders
            );
            if (!$success) {
                throw new InternalErrorException(json_encode(['errors' => $this->Product->validationErrors, 'data' => $this->Product->data]));
            }

            $this->setFlash('Product Sort Order successfully uploaded', 'success');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash('An error occurred uploading the file', 'error');
        } finally {
            if (!empty($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
        }
        $this->redirect($this->referer());
    }

    public function productVariantSortExport()
    {
        $this->autoRender = false;
        $fileName = sprintf(
            '%s %s %s.xlsx',
            $this->Auth->user('company_name'),
            'Product Variants',
            date('Y-m-d')
        );
        $tableModel = [
            $this->PhpExcel->newExportColumn(__('Variant'), function ($variant) {
                return $variant;
            }),
        ];

        /** @var VariantSortOrder $VariantSortOrder */
        $VariantSortOrder = ClassRegistry::init('VariantSortOrder');
        $variants = $VariantSortOrder->getExport($this->Auth->user('id')) + $this->Product->getVariantOptionsByUser($this->Auth->user('id'));

        $this->Spout->doWithOpenWriter($fileName, function () use ($tableModel, $variants) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));
            array_map(function ($variant) use ($tableModel) {
                    return $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $variant));
            }, $variants);
        });
    }

    public function productVariantSortImport()
    {
        $this->autoRender = false;

        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);

        $userId = $this->Auth->user('id');

        $this->request->allowMethod('post', 'put');

        $importHeaders = [
            'variant_options' => 'Variant',
        ];

        try {
            $filePath = $this->Upload->getTempFile($this->request->data('Product.upload'));
            $fileName = $this->request->data('Product.upload.name');

            $tableData = $this->Spout->extractTableData($filePath, $fileName);
            $tableMaxRows = floor(log(PHP_FLOAT_MAX, 2)) - 1;
            if(count($tableData) > $tableMaxRows){
                return $this->_exceptionResponse(new BadRequestException(), "Import File too large.  Maximum size: {$tableMaxRows} rows.");
            }
            $tableHeaders = array_keys(current($tableData));

            $importHeaders = array_filter($importHeaders, function($columnName) use ($tableHeaders) {
                return in_array($columnName, $tableHeaders);
            });
            if (!isset($importHeaders['variant_options'])) {
                return $this->_exceptionResponse(new BadRequestException(), 'Import file missing required column: ' . json_encode($importHeaders['variant_options']));
            }

            $success = $this->Product->saveProductVariantSortOrderImport(
                $userId,
                $tableData
            );
            if (!$success) {
                throw new InternalErrorException(json_encode(['errors' => $this->Product->validationErrors, 'data' => $this->Product->data]));
            }

            $this->setFlash('Product Sort Order successfully uploaded', 'success');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash('An error occurred uploading the file', 'error');
        } finally {
            if (!empty($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
        }
        $this->redirect($this->referer());
    }

    public function save_state()
    {
        $this->autoRender = false;
        $this->request->allowMethod('post', 'put');

        $this->Session->write('ProductIndexState', $this->request->data);
    }

    /**
     * creates a temporary cart to be used by B2bCatalogueHelper::b2bCartSelectorInput()
     * @param CakeRequest $request
     * @return array
     */
    protected function getTemporaryCart(CakeRequest $request): array
    {
        $discountId = (int)$request->query('discountId');
        $orderType = $request->query('orderType');

        if (!$orderType) {
            return [];
        }

        $tempCart = [
            'B2bCart' => [
                'id' => -1,
                'order_type' => $orderType,
            ],
        ];
        if ($discountId){
            $tempCart += $this->Discount->findForTemporaryCart($discountId);
        }
        if (!empty($tempCart)){
            $tempCart['B2bCartProduct'] = [];
        }

        return $tempCart;
    }

/**
 * check if the request is for a consumer view,
 * used to hide info like b2b specific elements/info
 * @param CakeRequest $request current request
 * @param mixed $userType type of logged in user
 * @return bool
 * @throws InvalidArgumentException
 */
    protected function isConsumerView(CakeRequest $request, $userType)
    {
        $isConsumerView = filter_var($this->Session->read('ProductIndexState.isConsumerView'), FILTER_VALIDATE_BOOLEAN);
        return $isConsumerView && !in_array($userType, User::TYPES_BRAND);
    }

    protected function _productExportConditions(int $userId): array
    {
        $productStatus = (string)$this->request->query('type');
        $collectionIds = array_filter((array)$this->request->query('collection'));
        $productTypes = array_filter((array)$this->request->query('categories'));
        $stockOption = (bool)$this->request->query('stock');
        $variants = array_filter((array)json_decode_if_array((string)$this->request->query('variants')));
        $search = trim((string)$this->request->query('search'));

        $conditions = [
            'Product.user_id' => $userId,
            'Product.deleted' => false,
        ];
        if ($productStatus) {
            $conditions['Product.product_status'] = $productStatus;
        }
        $conditions = $this->Product->_setCollectionsCondition($collectionIds, $conditions);
        $conditions = $this->Product->_setProductTypesCondition($productTypes, $conditions);
        $conditions = $this->Product->_setStockOptionCondition($stockOption, $conditions);
        if ($variants) {
            $variants = $this->_filterVariantsQueryParam($variants, $this->Product->getVariantOptionsByName($userId));
            $conditions = $this->Product->_setVariantOptionsCondition($variants, $conditions);
        }
        if ($search) {
            $conditions[] = $this->Product->buildSearchCondition($search);
        }

        return $conditions;
    }

    /**
     * @param array<string, string[]|string> $variants Value built from variant filter inputs Eg.
     * ```
     * ['color' => ['red', 'bogus'], 'size' => '']
     * ```
     * @param array<string, array{name: string, values: array<string, string>}> $variantOptionsByName Response from Product::getVariantOptionsByName(). Eg.
     * ```
     * ['color' => ['name' => 'Color', 'values' => ['red' => 'Red']]
     * ```
     * @return array<string, string[]> Eg.
     * ```
     * ['color' => ['red']]
     * ```
     */
    protected function _filterVariantsQueryParam(array $variants, array $variantOptionsByName): array
    {
        $variants = array_intersect_key($variants, $variantOptionsByName);

        foreach ($variants as $nameKey => $values) {
            $variants[$nameKey] = array_intersect((array)$values, array_flip($variantOptionsByName[$nameKey]['values']));
        }

        return array_filter($variants);
    }
}
