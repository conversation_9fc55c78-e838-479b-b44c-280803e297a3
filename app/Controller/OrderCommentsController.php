<?php
App::uses('AppController', 'Controller');

/**
 * OrderComments Controller
 *
 * @property OrderLogicComponent $OrderLogic
 *
 * @property OrderComment $OrderComment
 * @property Notification $Notification
 * @property Order $Order
 * @property User $User
 */
class OrderCommentsController extends AppController
{
    public $components = [
        'OrderLogic',
    ];

    public $uses = [
        'OrderComment',
        'Notification',
        'Order',
        'User',
    ];

    public function beforeFilter()
    {
        parent::beforeFilter();

        $this->setUserSession(User::revertAuthParent($this->Auth->user()));
    }

    public function isAuthorized()
    {
        return $this->OrderLogic->isAuthorizedForOrder($this->passedArgs[0]) && parent::isAuthorized();
    }

    public function tooltip($orderId)
    {
        $orderComment = current($this->OrderComment->findAllForOrderTimeline($orderId, $this->Auth->user('user_type')));
        if (!$orderComment) {
            throw new NotFoundException();
        }
        $this->set('orderComment', $orderComment);
    }

    public function view($orderId, $id = null)
    {
        if (!$this->OrderComment->exists(['OrderComment.id' => $id])) {
            throw new NotFoundException(__('Invalid order comment'));
        }
        $this->set('orderComment', $this->OrderComment->findForView($id));
        return $this->render('/Elements/OrderComments/view');
    }

    public function add($orderId)
    {
        if ($this->request->is('post')) {
            try {
                $author = (array)$this->Auth->user();
                if (!$this->OrderComment->addToOrder((int)$orderId, $author, $this->request->data)) {
                    throw new InternalErrorException(json_encode(['errors' => $this->OrderComment->validationErrors, 'data' => $this->OrderComment->data]));
                }
                if (!$this->Notification->createForOrderComment($this->OrderComment->id)) {
                    throw new InternalErrorException(json_encode(['errors' => $this->Notification->validationErrors, 'data' => $this->Notification->data]));
                }
            } catch (InternalErrorException $e) {
                return $this->_exceptionResponse($e, null, true);
            }
            if ($this->request->is('ajax')) {
                $this->response->body(json_encode(['OrderComment' => ['id' => $this->OrderComment->id]]));
            }
            return $this->_successResponse(__('The order comment has been saved.'));
        }
        $this->set('order_id', $orderId);
        return $this->render('/Elements/OrderComments/form');
    }

    public function delete($orderId, $id = null)
    {
        if (!$this->OrderComment->exists(['OrderComment.id' => $id])) {
            throw new NotFoundException(__('Invalid order comment'));
        }
        $this->request->allowMethod('post', 'delete');
        $userType = $this->Auth->user('user_type');
        if ($userType !== User::TYPE_MANUFACTURER) {
            throw new ForbiddenException(json_encode([
                'message' => $userType . ' is not allowed to delete order comments',
                'Auth' => array_intersect_key($this->Auth->user(), array_flip([
                    'id',
                    'Branch',
                    'user_type',
                    'email_address',
                    'company_name',
                ])),
            ]));
        }
        if (!$this->OrderComment->delete($id)) {
            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->OrderComment->validationErrors, 'data' => $this->OrderComment->data])), null, true);
        }
        return $this->_successResponse(__('The order comment has been deleted.'));
    }

}
