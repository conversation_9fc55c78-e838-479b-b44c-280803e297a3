<?php
App::uses('AppController', 'Controller');

/**
 * Class SearchController
 *
 * @property User $User
 * @property UserCategories $UserCategories
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Contactpersons $Contactpersons
 * @property Product $Product
 * @property ProductRetailer $ProductRetailer
 */
class SearchController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Search';
    /**
     * @var array
     */
    public $components = array();
    /**
     * @var array
     */
    public $uses = array('User', 'UserCategories', 'ManufacturerRetailer', 'Contactpersons', 'Product', 'ProductRetailer');

    /**
     *
     */
    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->Auth->deny('*');
    }

    public function admin_brand_search($retailerId = null)
    {
        $this->layout = 'admin';

        $user = (array)$this->User->findById($retailerId, [], null, -1);
        $this->shipearly_user = $user;
        $this->set('shipearly_user', $this->shipearly_user);

        $this->_index($retailerId);
    }

    /**
     * Shipearly user search both brand and retailer search (ajax_response)
     */
    public function admin_ajax_search()
    {
        $this->autoRender = false;
        $this->layout = '';

        $userId = $this->request->data['id'];
        $user = (array)$this->User->findById($userId, [], null, -1);
        if (empty($user)) {
            throw new BadRequestException();
        }
        $this->shipearly_user = $user;
        $this->set('shipearly_user', $this->shipearly_user);

        $this->set('view', (string)$this->request->data('view'));

        $this->_index($userId);

        $this->render('/Elements/usersearch');
    }

    protected function _index($userId = null)
    {
        if ($this->shipearly_user['User']['user_type'] == 'Retailer') {
            $this->set('title_for_layout', MANUFACTURE_LABEL_SINGLE . ' search');
            $search_type = 'Manufacturer';
        } else {
            $this->set('title_for_layout', 'Retailer search');
            $search_type = 'Retailer';
        }
        $this->set('retailerId', $userId);
        $this->set('usertype', $search_type);

        $conditions = [
            'User.status' => 'Active',
            'User.user_type' => $search_type,
            'User.Branch' => 0,
            $this->UserCategories->buildOtherUserCategoryMatchCondition($this->User->primaryKeyIdentifier(), $userId)
        ];

        $search = trim($this->request->data('search'));
        if ($search) {
            $conditions[] = $this->User->buildSearchCondition($search);
        }

        $count_users = (int)$this->User->find('count', [
            'recursive' => -1,
            'conditions' => $conditions,
        ]);
        $this->set('count_users', $count_users);

        $noRecords = (int)PAGINATION;
        $page = (int)($this->request->data('pageNumber') ?: 1);

        $this->set('paging', paging($page, $noRecords, $count_users));
        $this->set('users', $this->User->findAllForAdminBrandSearch($conditions, $noRecords, $page));
    }

    /**
     * Product search with in user category
     */
    public function productsearch()
    {
        $this->set('title_for_layout', 'Product search');
        if (!isset($this->request->data['psearch'])) $search = '';
        else $search = trim($this->request->data['psearch']);

        $conditions = array();
        $search_terms = explode(' ', $search);
        foreach ($search_terms as $search) {
            $conditions[] = array('Product.product_title LIKE' => '%' . $search . '%');
            $conditions[] = array('Product.product_sku LIKE' => '%' . $search . '%');
            $conditions[] = array('Product.product_description LIKE' => '%' . $search . '%');
            $conditions[] = array('Product.product_upc LIKE' => '%' . $search . '%');
        }

        $con['OR'] = $conditions;
        $con['AND'] = array(
            //'Product.product_status' => Product::STATUS_ENABLED,
            'Product.deleted' => 0,
            'ProductCategories.cat_id' => array_keys($this->Auth->user('user_categories'))
        );

        $joins = array(
            array('table' => 'product_categories',
                'alias' => 'ProductCategories',
                'type' => 'LEFT',
                'conditions' => '`ProductCategories`.`product_id` = `Product`.`id`'
            )
        );


        $productsQuery = $this->Product->find('first', array(
            'joins' => $joins,
            'conditions' => $con,
            'fields' => array('COUNT(DISTINCT `Product`.`id`) as Count')
        ));

        $count_products = $productsQuery[0]['Count'];

        $page = 1;
        if (!empty($this->request->params['pageNo'])) {
            $page = $this->request->params['pageNo'];
        }
        $paging = paging($page, 9, $count_products);
        $this->Product->virtualFields['watchCount'] = 'SELECT COUNT(*) FROM ' . $this->Product->tablePrefix . 'watch_products as WP WHERE WP.product_id = Product.id';
        $fields = array('DISTINCT id', 'productID', 'product_title', 'product_description', 'product_status', 'product_price', 'product_image', 'product_sku', 'invoice_amount', 'min_shipping', 'no_of_views', 'no_of_orders', 'user_id', 'watchCount', 'uuid');
        $all_products = $this->Product->find('all', array(
            'joins' => $joins,
            'conditions' => $con,
            'fields' => $fields,
            'order' => 'published_at DESC',
            'offset' => $paging['offset'],
            'limit' => 9
        ));
        $this->set('count_products', $count_products);
        $this->set('all_products', $all_products);
        $this->set('paging', $paging);
        $this->set('noProducts', 9);
    }

    /**
     * Product search with in user category (ajax_response)
     */
    public function ajax_productsearch()
    {
        if ($this->request->is('ajax')) {
            if (!isset($this->request->data['psearch'])) $search = '';
            else $search = trim($this->request->data['psearch']);

            $conditions = array();
            $search_terms = explode(' ', $search);
            foreach ($search_terms as $search) {
                $conditions[] = array('Product.product_title LIKE' => '%' . $search . '%');
                $conditions[] = array('Product.product_sku LIKE' => '%' . $search . '%');
                $conditions[] = array('Product.product_description LIKE' => '%' . $search . '%');
                $conditions[] = array('Product.product_upc LIKE' => '%' . $search . '%');
            }

            $con['OR'] = $conditions;
            $con['AND'] = array(
                'Product.product_status' => Product::STATUS_ENABLED,
                'Product.deleted' => 0,
                'ProductCategories.cat_id' => array_keys($this->Auth->user('user_categories'))
            );

            $joins = array(
                array('table' => 'product_categories',
                    'alias' => 'ProductCategories',
                    'type' => 'LEFT',
                    'conditions' => '`ProductCategories`.`product_id` = `Product`.`id`'
                )
            );

            $count_products = $this->Product->find('count', array(
                'joins' => $joins,
                'conditions' => $con
            ));
            $page = 1;
            if (!empty($this->request->data['pageNumber'])) $page = $this->request->data['pageNumber'];
            $paging = paging($page, 9, $count_products);
            $this->Product->virtualFields['watchCount'] = 'SELECT COUNT(*) FROM ' . $this->Product->tablePrefix . 'watch_products as WP WHERE WP.product_id = Product.id';
            $fields = array('id', 'productID', 'product_title', 'product_description', 'product_status', 'product_price', 'product_image', 'product_sku', 'invoice_amount', 'min_shipping', 'no_of_views', 'no_of_orders', 'user_id', 'watchCount', 'uuid');
            $all_products = $this->Product->find('all', array(
                'joins' => $joins,
                'conditions' => $con,
                'fields' => $fields,
                'order' => 'published_at DESC',
                'offset' => $paging['offset'],
                'limit' => 9
            ));
            $this->set('count_products', $count_products);
            $this->set('all_products', $all_products);
            $this->set('paging', $paging);
        }
    }

}
