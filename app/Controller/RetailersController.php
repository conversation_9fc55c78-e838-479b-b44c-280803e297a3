<?php
use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('AppController', 'Controller');
App::uses('ManufacturerRetailer', 'Model');
App::uses('User', 'Model');

/**
 * Class RetailersController.
 *
 * @property IndexQueryHandlerComponent $IndexQueryHandler
 * @property PhpExcelComponent $PhpExcel
 * @property UploadComponent $Upload
 * @property UserLogicComponent $UserLogic
 *
 * @property Label $Label
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property ManufacturerRetailerSalesRep $ManufacturerRetailerSalesRep
 * @property ManufacturerSalesRep $ManufacturerSalesRep
 * @property PricingTier $PricingTier
 * @property Product $Product
 * @property ProductCategories $ProductCategories
 * @property ProductRetailer $ProductRetailer
 * @property RetailerCreditTerm $RetailerCreditTerm
 * @property RetailDisplay $RetailDisplay
 * @property Collection $Collection
 * @property RetailCategory $RetailCategory
 * @property RetailCategoriesRetailer $RetailCategoriesRetailer
 * @property CreditTerm $CreditTerm
 * @property Storefront $Storefront
 * @property Tag $Tag
 * @property Territory $Territory
 * @property User $User
 * @property UserCategories $UserCategories
 * @property Warehouse $Warehouse
 */
class RetailersController extends AppController
{
    /**
     * @var array
     */
    public $components = [
        'IndexQueryHandler' => [
            'defaultQuery' => [
                'sort' => 'ManufacturerRetailer.created',
                'direction' => 'DESC',
                'limit' => '50',
                'search' => '',
                'status' => '',
            ],
        ],
        'PhpExcel',
        'Upload',
        'UserLogic',
    ];

    public $uses = [
        'Collection',
        'CreditTerm',
        'Label',
        'ManufacturerRetailer',
        'ManufacturerRetailerSalesRep',
        'ManufacturerSalesRep',
        'PricingTier',
        'Product',
        'ProductCategories',
        'ProductRetailer',
        'RetailerCreditTerm',
        'RetailDisplay',
        'RetailCategory',
        'RetailCategoriesRetailer',
        'Storefront',
        'Tag',
        'Territory',
        'User',
        'UserCategories',
        'Warehouse',
    ];

    public $importHeaders = [
        'Retailer.email_address' => 'Primary eMail', // Identifier
        'PricingTier.pricingtiername' => 'Pricing Tier',
        'Distributor.ManufacturerSalesRep.descriptor' => 'Distributor',
        'ManufacturerRetailer.local_delivery_radius' => 'Delivery',
        'ManufacturerRetailer.ship_from_store_distance' => 'Ship from Store Distance',
        'ManufacturerRetailer.dealer_protect_radius' => 'Dealer Protect',
        'ManufacturerRetailer.external_retailer_account' => 'Account ID',
        'ManufacturerRetailer.b2b_tax' => 'B2B Tax',
        'ManufacturerRetailer.vat_number' => 'VAT ID #',
        'ManufacturerRetailer.b2b_minimum' => 'Min B2B Value',
        'ManufacturerRetailer.enable_split_payment' => 'Enable Split Payment',
        'ManufacturerRetailer.is_commission_tier' => 'Dealer Type',
        'ManufacturerRetailer.commission_uses_retailer_tax_rate' => 'Commission Uses Dealer Tax Rate',
        'Warehouse.name' => 'Default Warehouse',
        'Territory.name' => 'Territory',
        'ManufacturerRetailer.enable_consumer_orders' => 'Enable Consumer Orders',
        'ManufacturerRetailer.is_ship_to_store_only' => 'Non-Stocking',
        'ManufacturerRetailer.credit_limit' => 'Credit Limit',
        'ManufacturerRetailer.enable_b2b_credit_card' => 'Credit Card',
        'ManufacturerRetailer.enable_b2b_external_payment' => 'File Payment',
        'ManufacturerRetailer.status' => 'Status',
        'ManufacturerRetailer.external_retailer_email' => 'Billing Email',
        'ManufacturerRetailer.external_retailer_company' => 'Billing Company',
        'B2bShipToAddress.address1' => 'Ship to Address 1',
        'B2bShipToAddress.address2' => 'Ship to Address 2',
        'B2bShipToAddress.city' => 'Ship to City',
        'B2bShipToAddress.State.state_name' => 'Ship to State/Province',
        'B2bShipToAddress.zipcode' => 'Ship to ZIP/Postal Code',
        'B2bShipToAddress.Country.country_name' => 'Ship to Country',
        'B2bShipToAddress.telephone' => 'Ship to Telephone',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $action = $this->request->param('action');

            $validUserTypes = in_array($action, ['index', 'index_salesrep', 'edit'], true)
                ? [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP]
                : [User::TYPE_MANUFACTURER];
            $this->Permissions->assertUserIsType($this->Auth->user(), $validUserTypes);

            if (in_array($action, ['remove', 'price_tier', 'import'], true)) {
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
            }
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    /**
     * List all associated and Requested retailer with Brand
     * @URL: /retailers
     * UserType: Brand
     */
    public function index()
    {
        $userId = (int)$this->Auth->user('id');
        $userType = $this->Auth->user('user_type');

        if ($userType === 'SalesRep') {
            return $this->index_salesrep();
        }

        if ($this->request->is('post') && $this->request->data('save')) {
            try {
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
            } catch (ForbiddenException $e) {
                return $this->_permissionDeniedResponse($e);
            }

            if (!empty($this->request->data['ManufacturerRetailer'])) {
                if (!$this->ManufacturerRetailer->saveRetailerConnections($userId, $this->request->data['ManufacturerRetailer'])) {
                    return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->ManufacturerRetailer->validationErrors, 'request' => $this->request->data['ManufacturerRetailer']])), null, true);
                }
            }

            return $this->_successResponse('Retailer details updated successfully.');
        }

        $this->set('title_for_layout', 'Retailers');

        $query = $this->_extractIndexQueryParams($this->request, $userType);
        $this->set('queryParams', $query);

        $noRecords = $query['show'];
        $page = $query['page'];
        $sortOrder = trim($query['sort'] . ' ' . $query['order']);
        $search = $query['search'];
        $statusFilter = $query['status'];
        $tierFilter = $query['tier'];
        $salesRepFilter = $query['sales_rep'];
        $territoryFilter = $query['territory'];

        $conditions = [];
        if ($statusFilter || is_numeric($statusFilter)) {
            $conditions['ManufacturerRetailer.status'] = $statusFilter;
        }
        if ($search) {
            $conditions[] = $this->ManufacturerRetailer->buildBrandRetailerSearchCondition($userId, $search);
        }
        if ($tierFilter) {
            $conditions['ManufacturerRetailer.pricingtierid'] = $tierFilter;
        }
        if ($salesRepFilter) {
            $conditions[] = [
                'OR' => [
                    'ManufacturerRetailer.distributor_id' => $salesRepFilter,
                    $this->ManufacturerRetailerSalesRep->buildExistsSubquery([
                        'ManufacturerRetailerSalesRep.manufacturer_retailer_id' => $this->ManufacturerRetailer->primaryKeyIdentifier(),
                        'ManufacturerRetailerSalesRep.sales_rep_id' => $salesRepFilter,
                    ]),
                ],
            ];
        }
        if ($territoryFilter) {
            $conditions['ManufacturerRetailer.territory_id'] = $territoryFilter;
        }

        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT));

        $this->set('count', $this->ManufacturerRetailer->countAllRetailersWithStores($userId, $conditions));
        $this->set('allRetailers', $this->ManufacturerRetailer->findAllRetailersWithStores($userId, $conditions, $sortOrder, $noRecords, $page));

        $this->set('tierOptions', $this->PricingTier->getSelectOptions($userId));
        $this->set('salesRepOptions', $this->ManufacturerSalesRep->listConnectedSalesReps($userId, false));
        $this->set('territoryOptions', $this->Territory->listOptions($userId));
        if ($this->request->is('ajax')) {
            return $this->render('/Elements/Retailers/ajax_index');
        }

        return $this->render('index');
    }

    public function updateCreditLimit($retailerId)
    {
        $this->autoRender = false;
        $this->response->type('json');
        $this->request->allowMethod('post');

        $authUserId = $this->Auth->user('id');
        $newLimit = (float)$this->request->data('credit_limit');

        $updated = $this->ManufacturerRetailer->updateAllJoinless(
            ['ManufacturerRetailer.credit_limit' => $newLimit],
            ['ManufacturerRetailer.user_id' => $authUserId, 'ManufacturerRetailer.retailer_id' => $retailerId]
        );

        $response = [
            'success' => (bool)$updated,
            'newLimit' => $newLimit
        ];

        $this->response->body(json_encode($response));
        return $this->response;
    }

    public function updateAccountId($retailerId)
    {
        $this->autoRender = false;
        $this->response->type('json');
        $this->request->allowMethod('post');

        $authUserId = $this->Auth->user('id');
        $newAccountId = trim($this->request->data('account_id')) ?: null;

        $escapedAccountId = $this->ManufacturerRetailer->value($newAccountId, 'string');
        $updated = $this->ManufacturerRetailer->updateAllJoinless(
            ['ManufacturerRetailer.external_retailer_account' => $escapedAccountId],
            [
                'ManufacturerRetailer.user_id' => $authUserId,
                'ManufacturerRetailer.retailer_id' => $retailerId
            ]
        );

        $response = [
            'success' => (bool)$updated,
            'newAccountId' => $newAccountId
        ];

        $this->response->body(json_encode($response));
        return $this->response;
    }

    public function getMapData()
    {
        $userId = (int)$this->Auth->user('id');
        $userType = $this->Auth->user('user_type');

        if (!empty($this->Auth->user('latitude'))) {
            $notifications = $this->Notification->findUserActivities($userId, $userType);
            $maporders = $this->Order->findMapOrders(['id' => Hash::extract($notifications, '{n}.Order.id')]);
            $mapstores = $this->User->findBrandDashboardMapStores($userId);
            $this->set(compact('maporders', 'mapstores'));

            $this->render('/Elements/dashboardmaps');
        }
    }

    /**
     * List all associated and Requested retailer with SalesRep
     * @URL: /retailers
     * UserType: SalesRep
     */
    public function index_salesrep()
    {
        $salesRepId = (int)$this->Auth->user('id');
        $userType = $this->Auth->user('user_type');

        $brandOptions = $this->User->listSalesRepBrandNames($salesRepId);

        $this->set('title_for_layout', 'Retailers');

        $query = $this->_extractIndexQueryParams($this->request, $userType);

        // determine the selected brand by following precedence
        // $query['brand'] -> brand of current subdomain -> first element of $brandOptions
        $query['brand'] = (
            $query['brand'] ?:
            (int)($this->UserSubdomain->findWhitelabelSettings()['user_id'] ?? 0) ?:
            key($brandOptions)
        );

        $this->set('queryParams', $query);

        $noRecords = $query['show'];
        $page = $query['page'];
        $sortOrder = trim($query['sort'] . ' ' . $query['order']);
        $search = $query['search'];
        $statusFilter = $query['status'];
        $brandFilter = $query['brand'];

        if ($this->request->is('post') && $this->request->data('save')) {
            try {
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
            } catch (ForbiddenException $e) {
                return $this->_permissionDeniedResponse($e);
            }

            if (!empty($this->request->data['ManufacturerRetailer'])) {
                if (!$this->ManufacturerRetailer->saveRetailerConnections($brandFilter, $this->request->data['ManufacturerRetailer'])) {
                    return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->ManufacturerRetailer->validationErrors, 'request' => $this->request->data['ManufacturerRetailer']])), null, true);
                }
            }

            return $this->_successResponse('Retailer details updated successfully.');
        }

        $conditions = [];

        $conditions = [
            'ManufacturerRetailer.removed' => false,
        ];

        if ($statusFilter || is_numeric($statusFilter)) {
            $conditions['ManufacturerRetailer.status'] = $statusFilter;
        }
        if ($search) {
            $conditions[] = $this->ManufacturerRetailer->buildSalesRepRetailerSearchCondition($salesRepId, $search);
        }
        if ($brandFilter) {
            $conditions['ManufacturerRetailer.user_id'] = $brandFilter;
        }

        $this->set('userHasEditPermission', (
            $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT)
            && $this->ManufacturerSalesRep->fieldByConditions('is_distributor', ['user_id' => $brandFilter, 'sales_rep_id' => $salesRepId])
        ));

        $this->set('count', $this->User->countSalesRepAllretailersIndex($salesRepId, $conditions));
        $this->set('retailers', $this->User->findSalesRepAllretailersIndex($salesRepId, $brandFilter, $conditions, $sortOrder, $noRecords, $page));
        $this->set('tierOptions', $this->PricingTier->getSelectOptions($brandFilter));
        $this->set('brandOptions', $brandOptions);

        if ($this->request->is('ajax')) {
            return $this->render('/Elements/Retailers/ajax_index_salesrep');
        }

        return $this->render('index_salesrep');
    }

    public function edit($storeId = null)
    {
        $userType = (string)$this->Auth->user('user_type');

        if ($userType === User::TYPE_SALES_REP) {
            $this->ManufacturerRetailer->addAssociations(['belongsTo' => ['User']]);
            $record = $this->ManufacturerRetailer->get($storeId, [
                'contain' => [
                    'User' => ['fields' => ['id', 'local_delivery', 'shipment']],
                ],
                'conditions' => [
                    'ManufacturerRetailer.distributor_id' => $this->Auth->user('id'),
                ],
                'fields' => ['id', 'user_id', 'retailer_id'],
            ]);
            $this->ManufacturerRetailer->unbindModel(['belongsTo' => ['User']], false);

            $brandId = (int)$record['ManufacturerRetailer']['user_id'];
            $localDelivery = (bool)$record['User']['local_delivery'];
            $shipment = (bool)$record['User']['shipment'];

            $storeId = (int)$record['ManufacturerRetailer']['retailer_id'];
        } else {
            $brandId = (int)$this->Auth->user('id');
            $localDelivery = (bool)$this->Auth->user('local_delivery');
            $shipment = (bool)$this->Auth->user('shipment');

            $storeId = (int)$storeId;
        }

        $retailer = $this->ManufacturerRetailer->findStoreSettings($brandId, $storeId);
        if (empty($retailer['ManufacturerRetailer']['id'])) {
            $store = $this->User->get($storeId, [
            'fields' => [
                'id',
                'Branch',
                'company_name',
                'currency_code',
                'install_hourly_rate',
            ],
        ]);

            $retailer = $this->ManufacturerRetailer->findStoreSettings($brandId, $store['User']['Branch']);
            $retailer['User'] = $store['User'];
        }

        if (empty($retailer['ManufacturerRetailer']['id'])) {
            throw new NotFoundException(__('Retailer settings not found'));
        }

        if ($this->request->is(['post', 'put'])) {
            try {
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
            } catch (ForbiddenException $e) {
                return $this->_permissionDeniedResponse($e);
            }

            if (empty($this->request->data['ManufacturerRetailer'])) {
                return $this->_exceptionResponse(new BadRequestException(__('No save data')), null, true);
            }
            if (!$this->ManufacturerRetailer->saveStoreSettings($brandId, $storeId, $this->request->data['ManufacturerRetailer'])) {
                return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->ManufacturerRetailer->validationErrors, 'request' => $this->request->data['ManufacturerRetailer']])), null, true);
            }

            return $this->_successResponse(null);
        }

        $this->request->data = $retailer;

        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT));
        $this->set('retailer', $retailer);
        $this->set('creditTermOptions', $this->CreditTerm->getAllCreditTermOptions($brandId));
        $groupedSalesReps = $this->ManufacturerSalesRep->listConnectedGroupedSalesReps($brandId);
        $this->set('salesRepOptions', $groupedSalesReps[0] ?? []);
        $this->set('distributorOptions', $groupedSalesReps[1] ?? []);
        $this->set('tagOptions', $this->Tag->listSelectOptions($brandId));
        $this->set('warehouseOptions', $this->Warehouse->listValidUserWarehouses($brandId, $storeId));
        $this->set('territoryOptions', $this->Territory->listOptions($brandId));
        $this->set('labelOptions', $this->Label->listOptions($brandId));
        $this->set('assembly_option', $localDelivery);
        $this->set('ship_from_store', $shipment);
        $this->set('user_type', $userType);
    }

    public function remove($storeId = null)
    {
        $userId = (int)$this->Auth->user('id');

        $this->ManufacturerRetailer->bindModel(array('belongsTo' => array(
            'Retailer' => array(
                'className' => 'User',
                'foreignKey' => 'retailer_id'
            ),
        )));
        $connection = $this->ManufacturerRetailer->find('first', [
            'contain' => [
                'Retailer'
            ],
            'conditions' => [
                'ManufacturerRetailer.user_id' => $userId,
                'ManufacturerRetailer.retailer_id' => $storeId
            ],
            'fields' => [
                'ManufacturerRetailer.id',
                'ManufacturerRetailer.status',
                'Retailer.Branch',
            ],
        ]);
        if (empty($connection['ManufacturerRetailer']['id'])) {
            $e = new NotFoundException('Retailer connection not found');
            return $this->_exceptionResponse($e, $e->getMessage(), true);
        }

        $this->request->allowMethod('post', 'put', 'delete');

        $status = $connection['ManufacturerRetailer']['status'];
        if ($status == ManufacturerRetailer::STATUS_CONNECTED) {
            $e = new BadRequestException('Cannot remove a retailer that is ' . strtolower(ManufacturerRetailer::STATUS_NAMES[$status]));
            return $this->_exceptionResponse($e, $e->getMessage(), true);
        }

        if ($connection['Retailer']['Branch']) {
            $e = new BadRequestException('Cannot remove an individual store connection');
            return $this->_exceptionResponse($e, $e->getMessage(), true);
        }

        if (!$this->ManufacturerRetailer->remove($userId, $storeId)) {
            return $this->_exceptionResponse(new InternalErrorException(), null, true);
        }

        return $this->_successResponse('The retailer connection has been removed. Contact the retailer if you wish to connect again.');
    }
    
    public function price_tier()
    {
        $userId = (int)$this->Auth->user('id');

        if ($this->request->is('post')) {
            if (!isset($this->request->data['PricingTier'])) {
                return $this->_exceptionResponse(new BadRequestException(), null, true);
            }

            if ($this->PricingTier->savePricingTierForm($userId, $this->request->data)) {
                $this->setFlash('Pricing tiers have been updated', 'success');
                return $this->_successResponse('');
            }
        } else {
            $this->request->data = $this->PricingTier->findAllForPricingTierForm($userId);
        }

        $this->set('warehouseOptions', $this->Warehouse->listValidUserWarehouses($userId));
        $this->set('collectionOptions', $this->Collection->getCollectionsOptionsByUser($userId));
        $this->set('storefrontOptions', $this->Storefront->getStorefrontSetsByUser($userId));
    }

    public function price_tier_row($key = null)
    {
        $userId = (int)$this->Auth->user('id');

        $this->set('key', ($key ?: '0'));
        $this->set('warehouseOptions', $this->Warehouse->listValidUserWarehouses($userId));
        $this->set('collectionOptions', $this->Collection->getCollectionsOptionsByUser($userId));
        $this->set('storefrontOptions', $this->Storefront->getStorefrontSetsByUser($userId));
        return $this->render('/Elements/Retailers/price_tier_row');
    }

    public function export()
    {
        $this->autoRender = false;

        $userId = (int)$this->Auth->user('id');

        $query = $this->_extractIndexQueryParams($this->request);

        $search = $query['search'];
        $statusFilter = $query['status'];
        $tierFilter = $query['tier'];
        $salesRepFilter = $query['sales_rep'];
        $territoryFilter = $query['territory'];

        $conditions = [];
        if ($statusFilter || is_numeric($statusFilter)) {
            $conditions['ManufacturerRetailer.status'] = $statusFilter;
        }
        if ($search) {
            $conditions[] = $this->ManufacturerRetailer->buildBrandRetailerSearchCondition($userId, $search);
        }
        if ($tierFilter) {
            $conditions['ManufacturerRetailer.pricingtierid'] = $tierFilter;
        }
        if ($salesRepFilter) {
            $conditions = array_merge($conditions, [
                [
                    'OR' => [
                        'ManufacturerRetailer.distributor_id' => $salesRepFilter,
                        $this->ManufacturerRetailerSalesRep->buildExistsSubquery([
                            'ManufacturerRetailerSalesRep.manufacturer_retailer_id = ManufacturerRetailer.id',
                            'ManufacturerRetailerSalesRep.sales_rep_id' => $salesRepFilter,
                        ]),
                    ],
                ],
            ]);
        }
        if ($territoryFilter) {
            $conditions['ManufacturerRetailer.territory_id'] = $territoryFilter;
        }

        if (!$this->ManufacturerRetailer->hasExportData($userId, $conditions)) {
            return $this->_exceptionResponse(new NotFoundException(), 'No active products found. Make sure they are configured and try again.');
        }

        $salesRepColumnLabels = $this->ManufacturerRetailerSalesRep->findSalesRepExportLabels($conditions);
        $creditTermOptions = $this->CreditTerm->getAllCreditTermOptions($userId);

        $tableModel = [
            $this->PhpExcel->newExportColumn(__('Retailer Name'), function($retailer) {
                return $retailer['User']['company_name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['PricingTier.pricingtiername'], function($retailer) {
                return $retailer['PricingTier']['pricingtiername'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.is_commission_tier'],  function($retailer) {
                return ($retailer['ManufacturerRetailer']['is_commission_tier']) ? 'Commission' : 'Standard';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.commission_uses_retailer_tax_rate'],  function($retailer) {
                return $retailer['ManufacturerRetailer']['commission_uses_retailer_tax_rate'] ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['Distributor.ManufacturerSalesRep.descriptor'], function($retailer) {
                return $retailer['Distributor']['ManufacturerSalesRep']['descriptor'] ?? null;
            }, ['filter' => true]),
        ];
        foreach ($salesRepColumnLabels as $idx => $label) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn($label, function($retailer) use ($idx) {
                    return $retailer['SalesRep'][$idx]['ManufacturerSalesRep']['descriptor'] ?? null;
                }, ['filter' => true]),
            ]);
        }
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.local_delivery_radius'], function($retailer) {
                return $retailer['ManufacturerRetailer']['local_delivery_radius'];
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.ship_from_store_distance'], function($retailer) {
                return $retailer['ManufacturerRetailer']['ship_from_store_distance'];
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.dealer_protect_radius'], function($retailer) {
                return $retailer['ManufacturerRetailer']['dealer_protect_radius'];
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.external_retailer_account'], function($retailer) {
                return $retailer['ManufacturerRetailer']['external_retailer_account'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.b2b_tax'], function($retailer) {
                return $retailer['ManufacturerRetailer']['b2b_tax'];
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.vat_number'], function($retailer) {
                return $retailer['ManufacturerRetailer']['vat_number'];
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.b2b_minimum'], function($retailer) {
                return $retailer['ManufacturerRetailer']['b2b_minimum'];
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.enable_split_payment'], function($retailer) {
                return $retailer['ManufacturerRetailer']['enable_split_payment'] ? 'Yes' : 'No';
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['Warehouse.name'], function($retailer) {
                return $retailer['Warehouse']['name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['Territory.name'], function($retailer) {
                return $retailer['Territory']['name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.enable_consumer_orders'], function($retailer) {
                return $retailer['ManufacturerRetailer']['enable_consumer_orders'] ? 'Yes' : 'No';
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.is_ship_to_store_only'], function($retailer) {
                return $retailer['ManufacturerRetailer']['is_ship_to_store_only'] ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Inventory Connected'), function($retailer) {
                return $retailer['ManufacturerRetailer']['has_active_integration'] ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Stripe Status'), function($retailer) {
                return ($retailer['StripeUser']['stripe_user_id']) ? ($retailer['StripeUser']['is_activated'] ? __('Activated') : __('Deferred')) : null;
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Klarna Payments Status'), function($retailer) {
                return !empty($retailer['StripeUserCapabilities']['klarna_payments']) ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Affirm Payments Status'), function($retailer) {
                return !empty($retailer['StripeUserCapabilities']['affirm_payments']) ? 'Yes' : 'No';
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Payments'), function($retailer) {
                return ($retailer['StripeUser']['stripe_user_id'] && $retailer['StripeUser']['charges_enabled']) ? __('Enabled') : __('Disabled');
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Outstanding Balance'), function($retailer) {
                return format_number($retailer['RetailerCredit']['total_balance']);
            }),
        ]);
        foreach ($creditTermOptions as $id => $description) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn($description, function($retailer) use ($id) {
                    return array_key_exists($id, $retailer['CreditTerm']) ? 'Yes' : 'No';
                }, ['filter' => true]),
            ]);
        }
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.credit_limit'], function($retailer) {
                return format_number($retailer['ManufacturerRetailer']['credit_limit']);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.enable_b2b_credit_card'], function($retailer) {
                return $retailer['ManufacturerRetailer']['enable_b2b_credit_card'] ? 'Yes' : 'No';
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.enable_b2b_external_payment'], function($retailer) {
                return $retailer['ManufacturerRetailer']['enable_b2b_external_payment'] ? 'Yes' : 'No';
            }),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.status'], function($retailer) {
                return ManufacturerRetailer::STATUS_NAMES[$retailer['ManufacturerRetailer']['status']];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.external_retailer_email'], function($retailer) {
                return $retailer['ManufacturerRetailer']['external_retailer_email'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['ManufacturerRetailer.external_retailer_company'], function($retailer) {
                return $retailer['ManufacturerRetailer']['external_retailer_company'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Address'), function($retailer) {
                return current($this->User->splitAddressField($retailer['User']['address']));
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('City'), function($retailer) {
                return $retailer['User']['city'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('State/Province'), function($retailer) {
                return $retailer['User']['State']['state_name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('ZIP'), function($retailer) {
                return $retailer['User']['zipcode'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Country'), function($retailer) {
                return $retailer['User']['Country']['country_name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Telephone'), function($retailer) {
                return $retailer['User']['Contact']['company'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['Retailer.email_address'], function($retailer) {
                return $retailer['User']['email_address'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Contact Person'), function($retailer) {
                return trim($retailer['User']['Contactperson']['firstname'] . ' ' . $retailer['User']['Contactperson']['lastname']);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Support eMail'), function($retailer) {
                return $retailer['User']['Contactperson']['email'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Support Telephone'), function($retailer) {
                return $retailer['User']['Contact']['person'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['B2bShipToAddress.address1'], function($retailer) {
                return $retailer['B2bShipToAddress']['address1'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['B2bShipToAddress.address2'], function($retailer) {
                return $retailer['B2bShipToAddress']['address2'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['B2bShipToAddress.city'], function($retailer) {
                return $retailer['B2bShipToAddress']['city'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['B2bShipToAddress.State.state_name'], function($retailer) {
                return $retailer['B2bShipToAddress']['State']['state_name'] ?? null;
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['B2bShipToAddress.zipcode'], function($retailer) {
                return $retailer['B2bShipToAddress']['zipcode'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['B2bShipToAddress.Country.country_name'], function($retailer) {
                return $retailer['B2bShipToAddress']['Country']['country_name'] ?? null;
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn($this->importHeaders['B2bShipToAddress.telephone'], function($retailer) {
                return $retailer['B2bShipToAddress']['telephone'];
            }, ['filter' => true]),
        ]);

        $this->PhpExcel->createWorksheet();
        $this->PhpExcel->setDefaultFont('Calibri', 12);
        $this->PhpExcel->addTableHeader($tableModel, ['name' => 'Cambria', 'bold' => true]);

        $this->ManufacturerRetailer->streamExportData($userId, $conditions, function($retailer) use ($tableModel) {
            $this->PhpExcel->addTableRow($this->PhpExcel->processExportColumns($tableModel, $retailer));
        });

        $this->PhpExcel->addTableFooter();
        $filename = "{$this->shipearly_user['User']['company_name']} Retailer Export " . date('Y-m-d') . ".xlsx";
        $this->PhpExcel->render($filename);
    }

    public function import()
    {
        $this->autoRender = false;

        $this->request->allowMethod('post', 'put');

        try {
            $brandId = $this->Auth->user('id');
            $idField = 'Retailer.email_address';

            $filePath = $this->Upload->getTempFile($this->request->data('ManufacturerRetailer.upload'));
            $fileName = $this->request->data('ManufacturerRetailer.upload.name');

            $tableMap = $this->PhpExcel->extractTableData($filePath, $fileName);
            $tableHeaders = array_keys(current($tableMap));

            $importHeaders = array_intersect($this->importHeaders, $tableHeaders);
            if (!isset($importHeaders[$idField])) {
                throw new Exception('Import file missing required column: ' . json_encode($this->importHeaders[$idField]));
            }
            $idColumn = $importHeaders[$idField];

            $salesRepHeaders = array_values(preg_grep('/^Sales Rep \d+$/', $tableHeaders));
            $creditTermOptions = array_intersect($this->CreditTerm->getAllCreditTermOptions($brandId), $tableHeaders);

            $this->ManufacturerRetailer->belongsTo('Retailer', ['className' => 'User']);
            $validRowIds = Hash::combine(
                $this->ManufacturerRetailer->find('all', [
                    'contain' => ['Retailer'],
                    'conditions' => [
                        $idField => array_column($tableMap, $idColumn, $idColumn),
                        'ManufacturerRetailer.user_id' => $brandId,
                    ],
                    'fields' => ['ManufacturerRetailer.id', 'ManufacturerRetailer.retailer_id', $idField]
                ]),
                "{n}.{$idField}",
                '{n}.ManufacturerRetailer'
            );
            $this->ManufacturerRetailer->unbindModel(['belongsTo' => ['Retailer']], false);

            // Do not create new records
            $tableMap = array_filter($tableMap, function($rowMap) use ($validRowIds, $idColumn) {
                return array_key_exists($rowMap[$idColumn], $validRowIds);
            });

            $pricingTierIds = array_flip($this->PricingTier->listNames($brandId));
            $warehouseIds = array_flip($this->Warehouse->listValidUserWarehouses($brandId));
            $groupedSalesReps = $this->ManufacturerSalesRep->listConnectedGroupedSalesReps($brandId);
            $salesRepIds = array_flip($groupedSalesReps[0] ?? []);
            $distributorIds = array_flip($groupedSalesReps[1] ?? []);

            $validCountryStates = [];
            if (
                isset($importHeaders['B2bShipToAddress.Country.country_name']) &&
                isset($importHeaders['B2bShipToAddress.State.state_name'])
            ) {
                $countryColumn = $importHeaders['B2bShipToAddress.Country.country_name'];
                $stateColumn = $importHeaders['B2bShipToAddress.State.state_name'];
                $countryNames = array_values(array_column($tableMap, $countryColumn, $countryColumn));
                $stateNames = array_values(array_column($tableMap, $stateColumn, $stateColumn));
                $validCountryStates = $this->State->listValidIdsByCountryAndStateNames($countryNames, $stateNames);
            }

            $connections = array_map(
                function($rowMap) use ($importHeaders, $salesRepHeaders, $creditTermOptions, $idField, $validRowIds, $pricingTierIds, $warehouseIds, $salesRepIds, $distributorIds, $validCountryStates) {
                    $retailer = array();
                    foreach ($importHeaders as $field => $columnName) {
                        $value = $rowMap[$columnName];
                        if ($field === $idField) {
                            $retailer['id'] = $validRowIds[$value]['id'];
                            $retailer['retailer_id'] = $validRowIds[$value]['retailer_id'];
                        } elseif ($field === 'PricingTier.pricingtiername') {
                            if (isset($pricingTierIds[$value])) {
                                $retailer['pricingtierid'] = $pricingTierIds[$value];
                            }
                        } elseif ($field === 'Distributor.ManufacturerSalesRep.descriptor') {
                            if (empty($value) || isset($distributorIds[$value])) {
                                $retailer['distributor_id'] = Hash::get($distributorIds, $value);
                            }
                        } elseif ($field === 'Warehouse.name') {
                            if (empty($value) || isset($warehouseIds[$value])) {
                                $retailer['warehouse_id'] = Hash::get($warehouseIds, $value);
                            }
                        } elseif ($field === 'Territory.name') {
                            $retailer['territory_name'] = $value;
                        } elseif ($field === 'ManufacturerRetailer.status') {
                            $validStatusNames = array_intersect_key(ManufacturerRetailer::STATUS_NAMES, array_flip([
                                ManufacturerRetailer::STATUS_DISCONNECTED,
                                ManufacturerRetailer::STATUS_CONNECTED,
                            ]));
                            if (in_array($value, $validStatusNames)) {
                                $retailer['status'] = Hash::get(array_flip($validStatusNames), $value);
                            }
                        } elseif ($field === 'B2bShipToAddress.Country.country_name') {
                            // Do nothing; only evaluate country if state is provided
                        } elseif ($field === 'B2bShipToAddress.State.state_name') {
                            $country = $rowMap[$importHeaders['B2bShipToAddress.Country.country_name']];
                            $retailer['B2bShipToAddress']['country_id'] = $validCountryStates[$country][$value]['Country']['id'] ?? null;
                            $retailer['B2bShipToAddress']['state_id'] = $validCountryStates[$country][$value]['State']['id'] ?? null;
                        } elseif ($field === 'ManufacturerRetailer.is_commission_tier') {
                            $commissionTierMap = [
                                'Commission' => true,
                                'Standard' => false
                            ];
                            $retailer['is_commission_tier'] = $commissionTierMap[$value] ?? false;
                        } else {
                            $model = 'ManufacturerRetailer';
                            if (strpos($field, '.') !== false) {
                                list($model, $field) = explode('.', $field, 2);
                            }

                            /** @var AppModel|null $Model */
                            $Model = $this->{$model} ?? (ClassRegistry::init($model, true) ?: null);
                            if ($Model && $Model->getColumnType($field) === 'boolean') {
                                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                            }

                            if ($model === 'ManufacturerRetailer') {
                                $retailer[$field] = $value;
                            } else {
                                $retailer[$model][$field] = $value;
                            }
                        }
                    }

                    if ($salesRepHeaders) {
                        $retailer['sales_rep_ids'] = [];
                        foreach ($salesRepHeaders as $columnName) {
                            $value = $rowMap[$columnName];
                            if (isset($salesRepIds[$value])) {
                                $retailer['sales_rep_ids'][] = $salesRepIds[$value];
                            }
                        }
                    }

                    if ($creditTermOptions) {
                        $retailer['credit_term_ids'] = [];
                        foreach ($creditTermOptions as $creditTermId => $description) {
                            $value = $rowMap[$description];
                            if (filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)) {
                                $retailer['credit_term_ids'][] = (string)$creditTermId;
                            }
                        }
                    }
                    return $retailer;
                },
                $tableMap
            );

            if (!$this->ManufacturerRetailer->saveManyImportedConnections($brandId, $connections)) {
                $log = array(
                    'message' => 'Failed to save imported retailer settings',
                    'errors' => $this->ManufacturerRetailer->validationErrors,
                    'data' => array_intersect_key($connections, $this->ManufacturerRetailer->validationErrors),
                );
                throw new Exception(json_encode($log));
            }

            foreach ($connections as $connection) {
                if (Hash::get($connection, 'status') == ManufacturerRetailer::STATUS_CONNECTED) {
                    $this->UserLogic->productAssociation($brandId, $connection['retailer_id']);
                }
            }

            $this->setFlash('Retailer details successfully uploaded', 'success');
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->setFlash('An error occurred uploading the file', 'error');
        } finally {
            if (!empty($filePath) && file_exists($filePath)) {
                unlink($filePath);
            }
        }
        $this->redirect($this->referer());
    }

    private function _extractIndexQueryParams(CakeRequest $request = null, $userType = null)
    {
        if (!$request) {
            $request = $this->request;
        }
        if (!$userType) {
            $userType = $this->Auth->user('user_type');
        }

        // Dropdown filters
        $this->IndexQueryHandler->defaultQuery += ($userType === User::TYPE_SALES_REP)
            ? ['brand' => '']
            : ['tier' => '', 'sales_rep' => '', 'territory' => ''];

        $legacyParamMap = [
            'show' => 'limit',
            'order' => 'direction',
        ];
        // Handle legacy params by temporarily moving them to the new params
        foreach ($legacyParamMap as $legacy => $current) {
            if (array_key_exists($legacy, $request->query)) {
                $request->query += [$current => $request->query[$legacy]];
                unset($request->query[$legacy]);
            }
        }

        $request->query = $this->IndexQueryHandler->extractModifiedParams($request->query);
        $allParams = $this->IndexQueryHandler->extractAllParams($request->query);

        // Moving legacy params back to their legacy fields
        foreach ($legacyParamMap as $legacy => $current) {
            if (array_key_exists($current, $request->query)) {
                $request->query += [$legacy => $request->query[$current]];
                unset($request->query[$current]);
            }
            if (array_key_exists($current, $allParams)) {
                $allParams += [$legacy => $allParams[$current]];
                // Keep both versions in all params to help with migrating
            }
        }

        // Legacy url filter
        if ($request->param('filter')) {
            $statuses = array_flip(ManufacturerRetailer::STATUS_URL_FILTERS);
            $allParams['status'] = Hash::get($statuses, $request->param('filter'), array_values($statuses));
        }

        return $allParams;
    }

    public function ajax_toggle_product()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;
        $this->response->type('json');

        $data = json_decode($this->request->input(), true);
        $productIds = is_array($data['product_ids'] ?? null) ? array_map('intval', $data['product_ids']) : [];
        $selected = (bool)($data['selected'] ?? false);
        $retailerId = $data['retailer_id'] ?? null;

        if (!$productIds || !$retailerId) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Invalid product or retailer ID.'
            ]));
            return $this->response;
        }

        if ($selected) {
            $existingProductIds = (array)$this->RetailDisplay->find('list', [
                'conditions' => [
                    'RetailDisplay.user_id' => $retailerId,
                    'RetailDisplay.product_id' => $productIds,
                ],
                'fields' => ['product_id', 'product_id'],
            ]);
            $newProductIds = array_diff($productIds, $existingProductIds);
            foreach ($newProductIds as $productId) {
                $this->RetailDisplay->create();
                $this->RetailDisplay->save([
                    'user_id' => $retailerId,
                    'product_id' => $productId
                ]);
            }
        } else {
            $this->RetailDisplay->deleteAllJoinless([
                'RetailDisplay.user_id' => $retailerId,
                'RetailDisplay.product_id' => $productIds,
            ], false);
        }

        $this->response->body(json_encode(['success' => true]));
        return $this->response;
    }

    public function categories()
    {
        $originalVirtualFields = $this->RetailCategory->virtualFields;

        $this->RetailCategory->virtualFields['retailer_count'] = 'COALESCE(RetailCategoriesRetailerCount.count, 0)';

        $search = $this->request->query('search') ?? '';
        $sortField = $this->request->query('sortField') ?? 'category_order';
        $sortOrder = $this->request->query('sortOrder') ?? 'ASC';
        $pageNumber = $this->request->query('pageNumber') ?? 1;
        $limit = 10;

        $conditions = [
            'RetailCategory.user_id' => $this->Auth->user('id'),
        ];

        if (!empty($search)) {
            $conditions['RetailCategory.title LIKE'] = '%' . $search . '%';
        }

        $categories = $this->RetailCategory->find('all', [
            'joins' => [
                [
                    'table' => $this->RetailCategoriesRetailer->buildSubquery([
                        'conditions' => [
                            'User.user_type' => 'Retailer',
                        ],
                        'joins' => [
                            [
                                'table' => 'users',
                                'alias' => 'User',
                                'type' => 'INNER',
                                'conditions' => [
                                    'User.id = RetailCategoriesRetailer.retailer_id'
                                ]
                            ]
                        ],
                        'fields' => ['RetailCategoriesRetailer.category_id', 'COUNT(DISTINCT RetailCategoriesRetailer.retailer_id) AS `count`'],
                        'group' => ['RetailCategoriesRetailer.category_id'],
                    ]),
                    'alias' => 'RetailCategoriesRetailerCount',
                    'type' => 'LEFT',
                    'conditions' => [
                        'RetailCategoriesRetailerCount.category_id = RetailCategory.id'
                    ]
                ]
            ],
            'conditions' => $conditions,
            'fields' => ['id', 'title', 'retailer_count', 'image_url', 'color', 'is_on_store_locator'],
            'order' => [$sortField => $sortOrder],
            'limit' => $limit,
            'page' => $pageNumber,
        ]);

        $totalCount = $this->RetailCategory->find('count', [
            'conditions' => $conditions,
        ]);

        $paging = [
            'total' => $totalCount,
            'current' => $pageNumber,
            'limit' => $limit,
        ];

        $this->RetailCategory->virtualFields = $originalVirtualFields;
        $this->set(compact('categories', 'paging', 'search'));
    }

    public function addCategory()
    {
        if ($this->request->is('post')) {
            $categoryName = $this->request->data['Collection']['title'];
            $userId = $this->Auth->user('id');
            $handle = generate_slug($categoryName);

            $data = [
                'user_id' => $userId,
                'title' => $categoryName,
                'handle' => $handle,
            ];

            $this->RetailCategory->create();
            if ($this->RetailCategory->save($data)) {
                return $this->_successResponse();
            }

            return $this->_exceptionResponse(new InternalErrorException(json_encode([
                'errors' => $this->RetailCategory->validationErrors,
                'data' => $this->RetailCategory->data
            ])), null, true);
        }
    }

    public function retailer_categories($id)
    {
        $category = $this->RetailCategory->recordWithAllTranslations($id, [
            'fields' => ['id', 'user_id', 'title', 'image_url', 'color', 'is_on_store_locator'],
        ]);

        if (!$category) {
            throw new NotFoundException(__('Category not found'));
        }

        if ($category['RetailCategory']['user_id'] !== $this->Auth->user('id')) {
            throw new ForbiddenException(__('Unauthorized'));
        }

        $search = $this->request->query('search') ?? '';
        $sortField = $this->request->query('sortField') ?? 'RetailCategoriesRetailer.retailer_order';
        $sortOrder = $this->request->query('sortOrder') ?? 'ASC';
        $pageNumber = (int)($this->request->query('pageNumber') ?? 1);
        $limit = 10;

        $conditions = ['RetailCategoriesRetailer.category_id' => $id];
        if (!empty($search)) {
            $conditions['User.company_name LIKE'] = '%' . $search . '%';
        }

        $retailers = $this->RetailCategoriesRetailer->find('all', [
            'conditions' => $conditions,
            'contain' => ['Retailer'],
            'order' => [$sortField => $sortOrder],
            'limit' => $limit,
            'page' => $pageNumber,
        ]);

        $totalCount = $this->RetailCategoriesRetailer->find('count', [
            'conditions' => $conditions,
            'contain' => ['Retailer'],
        ]);

        $paging = [
            'total' => $totalCount,
            'current' => $pageNumber,
            'limit' => $limit,
        ];
        $this->set(compact('category', 'retailers', 'paging', 'search'));
    }

    public function ajax_add_category_retailer_search($categoryId = null)
    {
        $this->autoRender = false;
        $this->layout = null;
        $this->response->type('json');

        $term = $this->request->query('term');
        $excludes = (array)$this->request->query('exclude');
        $userId = $this->Auth->user('id');

        $alreadySelectedRetailerIds = $this->RetailCategoriesRetailer->find('list', [
            'conditions' => ['category_id' => $categoryId],
            'fields' => ['retailer_id', 'retailer_id'],
            'recursive' => -1
        ]);

        $excludedIdsFromQuery = array_column($excludes, 'retailer_id');
        $allExcludedIds = array_unique(array_merge($excludedIdsFromQuery, array_values($alreadySelectedRetailerIds)));

        $conditions = ['User.company_name LIKE' => '%' . $term . '%'];
        if (!empty($allExcludedIds)) {
            $conditions['ManufacturerRetailer.retailer_id NOT IN'] = $allExcludedIds;
        }

        $retailers = $this->ManufacturerRetailer->findAllRetailersByUserId($userId, $conditions);
        $formatted = array_map(fn($r) => [
            'label' => $r['User']['company_name'],
            'value' => $r['User']['company_name'],
            'Retailer' => $r['User'],
            'ManufacturerRetailer' => $r['ManufacturerRetailer']
        ], $retailers);

        return $this->response->body(json_encode($formatted));
    }

    public function ajax_add_category_retailers()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;
        $userId = (int)$this->Auth->user('id');
        $data = json_decode($this->request->input(), true);

        $categoryId = (int)($data['category_id'] ?? 0);
        $retailerIds = array_map('intval', (array)($data['retailer_ids'] ?? []));
        $mode = $data['mode'] ?? 'append';

        if (!$categoryId) {
            throw new BadRequestException(__('Invalid category ID.'));
        }

        $exists = $this->RetailCategory->exists([
            'RetailCategory.id' => $categoryId,
            'RetailCategory.user_id' => $userId,
        ]);
        if (!$exists) {
            throw new NotFoundException(__('Category not found.'));
        }

        $validRetailerIds = $this->ManufacturerRetailer->find('list', [
            'fields' => ['retailer_id'],
            'conditions' => ['user_id' => $userId],
            'recursive' => -1
        ]);
        $validRetailerIds = array_map('intval', $validRetailerIds);

        $retailerIds = array_intersect($retailerIds, $validRetailerIds);

        $existing = $this->RetailCategoriesRetailer->find('list', [
            'fields' => ['retailer_id'],
            'conditions' => ['category_id' => $categoryId],
            'recursive' => -1
        ]);
        $existingIds = array_map('intval', $existing);

        $toAdd = array_diff($retailerIds, $existingIds);
        $toRemove = [];

        if ($mode === 'replace') {
            $toRemove = array_diff($existingIds, $retailerIds);

            if (!empty($toRemove)) {
                $this->RetailCategoriesRetailer->deleteAllJoinless([
                    'category_id' => $categoryId,
                    'retailer_id' => $toRemove
                ], false);
            }
        }

        $added = [];
        foreach ($toAdd as $retailerId) {
            $this->RetailCategoriesRetailer->create();
            if ($this->RetailCategoriesRetailer->save([
                'category_id' => $categoryId,
                'retailer_id' => $retailerId
            ])) {
                $added[] = $retailerId;
            }
        }

        $this->response->type('json');
        $this->response->body(json_encode([
            'success' => true,
            'added' => $added,
            'removed' => array_values($toRemove),
            'message' => __('Retailers updated successfully.')
        ]));
        return $this->response;
    }

    public function ajax_browse_retailers($categoryId = null)
    {
        $userId = $this->Auth->user('id');
        $retailers = $this->ManufacturerRetailer->findAllRetailersByUserId($userId, []);
        foreach ($retailers as &$retailer) {
            $stateId = $retailer['User']['state_id'];
            $countryId = $retailer['User']['country_id'];
            $retailer['User']['state_name'] = $this->State->getStateName($stateId, $countryId);
            $retailer['User']['country_name'] = $this->Country->getCountryName($countryId);
        }

        $alreadySelectedRetailerIds = $this->RetailCategoriesRetailer->find('list', [
            'conditions' => ['category_id' => $categoryId],
            'fields' => ['retailer_id', 'retailer_id'],
            'recursive' => -1
        ]);

        $this->set(compact('retailers', 'categoryId', 'alreadySelectedRetailerIds'));
    }

    public function ajax_update_retailer_categories($retailerId = null)
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;
        $this->response->type('json');

        $userId = (int)$this->Auth->user('id');
        $data = json_decode($this->request->input(), true);
        $categoryIds = (array)($data['collection_ids'] ?? []);

        if (empty($retailerId) || empty($categoryIds)) {
            return $this->response->body(json_encode([
                'success' => false,
                'message' => 'Invalid input.'
            ]));
        }

        $validRetailer = $this->ManufacturerRetailer->find('count', [
            'conditions' => [
                'ManufacturerRetailer.retailer_id' => $retailerId,
                'ManufacturerRetailer.user_id' => $userId
            ],
            'recursive' => -1
        ]);

        if (!$validRetailer) {
            return $this->response->body(json_encode([
                'success' => false,
                'message' => 'Unauthorized retailer.'
            ]));
        }

        $existing = $this->RetailCategoriesRetailer->find('list', [
            'conditions' => ['retailer_id' => $retailerId],
            'fields' => ['category_id', 'category_id'],
            'recursive' => -1
        ]);

        $toAdd = array_diff($categoryIds, $existing);
        $toRemove = array_diff($existing, $categoryIds);

        foreach ($toAdd as $categoryId) {
            $this->RetailCategoriesRetailer->create();
            $this->RetailCategoriesRetailer->save([
                'category_id' => $categoryId,
                'retailer_id' => $retailerId
            ]);
        }

        if (!empty($toRemove)) {
            $this->RetailCategoriesRetailer->deleteAllJoinless([
                'category_id' => $toRemove,
                'retailer_id' => $retailerId
            ], false);
        }

        return $this->response->body(json_encode([
            'success' => true,
            'message' => 'Categories updated.'
        ]));
    }

    public function ajax_remove_category_retailer()
    {
        $categoryId = $this->request->data('category_id');
        $retailerId = $this->request->data('retailer_id');

        $retailer = $this->ManufacturerRetailer->find('first', [
            'recursive' => -1,
            'conditions' => ['ManufacturerRetailer.retailer_id' => $retailerId],
            'fields' => ['id'],
        ]);

        if (empty($retailer)) {
            throw new NotFoundException(__('Retailer not found'));
        }

        $result = $this->RetailCategoriesRetailer->deleteAllJoinless([
            'category_id' => $categoryId,
            'retailer_id' => $retailerId
        ], false);

        $this->set([
            'success' => (bool)$result,
            '_serialize' => ['success']
        ]);
    }

    public function update_category_retailer_order($categoryId = null)
    {
        $this->request->allowMethod(['post']);

        $retailerOrder = $this->request->data('order');
        $category = $this->RetailCategory->record($categoryId, [
            'fields' => ['id', 'user_id'],
        ]);

        if (!$category) {
            throw new NotFoundException(__('Category not found'));
        }

        if ($category['RetailCategory']['user_id'] !== $this->Auth->user('id')) {
            throw new ForbiddenException(__('You do not have permission to edit this category'));
        }

        $success = $this->RetailCategoriesRetailer->doInTransaction(function () use ($categoryId, $retailerOrder): bool {
            foreach ($retailerOrder as $index => $retailerId) {
                $result = $this->RetailCategoriesRetailer->updateAllJoinless(
                    ['retailer_order' => $index + 1],
                    ['retailer_id' => $retailerId, 'category_id' => $categoryId]
                );
                if (!$result) {
                    return false;
                }
            }
            return true;
        });

        $this->set([
            'success' => $success,
            'message' => $success
                ? __('Retailer order updated successfully.')
                : __('Failed to update some retailer orders. Please try again.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function updateCategoriesTitle()
    {
        $this->request->allowMethod(['post']);

        $categoryId = $this->request->data('id');
        $newTitle = $this->request->data('title');
        $language = $this->request->data('locale') ?? SupportedLanguages::DEFAULT_LOCALE;

        if (empty($categoryId) || empty($newTitle)) {
            $this->set([
                'success' => false,
                'message' => __('Invalid input data.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        if (!$this->RetailCategory->existsById($categoryId)) {
            $this->set([
                'success' => false,
                'message' => __('Category not found.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $this->RetailCategory->locale = $language;
        $success = (bool)$this->RetailCategory->save(['id' => $categoryId, 'title' => $newTitle]);

        $this->set([
            'success' => $success,
            'message' => $success
                ? __('Category title updated successfully.')
                : __('Failed to update title. Please try again.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function uploadCategoryImage($id)
    {
        if ($this->request->is('post')) {
            $category = $this->RetailCategory->record($id, [
                'contain' => [
                    'User' => ['fields' => ['id', 'uuid']],
                ],
                'fields' => ['id', 'user_id', 'image_url'],
            ]);

            if (empty($category['RetailCategory']['id'])) {
                throw new NotFoundException('Retail category not found.');
            }

            if ($category['RetailCategory']['user_id'] !== $this->Auth->user('id')) {
                throw new ForbiddenException('You do not have permission to upload an image for this category.');
            }

            $file = (array)$this->request->data['collection_image'];
            $sanitizedFileName = !empty($file['name']) ? preg_replace('/[^a-zA-Z0-9-_\.]/', '_', basename($file['name'])) : null;

            if (!empty($sanitizedFileName)) {
                $imageUrl = $this->Upload->replaceFileInUserHash(
                    $category['RetailCategory']['image_url'],
                    $file,
                    $category['User']['uuid'],
                    'retail_categories',
                    $sanitizedFileName
                );

                if (!$this->RetailCategory->save(['id' => $id, 'image_url' => $imageUrl])) {
                    throw new InternalErrorException(json_encode([
                        'message' => 'Failed to save category image',
                        'errors' => $this->RetailCategory->validationErrors,
                        'data' => $this->RetailCategory->data,
                    ]));
                }
            }

            $this->set('response', [
                'imageUrl' => $imageUrl,
                'fileName' => $sanitizedFileName
            ]);
            $this->set('_serialize', ['response']);
            return $this->response;
        }
    }

    public function updateCategoryColor()
    {
        $this->request->allowMethod(['post']);

        $categoryId = $this->request->data('id');
        $color = $this->request->data('color');

        if (empty($categoryId) || empty($color)) {
            $this->set([
                'success' => false,
                'message' => __('Invalid input data.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        if (!$this->RetailCategory->existsById($categoryId)) {
            $this->set([
                'success' => false,
                'message' => __('Category not found.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $success = (bool)$this->RetailCategory->save(['id' => $categoryId, 'color' => $color]);

        $this->set([
            'success' => $success,
            'message' => $success
                ? __('Color updated successfully.')
                : __('Failed to update color.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function updateStoreLocator()
    {
        $this->request->allowMethod(['post']);

        $categoryId = $this->request->data('id');
        $isOnStoreLocator = $this->request->data('is_on_store_locator');

        if (empty($categoryId)) {
            $this->set([
                'success' => false,
                'message' => __('Missing category ID.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        if (!$this->RetailCategory->existsById($categoryId)) {
            $this->set([
                'success' => false,
                'message' => __('Category not found.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $success = (bool)$this->RetailCategory->save(['id' => $categoryId, 'is_on_store_locator' => $isOnStoreLocator]);

        $this->set([
            'success' => $success,
            'message' => $success
                ? __('Store Locator setting updated.')
                : __('Failed to update setting.'),
            '_serialize' => ['success', 'message']
        ]);
    }

    public function ajax_remove_category()
    {
        $categoryId = $this->request->data('category_id');

        if (!$this->RetailCategory->existsById($categoryId)) {
            throw new NotFoundException(__('Category not found'));
        }

        $result = $this->RetailCategory->delete($categoryId);

        $this->set([
            'success' => $result,
            '_serialize' => ['success']
        ]);
    }

    public function update_category_order()
    {
        $this->request->allowMethod(['post']);

        $categoryOrder = $this->request->data('order');

        $validCategoryIds = array_keys($this->RetailCategory->getCategoriesOptionsByUser($this->Auth->user('id')));
        $invalidCategoryIds = array_diff($categoryOrder, $validCategoryIds);
        if ($invalidCategoryIds) {
            $this->set([
                'success' => false,
                'message' => __('Some categories were not found. Please try again.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $saveData = [];
        foreach ($categoryOrder as $index => $categoryId) {
            $saveData[] = [
                'id' => $categoryId,
                'category_order' => ($index + 1),
            ];
        }

        if (!$this->RetailCategory->saveMany($saveData)) {
            $this->set([
                'success' => false,
                'message' => __('Failed to update some category orders. Please try again.'),
                '_serialize' => ['success', 'message']
            ]);
            return;
        }

        $this->set([
            'success' => true,
            'message' => __('Category order updated successfully.'),
            '_serialize' => ['success', 'message']
        ]);
    }
}
