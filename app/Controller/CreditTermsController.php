<?php
App::uses('AppController', 'Controller');
App::uses('CreditTermPaymentStatus', 'Utility');
/**
 * CreditTerms Controller
 *
 * @property CreditTerm $CreditTerm
 * @property PaginatorComponent $Paginator
 * @property SessionComponent $Session
 * @property FlashComponent $Flash
 */
class CreditTermsController extends AppController
{
    /**
     * Components
     *
     * @var array
     */
    public $components = ['Paginator', 'Session', 'Flash'];

    public function creditTermEdit()
    {
        $userId = (int)$this->Auth->user('id');
        if ($this->request->is('post')) {
            $this->creditTermEditPost($userId);
        } else {
            $this->creditTermEditGet($userId);
        }
    }

    public function creditTermRow($key = null)
    {
        $this->set('key', ($key ?: '0'));
        $paymentStatusOptions = array_combine(CreditTermPaymentStatus::STATUSES, CreditTermPaymentStatus::STATUSES);
        $this->set('paymentStatusOptions', $paymentStatusOptions);
        $this->set('defaultPaymentStatus', CreditTermPaymentStatus::DEFAULT_STATUS);
        $this->set('productCategories', $this->Product->getWholesaleProductTypesByUser($this->Auth->user('id')));
        $this->set('creditTerm', [
            'id' => null,
            'description' => null,
            'days_due' => null,
            'qualifying_discount_days' => 0,
            'early_payment_discount_percentage' => 0.00,
            'condition_type' => 'None',
            'condition_value' => null,
            'product_type' => null,
            'payment_status' => null,
            'can_delete' => true,
        ]);

        return $this->render('/Elements/credit_term_row');
    }

    protected function creditTermEditPost($userId)
    {
        $this->autoRender = false;
        $this->request->allowMethod('post');
        if (!isset($this->request->data['CreditTerm'])) {
            return $this->_exceptionResponse(new BadRequestException(), null, true);
        }

        if ($this->CreditTerm->saveCreditTermForm($userId, $this->request->data['CreditTerm'])) {
            $this->setFlash('Credit Terms have been updated', 'success');

            return $this->_successResponse('');
        }
        $this->setFlash('There was an error saving credit terms. Please try again.', 'error');
    }

    protected function creditTermEditGet($userId)
    {
        /** @var UserSetting $UserSetting */
        $UserSetting = ClassRegistry::init('UserSetting');

        $creditTerms = Hash::extract($this->CreditTerm->getForEdit($userId), '{n}.CreditTerm');

        if (isset($this->request->query['lang'])) {
            $UserSetting->locale = $this->request->query['lang'];
        }
        $termsAndConditions = $UserSetting->getTermsAndConditions($userId);
        $this->set('creditTerms', $creditTerms);
        $this->set('termsAndConditions', $termsAndConditions);

        $paymentStatusOptions = array_combine(CreditTermPaymentStatus::STATUSES, CreditTermPaymentStatus::STATUSES);
        $this->set('paymentStatusOptions', $paymentStatusOptions);
        $this->set('defaultPaymentStatus', CreditTermPaymentStatus::DEFAULT_STATUS);

        $this->set('creditTerms', $creditTerms);
        $this->set('productCategories', $this->Product->getWholesaleProductTypesByUser($userId));

        return $this->render('/Elements/credit_terms_edit');
    }
}
