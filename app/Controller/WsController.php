<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppController', 'Controller');
App::uses('UpdateCron', 'Model');
App::uses('DeliveryOptions', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('PreorderType', 'Utility');
App::uses('ProductSellDirect', 'Utility');
App::uses('Xml', 'Utility');
App::uses('View', 'View');

/**
 * Class WsController
 *
 * @property AddressValidatorComponent $AddressValidator
 * @property AuthComponent $Auth
 * @property CurrencyComponent $Currency
 * @property LockComponent $Lock
 * @property OrderPlacerComponent $OrderPlacer
 * @property AvataxComponent $Avatax
 * @property LightspeedComponent $Lightspeed
 * @property SquarePosComponent $SquarePos
 * @property OrderLogicComponent $OrderLogic
 * @property NotificationLogicComponent $NotificationLogic
 * @property ShopifyPOSComponent $ShopifyPOS
 * @property QuickbookComponent $Quickbook
 * @property ShopifyComponent $Shopify
 * @property ShopifyWebhookHandlerComponent $ShopifyWebhookHandler
 * @property ShopifyWebhookListenerComponent $ShopifyWebhookListener
 * @property WoocommerceComponent $Woocommerce
 * @property WoocommerceWebhookHandlerComponent $WoocommerceWebhookHandler
 * @property WoocommerceWebhookListenerComponent $WoocommerceWebhookListener
 * @property StripeComponent $Stripe
 * @property VendPOSComponent $VendPOS
 * @property TaxCalculatorComponent $TaxCalculator
 * @property VelofixComponent $Velofix
 *
 * @property Cron $Cron
 * @property User $User
 * @property Collection $Collection
 * @property Contact $Contact
 * @property Discount $Discount
 * @property State $State
 * @property PricingTier $PricingTier
 * @property Product $Product
 * @property ProductStateFee $ProductStateFee
 * @property Customer $Customer
 * @property CustomerAddress $CustomerAddress
 * @property Country $Country
 * @property Order $Order
 * @property DealerOrder $DealerOrder
 * @property AppModel $ProductImage
 * @property UpdateCron $UpdateCron
 * @property OrderProduct $OrderProduct
 * @property EcommerceView $EcommerceView
 * @property EmailTemplate $EmailTemplate
 * @property Notification $Notification
 * @property Preorder $Preorder
 * @property ProductRetailer $ProductRetailer
 * @property Tag $Tag
 * @property AppModel $Masspay
 * @property AppModel $Notifycustomer
 * @property Store $Store
 * @property Page $Page
 * @property QuickbookProduct $QuickbookProduct
 * @property Btask $Btask
 * @property StripeUser $StripeUser
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property UserSetting $UserSetting
 * @property UserCountryTax $UserCountryTax
 * @property Warehouse $Warehouse
 * @property WarehouseProduct $WarehouseProduct
 */
class WsController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Ws';

    public $components = [
        'AddressValidator',
        'Auth',
        'Currency',
        'Lock',
        'OrderPlacer',
        'Avatax',
        'Lightspeed',
        'SquarePos',
        'OrderLogic',
        'NotificationLogic',
        'Shopifypos.ShopifyPOS',
        'Quickbook.Quickbook',
        'Shopify.Shopify',
        'Shopify.ShopifyWebhookHandler',
        'Shopify.ShopifyWebhookListener',
        'Woocommerce.Woocommerce',
        'Woocommerce.WoocommerceWebhookHandler',
        'Woocommerce.WoocommerceWebhookListener',
        'Stripe.Stripe',
        'Vendpos.VendPOS',
        'TaxCalculator',
        'Velofix',
    ];

    /**
     * @var array
     */
    public $uses = array('Cron', 'User', 'Collection', 'Contact', 'Discount', 'State', 'PricingTier', 'Product', 'ProductStateFee', 'Customer', 'CustomerAddress', 'Country', 'Order', 'DealerOrder', 'ProductImage', 'UpdateCron', 'OrderProduct', 'EcommerceView', 'EmailTemplate', 'Notification', 'Preorder', 'ProductRetailer', 'Tag', 'Masspay', 'Notifycustomer', 'Store', 'Page', 'Quickbook.QuickbookProduct', 'Btask', 'StripeUser', 'ManufacturerRetailer', 'UserSetting', 'UserCountryTax', 'Warehouse', 'WarehouseProduct');

    /**
     * @var bool
     * @see Controller::$autoRender
     */
    public $autoRender = false;

    /**
     * Specifies the level of accuracy of GeoLocation with the Google API
     * GEOPOINT_LEVEL_HIGH - $city, $zip, $state, $country
     * GEOPOINT_LEVEL_MIDDLE - $zip, $state, $country
     * GEOPOINT_LEVEL_LOW - $zip
     */
     const GEOPOINT_LEVEL_HIGH = 1;
     const GEOPOINT_LEVEL_MIDDLE = 2;
     const GEOPOINT_LEVEL_LOW = 3;

    /**
     *
     */
    function beforeFilter()
    {
        parent::beforeFilter();
        $this->Auth->allow(array(
            'add',
            'view',
            'cron',
            'update',
            'checkUpdatecron',
            'test',
            'getretailers',
            'retpayment',
            'payment_cron_ipn',
            'payment_ipn',
            'payment_cancel',
            'ipnNotification',
            'shipearlyPay',
            'notifyCustomer',
            'shippingAddressValidation',
            'getSiftKey',
            'getGoogleApiKey',
            'shopifyUpdate',
            'shopifyGetRetailers',
            'getRetailerIds',
            'checkProduct',
            'productCheck',
            'brandPolicies',
            'paymentPageCustomContent',
            'runCron',
            'checkSellExclusiveProduct',
            'validate_address',
            'verifyStoreAssociatePin'
        ));
    }

    /**
     ** add() - Fired when calling in POST method
     */
    function add()
    {
        $this->autoRender = false;
        if ($_POST) {
            if ($_POST['mode'] == 'shopify') {
                $response = array('id' => 23, 'name' => 'TESTING RESTFUL WEBSERVICE');
                echo header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            } else {
                $this->statusCode = "invalidMode";
                $this->msg = INVALID_REQUEST;
            }
        } else {
            $this->statusCode = "invalidURL";
            $this->msg = INVALID_REQUEST;
        }
    }

    /**
     ** view() - Fired when calling in GET method
     */
    function view($mode = '')
    {
        echo $mode;
        exit;
        $this->autoRender = false;
        if (!empty($mode)) {
            if ($mode == 'shopify') {
                $response = array('id' => 23, 'name' => 'TESTING RESTFUL WEBSERVICE');
                echo header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            } else {
                $this->statusCode = "invalidMode";
                $this->msg = INVALID_REQUEST;
            }
        } else {
            $this->statusCode = "invalidURL";
            $this->msg = INVALID_REQUEST;
        }
    }

    /**
     * Synchronize a brand's orders, products, customers, and warehouses with their Ecommerce store.
     *
     * This cron runs every 1 minute.
     *
     * @throws Exception
     */
    public function runCron()
    {
        set_time_limit(0);

        $cron = $this->Lock->applyGlobalLock($this->request->url, [$this->Cron, 'dequeueUser']);
        if (empty($cron['Cron']['cronid'])) {
            return true;
        }

        $userLog = array_intersect_key($cron['User'], array_flip(['email_address', 'company_name', 'site_type', 'shop_url']));
        CakeLog::info(json_encode(['message' => 'Processing brand sync cronjob', 'Cron' => $cron['Cron'], 'User' => $userLog]));

        try {
            $user = ['User' => $cron['User']];
            switch ($user['User']['site_type']) {
                case UserSiteType::MAGENTO:
                    $this->_magentoData($user, $cron);
                    break;
                case UserSiteType::SHIPEARLY:
                    // Do nothing
                    break;
                case UserSiteType::SHOPIFY:
                    try {
                        $this->requestAction('/shopifysetup', array('data' => array('user' => $user['User']['api_key'], 'pass' => $user['User']['secret_key'], 'domain' => $user['User']['shop_url'], 'uuid' => $user['User']['uuid'])));
                    } catch (Exception $e) {
                        CakeLog::error($e);
                    }
                    $this->_shopifyData($user);
                    break;
                case UserSiteType::WOOCOMMERCE:
                    $this->_woocommerceData($user);
                    break;
                default:
                    triggerWarning(json_encode(['message' => 'Unknown site_type', 'User' => $userLog]));
            }

            $this->Cron->close($cron['Cron']['cronid']);

            return true;
        } catch (Exception $e) {
            $status = ($cron['Cron']['status'] !== Cron::STATUS_ERROR) ? Cron::STATUS_ERROR : Cron::STATUS_CLOSED;
            $this->Cron->setStatus($cron['Cron']['cronid'], $status, $e->getMessage());

            throw $e;
        }
    }

    /**
     * Temporary cron fix
     */
    public function cron()
    {
        $this->autoRender = false;
        return true;
    }

    /**
     * Sub function for magento data synchronization
     *
     * @param array $user
     * @param array $value
     * @return bool
     * @throws SoapFault
     */
    public function _magentoData(array $user, array $value)
    {
        $proxy = new SoapClient(rtrim($user['User']['shop_url'], "/") . '/api/soap/?wsdl');
        $session = $proxy->login($user['User']['api_key'], $user['User']['secret_key']);

        $result = $proxy->call($session, 'catalog_product.list');
        foreach ($result as $values) {
            sleep(10); /* To avoid multiple api request */
            $this->_addMagentoProduct($proxy, $session, $values['product_id'], $value['Cron']['userid']);
        }

        $orderlist = $proxy->call($session, 'order.list');
        foreach ($orderlist as $values) {
            sleep(10); /* To avoid multiple api request */
            $this->_addMagentoOrder($proxy, $session, $values['increment_id'], $value['Cron']['userid'], 'cron', $user['User']);
        }
        return true;
    }

    /**
     * @param SoapClient $proxy
     * @param $session
     * @param string $id
     * @param int $userid
     * @param string $updateid
     * @return bool
     */
    protected function _addMagentoProduct($proxy, $session, $id, $userid, $updateid = "")
    {
        try {
            $product = $proxy->call($session, 'catalog_product.info', $id);
            $media = $proxy->call($session, 'catalog_product_attribute_media.list', $id);
        } catch (Exception $e) {
            $msg = "update product: " . $e->faultstring;
            $this->UpdateCron->saveMagentoError($updateid, $userid, $msg);
            return false;
        }
        $productinfo['product_type'] = $product['type'];
        $productinfo['user_id'] = $userid;
        $productinfo['productID'] = $product['product_id'];
        $productinfo['product_title'] = $product['name'];
        if (!empty($product['sku'])) {
            $productinfo['product_sku'] = $product['sku'];
        }
        $productinfo['product_price'] = isset($product['price']) ? $product['price'] : 0;
        $productinfo['product_description'] = $product['description'];
        $productinfo['product_image'] = isset($media[0]['url']) ? $media[0]['url'] : '';
        $productinfo['created_at'] = $product['created_at'];
        $productinfo['updated_at'] = $product['updated_at'];

        $productinfo = $this->Product->syncEcommerceVariant($productinfo);

        foreach ($media as $media_value) {
            if (in_array('image', $media_value['types']) && in_array('small_image', $media_value['types']) && in_array('thumbnail', $media_value['types'])) {
                $this->ProductImage->create();
                $this->ProductImage->save(array('product_id' => $productinfo['id'], 'image_url' => $media_value['url']));
            } else if (in_array('image', $media_value['types'])) {

            } else {
                $this->ProductImage->create();
                $this->ProductImage->save(array('product_id' => $productinfo['id'], 'image_url' => $media_value['url']));
            }
        }

//        foreach ($product['categories'] as $cat_key => $cat_value) {
//            $this->_addcat($proxy, $session, $cat_value, $userid, $pid);
//        }
    }

    /**
     * @param SoapClient $proxy
     * @param $session
     * @param string $id
     * @param int $userid
     * @param string $ordtype
     * @param null|array $user
     * @param string|int $updateid
     */
    protected function _addMagentoOrder($proxy, $session, $id, $userid, $ordtype, $user = null, $updateid = '')
    {
        $c_retailer = array();
        $name = $orderID = '';
        $userid = (int)$userid;

        try {
            $order = $proxy->call($session, 'sales_order.info', $id);
        } catch (Exception $e) {
            $msg = "update order: " . $e->faultstring;
            $this->UpdateCron->saveMagentoError($updateid, $userid, $msg);
            return false;
        }

        $cusId = null;
        if (!empty($order['customer_id'])) {
            try {
                $cusId = $this->_addMagentoCustomer($proxy, $session, $order['customer_id'], $userid);
            } catch (Exception $e) {
                CakeLog::warning($e);
            }
        }
        if (empty($cusId)) {
            $cusId = $this->_addCustomerByEmail($userid, array_merge(
                $order['billing_address'],
                $order['shipping_address'],
                ['email' => $order['customer_email']]
            ));
        }

        $orderinfo['user_id'] = $userid;
        $orderinfo['customerID'] = $cusId;
        //$orderinfo['orderID'] = $order['order_id'];
        $orderinfo['orderNO'] = $order['increment_id'];
        $orderinfo['retailer_id'] = '';
        if ($order['shipping_method'] == 'shipearly_shipearly' && !empty($orderinfo['retailer_id'])) {
            $c_retailer = $this->User->findById($orderinfo['retailer_id'], array('address', 'email_address', 'company_name', 'id', 'store_timing', 'city', 'zipcode'));
            $type = 'In_store';
            $orderinfo['order_status'] = 'Not picked up';
            $name = $order['billing_address']['firstname'] . " " . $order['billing_address']['lastname'];
            if ($ordtype == 'update') {
                $code = $this->OrderLogic->generateCode(); //$order['customer_email'], $name, $c_retailer['User']
                $orderinfo['secretcode'] = $code;
            }
        } else if ($order['shipping_method'] == 'shipfromstore_shipfromstore' && !empty($orderinfo['retailer_id'])) {
            $c_retailer = $this->User->findById($orderinfo['retailer_id'], array('address', 'email_address', 'company_name', 'id', 'store_timing', 'city', 'zipcode'));
            $type = 'Ship_store';
            $orderinfo['order_status'] = $order['status'];
        } else {
            $orderinfo['retailer_id'] = null;
            $type = $order['shipping_method'];
            $orderinfo['order_status'] = $order['status'];
        }
        $orderinfo['order_type'] = $type;
        if ($ordtype == 'update') {
            $orderinfo['created_at'] = $orderinfo['updated_at'] = date('Y-m-d H:i:s');
        } else {
            $orderinfo['created_at'] = $order['created_at'];
            $orderinfo['updated_at'] = $order['updated_at'];
        }
        $orderinfo['total_price'] = $order['base_grand_total'];
        $orderinfo['total_discount'] = abs($order['base_discount_invoiced']);
        $orderinfo['totalPriceConversion'] = $order['base_grand_total'];
        $orderinfo['currency_code'] = $order['base_currency_code'];
        $orderinfo['shipping_amount'] = $order['shipping_amount'];
        $orderinfo['billing_firstname'] = $order['billing_address']['firstname'];
        $orderinfo['billing_lastname'] = $order['billing_address']['lastname'];
        $orderinfo['shipping_address1'] = isset($order['shipping_address']['street']) ? $order['shipping_address']['street'] : '';
        $orderinfo['shipping_address2'] = '';
        $orderinfo['shipping_city'] = isset($order['shipping_address']['city']) ? $order['shipping_address']['city'] : '';
        $orderinfo['shipping_state'] = isset($order['shipping_address']['region']) ? $order['shipping_address']['region'] : '';
        $orderinfo['shipping_countrycode'] = $orderinfo['shipping_country'] = isset($order['shipping_address']['country_id']) ? $order['shipping_address']['country_id'] : '';
        $orderinfo['shipping_zipcode'] = isset($order['shipping_address']['postcode']) ? $order['shipping_address']['postcode'] : '';
        $orderinfo['customerEmail'] = $order['customer_email'];
        if (isset($order['shipping_address']['postcode']) && !empty($order['shipping_address']['postcode'])) {
            $geopoints = $this->_getLnt($order['shipping_address']['street'], $order['shipping_address']['city'], $order['shipping_address']['postcode'], $order['shipping_address']['region'], $order['shipping_address']['country_id']);
            $orderinfo['latitude'] = $geopoints['lat'];
            $orderinfo['longitude'] = $geopoints['lng'];
        }
        $orderinfo['shipping_telephone'] = isset($order['shipping_address']['telephone']) ? $order['shipping_address']['telephone'] : '';
        $orderinfo['shipping_statecode'] = '';
        $orderinfo['total_qty_ordered'] = $order['total_qty_ordered'];
        $orderinfo['total_tax'] = $order['base_tax_amount'];

        if ($order['shipping_method'] == 'shipearly_shipearly' || $order['shipping_method'] == 'shipfromstore_shipfromstore') {
            $orderinfo['shipearlyFees'] = $this->Currency->formatAsDecimal(
                $this->OrderLogic->CalculateFees($orderinfo['total_price'], 'retailer', $user['revenue_model'], $user['retailer_default_amount'], $user['retailer_revenue_maximum']),
                $orderinfo['currency_code']
            );
            $orderinfo['retailerAmount'] = $this->Currency->formatAsDecimal($orderinfo['total_price'] - $orderinfo['shipearlyFees'], $orderinfo['currency_code']);
        }

        if (isset($c_retailer) && count($c_retailer)) {
            $timing = json_decode($this->User->getStoreTiming($c_retailer['User']['store_timing'], $c_retailer['User']['id']));
            $c_retailer['User']['store_timing'] = $timing->currentTime;
        }

        $id = $this->Order->find('first', array('fields' => array('id'), 'conditions' => array('user_id' => $userid, 'orderNO' => $order['increment_id'])));
        if (count($id) == 1) {
            $inc_oid = $orderinfo['id'] = $id['Order']['id'];
            $orderinfo['order_status'] = $order['status'];
            $this->Order->save($orderinfo);
        } else {
            $this->Order->create();
            $this->Order->save($orderinfo);
            $inc_oid = $this->Order->getLastInsertID();
            //update orderID
            $orderID = $this->formatOrderId($inc_oid);
            $order['orderID'] = $orderID;
            $this->Order->save(array('id' => $inc_oid, 'orderID' => $orderID));
        }
        foreach ($order['items'] as $pro) {
            $pro_ids = $this->Product->find('first', array('fields' => array('id'), 'conditions' => array('user_id' => $userid, 'productID' => $pro['product_id'])));
            if (isset($pro_ids['Product']['id'])) {
                $poid = $this->OrderProduct->find('first', array('fields' => array('id'), 'conditions' => array('order_id' => $inc_oid, 'product_id' => $pro_ids['Product']['id'], 'quantity' => $pro['qty_ordered'])));
                if (count($poid) == 1) {
                    $inc_opid = $poid['OrderProduct']['id'];
                    $this->OrderProduct->save(array('id' => $inc_opid, 'order_id' => $inc_oid, 'product_id' => $pro_ids['Product']['id'], 'quantity' => $pro['qty_ordered'], 'total_tax' => $pro['base_tax_amount'], 'total_price' => $pro['base_row_total'], 'totalPriceConversion' => $pro['base_row_total'], 'total_discount' => abs($pro['base_discount_amount'])));
                } else {
                    $this->OrderProduct->create();
                    $this->OrderProduct->save(array('order_id' => $inc_oid, 'product_id' => $pro_ids['Product']['id'], 'quantity' => $pro['qty_ordered'], 'total_tax' => $pro['base_tax_amount'], 'total_price' => $pro['base_row_total'], 'totalPriceConversion' => $pro['base_row_total'], 'total_discount' => abs($pro['base_discount_amount'])));
                }
            }
        }
        if ($ordtype == 'update') {
            if ($order['shipping_method'] == 'shipfromstore_shipfromstore' && !empty($orderinfo['retailer_id'])) {
                $notification_msg = "A customer has placed an order to be shipped from store";
                $this->Notification->createNotification($userid, $userid, Notification::TYPE_SHIP_FROM_STORE_ORDER, $inc_oid, $notification_msg);
                $this->Notification->createNotification($userid, $orderinfo['retailer_id'], Notification::TYPE_SHIP_FROM_STORE_ORDER, $inc_oid, $notification_msg);
                $this->shipcustomer($order['customer_email'], $name, $c_retailer, $orderinfo, $inc_oid);
            } else if ($order['shipping_method'] == 'shipearly_shipearly' && !empty($orderinfo['retailer_id'])) {
                $this->_sendInstoreCustomerMail($order['customer_email'], $name, $c_retailer, $inc_oid, $code);
                $this->_sendInStoreNotification($inc_oid, $c_retailer['User'], $user, $order, $orderID);
            } else {
                $this->Notification->createForSellDirect($userid, $userid, $inc_oid);
            }
        }
    }

    /**
     * @param SoapClient $proxy
     * @param $session
     * @param string $customerId
     * @param int $userId
     * @return int|null
     */
    protected function _addMagentoCustomer($proxy, $session, $customerId, int $userId): ?int
    {
        $customer = (array)$proxy->call($session, 'customer.info', $customerId);
        $addressList = (array)$proxy->call($session, 'customer_address.list', $customerId);

        return $this->Customer->syncMagentoCustomer($userId, $customer, $addressList);
    }

    /**
     * Send email to end customer about the ship from store invoice
     * @param $email
     * @param $name
     * @param $retailer
     * @param $shipping
     * @param $orderID
     */
    public function shipcustomer($email, $name, $retailer, $shipping, $orderID)
    {
        //$this->requestAction('viewPdf/' . $orderID . '/ship_from_store');
        $pdfUrl = 'viewPdf/' . $orderID . '/b2c';
        /** customer **/
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Shipfromstore Customer');
        $street = $this->emailFormatAddress($retailer['User']);
        $variables = array();
        $variables['{customer_name}'] = $name;
        $variables['{customer_address}'] = $this->emailAddressDisplayFormat($name, $shipping['shipping_address1'], $shipping['shipping_address2'], $shipping['shipping_city'], $shipping['shipping_state'], $shipping['shipping_country'], $shipping['shipping_zipcode']);
        $variables['{customer_email}'] = $shipping['customerEmail'];
        $variables['{customer_phone}'] = $shipping['shipping_telephone'];
        $variables['{site_name}'] = SITE_NAME;
        $variables['{retailer_name}'] = $retailer['User']['company_name'];
        $variables['{store_timing}'] = $retailer['User']['store_timing'];
        $variables['{retailer_email}'] = $retailer['User']['email_address'];
        $variables['{retailer_address}'] = $this->emailAddressDisplayFormat($retailer['User']['company_name'], $street['address1'], $street['address2'], $retailer['User']['city'], $retailer['State']['state_name'], $retailer['Country']['country_name'], $retailer['User']['zipcode']);
        /*$variables['{retailer_address}'] = $retailer['User']['address'];
        $variables['{retailer_city}'] = $retailer['User']['city'];
        $variables['{retailer_zipcode}'] = $retailer['User']['zipcode'];*/
        $variables['{retailer_telephone}'] = $retailer['Contact']['value'];
        /*$variables['{retailer_state}'] = $retailer['State']['state_name'];
        $variables['{retailer_country}'] = $retailer['Country']['country_name'];*/
        $this->sendEmail($emailTemplateArr, $variables, trim($email), '', '', array(get_order_invoice_pdf_filepath($orderID)), $pdfUrl); //trim($email)
    }

    /**
     * Send email to end customer about the Instore invoice
     * @param $email
     * @param $name
     * @param $retailer
     * @param $orderID
     * @param $randomString
     * @param null $subType
     */
    function _sendInstoreCustomerMail($email, $name, $retailer, $orderID, $randomString, $subType = null)
    {
        //$this->requestAction('viewPdf/' . $orderID . '/in_store');
        $pdfUrl = 'viewPdf/' . $orderID . '/b2c';
        /** customer **/
        if ($subType == 'nonstock') {
            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Nonstock Instore Code');
        } else {
            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Instore Code');
        }
        $street = $this->emailFormatAddress($retailer['User']);
        $variables = array();
        $variables['{user_name}'] = $name;
        $variables['{secret_code}'] = $randomString;
        $variables['{site_name}'] = SITE_NAME;
        $variables['{address}'] = $this->emailAddressDisplayFormat($retailer['User']['company_name'], $street['address1'], $street['address2'], $retailer['User']['city'], $retailer['State']['state_name'], $retailer['Country']['country_name'], $retailer['User']['zipcode']);

        /*$variables['{address}'] = $retailer['User']['address'];
        $variables['{company_name}'] = $retailer['User']['company_name'];
        $variables['{city}'] = $retailer['User']['city'];*/
        $variables['{telephone}'] = $retailer['Contact']['value'];
        // $variables['{zipcode}'] = $retailer['User']['zipcode'];
        $variables['{store_timing}'] = $retailer['User']['store_timing'];
        /*$variables['{state}'] = $retailer['State']['state_name'];
        $variables['{country}'] = $retailer['Country']['country_name'];*/
        $this->sendEmail($emailTemplateArr, $variables, trim($email), '', '', '', array(get_order_invoice_pdf_filepath($orderID)), $pdfUrl);
    }

    /**
     * Send notification to retailer as well for brand
     * @param $inc_oid
     * @param $retailer
     * @param $user
     * @param $order
     * @param string $orderID
     */
    function _sendInStoreNotification($inc_oid, $retailer, $user, $order, $orderID = '')
    {
        $notification_msg = "A customer has placed an order on an in-store pick up";

        $variables = array();
        $variables['{site_name}'] = SITE_NAME;
        $variables['{order_id}'] = $this->makeOrderLink($orderID, $inc_oid);
        $variables['{customer_email}'] = $order['customer_email'];
        $variables['{customer_phone}'] = $order['shipping_telephone'];
        $variables['{order_type}'] = "In Store Pickup";
        $variables['{customer_address}'] = $this->emailAddressDisplayFormat(' ', $order['shipping_address']['street'], ' ', $order['shipping_address']['city'], ' ', ' ', ' ');
        $variables['{customer_name}'] = $order['customer_firstname'] . " " . $order['customer_lastname'];
        // $variables['{customer_address}'] = $order['shipping_address']['street'] . ", <br/>" . $order['shipping_address']['city'];


        /** retailer **/
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Instore Retailer');
        $variables['{user_name}'] = $retailer['company_name'];
        $this->sendEmail($emailTemplateArr, $variables, trim($retailer['email_address']), '', '', array(get_order_invoice_pdf_filepath($inc_oid)));

        $this->Notification->createNotification($user['id'], $user['id'], Notification::TYPE_INSTORE_ORDER, $inc_oid, $notification_msg);

        /** manufacturer **/
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Instore Manufacturer');
        $variables['{user_name}'] = $user['company_name'];
        $variables['{retailer_name}'] = "<a href='" . BASE_PATH . "contact/" . $retailer['id'] . "'>" . $retailer['company_name'] . "</a>";
        $this->sendEmail($emailTemplateArr, $variables, trim($user['email_address']), '', '', array(get_order_invoice_pdf_filepath($inc_oid)));

        $this->Notification->createNotification($user['id'], $retailer['id'], Notification::TYPE_INSTORE_ORDER, $inc_oid, $notification_msg);
    }

    /**
     * Sub function for Shopify data synchronization
     *
     * @param array $user
     */
    public function _shopifyData($user)
    {
        $userId = (int)$user['User']['id'];
        $apiKey = $user['User']['api_key'];
        $secretKey = $user['User']['secret_key'];
        $shopUrl = $user['User']['shop_url'];

        $syncedProducts = $this->_syncShopifyProducts($userId, $apiKey, $secretKey, $shopUrl);

        $this->_syncShopifyCollections($userId, $apiKey, $secretKey, $shopUrl);

        $inventoryItemIds = array_values(array_filter(
            Hash::combine($syncedProducts, '{n}.variants.{n}.inventory_item_id', '{n}.variants.{n}.inventory_item_id')
        ));
        $this->_syncShopifyLocations($userId, $inventoryItemIds, $apiKey, $secretKey, $shopUrl);

        $this->_syncShopifyOrders($userId, $apiKey, $secretKey, $shopUrl);
    }

    private function _syncShopifyProducts(int $userId, $apiKey, $secretKey, $shopUrl)
    {
        return array_filter(
            $this->Shopify->syncProducts($apiKey, $secretKey, $shopUrl, function($product) use ($userId) {
                try {
                    $success = $this->ShopifyWebhookHandler->syncProduct($userId, (array)$product);
                } catch (Exception $e) {
                    CakeLog::error($e);
                    $success = false;
                }
                if (!$success) {
                    return null;
                }

                return [
                    'id' => $product['id'],
                    'variants' => array_map(
                        function($variant) {
                            return [
                                'id' => $variant['id'],
                                'inventory_item_id' => $variant['inventory_item_id'],
                            ];
                        },
                        $product['variants']
                    ),
                ];
            })
        );
    }

    private function _syncShopifyCollections($userId, $apiKey, $secretKey, $shopUrl)
    {
        try {
            $collections = $this->Shopify->getAllPaginatedCustomCollections($secretKey, $shopUrl);
            $collects = $this->Shopify->getAllPaginatedCollects($secretKey, $shopUrl);
            if (!$this->Collection->syncAllShopifyCollections((int)$userId, $collections, $collects)) {
                CakeLog::error(json_encode(['errors' => $this->Collection->validationErrors, 'data' => $this->Collection->data]));
            }
        } catch (ShopifyApiException $e) {
            CakeLog::error($e);
        }
    }

    private function _syncShopifyLocations($userId, array $inventoryItemIds, $apiKey, $secretKey, $shopUrl)
    {
        // The Locations API does not paginate so it may as well be processed as a whole set
        $locations = $this->Shopify->getAllLocations($apiKey, $secretKey, $shopUrl);
        if ($locations) {
            $this->_webhookServiceLog(compact('userId', 'locations'));
            if (!$this->Warehouse->syncAllShopifyLocations($userId, $locations)) {
                CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . json_encode(['errors' => $this->Warehouse->validationErrors, 'data' => $this->Warehouse->data]));
            }

            $locationIds = array_column($locations, 'id');
            $this->Shopify->syncInventoryLevels($inventoryItemIds, $locationIds, $apiKey, $secretKey, $shopUrl, function($inventoryLevel) use ($userId) {
                $this->_webhookServiceLog(compact('userId', 'inventoryLevel'));
                // This will only add/update records; we rely on the webhook to delete invalid ones
                if (!$this->ShopifyWebhookHandler->syncInventory($userId, $inventoryLevel['inventory_item_id'], $inventoryLevel['location_id'], $inventoryLevel)) {
                    CakeLog::error(json_encode(['errors' => $this->WarehouseProduct->validationErrors, 'data' => $this->WarehouseProduct->data]));
                }
            });
        }
    }

    private function _syncShopifyOrders($userId, $apiKey, $secretKey, $shopUrl)
    {
        $customersIds = array_unique(array_filter(
            $this->Shopify->syncOrders($apiKey, $secretKey, $shopUrl, function($order) use ($userId) {
                $this->ShopifyWebhookHandler->syncOrder($userId, $order);
                return Hash::get($order, 'customer.id');
            })
        ));
        // Cannot assume that customers in the Orders API are up to date so sync with the Customer API
        $this->_syncShopifyCustomersById($customersIds, $userId, $apiKey, $secretKey, $shopUrl);
    }

    private function _syncShopifyCustomersById(array $customerIds, $userId, $apiKey, $secretKey, $shopUrl): void
    {
        // Be cautious of how many characters are sent in the `?ids=207119551,1073339469` query string
        $customerIdChunks = array_chunk($customerIds, 100);

        // Optimize for sending fewer API requests
        $numberOfCustomers = $this->Shopify->countCustomers($apiKey, $secretKey, $shopUrl);
        $numberOfCustomerPages = (int)ceil((float)$numberOfCustomers / 250);
        $numberOfCustomerIdPages = count($customerIdChunks);
        $fetchedByIds = ($numberOfCustomerIdPages < $numberOfCustomerPages);
        CakeLog::debug(json_encode(compact('userId', 'numberOfCustomerPages', 'numberOfCustomerIdPages', 'fetchedByIds')), 'webhookService');

        if ($fetchedByIds) {
            foreach ($customerIdChunks as $ids) {
                $this->Shopify->syncCustomers($apiKey, $secretKey, $shopUrl, function($customer) use ($userId) {
                    $this->Customer->syncShopifyCustomer($userId, $customer);
                }, ['ids' => implode(',', $ids)]);
            }
        } else {
            $this->Shopify->syncCustomers($apiKey, $secretKey, $shopUrl, function($customer) use ($userId, $customerIds) {
                if (in_array($customer['id'], $customerIds)) {
                    $this->Customer->syncShopifyCustomer($userId, $customer);
                }
            });
        }
    }

    /**
     * Sub function for woocommerce data synchronization
     *
     * @param array $user
     */
    public function _woocommerceData($user)
    {
        $this->_syncWooCommerceProducts($user);
        $this->_syncWooCommerceOrders($user);
    }

    private function _syncWooCommerceProducts(array $user): array
    {
        $userId = (int)$user['User']['id'];

        return array_filter(
            $this->Woocommerce->syncProducts($user, function(stdClass $product) use ($userId) {
                try {
                    $success = $this->WoocommerceWebhookHandler->syncProduct($userId, (array)$product);
                } catch (Exception $e) {
                    CakeLog::error($e);
                    $success = false;
                }
                if (!$success) {
                    return null;
                }

                return [
                    'id' => $product->id,
                ];
            })
        );
    }

    private function _syncWooCommerceOrders($user): array
    {
        $userId = $user['User']['id'];

        return array_filter(
            $this->Woocommerce->syncOrders($user, function($order) use ($userId) {
                $this->WoocommerceWebhookHandler->syncOrder($userId, (array)$order);

                return [
                    'id' => ((array)$order)['id'],
                ];
            })
        );
    }

    /**
     * Update product, order, customer details on magento update
     */
    function update()
    {
        try {
            $user = $this->User->authenticateFromEcommerce($this->request, [
                'User.id',
                'User.email_address',
                'User.company_name',
                'User.user_type',
                'User.site_type',
                'User.shop_url',
            ]);
            $log = ['payload' => $this->request->data] + $user;
            CakeLog::debug('Webhook Received ' . json_encode($log), 'webhookService');

            $siteType = $user['User']['site_type'];
            if ($siteType === UserSiteType::MAGENTO) {
                $this->UpdateCron->queueEcommerceWebhook($user['User']['id'], $this->request->data['type'], $this->request->data['id']);
            } elseif ($siteType === UserSiteType::WOOCOMMERCE) {
                $this->WoocommerceWebhookListener->queueWebhookTask($user['User']['id'], $this->request);
            } else {
                throw new BadRequestException(json_encode(['message' => "User site_type '{$siteType}' is not supported"] + $log));
            }
        } catch (HttpException $e) {
            CakeLog::error($e, 'webhookService');
            $this->response->statusCode($e->getCode());
        }
        return $this->response;
    }

    /**
     * Receive Shopify webhooks.
     *
     * @link https://help.shopify.com/en/api/reference/events/webhook
     */
    public function shopifyUpdate()
    {
        try {
            $this->ShopifyWebhookListener->queueWebhookTask($this->request);
        } catch (HttpException $e) {
            CakeLog::error($e, 'webhookService');
            $this->response->statusCode($e->getCode());
        }
        return $this->response;
    }

    /**
     * Cron runs on every 1 min to check any data available in updateCron
     */
    function checkUpdatecron()
    {
        $crons = $this->Lock->applyGlobalLock($this->request->url, [$this->UpdateCron, 'dequeueEcommerceWebhook']);
        foreach ($crons as $value) {
            try {
                $userId = $value['UpdateCron']['user_id'];
                $user = $this->User->record($userId);
                if (empty($user)) {
                    throw new NotFoundException('No User where id=' . $userId);
                }

                $userLog = array_intersect_key($user['User'], array_flip(['email_address', 'company_name', 'site_type', 'shop_url']));
                CakeLog::info(json_encode(['message' => 'Cron webhook handler', 'UpdateCron' => $value['UpdateCron'], 'User' => $userLog]));

                switch ($user['User']['site_type']) {
                    case UserSiteType::MAGENTO:
                        try {
                            $proxy = new SoapClient(rtrim($user['User']['shop_url'], "/") . '/api/soap/?wsdl');
                            $session = $proxy->login($user['User']['api_key'], $user['User']['secret_key']);
                            if ($value['UpdateCron']['type'] == 'product') {
                                $this->_addMagentoProduct($proxy, $session, $value['UpdateCron']['unique_id'], $userId, $value['UpdateCron']['id']);
                            } elseif ($value['UpdateCron']['type'] == 'order') {
                                $this->_addMagentoOrder($proxy, $session, $value['UpdateCron']['unique_id'], $userId, 'update', $user['User'], $value['UpdateCron']['id']);
                            }
                        } catch (Exception $e) {
                            throw new Exception("update login: " . $e->faultstring, $e->getCode(), $e);
                        }
                        break;
                    case UserSiteType::SHIPEARLY:
                        // Do nothing
                        break;
                    case UserSiteType::SHOPIFY:
                        $this->ShopifyWebhookHandler->handleUpdateCron($user, $value);
                        break;
                    case UserSiteType::WOOCOMMERCE:
                        $this->WoocommerceWebhookHandler->handleUpdateCron($user, $value);
                        break;
                    default:
                        triggerWarning(json_encode(['message' => 'Unknown site_type', 'User' => $userLog]));
                }

                $this->UpdateCron->delete($value['UpdateCron']['id'], false);
            } catch (Exception $e) {
                CakeLog::error($e);
                $status = ($value['UpdateCron']['status'] !== UpdateCron::STATUS_ERROR) ? UpdateCron::STATUS_ERROR : UpdateCron::STATUS_CLOSED;
                $this->UpdateCron->setStatus((int)$value['UpdateCron']['id'], $status, $e->getMessage());
            }
        }
        return true;
    }

    /**
     * Place pre-order and collect payment for Magento orders.
     *
     * Deprecated POST data:
     * - eMail
     * - sessionId
     *
     * Deprecated errors:
     * - 'Not allowed' to indicate fraud
     *
     * @return string|CakeResponse
     */
    public function shipearlyPay()
    {
        $this->autoRender = false;
        $users = $this->User->authenticateFromEcommerce($this->request);
        $error = '';

        if (!empty($users)) {
            $logs = json_decode($this->request->data('logdata'), true);

            $preorder = $this->Preorder->createForEcommerce(
                PreorderType::MAN_ORDER,
                $users['User']['id'],
                $this->request->data('retailer_id'),
                null,
                '',
                $this->request->data('currency_code'),
                $this->request->data('total_price'),
                $this->request->data('total_price'),
                (array)$this->Product->find('list', [
                    'recursive' => -1,
                    'conditions' => [
                        'Product.user_id' => $users['User']['id'],
                        'Product.productID' => array_column($logs['itemDetails'], 'product_id'),
                    ],
                    'fields' => ['id', 'id'],
                ]),
                (array)$logs
            );

            $orderSubType = array_values($logs['log']);
            $orderSubType = json_decode($orderSubType[0]['store_info'], true);
            if($orderSubType['type']  == 'nonstock') {
                $amount['orderNonstock'] = 'nonstock';
            }
            $amount['amt'] = round($preorder['totalprice'], 2);
            $amount['currencycode'] = $preorder['currency_code'];
            $amount['shippingamt'] = $logs['shippingAmount'];
            $amount['taxamt'] = round($logs['tax'], 2);
            $amount['itemamt'] = round(($amount['amt'] - ($amount['shippingamt'] + $amount['taxamt'])), 2);
            $amount['revenue_model'] = $users['User']['revenue_model'];
            $amount['retailer_default_amount'] = $users['User']['retailer_default_amount'];
            $amount['retailer_revenue_maximum'] = $users['User']['retailer_revenue_maximum'];

            $lastid = $preorder['id'];
            $paymentdata = json_decode($this->request->data('paymentdata'), true);

            $url = rtrim($users['User']['shop_url'], "/") . "/checkout/cart/";

            try {
                $stripe_account = $this->StripeUser->getAccountId($preorder['retailer_id']);
                if ($stripe_account) {
                    $payment = $this->Stripe->chargeCreditCard($stripe_account, $paymentdata, $amount, $lastid);
                    if (isset($payment->paid) && $payment->paid == true && !empty($payment->id)) {
                        $paymentDetails = $this->Stripe->getPaymentMethodDetailsFromCharge($payment, $stripe_account);

                        $this->Preorder->markPaymentSuccess($lastid, [
                            'paykey' => $payment->id,
                            'card_type' => $paymentDetails->card_type,
                            'last_four_digit' => $paymentDetails->last_four_digit,
                            'fraud_check_address' => $paymentDetails->fraud_check_address,
                            'fraud_check_postal_code' => $paymentDetails->fraud_check_postal_code,
                            'fraud_check_cvc' => $paymentDetails->fraud_check_cvc,
                        ]);
                    } else {
                        $this->Preorder->markPaymentError($lastid);
                        return json_encode(array('redirectUrl' => $url, 'error' => "Payment error. Try again"));
                    }
                } else {
                    $this->Preorder->markPaymentError($lastid);
                    return json_encode(array('redirectUrl' => $url, 'error' => "Payment error. Try again"));
                }
            } catch (Exception $e) {
                if ($e instanceof \Stripe\Exception\ApiErrorException) {
                    CakeLog::error('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()));
                }
                CakeLog::error($e);
                $this->Preorder->markPaymentError($lastid);
                return json_encode(array('redirectUrl' => $url, 'error' => $e->getMessage()));
            }

            try {
                $Preorder = $this->Preorder->findForPaymentSuccess($lastid);
                $url = $this->_paymentSuccess($users, $Preorder, $payment->id);
            } catch (Exception $e) {
                $this->Preorder->markOrderError($lastid);
                $error = 'Internal Error. Please try again';
            }
            return new CakeResponse(array('body' => json_encode(array('redirectUrl' => $url, 'error' => $error))));
        }
        return json_encode(array());
    }

    /**
     * Log payment success on pre-order table and return orderId
     * @param $user
     * @param $Preorder
     * @param $TransactionID
     * @return string
     */
    public function _paymentSuccess($user, $Preorder, $TransactionID)
    {
        $orderId = json_decode($this->_directorder($Preorder['Preorder'], $user['User'], $TransactionID), true); //$paydetails['PaymentInfo']['TransactionID']
        $this->Preorder->markSuccess($Preorder['Preorder']['id']);
        $url = rtrim($user['User']['shop_url'], "/") . "/checkout/onepage/shipearlyOrder/id/" . $orderId['orderId'] . "/";
        if ($orderId['code']) {
            $url .= 'code/' . $orderId['code'];
        }
        return $url;
    }

    /**
     * sub function to place magento order
     * @param $preorder
     * @param $user
     * @param $transactionID
     * @return string
     */
    function _directorder($preorder, $user, $transactionID)
    {
        $logs = json_decode($preorder['log'], true);
        $shipping = $logs['shipping'];
        $products = array_values($logs['log']);
        $products = json_decode($products[0]['store_info'], true);
        $retailer_id = array_values($logs['log']);
        $retailer_id = $retailer_id[0]['retailer_id'];
        $type = '';
        $c_retailer = array();
        $code = '';

        $cusId = $this->_addCustomerById($logs, $user, $shipping);

        $orderinfo['user_id'] = $user['id'];
        $orderinfo['customerID'] = $cusId;
        $orderinfo['preOrderId'] = $preorder['id'];
        $orderinfo['orderID'] = '';
        $orderinfo['orderNO'] = '';
        $orderinfo['retailer_id'] = $retailer_id;
        $orderinfo['subType'] = $products['type'];
        $billing = $logs['billing'];
        $orderinfo['tax_included'] = $logs['tax_included'];

        if ($shipping['shipping_method'] == 'shipearly_shipearly') {
            $type = Order::TYPE_IN_STORE_PICKUP;
            if ($orderinfo['subType'] == 'stock') {
                $orderinfo['order_status'] = Order::STATUS_NOT_PICKED_UP;
            } elseif ($orderinfo['subType'] == 'nonstock') {
                $orderinfo['order_status'] = Order::STATUS_NEED_TO_CONFIRM;
            }
            $code = $this->OrderLogic->generateCode(); //$shipping['email'], $name, $c_retailer['User']
            $orderinfo['secretcode'] = $code;
        } else if ($shipping['shipping_method'] == 'shipfromstore_shipfromstore') {
            $type = Order::TYPE_SHIP_FROM_STORE;
            $orderinfo['order_status'] = Order::STATUS_OPEN;
        }

        $c_retailer = $this->User->findForEcommerceNotifications($retailer_id);

        $orderinfo['order_type'] = $type;
        $orderinfo['created_at'] = $preorder['created'];
        $orderinfo['total_price'] = $preorder['totalprice'];

        if ($preorder['currency_code'] == $c_retailer['User']['currency_code']) {
            $retailerCurrency = 1;
        } else {
            $retailerCurrency = $this->currencyConversion(1, $preorder['currency_code'], $c_retailer['User']['currency_code']);
        }
        $orderinfo['totalPriceConversion'] = $retailerCurrency * $orderinfo['total_price'];

        $orderinfo['currency_code'] = $preorder['currency_code'];
        $orderinfo['billing_firstname'] = $shipping['firstname'];
        $orderinfo['billing_lastname'] = $shipping['lastname'];
        if (isset($shipping['street']) && !empty($shipping['street'])) {
            $tempAddress = explode('\n', $shipping['street']);
            $shipping['address1'] = $shipping['address2'] = '';
            if (isset($tempAddress[0])) {
                $shipping['address1'] = $tempAddress[0];
            }
            if (isset($tempAddress[1])) {
                $shipping['address2'] = $tempAddress[1];
            }
        }
        $orderinfo['shipping_address1'] = $shipping['address1'];
        $orderinfo['shipping_address2'] = $shipping['address2'];
        $orderinfo['shipping_city'] = $shipping['city'];
        $orderinfo['shipping_state'] = $shipping['region'];
        $orderinfo['shipping_country'] = $shipping['country_id'];
        $orderinfo['shipping_zipcode'] = $shipping['postcode'];
        $orderinfo['shipping_amount'] = $logs['shippingAmount'];
        $orderinfo['transactionID'] = $transactionID;

        if ($shipping['shipping_method'] == 'shipearly_shipearly' || $shipping['shipping_method'] == 'shipfromstore_shipfromstore') {
            $orderinfo['shipearlyFees'] = $this->Currency->formatAsDecimal(
                $this->OrderLogic->CalculateFees($preorder['totalpricediscount'], 'retailer', $user['revenue_model'], $user['retailer_default_amount'], $user['retailer_revenue_maximum']),
                $orderinfo['currency_code']
            );
            $orderinfo['retailerAmount'] = $this->Currency->formatAsDecimal($orderinfo['total_price'] - $orderinfo['shipearlyFees'], $orderinfo['currency_code']);
        }

        $order['customer_email'] = $shipping['email'];
        $order['customer_firstname'] = $shipping['firstname'];
        $order['customer_lastname'] = $shipping['lastname'];
        $order['shipping_address']['street1'] = $shipping['address1'];
        $order['shipping_address']['street2'] = $shipping['address2'];
        $order['shipping_address']['city'] = $shipping['city'];
        $order['shipping_address']['state'] = $shipping['region'];
        $order['shipping_address']['country'] = $shipping['country_id'];
        $order['shipping_address']['zipcode'] = $shipping['postcode'];
        $order['shipping_telephone'] = $shipping['telephone'];
        $order['order_type'] = $type;
        $shipping_method_ship_subtype = isset($shipping['shipping_method_ship_subtype']) ? $shipping['shipping_method_ship_subtype'] : ""; 

        $orderinfo['shipping_telephone'] = $shipping['telephone'];
        $orderinfo['shipping_statecode'] = '';
        $orderinfo['shipping_countrycode'] = $shipping['country_id'];
        $orderinfo['customerEmail'] = $order['customer_email'];
        //$orderinfo['total_qty_ordered'] = (int)$order['items_qty'];
        //$orderinfo['total_tax'] = $logs['tax'];
        $orderinfo['card_type'] = $preorder['card_type'];
        $orderinfo['last_four_digit'] = $preorder['last_four_digit'];
        $orderinfo['fraud_check_cvc'] = $preorder['fraud_check_cvc'];
        $orderinfo['fraud_check_address'] = $preorder['fraud_check_address'];
        $orderinfo['fraud_check_postal_code'] = $preorder['fraud_check_postal_code'];
        $orderinfo['total_tax'] = $logs['tax'];
        if ($orderinfo['subType'] == 'nonstock') {
            $orderinfo['payment_status'] = (int)OrderPaymentStatus::AUTHORIZED;
        }
        $this->Order->create();
        if (!$this->Order->save($orderinfo)) {
            throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
        }
        $inc_oid = $this->Order->getLastInsertID();
        $newOrder = $this->Order->get($inc_oid);
        $orderID = $newOrder['Order']['orderID'];

        foreach ($logs['itemDetails'] as $pro) {
            $temp = $products['product'][$pro['product_id']];
            $pro_ids = $this->Product->find('first', array('fields' => array('id'), 'conditions' => array('user_id' => $user['id'], 'productID' => $pro['product_id'])));
            $this->OrderProduct->create();
            $this->OrderProduct->save(array('order_id' => $inc_oid, 'product_id' => $pro_ids['Product']['id'], 'quantity' => $pro['qty'], 'total_tax' => $temp['tax'], 'total_price' => $temp['unformatedprice'], 'totalPriceConversion' => $retailerCurrency * $temp['unformatedprice']));
        }

        $order = array_merge($newOrder['Order'], $order);
        $name = $billing['firstname'] . " " . $billing['lastname'];
        $this->NotificationLogic->handleNotification($shipping['shipping_method'], $inc_oid, $c_retailer, $user, $order, $shipping_method_ship_subtype);

        $this->OrderLogic->updateChargeForNewOrder((int)$inc_oid);

        if ($shipping['shipping_method'] == 'shipfromstore_shipfromstore') {
            $avataxShipping = array(
                'address' => $shipping['address1'],
                'address2' => $shipping['address2'],
                'city' => $shipping['city'],
                'regionName' => $shipping['region'],
                'PostalCode' => $shipping['postcode'],
                'countryName' => $shipping['country_id'],
            );
            $this->Avatax->setTotalTaxFromEcommerce(
                $preorder['currency_code'], $retailer_id, $avataxShipping, $orderID, $logs, $products['product']
            );
        }

        $logs['taxamt'] = $logs['tax'];
        $logs['product'] = $products['product'];
        $this->OrderPlacer->createPosOrder($retailer_id, $newOrder['Order'], $logs, $shipping, $billing, $shipping['shipping_method']);

        return json_encode(array('id' => $inc_oid, 'orderId' => $this->formatOrderId($inc_oid, false), 'code' => $code));
    }

    /**
     * @param array $logs
     * @param array $user
     * @param array $shipping
     * @return int|null
     */
    protected function _addCustomerById($logs, $user, $shipping): ?int
    {
        $userId = (int)$user['id'];
        $shipping = (array)$shipping;

        $cusId = null;
        if (!empty($logs['customerID'])) {
            try {
                $proxy = new SoapClient(rtrim($user['shop_url'], "/") . '/api/soap/?wsdl');
                $session = $proxy->login($user['api_key'], $user['secret_key']);

                $cusId = $this->_addMagentoCustomer($proxy, $session, $logs['customerID'], $userId);
            } catch (Exception $e) {
                CakeLog::warning($e);
            }
        }
        if (empty($cusId)) {
            $cusId = $this->_addCustomerByEmail($userId, $shipping);
        }

        return $cusId;
    }

    protected function _addCustomerByEmail(int $userId, array $shipping): ?int
    {
        return $this->Customer->addEcommerceCustomerByEmail($userId, [
            'First_name' => $shipping['firstname'],
            'Last_name' => $shipping['lastname'],
            'email' => $shipping['email'],
            'company' => '',
            'address' => $shipping['street'],
            'address2' => '',
            'city' => $shipping['city'],
            'regionName' => $shipping['region'],
            'regionCode' => '',
            'countryName' => $shipping['country_id'],
            'countryCode' => '',
            'PostalCode' => $shipping['postcode'],
            'phone' => $shipping['telephone'],
        ]);
    }

    /**
     * sub function to send ship from store notification
     * @param $inc_oid
     * @param $retailer
     * @param $user
     * @param $order
     * @param string $orderID
     */
    function _sendShipfromstoreNotification($inc_oid, $retailer, $user, $order, $orderID = '')
    {
        $notification_msg = "A customer has placed an order to be shipped from store";

        $variables = array();
        $variables['{site_name}'] = SITE_NAME;
        $variables['{order_id}'] = $this->makeOrderLink($orderID, $inc_oid);
        $variables['{customer_email}'] = $order['customer_email'];
        $variables['{order_type}'] = "Ship from Store";
        $variables['{customer_name}'] = $order['customer_firstname'] . " " . $order['customer_lastname'];
        $variables['{customer_address}'] = $this->emailAddressDisplayFormat(' ', $order['shipping_address']['street'], ' ', $order['shipping_address']['city'], ' ', ' ', ' ');
        // $variables['{customer_address}'] = $order['shipping_address']['street'] . ", <br/>" . $order['shipping_address']['city'];
        $variables['{customer_phone}'] = $order['shipping_telephone'];

        /** retailer **/
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Shipfromstore Retailer');
        $variables['{user_name}'] = $retailer['company_name'];
        $this->sendEmail($emailTemplateArr, $variables, trim($retailer['email_address']), '', '', array(get_order_invoice_pdf_filepath($inc_oid)));
        $this->Notification->createNotification($user['id'], $user['id'], Notification::TYPE_SHIP_FROM_STORE_ORDER, $inc_oid, $notification_msg);

        /** manufacturer **/
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Instore Manufacturer');
        $variables['{user_name}'] = $user['company_name'];
        $variables['{retailer_name}'] = "<a href='" . BASE_PATH . "contact/" . $retailer['id'] . "'>" . $retailer['company_name'] . "</a>";
        $this->sendEmail($emailTemplateArr, $variables, trim($user['email_address']), '', '', array(get_order_invoice_pdf_filepath($inc_oid)));
        $this->Notification->createNotification($user['id'], $retailer['id'], Notification::TYPE_SHIP_FROM_STORE_ORDER, $inc_oid, $notification_msg);
    }

    /**
     * @deprecated Since 2.22. Sift Science is no longer supported.
     */
    public function getSiftKey()
    {
        deprecationWarning(sprintf(
            '2.22 %s is deprecated. Sift Science is no longer supported. %s',
            $this->request->here(),
            json_encode(['app_username' => $this->request->data('app_username')])
        ), 0);

        return $this->response;
    }

    /**
     * @deprecated Since 2.26. Ecommerce plugins should use an equivalent endpoint belonging to their controller.
     */
    public function getGoogleApiKey()
    {
        deprecationWarning(sprintf(
            '2.26 %s is deprecated. Ecommerce plugins should use an equivalent endpoint belonging to their controller. %s',
            $this->request->here(),
            json_encode(['app_username' => $this->request->data('app_username')])
        ), 0);

        try {
            $this->User->authenticateFromEcommerce($this->request, ['User.id']);
            $this->response->body(GOOGLE_GEOCODING_API_KEY);
        } catch (HttpException $e) {
            CakeLog::error($e);
            $this->response->statusCode($e->getCode());
        }

        return $this->response;
    }

    public function verifyStoreAssociatePin()
    {
        $this->autoRender = false;
        $associate = array();
        if (!empty($this->request->data['store_associate_pin'])) {
            $associate = $this->User->findStoreAssociateByPin($this->request->data['store_associate_pin']);
        }
        $this->response->body(json_encode($associate));
    }

    private function filterRetailersByCommissionTier(array $storeConnections, array $productIds): array
    {
        $commissionConnections = array_filter($storeConnections, function($connection){
            return $connection['is_commission_tier'] && $connection['pricingtierid'];
        });
        if (!$commissionConnections) {
            return [];
        }

        $pricingTierIds = array_keys(array_flip(array_map(fn($connection) => $connection['pricingtierid'], $commissionConnections)));

        /** @var ProductTier $ProductTier */
        $ProductTier = ClassRegistry::init('ProductTier');

        $productTierCommission = $ProductTier->find('list', [
            'conditions' => [
                'ProductTier.pricingtierid' => $pricingTierIds,
                'ProductTier.product_id' => $productIds,
            ],
            'fields' => ['product_id', 'commission', 'pricingtierid'],
        ]);
        $commissionConnections = array_filter($commissionConnections, function($connection) use ($productTierCommission){
            $pricingTierId = $connection['pricingtierid'];
            $commissions = (array)($productTierCommission[$pricingTierId] ?? []);
            foreach ($commissions as $commission) {
                if ($commission > 0) {
                    return true;
                }
            }

            return false;
        });

        return array_map(fn($connection) => $connection['retailer_id'], $commissionConnections);
    }

    /**
     * return json response with retailer ids based on given shipping address and radius
     * @return string
     */
    public function getRetailerIds()
    {
        $start_time_function = microtime(true);
        $start_time = $start_time_function;

        $this->response->body(json_encode(array()));

        try {
            $brandUser = $this->User->authenticateFromEcommerce($this->request);
        } catch (UnauthorizedException $e) {
            CakeLog::error(strval($e));
            return $this->response;
        }
        if (isset($this->request->data['token'])) {
            $this->request->data = $this->_formatGetRetailerInputs($this->request->data);
        }

        $this->_webServiceLog('Authenticated ' . json_encode($brandUser));

        $brandId = (int)$brandUser['User']['id'];

        $cartItemByVariantId = Hash::combine((array)json_decode($this->request->data['items'], true), '{*}.product_id', '{*}');

        $this->request->data['ids'] = json_decode($this->request->data['ids'], true);
        $this->_webServiceLog(['productIDs' => $this->request->data['ids']]);
        $this->request->data['Qty'] = json_decode($this->request->data('Qty'), true);
        $this->_webServiceLog(['productQty' => $this->request->data['Qty']]);

        $discountRetailerIds = is_array($this->request->data('discountRetailerIds')) ? (array)$this->request->data['discountRetailerIds'] : null;
        $requestedRetailerId = $this->request->data('requestedRetailerId');
        $requestedDeliveryOption = DeliveryOptions::denormalize($this->request->data('requestedDeliveryOption'));

        $start_time = $this->_logDeltaTime($start_time, 'Authenticated');

        $productsInfo = $this->Product->findAllForEcommerceGetRetailerIds($brandId, $cartItemByVariantId);
        $productsInfo = array_map(function($product) {
            if ($this->request->data('force_sell_direct_exclusive')) {
                $product['Product']['sell_direct'] = ProductSellDirect::EXCLUSIVELY;
            }
            return $product;
        }, $productsInfo);
        $this->_webServiceLog(compact('productsInfo'));

        $productIds = Hash::extract($productsInfo, '{n}.Product.id');

        if (count($this->_getUniqueUpcProducts($productsInfo)) !== count($productIds)) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . 'Duplicate UPCs not allowed in cart');
            return $this->response;
        }
        $start_time = $this->_logDeltaTime($start_time, 'Fetch productsInfo');

        $geopoints = json_decode($this->request->data('geopoints'), true);
        if (isset($this->request->data['token'])) {
            $tsdata = json_decode($this->request->data('address'), true);
            $geopoints = $geopoints ?: $this->_getLnt($tsdata['address'], $tsdata['city'], $tsdata['PostalCode'], $tsdata['regionName'], $tsdata['countryName'], self::GEOPOINT_LEVEL_MIDDLE);
        } else {
            $tsdata = json_decode($this->request->data('code'), true);
            $geopoints = $geopoints ?: $this->_getLnt($tsdata['street'], $tsdata['city'], $tsdata['postcode'], $tsdata['region'], $tsdata['country_id'], self::GEOPOINT_LEVEL_MIDDLE);
        }
        $this->_webServiceLog(compact('tsdata', 'geopoints'));
        if (empty($geopoints)) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . " - Geopoints not found for " . json_encode($tsdata));
            return $this->response;
        }
        $start_time = $this->_logDeltaTime($start_time, 'Customer geopoints');

        $retailerIds = $this->ProductRetailer->findAvailableRetailerIds($brandId, $productIds, $requestedRetailerId ?? $discountRetailerIds);
        $this->_webServiceLog("Retailer ids associated with this product " . json_encode(array_values($retailerIds)));
        $start_time = $this->_logDeltaTime($start_time, 'Fetching ProductRetailers');

        $associate = array();
        if (!empty($this->request->data['store_associate_pin'])) {
            $associate = $this->User->findStoreAssociateByPin($this->request->data['store_associate_pin'], ['Branch']);
            $retailer_ids = $this->User->listAllRelatedStoreIds($associate['User']['Branch']);
            $this->_webServiceLog("Associate retailer ids " . json_encode($retailer_ids));

            $retailerIds = array_intersect($retailerIds, $retailer_ids);
            $this->_webServiceLog("Retailer ids for associate " . json_encode(array_values($retailerIds)));
            $start_time = $this->_logDeltaTime($start_time, 'Associate retailer filter');
        }

        $storeConnections = $this->ManufacturerRetailer->findStoreConnections($brandId, $retailerIds, [
            'id',
            'retailer_id',
            'pricingtierid',
            'is_commission_tier',
            'dealer_protect_radius',
            'ship_from_store_unprotected_zones_only',
            'enable_install',
            'enable_strict_tag_matching',
        ]);
        $storeConnections = $this->Tag->filterAssignedStoreConnections($storeConnections, $productIds);
        $retailerIds = array_intersect_key($retailerIds, $storeConnections);

        $nonCommissionIds = array_filter($retailerIds, fn($retailerId) => !$storeConnections[$retailerId]['is_commission_tier']);
        $nonCommissionIds = $this->StripeUser->filterConnectedUserIds($nonCommissionIds);
        $inStockRetailerIds = $this->Store->filterInStockOnlyStoreIds($nonCommissionIds, $productsInfo);

        $commissionIds = (!$this->request->data('non_commission'))
            ? $this->filterRetailersByCommissionTier($storeConnections, $productIds)
            : [];
        $retailerIds = $nonCommissionIds + $commissionIds;
        CakeLog::debug('Retailer ids with charges enabled ' . json_encode(array_values($retailerIds)));
        $start_time = $this->_logDeltaTime($start_time, 'Filter retailer ids with charges enabled');

        $sellDirectOption = ProductSellDirect::getHighestPriorityOption(Hash::extract($productsInfo, '{n}.Product.sell_direct'));

        $anyProductDisabledStorePickup = array_has_any($productsInfo, function($product) {
            return !$product['Product']['store_pickup_option'];
        });
        $anyProductHasLocalDelivery = array_has_any($productsInfo, function($product) {
            return $product['Product']['assembly_option'];
        });
        $anyProductHasInstallation = array_has_any($productsInfo, function($product) {
            return $product['Product']['installation_hours'] > 0;
        });
        $anyRetailerHasInstallation = array_has_any($retailerIds, function($retailerId) use ($storeConnections) {
            return $storeConnections[$retailerId]['enable_install'];
        });
        $anyProductHasShipFromStore = array_has_any($productsInfo, function($product) {
            return $product['Product']['ship_from_store'];
        });

        $isSellDirectEnabled = ($brandUser['User']['sell_direct'] && $brandUser['User']['admin_sell_direct'] && empty($associate));

        $start_time = $this->_logDeltaTime($start_time, 'Process productOptions');

        $shipfromstore_instock_only = $brandUser['User']['shipfromstore_instock_only'];
        $retailers = [];

        if (!empty($retailerIds)) {
            // Closure to simplify calling $this->_getRetailerIdsByRadius()
            $countryCode = (string)(isset($this->request->data['token']) ? $tsdata['countryCode'] : $tsdata['country_id']);
            if (is_numeric($countryCode)) {
                $countryCode = $this->Country->getCountryCode($countryCode);
            }
            $postalCode = (string)(isset($this->request->data['token']) ? $tsdata['PostalCode'] : $tsdata['postcode']);
            $getRetailerIdsByRadius = function(array $retailerIds, string $shippingMethod, int $limit) use ($brandId, $geopoints, $countryCode, $postalCode, $associate) {
                return $this->_getRetailerIdsByRadius($brandId, $retailerIds, $countryCode, $postalCode, (array)$geopoints, $shippingMethod, $limit, (bool)$associate);
            };

            $shippingMethods = array_keys([
                DeliveryOptions::IN_STORE => ($brandUser['User']['instore'] && !$anyProductDisabledStorePickup),
                DeliveryOptions::LOCAL_INSTALL => ($anyProductHasInstallation && $anyRetailerHasInstallation && $brandUser['User']['enable_install']),
                DeliveryOptions::LOCAL_DELIVERY => ($brandUser['User']['local_delivery'] && $anyProductHasLocalDelivery),
                DeliveryOptions::SHIP_FROM_STORE => (($brandUser['User']['shipment'] || !empty($associate)) && $anyProductHasShipFromStore),
            ], true, true);
            $shippingMethods = array_combine($shippingMethods, $shippingMethods);
            if ($requestedDeliveryOption !== null) {
                $shippingMethods = array_intersect_key($shippingMethods, array_flip([$requestedDeliveryOption]));
            }

            $productsRequiringWarehouseInventory = array_filter($productsInfo, function(array $product): bool {
                return !$product['Product']['enable_oversell'] && !$product['Product']['is_fee_product'];
            });
            if ($productsRequiringWarehouseInventory) {
                $soldOutRetailerIds = $this->User->listSoldOutRetailerIds($brandId, $productsRequiringWarehouseInventory, $inStockRetailerIds, array_map(
                    fn(array $connection): array => ['ManufacturerRetailer' => $connection],
                    array_intersect_key($storeConnections, $retailerIds)
                ));

                $retailerIds = array_diff_key($retailerIds, $soldOutRetailerIds);
                $inStockRetailerIds = array_diff_key($inStockRetailerIds, $soldOutRetailerIds);
            }

            $retailerIds = $this->Store->filterInStockOnlyStoreIds($retailerIds, array_filter($productsInfo, function(array $product): bool {
                return (bool)$product['Product']['is_in_stock_only'];
            }));

            $retailers = array_map(function($method) use (
                $retailerIds,
                $inStockRetailerIds,
                $shipfromstore_instock_only,
                $getRetailerIdsByRadius
            ) {
                $limit = 5;
                if ($method === DeliveryOptions::SHIP_FROM_STORE && $shipfromstore_instock_only) {
                    $retailerIds = $inStockRetailerIds;
                    $limit = 1;
                }

                return $getRetailerIdsByRadius($retailerIds, $method, $limit);
            }, $shippingMethods);
            CakeLog::debug('Retailers by radius ' . json_encode($retailers));
            $start_time = $this->_logDeltaTime($start_time, 'Retailers by radius');

            // Save retailers viewed before they are filtered by sell direct method
            // Only save views for ecommerce plugins that provide and expect an ecommerceViewId field
            if (array_key_exists('ecommerceViewId', $this->request->data)) {
                try {
                    $retailers['ecommerceViewId'] = $this->EcommerceView->saveView($brandId, $productIds, $retailers, $this->request->data['ecommerceViewId']);
                    if (!$retailers['ecommerceViewId']) {
                        throw new InternalErrorException(json_encode(['errors' => $this->EcommerceView->validationErrors, 'data' => $this->EcommerceView->data]));
                    }
                } catch (Exception $e) {
                    CakeLog::warning($e);
                }
            }
            $start_time = $this->_logDeltaTime($start_time, 'EcommerceView::save');
        } else {
            $this->_webServiceLog("No Retailers");
        }

        $inStoreRetailers = array_intersect_key($retailers, array_flip(DeliveryOptions::getInStoreOptions()));
        $hasProtectedRetailer = $this->ManufacturerRetailer->hasProtectedRetailer($inStoreRetailers, $storeConnections);
        $retailers = $this->_sellDirectCheck($brandId, $sellDirectOption, $hasProtectedRetailer, $retailers);
        $retailers = $this->_ShipFromStoreCheck($storeConnections, $hasProtectedRetailer, $retailers);
        if (
            !$isSellDirectEnabled
            || (!empty($retailers['shipfromstore']) && $shipfromstore_instock_only)
            || ($requestedDeliveryOption !== null && $requestedDeliveryOption !== DeliveryOptions::SELL_DIRECT)
            || $requestedRetailerId !== null
            ) {
            unset($retailers['selldirect']);
        }

        $retailers['map'] = $this->UserSetting->field('map', ['user_id' => $brandId]) ? 1 : 0;
        $retailers['geopoints'] = array_map(function($lnt) { return format_number($lnt, 6); }, $geopoints);
        $start_time = $this->_logDeltaTime($start_time, 'sellDirectCheck');

        $isVelofixEnabled = (
            $brandUser['User']['site_type'] === UserSiteType::SHOPIFY &&
            $brandUser['User']['enable_velofix'] &&
            isset($retailers['local_delivery']) &&
            empty($retailers['local_delivery']) &&
            !empty($retailers['selldirect']) &&
            empty($associate)
        );

        if ($isVelofixEnabled) {
            $retailers['enable_velofix'] = $this->Velofix->isAddressCovered($tsdata);
            $start_time = $this->_logDeltaTime($start_time, 'Velofix::isAddressCovered');
        }

        $messages = $this->Page->getOrderResultPages($brandUser['User']['email_address']);
        $messages['return-policy'] = nl2br($this->UserSetting->getReturnPolicy($brandId, $brandUser['User']['email_address']));
        $messages['shipping_infographic'] = $brandUser['User']['shipping_infographic'];

        $retailers['messages'] = json_encode($messages);
        
        $start_time = $this->_logDeltaTime($start_time, 'getOrderResultPages');

        $this->_webServiceLog("Response " . json_encode(compact('retailers')));
        $this->_logDeltaTime($start_time_function, __METHOD__);
        $this->response->body(json_encode($retailers));
        return $this->response;
    }

    public function brandPolicies()
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['User.id', 'User.email_address']);

        $policies = $this->UserSetting->listPolicies((int)$user['User']['id'], (string)$user['User']['email_address']);
        if (!$policies) {
            throw new NotFoundException(json_encode(['UserSetting' => ['user_id' => $user['User']['id']]]));
        }

        $this->_webServiceLog(compact('policies'));
        $this->response->body(json_encode($policies));
        return $this->response;
    }

    public function paymentPageCustomContent()
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['id', 'email_address']);

        $content = $this->User->findEcommerceCustomContent($user['User']['id'], $user['User']['email_address']);

        $this->_webServiceLog(compact('content'));
        $this->response->body(json_encode($content));
        return $this->response;
    }

    /**
     * Derives fields from Shopify inputs to match WooCommerce inputs.
     *
     * @param array $data
     * @return array
     */
    public function _formatGetRetailerInputs($data): array
    {
        $items = json_decode($data['items'], true);
        $address = json_decode($data['address'], true);
        $tsdata = [
            'street' => $address['address'],
            'city' => $address['city'],
            'postcode' => $address['PostalCode'],
            'region' => $address['regionName'],
            'country_id' => $address['countryName'],
        ];
        $itemDetails = array_map(
            function($item) {
                return [
                    'product_id' => $item['variant_id'],
                    'qty' => $item['quantity'],
                    'price' => format_number($item['price'] / 100),
                    'compare_at_price' => isset($item['compare_at_price']) ? format_number($item['compare_at_price'] / 100) : null,
                ];
            },
            array_combine(array_column($items, 'key'), $items)
        );

        return array_merge($data, [
            'ids' => json_encode(array_column($itemDetails, 'product_id')),
            'Qty' => json_encode(array_column($itemDetails, 'qty', 'product_id')),
            'items' => json_encode($itemDetails),
            'shipping' => json_encode($tsdata),
            'code' => json_encode($tsdata),
            'customerID' => $data['customer'],
        ]);
    }

    /**
     * Filter retailer ids by distance relative to the customer.
     * Also returns their distance from the customer.
     *
     * @param int $brandId
     * @param int[] $retailerIds
     * @param string $countryCode 2-letter country code
     * @param string $postalCode
     * @param float[] $customerGeopoints
     * @param string $shippingMethod
     * @param int $limit
     * @param bool $isAssociate
     * @return array Retailer ids and distance from customer
     */
    public function _getRetailerIdsByRadius(int $brandId, array $retailerIds, string $countryCode, string $postalCode, array $customerGeopoints, string $shippingMethod, int $limit, bool $isAssociate): array
    {
        $retailers = $this->User->findRetailersByRadius($brandId, $retailerIds, $countryCode, $postalCode, $customerGeopoints, $shippingMethod, $limit, $isAssociate);

        return array_map(function($retailer) {
            return [
                'u' => [
                    'retailer_id' => $retailer['User']['id'],
                ],
                0 => [
                    'distance' => format_number($retailer['User']['distance']),
                ],
            ];
        }, $retailers);
    }

    /**
     * Check the product availability on the inventory system using API
     * and return price, Tax information
     * @return string
     */
    public function checkProduct()
    {
        $this->autoRender = false;

        try {
            $users = $this->User->authenticateFromEcommerce($this->request);
        } catch (UnauthorizedException $e) {
            CakeLog::error(strval($e));
            $users = null;
        }
        if (isset($this->request->data['token'])) {
            $this->request->data = $this->_formatGetRetailerInputs($this->request->data);
        }

        $this->_webServiceLog('Authenticated ' . json_encode($users));
        if (!empty($users)) {
            $brandId = $users['User']['id'];

            // Multi-currency overrides
            $currencyCode = $this->request->data('currency_code') ?: $users['User']['currency_code'];
            $this->request->data['currency_code'] = $currencyCode;
            $users['User']['currency_code'] = $currencyCode;
            $cartItemByVariantId = Hash::combine((array)json_decode($this->request->data['items'], true), '{*}.product_id', '{*}');

            $this->request->data['Qty'] = json_decode($this->request->data['Qty'], true);
            $this->_webServiceLog("request data " . json_encode($this->request->data));

            $geopoints = $this->request->data['geopoints'];
            if (!is_object($geopoints)) {
                $geopoints = json_decode($geopoints);
            }

            $products_info = $this->_getUniqueUpcProducts(
                $this->Product->findAllForEcommerceCheckProduct($brandId, $cartItemByVariantId, (bool)$this->request->data('buyDirectOnly'))
            );

            $nonFeeProducts = array_filter($products_info, function($product) {
                return !$product['Product']['is_fee_product'];
            });

            $item_discounts = isset($this->request->data['discounts']) ? (array)json_decode($this->request->data['discounts'], true) : [];
            CakeLog::debug(json_encode(compact('item_discounts')));

            $retailers_inv = $retailers_price = $retailers_currency = $retailers_tax = $retailers_price_discount = $retailers_tax_discount = array();

            if (isset($this->request->data['brand'])) {
                $this->_searchEcommerceSoftware($products_info, $users, $this->request->data, $item_discounts, $retailers_inv, $retailers_price, $retailers_tax, $retailers_currency, $retailers_price_discount, $retailers_tax_discount);

                return $this->_formatBrandOutput($users, $products_info, $retailers_inv, $retailers_price, $retailers_currency, $retailers_tax, $retailers_price_discount, $retailers_tax_discount);
            } else {
                $retailerList = array();

                $retailerId = (int)$this->request->data['retailers'];
                $retailers_info = [$this->User->findRetailerForSearchInventorySoftware($retailerId)];
                if ($this->request->data('bypass_pos')) {
                    $retailers_info = array_map(function($value) {
                        if($value['User']['inventory_type'] !== 'lightspeed_cloud'){
                            $value['User']['otherInventory'] = $value['User']['inventory_type'];
                            $value['User']['inventory_type'] = 'other';
                        }

                        return $value;
                    }, $retailers_info);
                }
                CakeLog::debug(json_encode(compact('retailers_info')));

                $retailers[$retailerId] = $this->User->setRetailerData($retailerId, (float)$geopoints->lat, (float)$geopoints->lng);
                CakeLog::debug(json_encode(compact('retailers')));

                // Check retailer inventory system
                $uniqueUpcs = Hash::combine($nonFeeProducts, '{n}.Product.product_upc', '{n}.Product.product_upc');
                foreach ($retailers_info as $value) {
                    $retailer_id = $value['User']['id'];

                    $isVelofix = (in_array($this->request->data['type'], ['instore', 'local_delivery'], true) && $retailer_id == $brandId);
                    if ($isVelofix) {
                        $this->request->data['type'] = 'local_delivery';
                    }
                    $retailers[$retailer_id]['u']['velofix'] = $isVelofix;

                    $connection = $this->ManufacturerRetailer->getStoreConnection($brandId, $retailer_id, [
                        'id',
                        'is_commission_tier',
                        'is_ship_to_store_only',
                    ]);
                    $isCommissionRetailer = (bool)($connection['is_commission_tier'] ?? false);
                    $isShipToStoreOnly = (bool)($connection['is_ship_to_store_only'] ?? false);

                    $inventoryRetailerConditions = [
                        'isInstoreRequest' => in_array($this->request->data['type'], ['instore', 'local_delivery'], true),
                        'allProductsHaveUpcs' => !array_key_exists('', $uniqueUpcs),
                        'notCommission' => !$isCommissionRetailer,
                        'hasInventoryApi' => (
                            $value['User']['inventory_type'] === 'other' || (
                                $value['User']['inventory_password'] &&
                                ($value['User']['inventory_apiuser'] || $value['User']['inventory_type'] === 'shopify_pos') &&
                                ($value['User']['vend_refresh_access_token'] || !in_array($value['User']['inventory_type'], ['lightspeed_cloud', 'vend_pos'], true))
                            )
                        ),
                        'notVelofix' => !$isVelofix,
                    ];
                    $hasNonStockingProduct = $isShipToStoreOnly || array_has_any($nonFeeProducts, fn($product) => $product['Product']['non_stocking']);
                    $isInventoryRetailer = !in_array(false, $inventoryRetailerConditions, true);
                    CakeLog::debug(json_encode(compact('isInventoryRetailer', 'inventoryRetailerConditions', 'hasNonStockingProduct')));

                    if ($isInventoryRetailer) {
                        $response = $this->_searchInventorySoftware(
                            $nonFeeProducts,
                            $value,
                            $this->request->data,
                            $item_discounts,
                            (bool)$this->UserSetting->fieldByConditions('msrp', ['UserSetting.user_id' => $brandId]),
                            $retailers_inv,
                            $retailers_price,
                            $retailers_tax,
                            $retailers_currency,
                            $retailers_price_discount,
                            $retailers_tax_discount
                        );

                        $retailers[$retailer_id]['u']['type'] = ($response && !$hasNonStockingProduct) ? 'stock' : 'nonstock';
                    } else {
                        if ($isCommissionRetailer) {
                            $response = $this->_searchEcommerceSoftware($products_info, $users, $this->request->data, $item_discounts, $retailers_inv, $retailers_price, $retailers_tax, $retailers_currency, $retailers_price_discount, $retailers_tax_discount);
                            if ($response) {
                                // Assign items from the brand's platform to the commission retailer
                                $retailers_inv[$retailer_id] = $retailers_inv[$brandId];
                            }
                        } elseif ($isVelofix) {
                            try {
                                $velofix_franchise = $this->Velofix->getCoveringFranchise(json_decode($this->request->data['address'], true));
                            } catch (Exception $e) {
                                CakeLog::error($e);
                                $velofix_franchise = [];
                            }
                            CakeLog::debug(json_encode(compact('velofix_franchise')));
                            if (!$velofix_franchise) {
                                continue;
                            }

                            $velofix_user = $this->_formatVelofixBrand($users, $velofix_franchise);

                            $item_discounts = array_map(function($value) use ($retailer_id) {
                                $value['retailers'][] = $retailer_id;
                                return $value;
                            }, $item_discounts);
                            $this->_searchEcommerceSoftware($products_info, $velofix_user, $this->request->data, $item_discounts, $retailers_inv, $retailers_price, $retailers_tax, $retailers_currency, $retailers_price_discount, $retailers_tax_discount);

                            $local_delivery_shipping = $this->User->calcVelofixShipping($velofix_user, $nonFeeProducts);
                            $this->request->data['shippingAmount'] = $local_delivery_shipping;

                            // Save brand output for payment processing
                            $velofix_selldirect_result = json_decode($this->_formatBrandOutput($velofix_user, $products_info, $retailers_inv, $retailers_price, $retailers_currency, $retailers_tax, $retailers_price_discount, $retailers_tax_discount), true);
                            $velofix_selldirect_result['velofix_franchise'] = $velofix_franchise;

                            // Format brand as a retailer for the shipping method display
                            $velofix_user['User']['defaultTax'] = format_number(($velofix_selldirect_result['taxamtwithShippingDiscount'] - $velofix_selldirect_result['shippingTax']) / $velofix_selldirect_result['totalproductamountdiscount'] * 100, 6);
                            $retailers[$retailer_id]['u'] = array_merge($retailers[$retailer_id]['u'], [
                                'company_name' => $velofix_user['User']['company_name'],
                                'defaultTax' => $velofix_user['User']['defaultTax'],
                                'shiptostore_tax' => $this->_brandShippingTaxOption($brandId, $users['User']['site_type']),
                                'velofix_selldirect_result' => json_encode($velofix_selldirect_result),
                            ]);
                            $value['User'] = array_intersect_key($velofix_user['User'], $value['User']);
                        }

                        $retailers[$retailer_id]['u']['type'] = 'nonstock';
                    }

                    $local_delivery_shipping = ($isVelofix)
                        ? ($local_delivery_shipping ?? $this->User->calcVelofixShipping($users, $nonFeeProducts))
                        : $this->User->calcLocalDeliveryShipping($users, $nonFeeProducts, $retailer_id, $retailers_inv[$retailer_id] ?? []);
                    $retailers[$retailer_id]['u']['local_delivery_shipping'] = format_number($local_delivery_shipping);

                    $retailerList[$retailers[$retailer_id]['u']['retailer_id']] = $retailers[$retailer_id];
                    $retailerList[$retailers[$retailer_id]['u']['retailer_id']]['defaultTax'] = $value['User']['defaultTax'];
                }

                if (!empty($retailerList)) {
                    return $this->_formatRetailerOutput($retailerList, $products_info, $retailers_inv, $retailers_price, $retailers_currency, $retailers_tax, $users, $this->request->data['type'], $item_discounts, $retailers_price_discount, $retailers_tax_discount);
                }
            }

        }
    }

    private function _formatVelofixBrand(array $value, array $franchise): array
    {
        // Convert USA to US and CAN to CA; only needs to work for those countries
        $countryCode = substr((string)$franchise['contact']['address']['country'], 0, 2);
        $stateCode = (string)$franchise['contact']['address']['state'];
        $state = $this->State->findByCountryCodeAndStateCode($countryCode, $stateCode, ['State.id', 'State.country_id']);

        $value['User'] = array_merge($value['User'], [
            'company_name' => "Velofix ({$franchise['name']})",
            'address' => $franchise['contact']['address']['line1'],
            'address1' => $franchise['contact']['address']['line1'],
            'address2' => '',
            'city' => $franchise['contact']['address']['city'],
            'zipcode' => $franchise['contact']['address']['zip'],
            'state_id' => $state['State']['id'],
            'country_id' => $state['State']['country_id'],
        ]);

        return $value;
    }

    /**
     * Check the sell exclusive product availability on the ecommerce system using API
     * and return price, Tax information
     * @return string
     */
    public function checkSellExclusiveProduct()
    {
        $this->autoRender = false;

        try {
            $users = $this->User->authenticateFromEcommerce($this->request);
        } catch (UnauthorizedException $e) {
            CakeLog::error(strval($e));
            return $this->response;
        }
        $itemsForDiscountCalc = json_decode($this->request->data('items'), true);
        if (isset($this->request->data['token'])) {
            $this->request->data = $this->_formatGetRetailerInputs($this->request->data);
        }

        $this->_webServiceLog('Authenticated ' . json_encode($users));

        $brandId = $users['User']['id'];
        $this->request->data['brand'] = $brandId;

        // Multi-currency overrides
        $currencyCode = $this->request->data('currency_code') ?: $users['User']['currency_code'];
        $this->request->data['currency_code'] = $currencyCode;
        $users['User']['currency_code'] = $currencyCode;
        $cartItemByVariantId = Hash::combine((array)json_decode($this->request->data['items'], true), '{*}.product_id', '{*}');

        $this->request->data['Qty'] = json_decode($this->request->data['Qty'], true);
        $this->request->data['ids'] = json_decode($this->request->data['ids'], true);
        $this->_webServiceLog("request data " . json_encode($this->request->data));

        $item_discounts = json_decode($this->request->data['discounts'], true);

        $geopoints = $this->request->data['geopoints'];
        if (!is_object($geopoints)) {
            $geopoints = json_decode($geopoints);
        }

        $products_info = $this->_getUniqueUpcProducts(
            $this->Product->findAllForEcommerceCheckSellExclusiveProduct($brandId, $cartItemByVariantId)
        );
        if (empty($products_info)) {
            return $this->response;
        }

        $users['User']['buyDirectOnly'] = (count($products_info) === count($this->request->data['ids'])) ? 1 : 0;

        $retailers_inv = $retailers_price = $retailers_currency = $retailers_tax = $retailers_price_discount = $retailers_tax_discount = array();
        $this->_searchEcommerceSoftware($products_info, $users, $this->request->data, $item_discounts, $retailers_inv, $retailers_price, $retailers_tax, $retailers_currency, $retailers_price_discount, $retailers_tax_discount);

        return $this->_formatBrandOutput($users, $products_info, $retailers_inv, $retailers_price, $retailers_currency, $retailers_tax, $retailers_price_discount, $retailers_tax_discount);
    }

    public function _searchInventorySoftware(
        array $products_info,
        array $value,
        array $requestData,
        array $discounts,
        bool $msrpOption,
        array &$retailers_inv,
        array &$retailers_price,
        array &$retailers_tax,
        array &$retailers_currency,
        array &$retailers_price_discount,
        array &$retailers_tax_discount
    ): bool
    {
        $retailerId = (int)$value['User']['id'];
        $inventoryType = (string)$value['User']['inventory_type'];

        $discounts = array_filter($discounts, fn(array $discount): bool => in_array($retailerId, $discount['retailers']));

        CakeLog::debug(json_encode(['message' => 'Args for PosList function'] + compact('products_info', 'value', 'requestData', 'discounts', 'msrpOption')));

        try {
            if ($inventoryType === 'lightspeed_cloud') {
                $posList = $this->_lightspeedPosList($products_info, $value, $requestData);
            } elseif ($inventoryType === 'shopify_pos') {
                $posList = $this->_shopifyPosList($products_info, $value, $requestData);
            } elseif ($inventoryType === 'quickbook_pos') {
                $posList = $this->_quickbookPosList($products_info, $value, $requestData);
            } elseif ($inventoryType === 'vend_pos') {
                $posList = $this->_vendPosList($products_info, $value, $requestData);
            } elseif ($inventoryType === 'square') {
                $posList = $this->_squarePosList($products_info, $value, $requestData);
            } else {
                $posList = $this->_otherPosList($products_info, $value, $requestData);
            }
        } catch (Exception $e) {
            CakeLog::error($e);
            $posList = [];
        }

        $response = $this->_deriveRetailersInvFromPosList(
            $posList,
            $products_info,
            $retailerId,
            $inventoryType,
            (array)$requestData['Qty'],
            (string)$requestData['currency_code'],
            $discounts,
            $msrpOption,
            $retailers_inv
        );

        if (!empty($retailers_inv[$retailerId])) {
            $retailers_price[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'unformatedprice'));
            $retailers_tax[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'tax'));
            $retailers_currency[$retailerId] = (string)current(array_column($retailers_inv[$retailerId], 'currency'));
            $retailers_price_discount[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'unformatedpricediscount'));
            $retailers_tax_discount[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'taxdiscount'));

            // Maintaining SHIP-2990 (#1206) behaviour of only updating Lightspeed inventory records for now.
            if ($inventoryType === 'lightspeed_cloud') {
                $this->Store->updateFromRetailersInv($retailers_inv);
            }
        }

        CakeLog::debug(json_encode(['message' => 'Output of PosList function'] + compact('response', 'retailers_inv', 'retailers_price', 'retailers_tax', 'retailers_currency', 'retailers_price_discount', 'retailers_tax_discount')));

        return $response;
    }

    public function _lightspeedPosList(array $products_info, array $value, array $requestData): array
    {
        $retailerId = (int)$value['User']['id'];
        $currencyCode = (string)$value['User']['currency_code'];

        $accessToken = $value['User']['inventory_apiuser'];
        $accountId = $value['User']['inventory_password'];
        $refreshToken = $value['User']['vend_refresh_access_token'];
        $tokenExpiresAt = $value['User']['vend_access_token_expires'];
        $shopID = $value['User']['Inventory_Store_ID'];

        $refreshed = [];

        $itemsByUpc = [];
        $allProductUpcs = Hash::extract($products_info, '{n}.Product.product_upc');
        foreach (array_chunk($allProductUpcs, 100) as $productUpcs) {
            $items = $this->Lightspeed->getAllProductInventoriesByUpc($accessToken, $accountId, $productUpcs, $refreshToken, $tokenExpiresAt);
            $refreshed = $this->Lightspeed->getNewAccessTokenResponse($accountId, $refreshed);
            if (!empty($refreshed['access_token'])) {
                $accessToken = $refreshed['access_token'];
                $tokenExpiresAt = $refreshed['expires_at'];
            }
            if ($items && $items->count()) {
                foreach ($items->children() as $item) {
                    $itemsByUpc[(string)$item->upc] = $item;
                }
            }
        }
        if (!$itemsByUpc) {
            CakeLog::debug(json_encode(['message' => 'No inventory items found'] + compact('retailerId', 'allProductUpcs')));

            return [];
        }

        $shop = $this->Lightspeed->getShop($accessToken, $accountId, $shopID, $refreshToken, $tokenExpiresAt);
        $refreshed = $this->Lightspeed->getNewAccessTokenResponse($accountId, $refreshed);
        if (!empty($refreshed['access_token'])) {
            $accessToken = $refreshed['access_token'];
            $tokenExpiresAt = $refreshed['expires_at'];
        }

        $taxCategoryID = (string)$shop->taxCategoryID;
        $priceLevelName = (string)$shop->PriceLevel->name;

        $taxCategory = null;
        if ($taxCategoryID != '0') {
            $taxCategory = $this->Lightspeed->getTaxCategory($accessToken, $accountId, $taxCategoryID, $refreshToken, $tokenExpiresAt);
            $refreshed = $this->Lightspeed->getNewAccessTokenResponse($accountId, $refreshed);
        }

        try {
            if (!$this->User->refreshLightspeedAccessToken($retailerId, $refreshed)) {
                throw new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data]));
            }
        } catch (Exception $e) {
            CakeLog::warning($e);
            // Do not interrupt the inventory check
        }

        return array_map(function(array $product) use ($currencyCode, $itemsByUpc, $shopID, $priceLevelName, $taxCategoryID, $taxCategory): ?array {
            $item = $itemsByUpc[$product['product_upc']] ?? null;
            if (!$item) {
                CakeLog::debug(json_encode(['message' => 'Item not found for product', 'Product' => $product]));

                return null;
            }

            $itemShop = null;
            foreach ($item->ItemShops->children() as $_itemShop) {
                if ((string)$_itemShop->shopID == $shopID) {
                    $itemShop = $_itemShop;
                    break;
                }
            }

            $itemPrice = null;
            foreach ($item->Prices->children() as $_itemPrice) {
                $useType = (string)$_itemPrice->useType;
                if ($useType === 'Default') {
                    $itemPrice = $_itemPrice;
                }
                if ($useType === $priceLevelName) {
                    $itemPrice = $_itemPrice;
                    break;
                }
            }

            if (!$itemShop || !$itemPrice) {
                CakeLog::warning(json_encode(['message' => 'ItemShop not found for product', 'Product' => $product, 'Item' => $item]));

                return null;
            }

            $currency = (string)$itemPrice->amount->attributes()->currency;
            if ($currency !== $currencyCode) {
                throw new InternalErrorException(json_encode(['message' => 'Currency mismatch', 'retailer' => $currencyCode, 'lightspeed' => $currency]));
            }

            $taxClassID = (string)$item->taxClassID;

            $taxName = 'None';
            $tax1Rate = '0';
            $tax2Rate = '0';
            if ($taxCategory) {
                $taxName = implode(' - ', array_filter([(string)$taxCategory->tax1Name, (string)$taxCategory->tax2Name]));
                $tax1Rate = (string)$taxCategory->tax1Rate;
                $tax2Rate = (string)$taxCategory->tax2Rate;
                if (isset($taxCategory->TaxCategoryClasses)) {
                    foreach ($taxCategory->TaxCategoryClasses->children() as $taxCategoryClass) {
                        if ((string)$taxCategoryClass->taxClassID == $taxClassID) {
                            $taxName = (string)$taxCategoryClass->TaxClass->name;
                            $tax1Rate = (string)$taxCategoryClass->tax1Rate;
                            $tax2Rate = (string)$taxCategoryClass->tax2Rate;
                            break;
                        }
                    }
                }
            }

            return [
                'inventoryId' => (string)$itemShop->itemID,
                'inventory' => (int)$itemShop->qoh,
                'currency' => $currency,
                'unformatedunitprice' => (float)$itemPrice->amount,
                'isTax' => ((string)$item->tax) === 'true',
                'tax_included' => ((string)($taxCategory->isTaxInclusive ?? 'false')) === 'true',
                'tax_name' => $taxName,
                'tax_rate' => format_number((float)$tax1Rate + (float)$tax2Rate, 9),
                'TaxCategoryID' => $taxCategoryID,
                'taxClassID' => $taxClassID,
            ];
        }, Hash::combine($products_info, '{n}.Product.productID', '{n}.Product'));
    }

    public function _vendPosList(array $products_info, array $value, array $requestData): array
    {
        $currencyCode = (string)$value['User']['currency_code'];

        $apikey = (string)$value['User']['inventory_apiuser'];
        $domainPrefix = isset($value['User']['inventory_password']) ? (string)$value['User']['inventory_password'] : null;
        $outletId = $value['User']['Inventory_Store_ID'];

        return array_map(function(array $product) use ($apikey, $domainPrefix, $outletId, $currencyCode): ?array {
            $productInfo = $this->VendPOS->getProductBySku($apikey, $domainPrefix, $product['product_upc']);
            if (empty($productInfo->products)) {
                CakeLog::debug(json_encode(['message' => 'Item not found for product', 'Product' => $product]));

                return null;
            }

            $inventory = 0;
            foreach ($productInfo->products as $product) {
                if (!empty($product->inventory)) {
                    foreach ($product->inventory as $inventorycount) {
                        if ($inventorycount->outlet_id == $outletId) {
                            $inventory = (int)$inventorycount->count;
                            // Prefer the $product where inventory was found
                            break 2;
                        }
                    }
                    // Use the last $product if inventory was not found
                }
            }

            return [
                'inventoryId' => (string)$product->id,
                'inventory' => $inventory,
                'currency' => $currencyCode,
                'unformatedunitprice' => (float)$product->price,

                // This might be wrong but has been carried over from the original implementation
                'isTax' => $product->display_retail_price_tax_inclusive,
                'tax_included' => false,

                'tax_name' => $product->tax_name,
                // TODO The fallback calculation can be removed if we are certain that `tax_rate` is part of the API spec
                'tax_rate' => $product->tax_rate ?? format_number($product->price > 0.00 ? $product->tax / $product->price : 0.0, 9),

                'tax_id' => $product->tax_id,
            ];
        }, Hash::combine($products_info, '{n}.Product.productID', '{n}.Product'));
    }

    public function _shopifyPosList(array $products_info, array $value, array $requestData): array
    {
        $retailerId = (int)$value['User']['id'];
        $currencyCode = (string)$value['User']['currency_code'];

        $currency = (string)$this->ShopifyPOS->getCurrency($retailerId);
        if ($currency !== $currencyCode) {
            throw new InternalErrorException(json_encode(['message' => 'Currency mismatch', 'retailer' => $currencyCode, 'shopify_pos' => $currency]));
        }

        $posVariantIdByProductId = array_map(fn($store) => $store['inventoryVariantId'], $this->Store->findInventoryIdsByProductId($retailerId));
        $posVariantByProductId = [];
        foreach ($products_info as $val1) {
            $productId = (int)$val1['Product']['id'];
            $posVariantId = $posVariantIdByProductId[$productId] ?? null;
            if ($posVariantId) {
                $posVariantByProductId[$productId] = $this->ShopifyPOS->getVariant($value['User']['inventory_password'], $value['User']['shop_url'], $posVariantId, [
                    'fields' => implode(',', ['id', 'inventory_quantity', 'price', 'taxable']),
                ]);
            }
        }
        if (!$posVariantByProductId) {
            CakeLog::debug(json_encode(['message' => 'No inventory items found'] + compact('retailerId', 'posVariantIdByProductId')));

            return [];
        }

        $isCustomerDeliveryMethod = !in_array($requestData['type'], ['instore', 'local_install'], true);
        $retailerCountryId = (int)$value['User']['country_id'];
        $destinationCountryId = $retailerCountryId;
        if ($isCustomerDeliveryMethod) {
            $shipping = (array)json_decode($requestData['address'], true);
            $destinationCountryId = (int)$shipping['country'];
        }

        $taxName = (string)$value['User']['defaultTaxName'] ?: null;
        $taxRate = (float)$value['User']['defaultTax'] / 100;
        $taxIncluded = ($destinationCountryId === $retailerCountryId)
            ? $this->ShopifyPOS->checkIncludeTax($retailerId)
            : $this->UserCountryTax->isIncludedInPrices($retailerId, $destinationCountryId);

        return array_map(function(array $product) use ($currency, $taxIncluded, $taxName, $taxRate, $posVariantByProductId): ?array {
            $posVariant = $posVariantByProductId[$product['id']] ?? null;
            if (!$posVariant) {
                CakeLog::debug(json_encode(['message' => 'Item not found for product', 'Product' => $product]));

                return null;
            }

            return [
                'inventoryId' => (string)$posVariant['id'],
                'inventory' => (int)$posVariant['inventory_quantity'],
                'currency' => $currency,
                'unformatedunitprice' => (float)$posVariant['price'],
                'isTax' => (bool)$posVariant['taxable'],
                'tax_included' => $taxIncluded,
                'tax_name' => $taxName,
                'tax_rate' => format_number($taxRate, 9),
            ];
        }, Hash::combine($products_info, '{n}.Product.productID', '{n}.Product'));
    }

    public function _squarePosList(array $products_info, array $value, array $requestData): array
    {
        $retailerId = (int)$value['User']['id'];
        $currencyCode = (string)$value['User']['currency_code'];

        $locationId = (string)$value['User']['inventory_apiuser'];
        $accessToken = (string)$value['User']['inventory_password'];

        $productInventoryIds = $this->Store->findInventoryIdsByProductId($retailerId);
        $inventoriesByVariantId = $this->SquarePos->listInventoriesByVariantId($accessToken, $locationId);

        return array_map(function(array $product) use ($accessToken, $currencyCode, $productInventoryIds, $inventoriesByVariantId): ?array {
            $posIds = (array)($productInventoryIds[$product['id']] ?? []);
            if (!$posIds) {
                CakeLog::debug(json_encode(['message' => 'Product not mapped to a POS item', 'Product' => $product]));

                return null;
            }
            $itemId = (string)$posIds['itemId'];
            $variantId = (string)$posIds['inventoryVariantId'];

            $inventoryItem = $this->SquarePos->findItem($accessToken, $itemId);
            if (!$inventoryItem) {
                CakeLog::debug(json_encode(['message' => 'Item not found for product', 'Product' => $product]));

                return null;
            }
            $priceMoney = (array)$inventoryItem['variations'][$variantId]['price_money'];

            $currency = (string)$priceMoney['currency'];
            if ($currency !== $currencyCode) {
                throw new InternalErrorException(json_encode(['message' => 'Currency mismatch', 'retailer' => $currencyCode, 'square_pos' => $currency]));
            }

            /**
             * @todo SquarePosComponent obfuscates inclusive tax in a way will need to be revised.
             * @see SquarePosComponent::_formatItem()
             */
            $taxIncluded = false;
            $taxRate = $inventoryItem['total_tax_rate'];

            return [
                'inventoryId' => $itemId,
                'inventory' => (int)$inventoriesByVariantId[$variantId],
                'currency' => $currency,
                'unformatedunitprice' => (float)$priceMoney['amount'],
                'isTax' => ($taxRate > 0.0),
                'tax_included' => $taxIncluded,
                'tax_rate' => $taxRate,
                'inventoryVariantId' => $variantId,
            ];
        }, Hash::combine($products_info, '{n}.Product.productID', '{n}.Product'));
    }

    public function _quickbookPosList($products_info, $value, $requestData): array
    {
        $retailerId = (int)$value['User']['id'];
        $currencyCode = (string)$value['User']['currency_code'];

        return array_map(function(array $product) use ($retailerId, $currencyCode): ?array {
            $productInfo = $this->Quickbook->getProductInfo($product['product_upc'], $retailerId);
            if (!$productInfo) {
                CakeLog::debug(json_encode(['message' => 'Item not found for product', 'Product' => $product]));

                return null;
            }

            return [
                'inventoryId' => (string)$productInfo['QuickbookProduct']['listId'],
                'inventory' => (int)$productInfo['QuickbookProduct']['qty'],
                'currency' => $currencyCode,
                'unformatedunitprice' => (float)$productInfo['QuickbookProduct']['price'],
                'isTax' => true,
                'POSTaxCode' => $productInfo['QuickbookProduct']['tax'],
            ];
        }, Hash::combine($products_info, '{n}.Product.productID', '{n}.Product'));
    }

    public function _otherPosList(array $products_info, array $value, array $requestData): array
    {
        $retailerId = (int)$value['User']['id'];
        $currencyCode = (string)$value['User']['currency_code'];

        $inventoryConditions = [
            'storeId' => $retailerId,
            'productId' => array_column(array_column($products_info, 'Product'), 'id'),
        ];
        $productInventories = Hash::combine((array)$this->Store->find('all', [
            'recursive' => -1,
            'conditions' => $inventoryConditions,
            'fields' => ['id', 'itemId', 'productId', 'inventoryCount'],
        ]), '{n}.Store.productId', '{n}.Store');
        if (!$productInventories) {
            CakeLog::debug(json_encode(['message' => 'No inventory items found'] + $inventoryConditions));

            return [];
        }

        $isCustomerDeliveryMethod = !in_array($requestData['type'], ['instore', 'local_install'], true);
        $destinationCountryId = (int)$value['User']['country_id'];
        if ($isCustomerDeliveryMethod) {
            $shipping = (array)json_decode($requestData['address'], true);
            $destinationCountryId = (int)$shipping['country'];
        }

        $taxName = (string)$value['User']['defaultTaxName'] ?: null;
        $taxRate = (float)$value['User']['defaultTax'] / 100;
        $taxIncluded = $this->UserCountryTax->isIncludedInPrices($retailerId, $destinationCountryId);

        return array_map(function(array $product) use ($retailerId, $currencyCode, $taxIncluded, $taxName, $taxRate, $productInventories): ?array {
            $posItem = $productInventories[$product['id']] ?? null;
            if (!$posItem) {
                CakeLog::debug(json_encode(['message' => 'Item not found for product', 'Product' => $product]));

                return null;
            }

            return [
                'inventoryId' => (string)$posItem['itemId'],
                'inventory' => (int)$posItem['inventoryCount'],
                'currency' => $currencyCode,
                'unformatedunitprice' => (float)$product['product_price'],
                'isTax' => true,
                'tax_included' => $taxIncluded,
                'tax_name' => $taxName,
                'tax_rate' => format_number($taxRate, 9),
            ];
        }, Hash::combine($products_info, '{n}.Product.productID', '{n}.Product'));
    }

    protected function _deriveRetailersInvFromPosList(
        array $posList,
        array $products_info,
        int $retailerId,
        string $inventoryType,
        array $quantityByProductID,
        string $msrpCurrency,
        array $discounts,
        bool $msrpOption,
        array &$retailers_inv
    ): bool
    {
        $response = true;
        foreach ($products_info as $val1) {
            $productID = $val1['Product']['productID'];

            $posProductInventory = $posList[$productID] ?? null;
            if (!$posProductInventory) {
                $response = false;
                continue;
            }

            $quantity = (int)$quantityByProductID[$productID];
            $currency = ($msrpOption)
                ? $msrpCurrency
                : (string)$posProductInventory['currency'];
            $unitPrice = ($msrpOption)
                ? (float)$val1['Product']['product_price']
                : (float)$posProductInventory['unformatedunitprice'];
            $price = $unitPrice * $quantity;

            $discount = $this->_getItemDiscount($discounts, $productID, $retailerId);
            $item_discount_amount = (float)$discount['discount_amount'];
            $item_unit_discount_amount = $item_discount_amount / $quantity;

            $unitPriceDiscounted = $unitPrice - $item_unit_discount_amount;
            $priceDiscounted = $price - $item_discount_amount;

            $isTax = (bool)$posProductInventory['isTax'];

            $tax = 0.0;
            $taxDiscounted = 0.0;
            if ($isTax) {
                if ($inventoryType !== 'quickbook_pos') {
                    $taxRate = (float)$posProductInventory['tax_rate'];
                    $taxIncluded = (bool)$posProductInventory['tax_included'];
                    $tax = calculate_tax_amount($price, $taxRate, $taxIncluded);
                    $taxDiscounted = calculate_tax_amount($priceDiscounted, $taxRate, $taxIncluded);
                } else {
                    $taxCode = (string)$posProductInventory['POSTaxCode'];
                    $tax = $this->Quickbook->calculateTax($unitPrice, $quantity, $taxCode, $retailerId);
                    $taxDiscounted = $this->Quickbook->calculateTax($unitPriceDiscounted, $quantity, $taxCode, $retailerId);
                }
            }

            $productInventory = [
                'id' => $val1['Product']['id'],
                'inventoryId' => $posProductInventory['inventoryId'],
                'inventory' => $posProductInventory['inventory'],
                'taxcode' => $val1['Product']['taxcode'],
                'currency' => $currency,
                'qty' => $quantity,
                'unformatedunitprice' => $this->Currency->formatAsDecimal($unitPrice, $currency),
                'unformatedprice' => $this->Currency->formatAsDecimal($price, $currency),
                'unitprice' => $this->Currency->formatCurrency($unitPrice, $currency),
                'price' => $this->Currency->formatCurrency($price, $currency),
                'unformatedunitpricediscount' => $this->Currency->formatAsDecimal($unitPriceDiscounted, $currency),
                'unformatedpricediscount' => $this->Currency->formatAsDecimal($priceDiscounted, $currency),
                'unitpricediscount' => $this->Currency->formatCurrency($unitPriceDiscounted, $currency),
                'pricediscount' => $this->Currency->formatCurrency($priceDiscounted, $currency),
                'isTax' => $isTax,
                'tax' => $this->Currency->formatAsDecimal($tax, $currency),
                'taxdiscount' => $this->Currency->formatAsDecimal($taxDiscounted, $currency),
            ];
            $retailers_inv[$retailerId][$productID] = $productInventory + $posProductInventory;

            if ($productInventory['inventory'] < $productInventory['qty']) {
                CakeLog::debug(json_encode(['message' => 'Quantity not available for product', 'inventory' => $productInventory['inventory'], 'quantity' => $productInventory['qty']] + $val1));
                $response = false;
            }
        }

        return $response;
    }

    private function _formatRetailerOutput(
        array $retailerList,
        array $products_info,
        array $retailers_inv,
        array $retailers_price,
        array $retailers_currency,
        array $retailers_tax,
        array $users,
        string $shippingType,
        array $discounts,
        array $retailers_price_discount,
        array $retailers_tax_discount
    ): string
    {
        /** Flag to designate output that differs between In-Store related delivery methods and Ship from Store. */
        $_formatInStoreRetailerOutput = in_array($shippingType, ['instore', 'local_delivery', 'local_install'], true);

        CakeLog::debug(json_encode(['message' => 'Args'] + compact('retailerList', 'products_info', 'retailers_inv', 'retailers_price', 'retailers_currency', 'retailers_tax', 'users', 'shippingType', 'discounts', 'retailers_price_discount', 'retailers_tax_discount')));

        $brandId = (int)$users['User']['id'];
        $discount_retailers = (array)($this->request->data('discountRetailerIds') ?: $this->_flatten(array_column($discounts, 'retailers')));

        $response = [];
        foreach ($retailerList as $value) {
            $retailerId = (int)$value['u']['retailer_id'];

            $retailerIsDiscounted = in_array($retailerId, $discount_retailers);
            $retailer_discounts = ($retailerIsDiscounted) ? $discounts : [];
            $shipping = (array)json_decode($this->request->data['address'], true);
            $defaultTaxRate = format_number($value['u']['defaultTax'] / 100, 8);

            $isCustomerDeliveryMethod = !in_array($shippingType, ['instore', 'local_install'], true);

            $connection = $this->ManufacturerRetailer->getStoreConnection($brandId, $retailerId, [
                'id',
                'is_commission_tier',
                'commission_uses_retailer_tax_rate',
            ]);
            $isCommissionRetailer = (bool)($connection['is_commission_tier'] ?? false);
            $commissionUsesRetailerTaxRate = (bool)($connection['commission_uses_retailer_tax_rate'] ?? false);
            // Assert this condition over something like `$taxUserId === $brandId` because of the Velofix case where `$retailerId === $brandId`.
            $taxUserIsBrand = ($isCommissionRetailer && !$commissionUsesRetailerTaxRate);

            $taxDestinationAddress = null; // No tax lookup; use retailer's local defaultTaxRate
            if ($isCustomerDeliveryMethod) {
                $taxDestinationAddress = $shipping;
            } elseif ($taxUserIsBrand) {
                $taxDestinationAddress = $this->TaxCalculator->getRetailerShippingAddress($retailerId);

                $value['u']['shiptostore_tax'] = $this->_brandShippingTaxOption($brandId, $users['User']['site_type']);
            }
            if ($taxDestinationAddress !== null) {
                $userTax = $this->TaxCalculator->findRatesForLocation($taxUserIsBrand ? $brandId : $retailerId, $taxDestinationAddress);
                $defaultTaxRate = $userTax['tax_rate'];
                foreach ($products_info as $val1) {
                    $productID = $val1['Product']['productID'];
                    $retailers_inv[$retailerId][$productID] = array_merge($retailers_inv[$retailerId][$productID] ?? [], $userTax);
                }
            }

            if ($isCustomerDeliveryMethod || $value['u']['type'] == 'nonstock') {
                $retailers_inv = $this->_applyShippingZoneTaxOverridesToRetailersInv($brandId, $retailerId, $shipping, $products_info, $retailers_inv);
            }

            $destinationCountryId = (int)($isCustomerDeliveryMethod ? $shipping['country'] : $value['u']['country_id']);
            $taxIncluded = $this->_extractTaxIncluded($retailerId, $destinationCountryId, $retailers_inv);
            foreach ($products_info as $val1) {
                $productID = $val1['Product']['productID'];
                $retailers_inv[$retailerId][$productID] = (array)($retailers_inv[$retailerId][$productID] ?? []) + ['tax_included' => $taxIncluded];
            }

            $this->formatRetailerInventory($products_info, $defaultTaxRate, $value, $retailers_inv, $retailers_price, $retailers_currency, $retailers_tax, $users, $retailer_discounts, $retailers_price_discount, $retailers_tax_discount);

            $currency = (string)$retailers_currency[$retailerId];

            $this->_applyFeeToPosRetailer(
                $brandId,
                $retailerId,
                $shipping['country'],
                $shipping['province'],
                $currency,
                $defaultTaxRate,
                $retailers_inv,
                $retailers_price,
                $retailers_tax,
                $retailers_price_discount,
                $retailers_tax_discount
            );

            if ($_formatInStoreRetailerOutput) {
                $retailers_inv = $this->_assignLocalDeliveryShippingToRetailersInv($brandId, $retailerId, $value, $products_info, $retailers_inv);
            }
            if ($shippingType === 'shipFromStore' && $users['User']['shipfromstore_instock_only']) {
                $value['u']['type'] = 'stock';
            }

            $price = (float)$retailers_price[$retailerId];

            $shippingAmount = (float)$this->request->data('shippingAmount');
            if ($_formatInStoreRetailerOutput) {
                if ($value['u']['type'] !== 'nonstock' || $price > $users['User']['shiptostore_free_shipping']) {
                    $shippingAmount = 0.00;
                }
            } else {
                if (!empty($value['u']['free_shipping']) && $value['u']['free_shipping'] <= $price) {
                    $shippingAmount = 0.00;
                } elseif (!empty($value['u']['shipment_option']) && $value['u']['shipment_type'] == 1) {
                    $shippingAmount = (float)$value['u']['shipment_option'];
                }
            }

            //TODO SHIP-2672 Apply free shipping discounts to ship from store
            $shippingDiscount = ($_formatInStoreRetailerOutput && $retailerIsDiscounted)
                ? (float)$this->request->data('shippingDiscount')
                : 0.00;
            $shippingDiscount = (float)min($shippingDiscount, $shippingAmount);

            $shippingAmountDiscounted = $shippingAmount - $shippingDiscount;

            $defaultShippingTaxRate = $defaultTaxRate;
            if (
                $shippingType === 'instore' &&
                $value['u']['type'] === 'nonstock' &&
                !$value['u']['shiptostore_tax']
            ) {
                $defaultShippingTaxRate = null;
            }
            $shippingTaxOverride = $this->_extractShippingTaxOverride($retailers_inv[$retailerId], $defaultShippingTaxRate);
            $shippingTaxName = $shippingTaxOverride['shipping_tax_name'];
            $instorePickupShippingTaxRate = $shippingTaxOverride['shipping_tax_rate'] ?? 0;
            $shippingTaxRate = $shippingTaxOverride['shipping_tax_rate'] ?? $defaultTaxRate;

            // eCommerce plugins expect shippingTax to be the final amount applied to the order (ie. after discounts)
            $shippingTax = calculate_tax_amount(
                $shippingAmountDiscounted,
                ($_formatInStoreRetailerOutput ? $instorePickupShippingTaxRate : $shippingTaxRate),
                $taxIncluded
            );
            $shippingTax = (float)$this->Currency->formatAsDecimal($shippingTax, $currency);

            $taxamount = (float)$retailers_tax[$retailerId];
            $taxamt = $taxamount + $shippingTax;

            $totalamount = $price;
            if (!$taxIncluded) {
                $totalamount += $taxamt;
            }
            $totalwithshipping = $totalamount + $shippingAmount;

            $pricediscount = (float)$retailers_price_discount[$retailerId];
            $discountAmount = $price - $pricediscount;

            $taxamountdiscount = (float)$retailers_tax_discount[$retailerId];
            $taxamtdiscount = $taxamountdiscount + $shippingTax;

            $totalamountdiscount = $pricediscount;
            if (!$taxIncluded) {
                $totalamountdiscount += $taxamtdiscount;
            }
            $totalwithshippingdiscount = $totalamountdiscount + $shippingAmountDiscounted;

            $response = array_merge($value['u'], [
                'formatAddress' => $this->_formatMapAddress($retailerId, $value['u'], $value['c']['telephone']),
                'state' => $value['s']['state_name'],
                'distance' => format_number($value[0]['distance']),
                'store_timing' => $this->User->getStoreTiming($value['u']['store_timing'], $retailerId),
                'rating' => $this->OrderLogic->getRetailerRating($retailerId),
                'product' => $retailers_inv[$retailerId],
                'currency' => $currency,
                'totalproductamount' => format_number($price),
                'priceformat' => $this->Currency->formatCurrency($price, $currency),
                'price' => $this->Currency->formatCurrency($price, $currency),
                'shippingAmount' => format_number($shippingAmount),
                'shippingAmountFormat' => $this->Currency->formatCurrency($shippingAmount, $currency),
                'shippingDiscount' => format_number($shippingDiscount),
                'shippingDiscountFormat' => $this->Currency->formatCurrency($shippingDiscount, $currency),
                'shippingAmountDiscounted' => format_number($shippingAmountDiscounted),
                'shippingAmountDiscountedFormat' => $this->Currency->formatCurrency($shippingAmountDiscounted, $currency),
                'shippingTax' => format_number($shippingTax),
                'taxamt' => format_number($taxamt),
                'tax_included' => $taxIncluded,
                'shipping_tax_name' => $shippingTaxName,
                'shipping_tax_rate' => $shippingTaxRate,
                'discountAmount' => format_number($discountAmount),
                'discountAmountFormat' => $this->Currency->formatCurrency($discountAmount, $currency),
                'totalproductamountdiscount' => format_number($pricediscount),
                'priceformatdiscount' => $this->Currency->formatCurrency($pricediscount, $currency),
                'taxamtdiscount' => format_number($taxamtdiscount),
            ]);
            if ($_formatInStoreRetailerOutput) {
                $response = array_merge($response, [
                    'totalamount' => format_number($totalwithshipping),
                    'totalformat' => $this->Currency->formatCurrency($totalwithshipping, $currency),
                    'totalamountdiscount' => format_number($totalwithshippingdiscount),
                    'totalformatdiscount' => $this->Currency->formatCurrency($totalwithshippingdiscount, $currency),
                    'taxformat' => $this->Currency->formatCurrency($taxamt, $currency),
                    'taxformatdiscount' => $this->Currency->formatCurrency($taxamtdiscount, $currency),
                ]);

                $installAmount = $this->User->calcInstallFee($users, $retailerId, $products_info);
                $response = array_merge($response, $this->_formatInStoreLocalInstallOutput($currency, $price, $pricediscount, $installAmount, $shippingTaxRate, $taxamount, $taxamountdiscount, $taxIncluded));

                $local_delivery_shipping = $this->_extractLocalDeliveryShippingFromRetailersInv($retailerId, $retailers_inv);
                $response = array_merge($response, $this->_formatInStoreLocalDeliveryOutput($currency, $price, $pricediscount, $local_delivery_shipping, $shippingTaxRate, $taxamount, $taxamountdiscount, $taxIncluded));
            } else {
                $response = array_merge($response, [
                    'totalamount' => format_number($totalamount),
                    'totalformat' => $this->Currency->formatCurrency($totalamount, $currency),
                    'totalamountdiscount' => format_number($totalamountdiscount),
                    'totalformatdiscount' => $this->Currency->formatCurrency($totalamountdiscount, $currency),
                    'taxamtwithShipping' => format_number($taxamt),
                    'taxformatwithShipping' => $this->Currency->formatCurrency($taxamt, $currency),
                    'totalwithshipping' => format_number($totalwithshipping),
                    'totalwithshippingformat' => $this->Currency->formatCurrency($totalwithshipping, $currency),
                    'pricediscount' => $this->Currency->formatCurrency($pricediscount, $currency),
                    'taxamtwithShippingDiscount' => format_number($taxamtdiscount),
                    'taxformatwithShippingDiscount' => $this->Currency->formatCurrency($taxamtdiscount, $currency),
                    'totalwithshippingdiscount' => format_number($totalwithshippingdiscount),
                    'totalwithshippingformatdiscount' => $this->Currency->formatCurrency($totalwithshippingdiscount, $currency),
                ]);

                $response = array_merge($response, $this->_formatShipFromStoreLocalDeliveryOutput($value, $currency, $shippingTaxRate, $price, $pricediscount, $taxamount, $taxamountdiscount));
            }
            $response = array_merge($response, [
                'shippingType' => $shippingType,
            ]);
        }

        CakeLog::debug(json_encode(compact('response')));

        return json_encode($response);
    }

    protected function formatRetailerInventory($products_info, $defaultTaxRate, $value, &$retailers_inv, &$retailers_price, &$retailers_currency, &$retailers_tax, $users, $discounts, &$retailers_price_discount, &$retailers_tax_discount)
    {
        $retailerId = $value['u']['retailer_id'];
        foreach ($products_info as $val1) {
            $productID = $val1['Product']['productID'];

            $retailers_inv[$retailerId][$productID] = array_merge((array)Hash::get($retailers_inv, "{$retailerId}.{$productID}"), [
                'title' => $val1['Product']['product_title'],
                'upc' => $val1['Product']['product_upc'],
            ]);
        }

        if ($value['u']['type'] === 'nonstock') {
            $this->_nonStockFormat($retailerId, $defaultTaxRate, $products_info, $users['User']['currency_code'], $discounts, $retailers_inv, $retailers_price, $retailers_tax, $retailers_currency, $retailers_price_discount, $retailers_tax_discount);
        } elseif ($this->request->data['type'] === 'local_delivery') {
            $this->_applyTaxOverrides($retailerId, (float)$defaultTaxRate, $products_info, $retailers_currency, $retailers_inv, $retailers_tax, $retailers_tax_discount);
        }
    }

    /**
     * @param $retailerId
     * @param $defaultTaxRate
     * @param $products_info
     * @param $currencyCode
     * @param $discounts
     * @param $retailers_inv
     * @param $retailers_price
     * @param $retailers_tax
     * @param $retailers_currency
     * @param $retailers_price_discount
     * @param $retailers_tax_discount
     * @return bool
     */
    public function _nonStockFormat($retailerId, $defaultTaxRate, $products_info, $currencyCode, $discounts, &$retailers_inv, &$retailers_price, &$retailers_tax, &$retailers_currency, &$retailers_price_discount, &$retailers_tax_discount)
    {
        CakeLog::debug('Args ' . json_encode(compact('retailerId', 'defaultTaxRate', 'products_info', 'currencyCode', 'discounts', 'retailers_inv', 'retailers_price', 'retailers_tax', 'retailers_currency', 'retailers_price_discount', 'retailers_tax_discount')));

        foreach ($products_info as $val1) {
            $productID = $val1['Product']['productID'];

            $qty = (int)$this->request->data['Qty'][$productID];

            $discount = $this->_getItemDiscount($discounts, $productID, $retailerId);
            $item_discount_amount = $discount['discount_amount'];
            $item_unit_discount_amount = $item_discount_amount / $qty;

            $unitprice = $val1['Product']['product_price'];
            $price = $unitprice * $qty;
            $unitpricediscount = max($unitprice - $item_unit_discount_amount, 0);
            $pricediscount = max($price - $item_discount_amount, 0);

            $taxRate = (float)($retailers_inv[$retailerId][$productID]['tax_rate'] ?? $defaultTaxRate);
            $taxIncluded = (bool)($retailers_inv[$retailerId][$productID]['tax_included'] ?? false);

            $tax = calculate_tax_amount($price, $taxRate, $taxIncluded);
            $taxdiscount = calculate_tax_amount($pricediscount, $taxRate, $taxIncluded);

            $retailers_inv[$retailerId][$productID] = array_merge($retailers_inv[$retailerId][$productID] ?? [], [
                'id' => $val1['Product']['id'],
                'inventory' => (int)$val1['Product']['brand_inventory'],
                'taxcode' => $val1['Product']['taxcode'],
                'currency' => $currencyCode,
                'qty' => $qty,
                'unformatedunitprice' => format_number($unitprice),
                'unitprice' => $this->Currency->formatCurrency($unitprice, $currencyCode),
                'unformatedprice' => format_number($price),
                'price' => $this->Currency->formatCurrency($price, $currencyCode),
                'tax' => format_number($tax),
                'unformatedunitpricediscount' => format_number($unitpricediscount),
                'unitpricediscount' => $this->Currency->formatCurrency($unitpricediscount, $currencyCode),
                'unformatedpricediscount' => format_number($pricediscount),
                'pricediscount' => $this->Currency->formatCurrency($pricediscount, $currencyCode),
                'taxdiscount' => format_number($taxdiscount),
            ]);
            if (!isset($retailers_inv[$retailerId][$productID]['inventoryId'])) {
                // Only overwrite if not already set by inventory software
                $retailers_inv[$retailerId][$productID]['inventoryId'] = $productID;
            }
        }

        $retailers_price[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'unformatedprice'));
        $retailers_tax[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'tax'));
        $retailers_currency[$retailerId] = $currencyCode;
        $retailers_price_discount[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'unformatedpricediscount'));
        $retailers_tax_discount[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'taxdiscount'));

        CakeLog::debug('Output ' . json_encode(compact('retailers_inv', 'retailers_price', 'retailers_tax', 'retailers_currency', 'retailers_price_discount', 'retailers_tax_discount')));
        return true;
    }

    private function _applyTaxOverrides(
        int $retailerId,
        float $defaultTaxRate,
        array $products_info,
        array $retailers_currency,
        array &$retailers_inv,
        array &$retailers_tax,
        array &$retailers_tax_discount
    ): bool
    {
        CakeLog::debug(json_encode(['message' => 'Args'] + compact('retailerId', 'defaultTaxRate', 'products_info', 'retailers_currency', 'retailers_inv')));

        $currencyCode = (string)$retailers_currency[$retailerId];

        foreach ($products_info as $val1) {
            $productID = $val1['Product']['productID'];

            $tax = 0.00;
            $taxdiscount = 0.00;
            if ($retailers_inv[$retailerId][$productID]['isTax'] ?? true) {
                $price = (float)$retailers_inv[$retailerId][$productID]['unformatedprice'];
                $pricediscount = (float)$retailers_inv[$retailerId][$productID]['unformatedpricediscount'];

                $taxRate = (float)($retailers_inv[$retailerId][$productID]['tax_rate'] ?? $defaultTaxRate);
                $taxIncluded = (bool)($retailers_inv[$retailerId][$productID]['tax_included'] ?? false);

                $tax = calculate_tax_amount($price, $taxRate, $taxIncluded);
                $taxdiscount = calculate_tax_amount($pricediscount, $taxRate, $taxIncluded);
            }

            $retailers_inv[$retailerId][$productID] = array_merge($retailers_inv[$retailerId][$productID], [
                'tax' => $this->Currency->formatAsDecimal($tax, $currencyCode),
                'taxdiscount' => $this->Currency->formatAsDecimal($taxdiscount, $currencyCode),
            ]);
        }

        $retailers_tax[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'tax'));
        $retailers_tax_discount[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'taxdiscount'));
        CakeLog::debug(json_encode(['message' => 'Output'] + compact('retailers_inv', 'retailers_tax', 'retailers_tax_discount')));

        return true;
    }

    private function _formatInStoreLocalInstallOutput(
        string $currency,
        float $price,
        float $pricediscount,
        float $installAmount,
        float $shippingTaxRate,
        float $taxamount,
        float $taxamountdiscount,
        bool $taxIncluded
    ): array
    {
        $installTax = (float)$this->Currency->formatAsDecimal(
            calculate_tax_amount($installAmount, $shippingTaxRate, $taxIncluded),
            $currency
        );

        $taxamt_localinstall = $taxamount + $installTax;
        $taxamtdiscount_localinstall = $taxamountdiscount + $installTax;

        $totalamount_localinstall = $price + $installAmount;
        $totalamountdiscount_localinstall = $pricediscount + $installAmount;
        if (!$taxIncluded) {
            $totalamount_localinstall += $taxamt_localinstall;
            $totalamountdiscount_localinstall += $taxamtdiscount_localinstall;
        }

        return [
            'shippingAmount_localinstall' => format_number($installAmount),
            'shippingAmountFormat_localinstall' => $this->Currency->formatCurrency($installAmount, $currency),
            'installTax' => format_number($installTax),
            'taxamt_localinstall' => format_number($taxamt_localinstall),
            'taxformat_localinstall' => $this->Currency->formatCurrency($taxamt_localinstall, $currency),
            'totalamount_localinstall' => format_number($totalamount_localinstall),
            'totalformat_localinstall' => $this->Currency->formatCurrency($totalamount_localinstall, $currency),
            'taxamtdiscount_localinstall' => format_number($taxamtdiscount_localinstall),
            'taxformatdiscount_localinstall' => $this->Currency->formatCurrency($taxamtdiscount_localinstall, $currency),
            'totalamountdiscount_localinstall' => format_number($totalamountdiscount_localinstall),
            'totalformatdiscount_localinstall' => $this->Currency->formatCurrency($totalamountdiscount_localinstall, $currency),
        ];
    }

    private function _formatInStoreLocalDeliveryOutput(
        string $currency,
        float $price,
        float $pricediscount,
        float $local_delivery_shipping,
        float $shippingTaxRate,
        float $taxamount,
        float $taxamountdiscount,
        bool $taxIncluded
    ): array
    {
        $shippingTax_localdelivery = (float)$this->Currency->formatAsDecimal(
            calculate_tax_amount($local_delivery_shipping, $shippingTaxRate, $taxIncluded),
            $currency
        );

        $taxamt_localdelivery = $taxamount + $shippingTax_localdelivery;
        $taxamtdiscount_localdelivery = $taxamountdiscount + $shippingTax_localdelivery;

        $totalamount_localdelivery = $price + $local_delivery_shipping;
        $totalamountdiscount_localdelivery = $pricediscount + $local_delivery_shipping;
        if (!$taxIncluded) {
            $totalamount_localdelivery += $taxamt_localdelivery;
            $totalamountdiscount_localdelivery += $taxamtdiscount_localdelivery;
        }

        return [
            'local_delivery_shipping' => format_number($local_delivery_shipping),
            'shippingAmount_localdelivery' => format_number($local_delivery_shipping),
            'shippingAmountFormat_localdelivery' => $this->Currency->formatCurrency($local_delivery_shipping, $currency),
            'shippingTax_localdelivery' => format_number($shippingTax_localdelivery),
            'taxamt_localdelivery' => format_number($taxamt_localdelivery),
            'taxformat_localdelivery' => $this->Currency->formatCurrency($taxamt_localdelivery, $currency),
            'totalamount_localdelivery' => format_number($totalamount_localdelivery),
            'totalformat_localdelivery' => $this->Currency->formatCurrency($totalamount_localdelivery, $currency),
            'taxamtdiscount_localdelivery' => format_number($taxamtdiscount_localdelivery),
            'taxformatdiscount_localdelivery' => $this->Currency->formatCurrency($taxamtdiscount_localdelivery, $currency),
            'totalamountdiscount_localdelivery' => format_number($totalamountdiscount_localdelivery),
            'totalformatdiscount_localdelivery' => $this->Currency->formatCurrency($totalamountdiscount_localdelivery, $currency),
        ];
    }

    /**
     * @param array $value
     * @param string $currency
     * @param float $shippingTaxRate
     * @param float $price
     * @param float $pricediscount
     * @param float $taxamount
     * @param float $taxamountdiscount
     * @return array
     * @deprecated Since Version1.39
     */
    private function _formatShipFromStoreLocalDeliveryOutput($value, $currency, $shippingTaxRate, $price, $pricediscount, $taxamount, $taxamountdiscount): array
    {
        $Currency = $this->Currency;
        $response = [];

        $shippingAmount_localdelivery = $value['u']['local_delivery_shipping'] ?? 0;
        $shippingTax_localdelivery = $shippingTaxRate * $shippingAmount_localdelivery;

        if (
            (empty($value['u']['free_shipping']) || $value['u']['free_shipping'] > $price) &&
            (empty($value['u']['shipment_option']) || $value['u']['shipment_type'] != 1)
        ) {
            $response['shippingAmount_localdelivery'] = $shippingAmount_localdelivery;
            $response['shippingAmountFormat_localdelivery'] = $Currency->formatCurrency($response['shippingAmount_localdelivery'], $currency);
        }

        $taxamt_localdelivery = isset($value['u']['local_delivery_shipping']) ? $taxamount + $shippingTax_localdelivery : 0;
        $totalamount_localdelivery = $price + $taxamt_localdelivery;
        $totalwithshipping_localdelivery = $totalamount_localdelivery + $response['shippingAmount_localdelivery'];

        $taxamtdiscount_localdelivery = isset($value['u']['local_delivery_shipping']) ? $taxamountdiscount + $shippingTax_localdelivery : 0;
        $totalamountdiscount_localdelivery = $pricediscount + $taxamtdiscount_localdelivery;
        $totalwithshippingdiscount_localdelivery = ($pricediscount + $taxamtdiscount_localdelivery) + $response['shippingAmount_localdelivery'];

        return array_merge($response, [
            'shippingAmount_localdelivery' => format_number($response['shippingAmount_localdelivery']),
            'shippingTax_localdelivery' => format_number($shippingTax_localdelivery),
            'taxamt_localdelivery' => format_number($taxamt_localdelivery),
            'taxamtwithShipping_localdelivery' => format_number($taxamt_localdelivery),
            'taxformatwithShipping_localdelivery' => $Currency->formatCurrency($taxamt_localdelivery, $currency),
            'totalamount_localdelivery' => format_number($totalamount_localdelivery),
            'totalformat_localdelivery' => $Currency->formatCurrency($totalamount_localdelivery, $currency),
            'totalwithshipping_localdelivery' => format_number($totalwithshipping_localdelivery),
            'totalwithshippingformat_localdelivery' => $Currency->formatCurrency($totalwithshipping_localdelivery, $currency),
            'taxamtdiscount_localdelivery' => format_number($taxamtdiscount_localdelivery),
            'taxamtwithShippingDiscount_localdelivery' => format_number($taxamtdiscount_localdelivery),
            'taxformatwithShippingDiscount_localdelivery' => $Currency->formatCurrency($taxamtdiscount_localdelivery, $currency),
            'totalamountdiscount_localdelivery' => format_number($totalamountdiscount_localdelivery),
            'totalformatdiscount_localdelivery' => $Currency->formatCurrency($totalamountdiscount_localdelivery, $currency),
            'totalwithshippingdiscount_localdelivery' => format_number($totalwithshippingdiscount_localdelivery),
            'totalwithshippingformatdiscount_localdelivery' => $Currency->formatCurrency($totalwithshippingdiscount_localdelivery, $currency),
        ]);
    }

    private function _applyFeeToPosRetailer($brandId, $retailerId, $countryId, $stateId, $currency, $taxRate, &$retailers_inv, &$retailers_price, &$retailers_tax, &$retailers_price_discount, &$retailers_tax_discount)
    {
        $retailers_inv_with_fee = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv[$retailerId],
            $brandId,
            $countryId,
            $stateId,
            $currency,
            $taxRate
        );
        $retailers_price_with_fee = array_sum(array_column($retailers_inv_with_fee, 'unformatedprice'));
        $retailers_tax_with_fee = array_sum(array_column($retailers_inv_with_fee, 'tax'));
        $retailers_inv[$retailerId] = $retailers_inv_with_fee;
        $retailers_price[$retailerId] = $retailers_price_with_fee;
        $retailers_tax[$retailerId] = $retailers_tax_with_fee;
        $retailers_price_discount[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'unformatedpricediscount'));
        $retailers_tax_discount[$retailerId] = array_sum(array_column($retailers_inv[$retailerId], 'taxdiscount'));
    }

    protected function _formatBrandOutput(
        array $user,
        array $products_info,
        array $retailers_inv,
        array $retailers_price,
        array $retailers_currency,
        array $retailers_tax,
        array $retailers_price_discount,
        array $retailers_tax_discount
    ): string
    {
        CakeLog::debug(json_encode(['message' => 'Args'] + compact('user', 'products_info', 'retailers_inv', 'retailers_price', 'retailers_currency', 'retailers_tax', 'retailers_price_discount', 'retailers_tax_discount')));
        $Currency = $this->Currency;

        $userId = (int)$user['User']['id'];
        $currencyCode = (string)$retailers_currency[$userId];

        $shipping = (array)json_decode($this->request->data['address'], true);

        if ($user['User']['site_type'] !== UserSiteType::WOOCOMMERCE) {
            $userTax = $this->TaxCalculator->findRatesForLocation($userId, $shipping);
            foreach ($products_info as $val1) {
                $productID = $val1['Product']['productID'];
                $retailers_inv[$userId][$productID] = array_merge($retailers_inv[$userId][$productID] ?? [], $userTax);
            }
            $retailers_inv = $this->_applyShippingZoneTaxOverridesToRetailersInv($userId, $userId, $shipping, $products_info, $retailers_inv);
            $this->_applyTaxOverrides($userId, (float)$userTax['tax_rate'], $products_info, $retailers_currency, $retailers_inv, $retailers_tax, $retailers_tax_discount);
        } else {
            $retailers_tax[$userId] = (float)$this->request->data['totalTax'];
            $retailers_tax_discount[$userId] = (float)$this->request->data['totalTax'];
        }

        $taxIncluded = $this->_extractTaxIncluded($userId, (int)$shipping['country'], $retailers_inv);
        foreach ($products_info as $val1) {
            $productID = $val1['Product']['productID'];
            $retailers_inv[$userId][$productID] = (array)($retailers_inv[$userId][$productID] ?? []) + ['tax_included' => $taxIncluded];
        }

        $shippingAmount = (float)$this->request->data('shippingAmount');
        $shippingDiscount = (float)$this->request->data('shippingDiscount');
        $shippingAmountDiscounted = $shippingAmount - $shippingDiscount;

        $shippingTaxOverride = $this->_extractShippingTaxOverride($retailers_inv[$userId]);
        $shippingTaxName = $shippingTaxOverride['shipping_tax_name'];
        $shippingTaxRate = $shippingTaxOverride['shipping_tax_rate'];

        // eCommerce plugins expect shippingTax to be the final amount applied to the order (ie. after discounts)
        $shippingTaxAmount = (float)$this->Currency->formatAsDecimal(
            calculate_tax_amount($shippingAmountDiscounted, $shippingTaxRate, $taxIncluded),
            $currencyCode
        );

        $price = $retailers_price[$userId];
        $taxamt = $retailers_tax[$userId] + $shippingTaxAmount;

        $totalamount = $price;
        if (!$taxIncluded) {
            $totalamount += $taxamt;
        }
        $totalwithshipping = $totalamount + $shippingAmount;

        $pricediscount = $retailers_price_discount[$userId];
        $discountAmount = $price - $pricediscount;
        $taxamtdiscount = $retailers_tax_discount[$userId] + $shippingTaxAmount;

        $totalamountdiscount = $pricediscount;
        if (!$taxIncluded) {
            $totalamountdiscount += $taxamtdiscount;
        }
        $totalwithshippingdiscount = $totalamountdiscount + $shippingAmountDiscounted;

        $response = array_merge($user['User'], array(
            'formatAddress' => $this->_formatMapAddress($userId, $user['User'], $this->Contact->getCompanyTelephone($userId)),
            'state' => $this->State->getStateName($user['User']['state_id'], $user['User']['country_id']),
            'product' => $retailers_inv[$userId],
            'currency' => $currencyCode,
            'shippingAmount' => format_number($shippingAmount),
            'shippingAmountFormat' => $Currency->formatCurrency($shippingAmount, $currencyCode),
            'shippingDiscount' => format_number($shippingDiscount),
            'shippingDiscountFormat' => $Currency->formatCurrency($shippingDiscount, $currencyCode),
            'shippingAmountDiscounted' => format_number($shippingAmountDiscounted),
            'shippingAmountDiscountedFormat' => $Currency->formatCurrency($shippingAmountDiscounted, $currencyCode),
            'shipping_tax_option' => ($shippingTaxRate > 0),
            'shipping_tax_name' => $shippingTaxName,
            'shipping_tax_rate' => $shippingTaxRate,
            'tax_included' => $taxIncluded,
            'shippingTax' => format_number($shippingTaxAmount),
            'totalproductamount' => format_number($price),
            'price' => $Currency->formatCurrency($price, $currencyCode),
            'priceformat' => $Currency->formatCurrency($price, $currencyCode),
            'taxamt' => format_number($taxamt),
            'taxamtwithShipping' => format_number($taxamt),
            'taxformatwithShipping' => $Currency->formatCurrency($taxamt, $currencyCode),
            'totalamount' => format_number($totalamount),
            'totalformat' => $Currency->formatCurrency($totalamount, $currencyCode),
            'totalwithshipping' => format_number($totalwithshipping),
            'totalwithshippingformat' => $Currency->formatCurrency($totalwithshipping, $currencyCode),
            'discountAmount' => format_number($discountAmount),
            'discountAmountFormat' => $Currency->formatCurrency($discountAmount, $currencyCode),
            'totalproductamountdiscount' => format_number($pricediscount),
            'pricediscount' => $Currency->formatCurrency($pricediscount, $currencyCode),
            'priceformatdiscount' => $Currency->formatCurrency($pricediscount, $currencyCode),
            'taxamtdiscount' => format_number($taxamtdiscount),
            'taxamtwithShippingDiscount' => format_number($taxamtdiscount),
            'taxformatwithShippingDiscount' => $Currency->formatCurrency($taxamtdiscount, $currencyCode),
            'totalamountdiscount' => format_number($totalamountdiscount),
            'totalformatdiscount' => $Currency->formatCurrency($totalamountdiscount, $currencyCode),
            'totalwithshippingdiscount' => format_number($totalwithshippingdiscount),
            'totalwithshippingformatdiscount' => $Currency->formatCurrency($totalwithshippingdiscount, $currencyCode),
        ));

        CakeLog::debug(json_encode(compact('response')));
        return json_encode($response);
    }

    private function _extractShippingTaxOverride($products, $defaultTaxRate = '0.00000'): array
    {
        $shipping_tax_name = 'Shipping Tax';
        $productShippingTaxRate = null;
        foreach ($products as $product) {
            if (isset($product['shipping_tax_rate']) && $productShippingTaxRate < $product['shipping_tax_rate']) {
                $productShippingTaxRate = $product['shipping_tax_rate'];
                $shipping_tax_name = $product['shipping_tax_name'];
            }
        }
        $shipping_tax_rate = $productShippingTaxRate ?? $defaultTaxRate;
        return compact('shipping_tax_name', 'shipping_tax_rate');
    }

    private function _brandShippingTaxOption(int $brandId, string $siteType): bool
    {
        return boolval(
            ($siteType !== UserSiteType::WOOCOMMERCE)
                ? $this->UserSetting->fieldByConditions('shipping_tax_option', ['UserSetting.user_id' => $brandId])
                : $this->request->data('shippingTotalTax')
        );
    }

    private function _formatMapAddress($userId, $user, $telephone = '')
    {
        /** @var FormatAddressHelper $FormatAddress */
        $FormatAddress = (new View($this))->loadHelper('FormatAddress');

        $user = (array)$user;
        $user = $this->User->formatAddress($user);
        $user = $this->_fillMissingMapAddressStateFields($user);
        $user['telephone'] = $telephone ?: ($user['telephone'] ?? null) ?: $this->Contact->getCompanyTelephone($userId);

        return $FormatAddress->forLegacyEcommerceMapAddress($user);
    }

    private function _fillMissingMapAddressStateFields(array $user): array
    {
        $fieldNames = ['state_code', 'state_name', 'country_code', 'country_name'];
        $fieldNames = array_combine($fieldNames, $fieldNames);

        $missing = array_diff_key($fieldNames, array_filter($user));
        if (!$missing) {
            return $user;
        }

        $record = is_numeric($user['state_id'])
            ? $this->State->findWithCountryName($user['state_id'])['State']
            : [];
        if (empty($record['id'])) {
            // Some workflows set state_id and country_id to names
            $stateName = !is_numeric($user['state_id']) ? $user['state_id'] : '';
            $countryName = !is_numeric($user['country_id']) ? $user['country_id'] : '';

            $record = [
                'state_code' => $stateName,
                'state_name' => $stateName,
                'country_code' => $countryName,
                'country_name' => $countryName,
            ];
        }

        return array_merge($user, array_intersect_key($record, $fieldNames));
    }

    public function _searchEcommerceSoftware(
        array $products_info,
        array $value,
        array $requestData,
        array $discounts,
        array &$retailers_inv,
        array &$retailers_price,
        array &$retailers_tax,
        array &$retailers_currency,
        array &$retailers_price_discount,
        array &$retailers_tax_discount
    ): bool
    {
        CakeLog::debug(json_encode(['message' => 'Args'] + compact('products_info', 'value', 'requestData', 'discounts')));

        $userId = (int)$value['User']['id'];
        $currencyCode = (string)$value['User']['currency_code'];
        $productQty = (array)$requestData['Qty'];
        $shipping = (array)json_decode($requestData['address'], true);

        if ($value['User']['site_type'] === UserSiteType::WOOCOMMERCE) {
            $ecommerce_inv_user = $this->_woocommerceList($products_info, $value, $requestData);
        } else {
            $ecommerce_inv_user = $this->_shopifyList($products_info, $value, $requestData);
        }

        $retailers_inv[$userId] = $this->_deriveRetailersInvFromEcommerceList(
            $ecommerce_inv_user,
            $products_info,
            $productQty,
            $discounts,
            $currencyCode,
            (array)($retailers_inv[$userId] ?? [])
        );

        $retailers_inv[$userId] = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv[$userId],
            $userId,
            $shipping['country'],
            $shipping['province'],
            $currencyCode
        );

        $retailers_price[$userId] = array_sum(array_column($retailers_inv[$userId], 'unformatedprice'));
        $retailers_tax[$userId] = array_sum(array_column($retailers_inv[$userId], 'tax'));
        $retailers_currency[$userId] = (string)current(array_column($retailers_inv[$userId], 'currency'));
        $retailers_price_discount[$userId] = array_sum(array_column($retailers_inv[$userId], 'unformatedpricediscount'));
        $retailers_tax_discount[$userId] = array_sum(array_column($retailers_inv[$userId], 'taxdiscount'));

        $response = true;
        CakeLog::debug(json_encode(['message' => 'Output'] + compact('response', 'retailers_inv', 'retailers_price', 'retailers_tax', 'retailers_currency', 'retailers_price_discount', 'retailers_tax_discount')));

        return $response;
    }

    public function _shopifyList(array $products_info, array $value, array $requestData): array
    {
        $userId = (int)$value['User']['id'];
        $currency = (string)$value['User']['currency_code'];
        $shipping = (array)json_decode($requestData['address'], true);

        $taxIncluded = $this->UserCountryTax->isIncludedInPrices($userId, (int)$shipping['country']);

        return array_map(
            function($product) use ($currency, $taxIncluded) {
                return [
                    'inventoryId' => (int)$product['Product']['productID'],
                    'inventory' => (int)$product['Product']['brand_inventory'],
                    'currency' => $currency,

                    // Ignore Shopify API {"variant":{"taxable":true}}
                    'isTax' => true,
                    // Ignore Shopify API {"shop":{"taxes_included":false}}
                    'tax_included' => $taxIncluded,

                    'unformatedunitprice' => $product['Product']['product_price'],

                    // Used to set Shopify API {"order":{"total_weight":300}}
                    'weight' => convertWeightToGrams((float)$product['Product']['weight'], (string)$product['Product']['weight_unit']),
                ];
            },
            Hash::combine($products_info, '{n}.Product.productID', '{n}')
        );
    }

    protected function _deriveRetailersInvFromEcommerceList(
        array $ecommerce_inv_user,
        array $products_info,
        array $productQty,
        array $discounts,
        string $currencyCode,
        array $retailers_inv_user
    ): array
    {
        $products_info = array_filter($products_info, function($val1) use ($ecommerce_inv_user) {
            return $ecommerce_inv_user[$val1['Product']['productID']];
        });

        return array_reduce(
            $products_info,
            function($retailers_inv_user, $val1) use (
                $ecommerce_inv_user,
                $productQty,
                $discounts,
                $currencyCode
            ) {
                $productId = $val1['Product']['id'];
                $productID = $val1['Product']['productID'];

                $ecommerce_inventory = (array)$ecommerce_inv_user[$productID];
                $qty = (int)$productQty[$productID];
                $discount = $this->_getItemDiscount($discounts, $productID);

                $item_discount_amount = (float)$discount['discount_amount'];
                $item_unit_discount_amount = $item_discount_amount / $qty;

                $unitprice = (float)$ecommerce_inventory['unformatedunitprice'];
                $price = $unitprice * $qty;
                $unitpricediscount = (float)max($unitprice - $item_unit_discount_amount, 0);
                $pricediscount = (float)max($price - $item_discount_amount, 0);

                $taxable = (bool)$ecommerce_inventory['isTax'];
                $taxIncluded = (bool)$ecommerce_inventory['tax_included'];

                $retailers_inv_user[$productID] = array_merge((array)($retailers_inv_user[$productID] ?? []), [
                    'id' => $productId,
                    'inventoryId' => (string)$ecommerce_inventory['inventoryId'],
                    'inventory' => $ecommerce_inventory['inventory'],
                    'weight' => (float)($ecommerce_inventory['weight'] * $qty),
                    'currency' => $ecommerce_inventory['currency'],
                    'isTax' => $taxable,
                    'tax_included' => $taxIncluded,
                    'taxcode' => $val1['Product']['taxcode'],
                    'qty' => $qty,
                    'unformatedunitprice' => $this->Currency->formatAsDecimal($unitprice, $currencyCode),
                    'unitprice' => $this->Currency->formatCurrency($unitprice, $currencyCode),
                    'unformatedprice' => $this->Currency->formatAsDecimal($price, $currencyCode),
                    'price' => $this->Currency->formatCurrency($price, $currencyCode),
                    'unformatedunitpricediscount' => $this->Currency->formatAsDecimal($unitpricediscount, $currencyCode),
                    'unitpricediscount' => $this->Currency->formatCurrency($unitpricediscount, $currencyCode),
                    'unformatedpricediscount' => $this->Currency->formatAsDecimal($pricediscount, $currencyCode),
                    'pricediscount' => $this->Currency->formatCurrency($pricediscount, $currencyCode),
                ]);

                return $retailers_inv_user;
            },
            $retailers_inv_user
        );
    }

    public function _woocommerceList(array $products_info, array $value, array $requestData): array
    {
        return array_map(
            function($product) use ($value) {
                return [
                    'inventoryId' => (int)$product['Product']['productID'],
                    'inventory' => (int)$product['Product']['brand_inventory'],
                    'currency' => $value['User']['currency_code'],

                    // Ignore WC API {"product":{"taxable":true}}
                    // This value is unused anyway because taxes are calculated in the WC plugin
                    'isTax' => true,
                    // Value from WC plugin `get_option( 'woocommerce_tax_display_shop' ) === 'incl'`
                    'tax_included' => (bool)($requestData['shopTaxOption'] ?? false),

                    'unformatedunitprice' => $product['Product']['product_price'],

                    // Not used by the WC plugin but provided for consistency with the Shopify response
                    'weight' => convertWeightToGrams((float)$product['Product']['weight'], (string)$product['Product']['weight_unit']),
                ];
            },
            Hash::combine($products_info, '{n}.Product.productID', '{n}')
        );
    }

    /**
     * Extract the order-level tax_included value that may have been assigned by a POS or eCom product pricing lookup.
     *
     * Falls back to looking up the resolved retailer country tax value from our database.
     *
     * @param int $retailerId
     * @param int $destinationCountryId
     * @param array $retailers_inv
     * @return bool The order-level tax_included value
     */
    protected function _extractTaxIncluded(int $retailerId, int $destinationCountryId, array $retailers_inv): bool
    {
        foreach (($retailers_inv[$retailerId] ?? []) as $item) {
            if (isset($item['tax_included'])) {
                return $item['tax_included'];
            }
        }

        return $this->UserCountryTax->isIncludedInPrices($retailerId, $destinationCountryId);
    }

    private function _applyShippingZoneTaxOverridesToRetailersInv(int $brandId, int $retailerId, array $shipping, array $products_info, array $retailers_inv): array
    {
        $productTaxOverrides = $this->_fetchTaxOverridesByProduct($brandId, $shipping, $products_info, false);
        $shippingTaxOverrides = $this->_fetchTaxOverridesByProduct($brandId, $shipping, $products_info, true);

        $anyProductHasShippingTaxOverride = (bool)array_intersect_key($shippingTaxOverrides, Hash::combine($products_info, '{n}.Product.id', '{n}.Product.id'));

        foreach ($products_info as $val1) {
            $productId = $val1['Product']['id'];
            $productID = $val1['Product']['productID'];
            if (!empty($productTaxOverrides[$productId]['id'])) {
                $retailers_inv[$retailerId][$productID]['tax_name'] = $productTaxOverrides[$productId]['name'];
                $retailers_inv[$retailerId][$productID]['tax_rate'] = $productTaxOverrides[$productId]['tax_rate'];
            }
            if (!empty($shippingTaxOverrides[$productId]['id'])) {
                $retailers_inv[$retailerId][$productID]['shipping_tax_name'] = $shippingTaxOverrides[$productId]['name'];
                $retailers_inv[$retailerId][$productID]['shipping_tax_rate'] = $shippingTaxOverrides[$productId]['tax_rate'];
            } elseif ($anyProductHasShippingTaxOverride) {
                // Prioritize shipping tax overrides by removing any other shipping tax rates
                $retailers_inv[$retailerId][$productID]['shipping_tax_name'] = null;
                $retailers_inv[$retailerId][$productID]['shipping_tax_rate'] = null;
            }
        }

        return $retailers_inv;
    }

    protected function _fetchTaxOverridesByProduct($brandId, $address, $products, $is_shipping_tax)
    {
        /** @var ShippingZone $ShippingZone */
        $ShippingZone = ClassRegistry::init('ShippingZone');

        return $ShippingZone->findMaxTaxOverridesByProductId((int)$brandId, (int)$address['country'], (int)$address['province'], Hash::extract((array)$products, '{n}.Product.id'), (bool)$is_shipping_tax);
    }

    private function _assignLocalDeliveryShippingToRetailersInv(int $brandId, int $retailerId, array $value, array $products_info, array $retailers_inv): array
    {
        $manufacturerRetailer = null;

        foreach ($products_info as $val1) {
            $productID = $val1['Product']['productID'];
            $assemblyOption = $val1['Product']['assembly_option'];

            $retailers_inv[$retailerId][$productID]['assembly_option'] = $assemblyOption;

            if ($assemblyOption == 1) {
                $manufacturerRetailer = $manufacturerRetailer ?? $this->ManufacturerRetailer->findForBrandLocalDeliveryRadius($brandId, $retailerId);
                if (
                    $value['u']['velofix']
                    || (
                        ($manufacturerRetailer['ManufacturerRetailer']['local_delivery_radius'] ?? null) > 0
                        && ($manufacturerRetailer['User']['local_delivery'] ?? null) == 1
                    )
                ) {
                    $retailers_inv[$retailerId][$productID]['brand_id'] = $brandId;
                    $retailers_inv[$retailerId][$productID]['local_delivery_shipping'] = $value['u']['local_delivery_shipping'];
                    $retailers_inv[$retailerId][$productID]['local_delivery_radius'] = (!$value['u']['velofix'])
                        ? $manufacturerRetailer['ManufacturerRetailer']['local_delivery_radius']
                        : $value[0]['distance'];
                }
            }
        }

        return $retailers_inv;
    }

    private function _extractLocalDeliveryShippingFromRetailersInv(int $retailerId, array $retailers_inv): float
    {
        foreach ($retailers_inv[$retailerId] as $product) {
            if (isset($product['local_delivery_shipping'])) {
                return (float)$product['local_delivery_shipping'];
            }
        }

        return 0.00;
    }

    /**
     * @param array $products
     * @return array
     */
    protected function _getUniqueUpcProducts(array $products)
    {
        $uniqueUpcProducts = array();

        $uniqueUpcs = array();
        foreach ($products as $product) {
            $upc = $product['Product']['product_upc'];
            if (empty($upc)) {
                $uniqueUpcProducts[] = $product;
                continue;
            } else {
                if (!array_key_exists($upc, $uniqueUpcs)) {
                    $uniqueUpcProducts[] = $product;
                    $uniqueUpcs[$upc] = 0;
                }
                $uniqueUpcs[$upc] += 1;
            }
        }

        $duplicateUpcs = array_filter($uniqueUpcs, function($count) { return $count > 1; });
        if ($duplicateUpcs) {
            $duplicateUpcProducts = array_filter($products, function($product) use ($duplicateUpcs) {
                return isset($duplicateUpcs[$product['Product']['product_upc']]);
            });
            CakeLog::warning(__METHOD__ . ', line ' . __LINE__ . ' - ' . 'Duplicate UPCs in cart ' . json_encode($duplicateUpcProducts));
        }

        return $uniqueUpcProducts;
    }

    /**
     * @param array $discounts
     * @param string $productID
     * @param int|null $retailerId
     * @return array
     */
    protected function _getItemDiscount($discounts, $productID, $retailerId = null)
    {
        $discount = (array)Hash::get($discounts, $productID);
        $retailersIds = (array)Hash::get($discount, 'retailers');
        $filtered = ($retailerId === null || in_array($retailerId, $retailersIds)) ? $discount : array();
        return array(
            'discount_amount' => (float)Hash::get($filtered, 'discount_amount'),
            'discount_quantity' => (int)Hash::get($filtered, 'discount_quantity'),
        );
    }

    /**
     * initiate the cron for logged in brand
     * @return CakeResponse
     */
    public function syncAll()
    {
        $authUser = $this->Auth->user();

        $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT);
        $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);

        $userId = (int)$authUser['id'];

        if (!$this->Cron->existsForUser($userId)) {
            $message = __('Synchronization settings not found. Confirm your E-Commerce Settings.');

            if ($this->request->is('ajax')) {
                $this->response->body(json_encode(['error' => $message]));

                return $this->response;
            }

            return $this->_exceptionResponse(null, $message);
        }

        if (!$this->Cron->queueUser($userId)) {
            $message = __('Failed to add synchronization to queue');

            return $this->_exceptionResponse(null, $message, $message . ' where ' . json_encode(['Cron.userid' => $userId]));
        }

        return $this->_successResponse(__('Synchronization added to queue'));
    }

    public function productCheck()
    {
        deprecationWarning("Version2.9 {$this->request->here} is deprecated. Ecommerce plugins should use an equivalent endpoint belonging to their controller.");

        $user = $this->User->authenticateFromEcommerce($this->request, [
            'User.id',
            'User.email_address',
            'User.fbpixelId',
        ]);

        $returnPolicy = nl2br($this->UserSetting->getReturnPolicy($user['User']['id'], $user['User']['email_address']));
        $fbpixelId = $user['User']['fbpixelId'];
        $allProductsAreEnabled = !$this->Product->exists([
            'Product.productID' => array_column((array)$this->request->data('items'), 'variant_id'),
            'Product.user_id' => $user['User']['id'],
            'Product.product_status !=' => Product::STATUS_ENABLED,
        ]);

        return [
            'return-policy' => $returnPolicy,
            'fbpixelId' => $fbpixelId,
            'status' => $allProductsAreEnabled,
        ];
    }

    public function _ShipFromStoreCheck($storeConnections, bool $hasProtectedRetailer, array $retailers): array
    {
        if (!$hasProtectedRetailer || !isset($retailers['shipfromstore'])) {
            return $retailers;
        }

        $retailers['shipfromstore'] = array_filter($retailers['shipfromstore'], function ($retailer) use($storeConnections){
            $retailerID = $retailer['u']['retailer_id'];
            return !$storeConnections[$retailerID]['ship_from_store_unprotected_zones_only'];
        });
        // Important that this always encodes as an array in JSON.
        $retailers['shipfromstore'] = array_values($retailers['shipfromstore']);

        return $retailers;
    }

    /**
     * To check the priority of the sell direct option in products.
     *
     * @param int|string $brandId
     * @param int|null $priorityOption
     * @param bool $hasProtectedRetailer
     * @param array $retailers
     * @return array filtered $retailers with sellDirect
     */
    public function _sellDirectCheck($brandId, ?int $priorityOption, bool $hasProtectedRetailer, array $retailers): array
    {
        if ($priorityOption === ProductSellDirect::RETAIL_EXCLUSIVE) {
            // No change
        } elseif ($priorityOption === ProductSellDirect::UNPROTECTED_TERRITORIES) {
            if (!$hasProtectedRetailer) {
                $retailers['selldirect'] = $brandId;
            }
        } elseif ($priorityOption === ProductSellDirect::ALWAYS) {
            $retailers['selldirect'] = $brandId;
        } elseif (in_array($priorityOption, [ProductSellDirect::UNLESS_BUNDLED, ProductSellDirect::EXCLUSIVELY], true)) {
            unset(
                $retailers['instore'],
                $retailers['local_install'],
                $retailers['local_delivery'],
                $retailers['shipfromstore']
            );
            $retailers['selldirect'] = $brandId;
        }

        return $retailers;
    }

    public function _flatten(array $array) {
        $return = array();
        array_walk_recursive($array, function($a) use (&$return) { $return[] = $a; });
        return $return;
    }

    /**
     * @return CakeResponse|null
     * @deprecated Since Version1.36 Ecommerce plugins should use an equivalent endpoint belonging to their controller.
     */
    public function validate_address()
    {
        trigger_error("Version1.36 {$this->request->here} is deprecated. Ecommerce plugins should use an equivalent endpoint belonging to their controller.", E_USER_DEPRECATED);
        $this->response->body(json_encode($this->AddressValidator->validate_address($this->request->data)));
        return $this->response;
    }
}
