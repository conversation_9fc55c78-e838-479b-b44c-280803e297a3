<?php
App::uses('AppController', 'Controller');
App::uses('User', 'Model');
App::uses('Permissions', 'Utility');
/**
 * Warehouses Controller
 *
 * @property PaginatorComponent $Paginator
 * @property PermissionsComponent $Permissions
 *
 * @property ManufacturerSalesRep $ManufacturerSalesRep
 * @property Warehouse $Warehouse
 * @property WarehouseDistributor $WarehouseDistributor
 */
class WarehousesController extends AppController
{
    public $components = [
        'Paginator' => [
            'paramType' => 'querystring',
            'contain' => [
                'Distributor' => [
                    'with' => ['WarehouseDistributor' => []],
                    'fields' => ['id'],
                ],
            ],
            'fields' => [
                'Warehouse.id',
                'Warehouse.user_id',
                'Warehouse.source_id',
                'Warehouse.name',
                'Warehouse.is_active',
                'Warehouse.created_at',
                'Warehouse.updated_at',
            ],
            'order' => ['Warehouse.created_at' => 'DESC'],
            'limit' => 50,
        ],
    ];

    public $uses = ['Warehouse', 'WarehouseDistributor', 'ManufacturerSalesRep'];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $authUser = $this->Auth->user();

            $this->Permissions->assertUserIsType($authUser, [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF]);
            $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_INVENTORY, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function index()
    {
        $userId = $this->Auth->user('id');
        $distributorOptions = $this->ManufacturerSalesRep->listConnectedSalesReps($userId, true);
        $this->set('distributorOptions', $distributorOptions);
        $this->set('warehouses', $this->Paginator->paginate(null, ['Warehouse.user_id' => $userId]));
    }

    public function add()
    {
        $userId = $this->Auth->user('id');
        $distributorOptions = $this->ManufacturerSalesRep->listConnectedSalesReps($userId, true);
        $this->set('distributorOptions', $distributorOptions);

        if ($this->request->is('post')) {
            $user_id = $this->Auth->user('id');
            $this->request->data['Warehouse']['user_id'] = $user_id;
            $this->Warehouse->create();
            if ($this->Warehouse->saveAssociated($this->request->data)) {
                return $this->_successResponse(__('The warehouse has been saved.'), ['action' => 'index']);
            }
            $this->setFlash(__('The warehouse could not be saved. Please, try again.'), 'error');
        }
    }

    public function saveDistributor($id = null)
    {
        $this->autoRender = false;

        if (!$this->Warehouse->exists($id)) {
            throw new NotFoundException(__('Invalid warehouse'));
        }

        if ($this->request->is(['post', 'put'])) {
            $distributorIds = (array)($this->request->data['WarehouseDistributor'][$id]['distributor_id'] ?? []);

            $data = [
                'Warehouse' => ['id' => $id],
                // See: https://book.cakephp.org/2.0/en/models/saving-your-data.html#saving-related-model-data-habtm
                'Distributor' => ['Distributor' => $distributorIds],
            ];
            if ($this->Warehouse->saveAssociated($data)) {
                return $this->_successResponse('Distributor data has been saved successfully');
            } else {
                return $this->_exceptionResponse(new InternalErrorException('Failed to save distributor data'));
            }
        }
    }

    public function edit($id = null)
    {
        if (!$this->Warehouse->exists($id)) {
            throw new NotFoundException(__('Invalid warehouse'));
        }
        if ($this->request->is(['post', 'put'])) {
            $user_id = $this->Auth->user('id');
            $this->request->data['Warehouse']['user_id'] = $user_id;
            if ($this->Warehouse->save($this->request->data)) {
                return $this->_successResponse(__('The warehouse has been saved.'), ['action' => 'index']);
            }
            $this->setFlash(__('The warehouse could not be saved. Please, try again.'), 'error');
        } else {
            $this->request->data = $this->Warehouse->find('first', [
                'recursive' => -1,
                'conditions' => ['Warehouse.' . $this->Warehouse->primaryKey => $id],
                'fields' => [
                    'Warehouse.id',
                    'Warehouse.user_id',
                    'Warehouse.source_id',
                    'Warehouse.name',
                    'Warehouse.is_active',
                    'Warehouse.created_at',
                    'Warehouse.updated_at',
                ],
            ]);
        }
    }

    public function delete($id = null)
    {
        if (!$this->Warehouse->exists($id)) {
            throw new NotFoundException(__('Invalid warehouse'));
        }

        $this->request->allowMethod('post', 'delete');

        if ($this->Warehouse->removeAllByIds([$id])) {
            $this->_successResponse(__('The warehouse has been deleted.'), ['action' => 'index']);
        } else {
            $this->setFlash(__('The warehouse could not be deleted. Please, try again.'), 'error');
        }

        return $this->redirect(['action' => 'index']);
    }
}
