<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppController', 'Controller');
App::uses('Discount', 'Model');
App::uses('OrderAction', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('ProductSellDirect', 'Utility');
App::uses('TimelineEvent', 'Lib');

/**
 * Class OrdersController
 *
 * @property CurrencyComponent $Currency
 * @property IndexQueryHandlerComponent $IndexQueryHandler
 * @property FulfillmentLogicComponent $FulfillmentLogic
 * @property LightspeedComponent $Lightspeed
 * @property NotificationLogicComponent $NotificationLogic
 * @property OrderLogicComponent $OrderLogic
 * @property OrderPlacerComponent $OrderPlacer
 * @property PhpExcelComponent $PhpExcel
 * @property SpoutComponent $Spout
 * @property QuickbookComponent $Quickbook
 * @property ShippingCalculatorComponent $ShippingCalculator
 * @property ShopifyComponent $Shopify
 * @property WoocommerceComponent $Woocommerce
 * @property ShopifyPOSComponent $ShopifyPOS
 * @property StripeComponent $Stripe
 * @property VendPOSComponent $VendPOS
 * @property UploadComponent $Upload
 *
 * @property B2bShippingRate $B2bShippingRate
 * @property Btask $Btask
 * @property Contact $Contact
 * @property Contactpersons $Contactpersons
 * @property Courier $Courier
 * @property Customer $Customer
 * @property DealerOrder $DealerOrder
 * @property DealerOrderProduct $DealerOrderProduct
 * @property DealerOrderRefund $DealerOrderRefund
 * @property Discount $Discount
 * @property EmailTemplate $EmailTemplate
 * @property Fulfillment $Fulfillment
 * @property InventoryTransfer $InventoryTransfer
 * @property InventoryTransferProduct $InventoryTransferProduct
 * @property InventoryTransferProductReservation $InventoryTransferProductReservation
 * @property MailQueue $MailQueue
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property ManufacturerSalesRep $ManufacturerSalesRep
 * @property Notification $Notification
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property OrderRefund $OrderRefund
 * @property OrderRefundProduct $OrderRefundProduct
 * @property OrderComment $OrderComment
 * @property OrderCustomerMessage $OrderCustomerMessage
 * @property OrderSalesRep $OrderSalesRep
 * @property OrderTag $OrderTag
 * @property OrderTagName $OrderTagName
 * @property Product $Product
 * @property ProductStateFee $ProductStateFee
 * @property ProductTier $ProductTier
 * @property QuickbookProduct $QuickbookProduct
 * @property LegacyRetailerCredit $LegacyRetailerCredit
 * @property RetailerCreditTerm $RetailerCreditTerm
 * @property Store $Store
 * @property StripeUser $StripeUser
 * @property User $User
 * @property UserSchedule $UserSchedule
 * @property UserSetting $UserSetting
 * @property Warehouse $Warehouse
 * @property WarehouseProduct $WarehouseProduct
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class OrdersController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Orders';
    /**
     * @var array
     */
    public $components = array(
        'Currency',
        'IndexQueryHandler' => [
            'defaultQuery' => [
                'sort' => 'Order.display_date',
                'direction' => 'DESC',
                'limit' => '50',
                'search' => '',
                'daterange' => '',
                'store' => '',
                'brand' => '',
                'distributor' => '',
                'tag' => '',
                'order_type' => '',
                'order_status' => '',
                'payment_status' => '',
                'fulfillment_status' => '',
                'payment_method' => '',
            ],
        ],
        'FulfillmentLogic',
        'Lightspeed',
        'NotificationLogic',
        'OrderLogic',
        'OrderPlacer',
        'PhpExcel',
        'Quickbook.Quickbook',
        'ShippingCalculator',
        'Shopify.Shopify',
        'Shopifypos.ShopifyPOS',
        'Spout',
        'Stripe.Stripe',
        'Upload',
        'Vendpos.VendPOS',
        'Woocommerce.Woocommerce',
    );

    /**
     * @var array
     */
    public $uses = array(
        'B2bShippingRate',
        'Btask',
        'Contact',
        'Contactpersons',
        'Courier',
        'Customer',
        'DealerOrder',
        'DealerOrderProduct',
        'DealerOrderRefund',
        'Discount',
        'EmailTemplate',
        'Fulfillment',
        'InventoryTransfer',
        'InventoryTransferProduct',
        'InventoryTransferProductReservation',
        'MailQueue',
        'ManufacturerRetailer',
        'ManufacturerSalesRep',
        'Notification',
        'Order',
        'OrderComment',
        'OrderCustomerMessage',
        'OrderProduct',
        'OrderRefund',
        'OrderRefundProduct',
        'OrderSalesRep',
        'OrderTag',
        'OrderTagName',
        'Product',
        'ProductStateFee',
        'ProductTier',
        'Quickbook.QuickbookProduct',
        'LegacyRetailerCredit',
        'RetailerCreditTerm',
        'Store',
        'StripeUser',
        'User',
        'UserSchedule',
        'UserSetting',
        'Warehouse',
        'WarehouseProduct',
        'WarehouseProductReservation',
    );

    /**
     *
     */
    public function beforeFilter()
    {
        $this->Auth->allow('aftershipwebhook');
        parent::beforeFilter();
        $this->Auth->deny('*');
    }

    /**
     * Export order details based on current filter
     */
    public function export()
    {
        $this->autoRender = false;

        $orderswithproduct_export = ($this->request->param('filter') === 'orderswithproduct');

        $this->request->query = $this->_extractIndexQueryParams($this->request);

        $conditions = $this->_orderIndexConditions(
            $this->getAuthUserIds(),
            $this->request->param('filter'),
            $this->request->query,
            $this->request->param('cid')
        );

        if (!$this->Order->countAllForIndex($conditions)) {
            return $this->_exceptionResponse(new NotFoundException(), 'No orders found');
        }

        $salesRepColumnLabels = $this->OrderSalesRep->findSalesRepExportLabels($conditions);

        $tableModel = [
            $this->PhpExcel->newExportColumn(__('Order ID'), function($order) {
                return $order['Order']['orderID'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('eCom ID'), function($order) {
                return $this->Order->getSourceOrderName($order);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Internal Order #'), function($order) {
                return $order['Order']['external_invoice_id'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Order Date'), function($order) {
                return format_datetime($order['Order']['created_at'], 'Y-m-d');
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Order Status'), function($order) {
                return $order['Order']['order_status'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Fulfill Type'), function($order) {
                return OrderType::getFulfillType($order['Order']['order_type'], $order['Order']['subType']);
            }, ['filter' => true, 'wrap' => true]),
            $this->PhpExcel->newExportColumn(__('Fulfillment Status'), function($order) {
                return $order['Order']['fulfillment_status'];
            }, ['filter' => true, 'wrap' => true]),
            $this->PhpExcel->newExportColumn(__('Retailer'), function($order) {
                return $order['User']['company_name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Account ID'), function($order) {
                return $order['ManufacturerRetailer']['external_retailer_account'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Distributor'), function($order) {
                return $order['Distributor']['company_name'];
            }, ['filter' => true]),
        ];
        foreach ($salesRepColumnLabels as $idx => $label) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn($label, function($order) use ($idx) {
                    return $order['SalesRep'][$idx]['company_name'] ?? null;
                }, ['filter' => true]),
            ]);
        };
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn(__('Currency'), function($order) {
                return $order['Order']['currency_code'];
            }, ['filter' => true]),
        ]);
        if ($orderswithproduct_export) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn(__('Product Title'), function($order, $orderProduct) {
                    return $orderProduct['Product']['product_title'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('Product SKU'), function($order, $orderProduct) {
                    return $orderProduct['Product']['product_sku'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('UPC'), function($order, $orderProduct) {
                    return $orderProduct['Product']['product_upc'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('Category'), function($order, $orderProduct) {
                    return $orderProduct['Product']['product_type'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('Quantity'), function($order, $orderProduct) {
                    return (int)$orderProduct['quantity'];
                }, ['filter' => true, 'data_type' => 'number']),
            ]);
        }
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn(__('Gross Sales'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['gross_sales']);
                } else {
                    return format_number($orderProduct['total_price']);
                }
            }, ['filter' => true, 'data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Discounts'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['total_discount']);
                } else {
                    return format_number($orderProduct['total_discount']);
                }
            }, ['filter' => true, 'data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Refunds'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['gross_refunds']);
                } else {
                    return format_number($orderProduct['gross_refunds']);
                }
            }, ['filter' => true, 'data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Net Sales'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['net_sales']);
                } else {
                    return format_number($orderProduct['net_sales']);
                }
            }, ['filter' => true, 'data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Tax'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['total_tax']);
                } else {
                    return format_number($orderProduct['total_tax']);
                }
            }, ['data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Shipping'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['shipping_amount']);
                } else {
                    return '';
                }
            }, ['data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Total'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['total_price'] - $order['Order']['total_discount'] - $order['OrderRefundTotals']['total_amount']);
                } else {
                    return format_number($orderProduct['net_sales'] + ($order['Order']['tax_included'] ? $orderProduct['total_tax'] : 0.00));
                }
            }, ['data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Service Fee'), function($order, $orderProduct = null) {
                if (!$orderProduct) {
                    return format_number($order['Order']['total_retailer_commission']);
                } else {
                    return format_number($orderProduct['total_retailer_commission']);
                }
            }, ['data_type' => 'number']),
            $this->PhpExcel->newExportColumn(__('Processing fees'), function($order) {
                return format_number($order['Order']['stripe_fees']);
            }),
            $this->PhpExcel->newExportColumn(__('ShipEarly fees'), function($order) {
                return format_number($order['Order']['shipearlyFees'] - $order['OrderRefundTotals']['total_fees']);
            }),
            $this->PhpExcel->newExportColumn(__('Customer'), function($order) {
                return trim($order['Customer']['firstname'] . ' ' . $order['Customer']['lastname']);
            }),
            $this->PhpExcel->newExportColumn(__('Customer Email'), function($order) {
                return $order['Order']['customerEmail'];
            }),
            $this->PhpExcel->newExportColumn(__('Shipping Address'), function($order) {
                return $order['Order']['shipping_address1'];
            }),
            $this->PhpExcel->newExportColumn(__('City'), function($order) {
                return $order['Order']['shipping_city'];
            }),
            $this->PhpExcel->newExportColumn(__('State/Province'), function($order) {
                return $order['Order']['shipping_state'];
            }),
            $this->PhpExcel->newExportColumn(__('Zip Code'), function($order) {
                return $order['Order']['shipping_zipcode'];
            }),
            $this->PhpExcel->newExportColumn(__('Country'), function($order) {
                return $order['Order']['shipping_country'];
            }),
            $this->PhpExcel->newExportColumn(__('Billing Name'), function($order) {
                return $order['Order']['customer_name'];
            }),
            $this->PhpExcel->newExportColumn(__('Billing Address'), function($order) {
                return '';
            }),
            $this->PhpExcel->newExportColumn(__('Billing City'), function($order) {
                return '';
            }),
            $this->PhpExcel->newExportColumn(__('Billing State/Province'), function($order) {
                return '';
            }),
            $this->PhpExcel->newExportColumn(__('Billing Zip Code'), function($order) {
                return '';
            }),
            $this->PhpExcel->newExportColumn(__('Billing Country'), function($order) {
                return '';
            }),
            $this->PhpExcel->newExportColumn(__('Payment status'), function($order) {
                return OrderPaymentStatus::getLabel((int)$order['Order']['payment_status']);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Tags'), function($order) {
                return implode(', ', Hash::extract($order['OrderTagName'] ?? [], '{n}.name'));
            }, ['filter' => true]),
        ]);

        $fileName = sprintf('%s %s %s.xlsx',
            $this->Auth->user('company_name'),
            ($orderswithproduct_export) ? 'Product Sales' : 'ShipEarly Orders',
            date('Y-m-d')
        );

        $this->Spout->doWithOpenWriter($fileName, function() use ($conditions, $orderswithproduct_export, $tableModel) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));

            $this->Order->streamExportData($conditions, function($order) use ($orderswithproduct_export, $tableModel) {
                if (!$orderswithproduct_export) {
                    $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $order));
                } else {
                    $this->Spout->addRows(array_map(
                        function($orderProduct) use ($tableModel, $order) {
                            return $this->PhpExcel->processExportColumns($tableModel, $order, $orderProduct);
                        },
                        $order['OrderProduct']
                    ));
                }
            });
        });
    }

    function getOrderTagUserId($order) {
        $userType = $this->Auth->user('user_type');
        $orderTagUserId = null;
        if (in_array($userType, User::TYPES_BRAND, true)) {
            $orderTagUserId = $order['Order']['user_id'];
        } elseif (in_array($userType, User::TYPES_RETAILER, true)) {
            $orderTagUserId = $order['Retailer']['Branch'] ?: $order['Order']['retailer_id'];
        }

        return $orderTagUserId;
    }

    /**
     * Update Order details through popup
     * To update InStore secret code
     * To update ShipToStore Tracking code
     * To cancel orders
     */
    public function edit($id = null)
    {
        if (!empty($this->request->data)) {
            $this->autoRender = false;
            $this->_webServiceLog($this->request->data);
            return $this->_ajax_edit_post($id);
        }

        $this->_validateOrderForEditGet($id);

        // Dealer Order and NTC popup

        $order = $this->Order->findForPopup($id);

        $inventory = array();
        if ($order['Order']['order_status'] == OrderStatus::NEED_TO_CONFIRM) {
            $order['Order']['dealer_qty_ordered'] = $this->ProductTier->calcNewDealerOrderPricing($order);

            $productIds = $this->OrderProduct->listProductIds($order['Order']['id']);
            $inventory = $this->getInventory($productIds, $order['Order']['user_id'], $order['Order']['retailer_id']);
        }
        $this->set('inventory', $inventory);

        $refunds = $this->OrderRefund->findForOrderTimeline($id);
        $shippingLabel = $this->_shippingLabel($order);
        $this->set('timeline_logs', $this->_timeline_logs($order, $refunds, $shippingLabel));

        $orderTagUserId = $this->getOrderTagUserId($order);

        /** @var RetailerCredit $RetailerCredit */
        $RetailerCredit = ClassRegistry::init('RetailerCredit');

        $this->set('existingTagOptions', $this->OrderTag->getTagsByOrder($id, $orderTagUserId));
        $this->set('tagOptions', $this->OrderTagName->getOrderTags($orderTagUserId));
        $this->set('CustomerAddress', $this->_formatOrderCustomerAddress($order));
        $this->set('BillingAddress', $this->_formatOrderAddress($order, 'BillingAddress'));
        $this->set('RetailerAddress', $this->User->formatUserAddress($order['Order']['retailer_id']));
        $this->set('BrandAddress', $this->User->formatUserAddress($order['Order']['user_id']));
        $this->set('invoiceLink', $this->_getInvoicePdfLink($id, $order['Order']['order_type']));
        $this->set('customTitles', $this->User->findEcommerceCustomContent($order['Order']['user_id']));
        $this->set('outstanding', $RetailerCredit->findTotals($order['Order']['user_id'], $order['Order']['retailer_id']));
        $this->_setDealerOrderPopupView($order);
    }

    protected function _ajax_edit_post($orderId = null)
    {
        $FlashError = $FlashSuccess = '';
        try {
            $userId = $this->Auth->user('id');
            if (empty($orderId)) {
                $orderId = $this->request->data['Order']['id'];
            }

            try {
                $this->OrderLogic->isAuthorizedForOrder($orderId);
                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
            } catch (NotFoundException $e) {
                $FlashError = 'Error: Order not found';
                throw $e;
            } catch (ForbiddenException $e) {
                $FlashError = "Error: You do not have permission to change this order";
                throw $e;
            }

            $Orderinfo = $this->Order->record($orderId);
            if (!in_array($Orderinfo['Order']['order_type'], [OrderType::IN_STORE_PICKUP, OrderType::LOCAL_DELIVERY, OrderType::SHIP_FROM_STORE, OrderType::WHOLESALE])) {
                throw new BadRequestException('Unhandled order type ' . json_encode([
                    'Order' => array_intersect_key($Orderinfo['Order'], array_flip(['id', 'orderID', 'order_type'])),
                    'request' => $this->request->data,
                ]));
            }

            $isRelatedRetailer = in_array($userId, $this->User->listAllRelatedStoreIds($Orderinfo['Order']['retailer_id']));

            $refundTotals = $this->OrderRefund->getTotalsByOrder($orderId);
            $orderTotal = (float)$Orderinfo['Order']['total_price'] - (float)$Orderinfo['Order']['total_discount'] - (float)$refundTotals['OrderRefund']['total_amount'];
            $applicationFees = (float)$Orderinfo['Order']['shipearlyFees'] - (float)$refundTotals['OrderRefund']['total_fees'];
            $retailerAmount = $orderTotal - $applicationFees;
            $compensationForLabour = (float)$this->request->data('compensation_for_labor') ?: 0.00;
            $compensationForLabourPaymentMethod = (string)$this->request->data('labor_payment_method') ?: null;

            if ($Orderinfo['Order']['order_status'] === OrderStatus::NEED_TO_CONFIRM && $isRelatedRetailer) {
                // Need To Confirm In Stock
                if (!$this->_splitpayment($orderId, $retailerAmount)) {
                    throw new InternalErrorException("Split payment failure " . json_encode(['order_id' => $orderId, 'splitpayment_amount' => $retailerAmount]));
                }
                $nextStatus = OrderStatus::getOrderStatusObject(
                    $this->Order->isDoubleShipOrder(
                        $Orderinfo['Order']['id']
                    )
                )->getNextOrderStatus(
                    $Orderinfo['Order']['order_type'],
                    OrderType::SUB_TYPE_STOCK,
                    $Orderinfo['Order']['order_status'],
                    OrderAction::CONFIRM
                );
                $success = $this->Order->save(array(
                    'id' => $orderId,
                    'order_status' => $nextStatus,
                    'subType' => OrderType::SUB_TYPE_STOCK,
                    'updated_at' => date('Y-m-d H:i:s'),
                ));
                if ($success) {
                    $FlashSuccess = "Delivery date sent to customer via email notification";
                    if ($Orderinfo['Order']['order_type'] == OrderType::SHIP_FROM_STORE) {
                        $FlashSuccess = "Enter Shipment Tracking to Release Order and Notify Consumer of Shipment";
                    }
                    // Release any warehouse reservations because they are currently only for out-of-stock orders.
                    $this->WarehouseProductReservation->release($orderId);
                    $this->NotificationLogic->sendInStockEmailsForOrder($orderId);
                } else {
                    throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors]));
                }
            } elseif ($Orderinfo['Order']['is_dealerorder']) {
                $dealerOrderId = $this->DealerOrder->field('id', ['order_id' => $orderId]);
                if (!$dealerOrderId) {
                    $dealerProducts = $this->_flattenDealerProductsRequest($this->request->data);

                    $confirmedDealerProducts = array_map(
                        function(array $item): array {
                            unset($item['unfulfilled_quantity']);

                            return $item;
                        },
                        array_filter($dealerProducts, fn(array $item): bool => $item['quantity'] > 0)
                    );
                    if (!$confirmedDealerProducts) {
                        $FlashError = 'The order cannot be confirmed with no items to fulfill';
                        throw new BadRequestException($FlashError . ' ' . json_encode([
                            'Order' => array_intersect_key($Orderinfo['Order'], array_flip(['id', 'orderID', 'user_id', 'retailer_id', 'order_type', 'order_status', 'is_dealerorder'])),
                            'User' => array_intersect_key($this->Auth->user(), array_flip(['id', 'Branch', 'email_address', 'user_type'])),
                            'request' => array_intersect_key($this->request->data['Order'], array_flip(['dealer_quantity', 'extra_quantity'])),
                        ]));
                    }
                    $confirmedShippingAmount = (float)$this->request->data['Order']['dealer_shipping_amount'];

                    if ($Orderinfo['Order']['order_type'] === OrderType::WHOLESALE) {
                        $unconfirmedDealerProducts = array_map(
                            function(array $item): array {
                                $item['quantity'] = $item['unfulfilled_quantity'];
                                unset($item['unfulfilled_quantity']);

                                return $item;
                            },
                            array_filter($dealerProducts, fn(array $item): bool => $item['unfulfilled_quantity'] > 0)
                        );
                        if ($unconfirmedDealerProducts) {
                            $discountData = $this->getDiscountDataForOrder($orderId);
                            $enableFreeFreight = $this->Discount->shouldApplyFreeFreightFromDealerProducts((int)$Orderinfo['Order']['user_id'], $discountData, $unconfirmedDealerProducts);
                            $unconfirmedShippingAmount = (!$enableFreeFreight)
                                ? $this->ShippingCalculator->calculateShippingFromDealerProducts($Orderinfo['Order']['user_id'], $Orderinfo['Order']['retailer_id'], $unconfirmedDealerProducts)
                                : 0.00;
                            $unconfirmedOrderId = $this->Order->splitUnconfirmedPurchaseOrder(
                                $orderId,
                                $confirmedDealerProducts,
                                (float)$this->request->data['DealerOrder']['dealerDiscount'],
                                $confirmedShippingAmount,
                                $unconfirmedDealerProducts,
                                0.00,
                                $unconfirmedShippingAmount
                            );
                            if (!$unconfirmedOrderId) {
                                $message = 'Failed to partially confirm order ' . json_encode($Orderinfo['Order']['orderID']);
                                $FlashError = $FlashError ? ($FlashError . '<br />' . $message) : $message;
                                throw new InternalErrorException($message . ' ' . json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                            }

                            $this->NotificationLogic->sendOrderNotifications('Partially confirmed purchase order ' . $Orderinfo['Order']['orderID'], Notification::TYPE_PURCHASE_ORDER, $unconfirmedOrderId);

                            if (!$this->OrderPlacer->splitUnconfirmedPurchaseOrderPayment($orderId, $unconfirmedOrderId)) {
                                $message = 'An error occurred while splitting the payment for order ' . json_encode($Orderinfo['Order']['orderID']);
                                $FlashError = $FlashError ? ($FlashError . '<br />' . $message) : $message;
                                // Show an error flash but continue to confirm the original order
                            }

                            $Orderinfo = $this->Order->find('first', [
                                'conditions' => ['Order.id' => $orderId],
                                'contain' => ['Retailer', 'OrderSalesRep']
                            ]);
                        }
                    }

                    $rateId = (int)$this->request->data('DealerOrder.b2b_shipping_rate_id');
                    $shippingName = (string)(
                        $this->request->data('DealerOrder.shipping_name') ?:
                        ($rateId ? $this->B2bShippingRate->fieldByConditions('name', ['B2bShippingRate.id' => $rateId]) : '')
                    ) ?: null;

                    $dealerOrderId = $this->_confirmDealerOrderPricing(
                        (int)$orderId,
                        $retailerAmount,
                        $confirmedDealerProducts,
                        (float)$this->request->data['DealerOrder']['total_discount'],
                        $confirmedShippingAmount,
                        $shippingName,
                        $compensationForLabour,
                        $compensationForLabourPaymentMethod,
                    );
                    $FlashSuccess = "Wholesale pricing confirmed for {$Orderinfo['Order']['orderID']}";
                    $this->NotificationLogic->sendPricingConfirmationEmail($orderId);

                    if ($this->request->data['Order']['place_ecommerce_order']) {
                        if (!$this->OrderPlacer->createEcommerceDealerOrder($dealerOrderId)) {
                            $message = __('Failed placing the wholesale order for %s in the ecommerce platform.', $Orderinfo['Order']['orderID']);
                            CakeLog::warning($message);

                            $message .= $this->OrderPlacer->buildCreateEcommerceDealerOrderRetryLink($dealerOrderId);

                            $FlashError = $FlashError ? $FlashError . '<br />' . $message : $message;
                        } else {
                            $FlashSuccess .= '<br />' . "Wholesale order for {$Orderinfo['Order']['orderID']} placed in the ecommerce platform.";
                        }
                    }
                } else {
                    $this->_triggerDeprecatedShipmentWarning((int)$orderId, 'Ship Without Tracking for dealer orders');

                    // Ship Without Tracking Number
                    // Also called after dealerOrderShipmentTracking by front end
                    $this->DealerOrder->markShippedToDealer((int)$dealerOrderId);
                    $this->NotificationLogic->sendNonStockEmailsForOrder((int)$orderId);
                    $FlashSuccess = "Delivery date sent to customer via email notification";
                }
            } else {
                throw new BadRequestException('Unhandled order status ' . json_encode([
                    'user_is_retailer' => $isRelatedRetailer,
                    'Order' => array_intersect_key($Orderinfo['Order'], array_flip(['id', 'orderID', 'user_id', 'retailer_id', 'order_type', 'order_status', 'is_dealerorder'])),
                    'User' => array_intersect_key($this->Auth->user(), array_flip(['id', 'Branch', 'email_address', 'user_type'])),
                    'request' => $this->request->data,
                ]));
            }
        } catch (Exception $e) {
            CakeLog::error($e);
            $FlashSuccess = '';
            if (empty($FlashError)) {
                $orderID = isset($Orderinfo['Order']['orderID']) ? (' ' . $Orderinfo['Order']['orderID']) : '';
                $errorDescription = $this->getFlash('error') ?: 'Please try again.';
                $FlashError = "Error processing order{$orderID}. {$errorDescription}";
            }
            unset($this->request->query['referer']);
        }

        if ($FlashSuccess) {
            $this->setFlash($FlashSuccess, 'success');
        }
        if ($FlashError) {
            $this->setFlash($FlashError, 'error');
        }
        if ($this->request->is('ajax')) {
            $this->response->body(json_encode(['error' => $FlashError, 'success' => $FlashSuccess]));
            return $this->response;
        }
        return $this->redirect($this->referer(null, true));
    }

    private function getDiscountDataForOrder(int $orderId): array
    {
        $this->Order->bindModel([
            'belongsTo' => ['Discount'],
        ], false);

        $order = $this->Order->get($orderId, [
            'contain' => [
                'Discount' => [
                    'fields' => Discount::CALCULATION_REQUIRED_FIELDS,
                    'DiscountRule' => ['fields' => DiscountRule::CALCULATION_REQUIRED_FIELDS],
                ],
            ],
            'fields' => ['id'],
        ]);

        $this->Order->unbindModel([
            'belongsTo' => ['Discount'],
        ], false);

        return $order['Discount'] ?? [];
    }

    /**
     * @param int $orderId
     * @param float $retailerAmount
     * @param array $dealerProducts
     * @param float $totalDiscount
     * @param float $shippingAmount
     * @param string|null $shippingName
     * @return int $dealerOrderId
     * @throws \Stripe\Exception\ApiErrorException
     * @throws HttpException
     */
    private function _confirmDealerOrderPricing(int $orderId, float $retailerAmount, array $dealerProducts, float $totalDiscount, float $shippingAmount, ?string $shippingName, float $compensationForLabour, ?string $compensationForLabourPaymentMethod): int
    {
        $order = $this->Order->findForConfirmPricing($orderId);
        $userId = (int)$order['Order']['user_id'];

        $dealerProducts = $this->ProductStateFee->applyToDealerProducts($order, $dealerProducts);

        $dealerProducts = $this->Discount->calculateDealerProductDiscounts($userId, (array)$order['Discount'], $dealerProducts);
        $calculatedDiscount = $this->Discount->calculateTotalDiscount(array_column($dealerProducts, 'discount'));
        $manualDiscount = (float)max($totalDiscount - $calculatedDiscount, 0);
        $dealerProducts = Discount::applyManualDiscountToDealerProducts($manualDiscount, $dealerProducts);

        $data = $this->DealerOrder->buildCreateFromConfirmedOrderData($order, $dealerProducts, $totalDiscount, $shippingAmount, $shippingName);
        $dealerTotalAmount = (float)$data['DealerOrder']['total_price'];

        if ($order['Order']['order_type'] === OrderType::WHOLESALE) {
            $this->OrderPlacer->chargePurchaseOrder($orderId, $dealerTotalAmount);

            $currencyConversion = ((float)$order['Order']['totalPriceConversion'] > 0 && (float)$order['Order']['total_price'] > 0)
                ? (float)$order['Order']['totalPriceConversion'] / (float)$order['Order']['total_price']
                : 1.0;

            $dealer_qty_ordered = json_decode($data['Order']['dealer_qty_ordered'], true);
            $b2bTaxPercent = $data['DealerOrder']['tax'];
            $totalTaxAmount = $data['DealerOrder']['total_tax'];

            if (!$this->Order->updateConfirmedPurchaseOrder($orderId, $dealer_qty_ordered, $dealerProducts, $totalDiscount, $b2bTaxPercent, $shippingAmount, $totalTaxAmount, $currencyConversion)) {
                throw new InternalErrorException("Failed to update order " . json_encode(['validationErrors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
            }
            if ($order['Order']['subType'] === OrderType::SUB_TYPE_B2B_WARRANTY) {
                /** @var RetailerCreditVoucher $RetailerCreditVoucher */
                $RetailerCreditVoucher = ClassRegistry::init('RetailerCreditVoucher');

                $order['Order']['labor_compensation_payment_method'] = $compensationForLabourPaymentMethod;
                $order['Order']['total_labor_compensation'] = $compensationForLabour;
                if (!$this->Order->save($order)) {
                    throw new InternalErrorException("Failed to save order with updated total_retailer_commission.");
                }
                if ($compensationForLabourPaymentMethod === 'Credit') {
                    /** @var ShipearlyHelper $Helper */
                    $Helper = (new View($this))->loadHelper('Shipearly');

                    $description = 'Warranty' . ' ' . $Helper->orderLink(
                        $order['Order']['id'],
                        $order['Order']['orderID'],
                        ($order['Retailer']['user_type'] === User::TYPE_RETAILER && $order['Order']['order_status'] === OrderStatus::NEED_TO_CONFIRM)
                    );
                    $RetailerCreditVoucher->saveVoucher($order['Order']['user_id'], $order['Retailer']['Branch'] ?: $order['Order']['retailer_id'], $compensationForLabour, $order['Order']['user_id'], $description);
                }
            }
        } else {
            $wholesale_charge_amount = $data['DealerOrder']['wholesale_charge_amount'];
            $isSplitPaymentOrder = $data['DealerOrder']['is_split_payment'];

            $splitpaymentAmount = $retailerAmount + $wholesale_charge_amount;
            if($isSplitPaymentOrder){
                $splitpaymentAmount -= $dealerTotalAmount;
            }
            if (!$this->_splitpayment($orderId, $splitpaymentAmount)) {
                throw new InternalErrorException("Split payment failure " . json_encode(['order_id' => $orderId, 'splitpayment_amount' => $splitpaymentAmount]));
            }
        }

        if (!$this->DealerOrder->createFromConfirmedOrderData($data)) {
            throw new InternalErrorException("Failed to create DealerOrder " . json_encode(['errors' => $this->DealerOrder->validationErrors, 'data' => $this->DealerOrder->data]));
        }

        return (int)$this->DealerOrder->id;
    }

    private function _flattenDealerProductsRequest(array $requestData, bool $filterZeroQuantityItems = true): array
    {
        $dealerProducts = array_reduce(
            array_keys($requestData['Order']['dealer_quantity']),
            function ($list, $warehouseId) use ($requestData) {
                $_dealer_quantity = $requestData['Order']['dealer_quantity'][$warehouseId];
                $_dealer_price = $requestData['Order']['dealer_price'][$warehouseId];
                $_inventory_transfer_id = $requestData['Order']['inventory_transfer_id'][$warehouseId] ?? [];
                $_extra_quantity = $requestData['Order']['extra_quantity'][$warehouseId] ?? [];
                $_restock_date = $requestData['Order']['restock_date'][$warehouseId] ?? [];

                foreach (array_keys($_dealer_quantity) as $productId) {
                    $quantity = (int)$_dealer_quantity[$productId];
                    $list[] = array(
                        'product_id' => $productId,
                        'warehouse_id' => $warehouseId ?: null,
                        'inventory_transfer_id' => $_inventory_transfer_id[$productId] ?? null,
                        'quantity' => $quantity,
                        'dealer_price' => round($_dealer_price[$productId], 2),
                        'unfulfilled_quantity' => isset($_extra_quantity[$productId])
                            ? (int)max((int)$_extra_quantity[$productId] - $quantity, 0)
                            : 0,
                        'restock_date' => $_restock_date[$productId] ?? null,
                    );
                }
                return $list;
            },
            array()
        );
        return $filterZeroQuantityItems ? array_filter($dealerProducts, function($product) { return $product['quantity'] > 0 || $product['unfulfilled_quantity'] > 0; }) : $dealerProducts;
    }

    private function _validateOrderForEditGet($id)
    {
        $log = $this->OrderLogic->isAuthorizedForOrder($id);

        $showEditViewToRetailer = (
            $this->Auth->user('user_type') === User::TYPE_RETAILER &&
            in_array($log['Order']['order_status'], [OrderStatus::NEED_TO_CONFIRM], true)
        );

        $showEditViewToAnyone = (bool)$log['Order']['is_dealerorder'];

        if (!$showEditViewToRetailer && !$showEditViewToAnyone) {
            $filter = ($log['Order']['order_type'] === OrderType::WHOLESALE) ? 'dealerorderTable' : null;
            $this->redirect(['controller' => 'orders', 'action' => 'invoice', 'id' => $id, 'filter' => $filter]);
        }

        return $log;
    }

    public function dealerorder_totals($id = null)
    {
        $log = $this->OrderLogic->isAuthorizedForOrder($id);
        $this->request->allowMethod('post', 'put');
        $authUser = $this->Auth->user();

        $id = (int)$id;
        $brandId = (int)$log['Order']['user_id'];
        $retailerId = (int)$log['Order']['retailer_id'];

        if (
            $log['Order']['order_type'] === OrderType::WHOLESALE &&
            in_array($authUser['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF], true) &&
            $this->Permissions->userHasPermission($authUser, Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT)
        ) {
            if ($log['Order']['order_status'] === OrderStatus::PURCHASE_ORDER) {
                $this->_editPurchaseOrder($id, $brandId, $retailerId, $this->request->data);
            } elseif ($log['Order']['order_status'] === OrderStatus::PROCESSING) {
                $this->_editWholesaleProcessingInventoryTransfers($id, $this->request->data);
            }
        }

        $order = $this->Order->findForPopup($id);

        $dealerProducts = $this->_flattenDealerProductsRequest($this->request->data, false);
        $dealerProducts = $this->ProductStateFee->applyToDealerProducts($order, $dealerProducts);

        $requestLineItems = Order::extractProductDetailsProducts($dealerProducts);

        // Keep line items that were removed from the request so they can be refund adjusted
        $placeholderLineItems = array_map(
            function($product) {
                $product['quantity'] = 0;
                return $product;
            },
            (array)Hash::get($order, 'Order.dealer_qty_ordered.products')
        );

        $order['Order']['dealer_qty_ordered']['products'] = $requestLineItems + $placeholderLineItems;

        // Add refunded quantities which will be subtracted later
        foreach ($order['OrderProduct'] as $orderProduct) {
            $productId = $orderProduct['product_id'];
            if (isset($order['Order']['dealer_qty_ordered']['products'][$productId]['quantity'])) {
                $order['Order']['dealer_qty_ordered']['products'][$productId]['quantity'] += array_sum(
                    array_column($orderProduct['OrderRefundProduct'], 'quantity')
                );
            }
        }

        if ($order['Order']['order_type'] === OrderType::WHOLESALE) {
            $existingOrderProducts = Hash::combine($order['OrderProduct'], '{n}.product_id', '{n}', '{n}.warehouse_id');

            // Override OrderProducts to be used for shipping by warehouse
            $order['OrderProduct'] = array_map(function ($requestOrderProduct) use ($existingOrderProducts) {
                $warehouseId = (int)$requestOrderProduct['warehouse_id'];
                $productId = (int)$requestOrderProduct['product_id'];
                $quantity = (int)$requestOrderProduct['quantity'];
                $unfulfilled_quantity = (int)$requestOrderProduct['unfulfilled_quantity'];
                $dealerPrice = (float)$requestOrderProduct['dealer_price'];

                $overrides = array(
                    'warehouse_id' => $warehouseId,
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'unfulfilled_quantity' => $unfulfilled_quantity,
                    'total_price' => $quantity * $dealerPrice,
                );

                return array_merge($existingOrderProducts[$warehouseId][$productId], $overrides);
            }, $dealerProducts);

            $order['Order']['total_discount'] = $this->request->data['DealerOrder']['total_discount'];
        }

        $order['Order']['dealer_qty_ordered']['b2b_tax'] = $this->request->data['Order']['b2b_tax_rate'];
        if (isset($this->request->data['Order']['dealer_shipping_amount'])) {
            $order['Order']['dealer_qty_ordered']['shipping_amount'] = $this->request->data['Order']['dealer_shipping_amount'];

            // Set to 'Custom'
            $this->request->data['DealerOrder']['b2b_shipping_rate_id'] = '';
        }

        $order = $this->Order->calculateOrderRefundFields($order);
        $order['OrderProduct'] = $this->Discount->calculateOrderProductDiscounts($brandId, (array)$order['Discount'], (array)$order['OrderProduct']);
        $order['Order']['dealerDiscount'] = $this->request->data['DealerOrder']['dealerDiscount'];
        $this->_setDealerOrderPopupView($order);

        return $this->render('/Elements/Orders/dealerorder_totals', false);
    }

    /**
     * @param int $id
     * @param int $brandId
     * @param int $retailerId
     * @param array $data
     */
    private function _editPurchaseOrder(int $id, int $brandId, int $retailerId, array $data)
    {
        // Do not save 'Qty to Fulfill' until pricing is confirmed.
        // Displayed totals are calculated from the column labelled 'Qty to Fulfill' (named 'dealer_quantity')
        // which is set to follow the value of the column labelled 'Qty' (named 'extra_quantity') on reload.
        $data['Order']['dealer_quantity'] = $data['Order']['extra_quantity'];
        unset($data['Order']['extra_quantity']);

        $dealerProducts = $this->_flattenDealerProductsRequest($data, false);

        $discountData = $this->getDiscountDataForOrder($id);

        $applyFreeFreight = $this->Discount->shouldApplyFreeFreightFromDealerProducts($brandId, $discountData, $dealerProducts);
        if ($applyFreeFreight) {
            $shippingAmount = 0.00;
        } else {
            $shippingAmount = $this->ShippingCalculator->calculateShippingFromDealerProducts($brandId, $retailerId, $dealerProducts);
        }

        if (!$this->Order->editPurchaseOrder($id, $dealerProducts, $shippingAmount, $data['DealerOrder']['dealerDiscount'] ?? 0)) {
            throw new BadRequestException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
        }
    }

    private function _editWholesaleProcessingInventoryTransfers(int $orderId, array $data)
    {
        $dealerProducts = $this->_flattenDealerProductsRequest($data);
        if (!$this->DealerOrder->updateWholesaleProcessingInventoryTransfers($orderId, $dealerProducts)) {
            throw new BadRequestException(json_encode(['errors' => $this->DealerOrder->validationErrors, 'data' => $this->DealerOrder->data]));
        }
    }

    /**
     * @param $order
     */
    protected function _setDealerOrderPopupView($order)
    {
        $is_wholesale = ($order['Order']['order_type'] === OrderType::WHOLESALE);
        $isSplitPaymentOrder = $order['DealerOrder']['is_split_payment'] ?? $order['ManufacturerRetailer']['enable_split_payment'] ?? true;

        if (empty($order['Order']['dealer_qty_ordered']['products'])) {
            $order['Order']['dealer_qty_ordered']['products'] = array();
        }
        if (!empty($order['DealerOrder']['id'])) {
            $order['Order']['dealer_qty_ordered']['products'] = array_reduce($order['DealerOrder']['DealerOrderProduct'],
                function($dealerProducts, $dealerOrderProduct) {
                    $pid = $dealerOrderProduct['product_id'];
                    $dealerProducts[$pid]['quantity'] = $dealerOrderProduct['remaining_quantity'];
                    $dealerProducts[$pid]['dealer_price'] = $dealerOrderProduct['product_price'];
                    return $dealerProducts;
                },
                $order['Order']['dealer_qty_ordered']['products']
            );
            $order['Order']['dealer_qty_ordered'] = $this->_set_dealer_qty_ordered_pricing($order['Order']['dealer_qty_ordered']);
            $b2bShippingTaxOption = $order['Order']['dealer_qty_ordered']['b2b_shipping_tax_option'] ?? false;
            $productSubtotal = $order['DealerOrder']['product_total_price'];
            $totalDiscount = $order['DealerOrder']['total_discount'];
            $shippingRateOptions = array();
            $shippingAmount = $order['DealerOrder']['shipping_amount'];
            $b2bTaxRate = $order['DealerOrder']['tax'] / 100;
            $dealerTaxes = $order['DealerOrder']['total_tax'];
            $dealerTotal = $order['DealerOrder']['total_price'];
            $wholesaleChargeAmount = $order['DealerOrder']['wholesale_charge_amount'];
        } else {
            $order['Order']['dealer_qty_ordered']['products'] = array_reduce($order['OrderProduct'],
                function($dealerProducts, $orderProduct) {
                    if (isset($dealerProducts[$orderProduct['product_id']])) {
                        //TODO determine if this assignment is necessary or a leftover of an old feature due for cleanup
                        $dealerProducts[$orderProduct['product_id']]['restock_date'] = $orderProduct['restock_date'];
                    }
                    return $dealerProducts;
                },
                $order['Order']['dealer_qty_ordered']['products']
            );
            $order['Order']['dealer_qty_ordered'] = $this->_process_dealer_qty_ordered_for_b2c_dealer_order($order, $order['Order']['dealer_qty_ordered']);

            $b2bTaxRate = isset($order['Order']['dealer_qty_ordered']['b2b_tax'])
                ? $order['Order']['dealer_qty_ordered']['b2b_tax'] / 100
                : 0;
            $b2bShippingTaxOption = $order['Order']['dealer_qty_ordered']['b2b_shipping_tax_option'] ??
                                    $this->UserSetting->field('b2b_shipping_tax_option', ['user_id' => $order['Order']['user_id']]);

            $productSubtotal = ($is_wholesale)
                ? array_sum(array_column($order['OrderProduct'], 'total_price'))
                : array_sum(array_column($order['Order']['dealer_qty_ordered']['products'], 'line_total'));

            $totalDiscount = 0;
            if ($is_wholesale) {
                $calculatedDiscount = $this->Discount->calculateTotalDiscount(array_column($order['OrderProduct'], 'discount'));
                $dealerDiscount = $order['Order']['dealerDiscount'] ?? ($order['Order']['total_discount'] - $calculatedDiscount);
                $totalDiscount = $calculatedDiscount + $dealerDiscount;
            }
            $dealerSubtotal = $productSubtotal - $totalDiscount;

            $warehouseShippingRates = $this->ShippingCalculator->calculateB2bOrderPopupShipping($order);

            $applyFreeFreight = false;
            if (!empty($order['Discount'])) {
                $applyFreeFreight = $this->Discount->shouldApplyFreeFreightFromOrderProducts(
                    (int)$order['Order']['user_id'],
                    (array)$order['Discount'],
                    (array)$order['OrderProduct']
                );
            }
            if ($applyFreeFreight) {
                $warehouseShippingRates = Hash::insert($warehouseShippingRates, '{n}.{s}.price', '0.00');
            }
            $shippingRateOptions = $this->ShippingCalculator->extractB2bShippingRateOptions($warehouseShippingRates);
            $shippingAmount = $order['Order']['dealer_qty_ordered']['shipping_amount'] ??
                $this->ShippingCalculator->sumWarehouseGroupedRates($warehouseShippingRates);

            $taxIncluded = (bool)$order['Order']['tax_included'];

            $dealerTaxes = ($is_wholesale)
                ? array_sum(array_column($order['OrderProduct'], 'total_tax'))
                : array_sum(array_map(
                    function($dealerProduct) use ($b2bTaxRate, $taxIncluded) {
                        $b2bTaxRate = (float)($dealerProduct['tax_rate'] ?? $b2bTaxRate);
                        $totalPrice = (float)$dealerProduct['line_total'];
                        $totalDiscount = 0.00;

                        return round(calculate_tax_amount($totalPrice - $totalDiscount, $b2bTaxRate, $taxIncluded), 2);
                    },
                    $order['Order']['dealer_qty_ordered']['products']
                ));
            if ($b2bShippingTaxOption) {
                $dealerTaxes += calculate_tax_amount($shippingAmount, $b2bTaxRate, $taxIncluded);
            }

            $dealerTotal = $dealerSubtotal + $shippingAmount;
            if (!$taxIncluded) {
                $dealerTotal += $dealerTaxes;
            }

            $wholesaleChargeAmount = isset($order['Order']['dealer_qty_ordered']['wholesale_charge_amount'])
                ? $order['Order']['dealer_qty_ordered']['wholesale_charge_amount']
                : 0;
        }

        $refundTotals = $this->OrderRefund->getTotalsByOrder($order['Order']['id']);

        $dealerOrderRefund = array_sum(Hash::extract($order, 'DealerOrder.DealerOrderRefund.{n}.amount'));

        $orderTotal = $order['Order']['total_price'] - $order['Order']['total_discount'];
        $orderRefund = $refundTotals['OrderRefund']['total_amount'];
        $orderFees = $order['Order']['shipearlyFees'] + $order['Order']['stripe_fees'] - $refundTotals['OrderRefund']['total_fees'];
        $orderBalance = $orderTotal - $orderRefund - $orderFees + $wholesaleChargeAmount;
        $wholesaleTotal = $dealerTotal - $dealerOrderRefund;
        if($isSplitPaymentOrder)
        {
            $orderBalance -= $wholesaleTotal;
        }

        $order['Order']['dealer_qty_ordered']['products'] = array_reduce($order['OrderProduct'],
            function($dealerProducts, $orderProduct) {
                $pid = $orderProduct['product_id'];
                if (isset($dealerProducts[$pid])) {
                    $orderProduct['Product']['product_price'] = round($orderProduct['quantity'] ? $orderProduct['total_price'] / $orderProduct['quantity'] : 0, 2);
                    $dealerProducts[$pid]['Product'] = $orderProduct['Product'];
                }
                return $dealerProducts;
            },
            $order['Order']['dealer_qty_ordered']['products']
        );
        $order['Order']['dealer_qty_ordered']['products'] = array_reduce(
            $this->Product->find('all', [
                'recursive' => -1,
                'conditions' => [
                    'Product.id' => array_keys(
                        array_filter($order['Order']['dealer_qty_ordered']['products'], function($dealerProduct) {
                            return empty($dealerProduct['Product']);
                        })
                    ),
                ],
            ]),
            function($dealerProducts, $product) {
                $pid = $product['Product']['id'];
                $dealerProducts[$pid]['Product'] = $product['Product'];
                return $dealerProducts;
            },
            $order['Order']['dealer_qty_ordered']['products']
        );

        $userId = $this->Auth->user('id');
        $pricingConfirmed = !empty($order['DealerOrder']['id']);

        $userHasEditPermission = $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);

        $enable_price_edit = (
            $userHasEditPermission &&
            $this->request->param('action') !== 'invoice' &&
            !$pricingConfirmed &&
            in_array($order['Order']['order_status'], [OrderStatus::DEALER_ORDER, OrderStatus::PURCHASE_ORDER]) &&
            in_array($userId, [$order['Order']['user_id'], $order['Order']['distributor_id']])
        );

        // Variables shared by parent views
        $this->set('userHasEditPermission', $userHasEditPermission);
        $this->set('CourierName', $order['Courier']['name']);
        $this->set('orderNO', $this->Order->getSourceOrderName($order));
        $this->set('show_secret_code', $this->_showSecretCode($this->Auth->user('id'), $order['Order']['user_id'], $order['Order']['retailer_id']));
        $this->set('order', $order);
        $creditTerms = [];
        if (in_array($order['Order']['order_status'], [OrderStatus::PROCESSING, OrderStatus::PURCHASE_ORDER])) {
            $creditTerms = $this->Discount->getCreditTermOptionsByDiscount($order['Discount']['id']) ?: $this->RetailerCreditTerm->findCreditTermOptions($userId, $order['Order']['retailer_id']);
        }
        $this->set('creditTermsOptions', $creditTerms);
        $paymentOptions = OrderPaymentMethod::getSelectOptions();
        // Exclude Stripe if transactionID does not contain a Stripe id like 'ch_****', 'pi_*****', or 'py_*****'.
        $doesNotHaveAStripePayment = !str_contains((string)$order['Order']['transactionID'], '_');
        if ($doesNotHaveAStripePayment && $order['Order']['payment_method'] !== OrderPaymentMethod::STRIPE) {
            $paymentOptions = array_diff_key($paymentOptions, array_flip([OrderPaymentMethod::STRIPE]));
        }
        $this->set('paymentOptions', $paymentOptions);
        $this->set('dealer_qty_ordered', $order['Order']['dealer_qty_ordered']);
        $this->set('b2bShippingTaxOption', $b2bShippingTaxOption ? 1 : 0);
        $this->set('b2bTaxRate', $b2bTaxRate);
        $this->set('refundTotals', $refundTotals);
        $this->set('pricingConfirmed', !empty($order['DealerOrder']['id']));
        $this->set('warehouseOptions', $this->Warehouse->listValidUserWarehouses($order['Order']['user_id'], $order['Order']['retailer_id']));
        $this->set('can_edit_dealer_price', ($userHasEditPermission && in_array($this->Auth->user('id'), [$order['Order']['user_id'], $order['Order']['distributor_id']])));
        $this->set('can_edit_dealer_quantity', ($userHasEditPermission && $this->Auth->user('user_type') == User::TYPE_MANUFACTURER && $order['Order']['order_status'] == OrderStatus::PURCHASE_ORDER));
        $this->set('enable_wholesale_topup', ($order['User']['enable_wholesale_topup'] && $order['Order']['order_type'] !== OrderType::SHIP_FROM_STORE));
        
        // Variables for dealerorder_invoice_totals
        $this->set('show_balance', !$is_wholesale);
        $this->set('enable_discount_edit', $enable_price_edit && $is_wholesale);
        $this->set('enable_shipping_edit', $enable_price_edit);
        $this->set('enable_shipping_name_edit', $enable_price_edit && !$order['Order']['distributor_id']);
        $this->set('enable_notes_edit', false);
        $this->set('isSplitPaymentOrder', $isSplitPaymentOrder);
        $this->set('currencyCode', $order['Order']['currency_code']);
        $this->set('dealerSubtotal', format_number($productSubtotal));
        $this->set('calculatedDiscount', format_number($calculatedDiscount ?? 0));
        $this->set('dealerDiscount', format_number($dealerDiscount ?? 0));
        $this->set('totalDiscount', format_number($totalDiscount));
        $this->set('shippingAmount', format_number($shippingAmount));
        $this->set('shippingRateOptions', $shippingRateOptions);
        $this->set('dealerTaxes', format_number($dealerTaxes));
        $this->set('dealerTotal', format_number($dealerTotal));
        $this->set('dealerOrderRefund', format_number($dealerOrderRefund));
        $this->set('orderTotal', format_number($orderTotal));
        $this->set('orderRefund', format_number($orderRefund));
        $this->set('orderFees', format_number($orderFees));
        $this->set('wholesaleTotal', format_number($wholesaleTotal));
        $this->set('wholesaleChargeAmount', format_number($wholesaleChargeAmount));
        $this->set('orderBalance', format_number($orderBalance));
        $this->set('notes', $order['Order']['notes']);
    }

    /**
     * @param int $orderId
     * @param float $retailerAmount
     * @return bool
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function _splitpayment($orderId, $retailerAmount)
    {
            //TODO: convert to use the calc discounts
        $order = $this->Order->get($orderId, [
            'fields' => [
                'id',
                'user_id',
                'retailer_id',
                'order_type',
                'is_commission_retailer',
                'payment_status',
                'total_price',
                'total_discount',
                'transactionID',
                'stripe_account',
            ],
        ]);

        if ($order['Order']['payment_status'] == OrderPaymentStatus::PAID) {
            return true;
        }

        $success = false;
        $oldPaymentStatus = $order['Order']['payment_status'];
        $this->Order->save(['id' => $orderId, 'payment_status' => OrderPaymentStatus::PAID]);
        try {
            //TODO: convert to use the calc discounts
            $orderTotal = $order['Order']['total_price'] - $order['Order']['total_discount'] - $this->OrderRefund->getTotalAmount($orderId);

            $stripe_account = $this->StripeUser->getOrderAccountId($orderId, $order['Order']);

            $charge = $this->Stripe->splitpaymentCapture($stripe_account, $order['Order']['transactionID'], $retailerAmount, $orderTotal);

            if (!empty($charge->balance_transaction)) {
                $success = (bool)$this->Order->save(array(
                    'id' => $order['Order']['id'],
                    'payment_captured_at' => date('Y-m-d H:i:s'),
                    'balance_transaction_id' => $charge->balance_transaction,
                    'stripe_fees' => $this->Stripe->retrieveStripeFees($stripe_account, $charge),
                ));
            }
            return $success;
        } catch (\Stripe\Exception\ApiErrorException $e) {
            CakeLog::error('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()) . PHP_EOL . 'Stack Trace:' . PHP_EOL . $e->getTraceAsString());

            $cancelledPaymentErrorCodes = [
                \Stripe\ErrorObject::CODE_PAYMENT_INTENT_UNEXPECTED_STATE,
                \Stripe\ErrorObject::CODE_CHARGE_EXPIRED_FOR_CAPTURE,
                \Stripe\ErrorObject::CODE_CHARGE_ALREADY_REFUNDED
            ];
            if (in_array($e->getError()->code, $cancelledPaymentErrorCodes, true)) {
                $this->setFlash($this->_buildExpiredChargeMessage($orderId, $this->request->data), 'error');
            }

            throw $e;
        } finally {
            if (!$success) {
                $this->Order->save(['id' => $orderId, 'payment_status' => $oldPaymentStatus]);
            }
        }
    }

    private function _buildExpiredChargeMessage(int $orderId, array $rechargeData): string
    {
        App::uses('AppView', 'View');
        $view = new AppView($this);
        $view->set('orderId', $orderId);
        $view->set('rechargeData', $rechargeData);

        return $view->render('/Elements/Orders/charge_expired_message', false);
    }

    public function recharge_expired($id = null)
    {
        $this->autoRender = false;
        $this->request->allowMethod('post');
        $log = $this->OrderLogic->isAuthorizedForOrder($id);
        if (!in_array($log['Auth']['id'], [$log['Order']['user_id'], $log['Order']['distributor_id']])) {
            throw new ForbiddenException(json_encode($log));
        }
        try {
            $order = $this->Order->findById($id, [
                'Order.id',
                'Order.user_id',
                'Order.retailer_id',
                'Order.orderID',
                'Order.order_type',
                'Order.is_commission_retailer',
                'Order.order_status',
                'Order.payment_method',
                'Order.payment_status',
                'Order.transactionID',
                'Order.stripe_account',
            ], null, -1);

            $canExpire = (
                !in_array($order['Order']['order_status'], OrderStatus::STATUSES_REFUNDED, true) &&
                $order['Order']['payment_method'] === OrderPaymentMethod::STRIPE &&
                $order['Order']['payment_status'] !== OrderPaymentStatus::PAID &&
                $order['Order']['transactionID']
            );
            if (!$canExpire) {
                throw new BadRequestException(json_encode(['message' => 'Order cannot be recharged'] + $order));
            }

            $stripe_account = $this->StripeUser->getOrderAccountId($id, $order['Order']);
            $charge = $this->Stripe->recreateExpiredCharge($stripe_account, $order['Order']['transactionID']);

            $transactionID = $charge->payment_intent ?? $charge->id;
            $paymentDetails = $this->Stripe->getPaymentMethodDetailsFromCharge($charge, $stripe_account);

            $success = $this->Order->save(array_merge($paymentDetails->toArray(), [
                'id' => $id,
                'risk_level' => $charge->outcome->risk_level ?? Order::RISK_LEVEL_NOT_ASSESSED,
            ]));
            if (!$success) {
                throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
            }
            CakeLog::info("Order {$order['Order']['orderID']} replaced expired charge {$order['Order']['transactionID']} with {$transactionID}");

            return $this->_ajax_edit_post($id);
        } catch (HttpException $e) {
            CakeLog::debug(json_encode($log));
            return $this->_exceptionResponse($e, json_decode($e->getMessage(), true)['message'] ?? null);
        } catch (Exception $e) {
            CakeLog::debug(json_encode($log));
            return $this->_exceptionResponse(null, null, $e);
        }
    }

    public function create_ecommerce_dealer_order($dealerOrderId = null): ?CakeResponse
    {
        $this->request->allowMethod('post', 'put');

        $orderId = $this->DealerOrder->field('order_id', ['id' => $dealerOrderId]);
        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);
        if (!in_array($log['Auth']['user_type'], User::TYPES_BRAND, true)) {
            throw new ForbiddenException(json_encode($log));
        }

        return $this->admin_create_ecommerce_dealer_order($dealerOrderId);
    }

    public function admin_create_ecommerce_dealer_order($dealerOrderId = null): ?CakeResponse
    {
        $this->request->allowMethod('post', 'put');

        $orderID = $this->DealerOrder->findOrderID($dealerOrderId);

        if (!$this->OrderPlacer->createEcommerceDealerOrder($dealerOrderId)) {
            $message = __('Failed placing the wholesale order for %s in the ecommerce platform.', $orderID);
            CakeLog::warning($message);

            $message .= $this->OrderPlacer->buildCreateEcommerceDealerOrderRetryLink($dealerOrderId);

            return $this->_exceptionResponse(null, $message);
        }

        return $this->_successResponse(__('Wholesale order for %s placed in the ecommerce platform.', $orderID));
    }

    public function mark_delivered($orderId = null): ?CakeResponse
    {
        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);
        if (!in_array($log['Order']['order_status'], [OrderStatus::NOT_PICKED_UP, OrderStatus::READY_FOR_DELIVERY], true)) {
            throw new BadRequestException('Order status is invalid ' . json_encode($log));
        }
        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPES_BRAND);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        $this->request->allowMethod(['post', 'put']);

        $order = $this->Order->findById($orderId, [
            'id',
            'user_id',
            'retailer_id',
            'orderID',
            'is_commission_retailer',
            'order_type',
            'payment_status',
            'payment_method',
            'total_price',
            'total_discount',
            'transactionID',
            'stripe_account',
            'shipearlyFees',
        ], null, -1);

        return $this->_captureAndMarkAsDelivered((int)$orderId, $order['Order']);
    }

    public function verification_code($orderId = null)
    {
        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);
        if (!in_array($log['Order']['order_status'], [OrderStatus::NOT_PICKED_UP, OrderStatus::READY_FOR_DELIVERY])) {
            throw new BadRequestException('Order status is invalid ' . json_encode($log));
        }

        $order = $this->Order->findForPopup($orderId);
        $this->set('order', $order);

        if ($this->request->is(['post', 'put'])) {
            if (!$this->Order->verifySecretCode($orderId, $this->request->data['Order']['instore_code'])) {
                $message = __('Incorrect verification code');

                return $this->_exceptionResponse(new BadRequestException($message), $message, true);
            }

            if ($this->request->data('Order.verification_image_file.error') !== UPLOAD_ERR_NO_FILE) {
                try {
                    $verificationImageUrl = $this->Upload->replaceFileInUserHash(
                        $order['Order']['verification_image_url'],
                        (array)$this->request->data['Order']['verification_image_file'],
                        (string)$this->Auth->user('uuid'),
                        'verification',
                        'order_' . $orderId,
                        null,
                        ['jpg', 'jpeg', 'gif', 'png']
                    );
                    if (!$this->Order->save(['id' => $orderId, 'verification_image_url' => $verificationImageUrl])) {
                        throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                    }
                } catch (Exception $e) {
                    CakeLog::error($e);

                    return $this->_exceptionResponse(null, __('There was an error uploading your attachment. Please, try again.'));
                }
            }

            return $this->_captureAndMarkAsDelivered((int)$orderId, $order['Order']);
        }

        $this->request->data['Order']['id'] = $orderId;
    }

    private function _captureAndMarkAsDelivered(int $orderId, array $orderFields): ?CakeResponse
    {
        $expectedFieldNames = [
            'id',
            'user_id',
            'retailer_id',
            'orderID',
            'order_type',
            'is_commission_retailer',
            'payment_status',
            'payment_method',
            'total_price',
            'total_discount',
            'transactionID',
            'stripe_account',
            'shipearlyFees',
        ];
        $orderFields = $this->Order->resolveProvidedFields($orderId, $orderFields, $expectedFieldNames);

        $orderID = $orderFields['orderID'];

        if ($orderFields['payment_status'] != OrderPaymentStatus::PAID) {
            try {
                if (!$this->OrderLogic->captureStripeCharge($orderId, $orderFields)) {
                    throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                }
            } catch (Exception $e) {
                CakeLog::error($e);

                return $this->_exceptionResponse(null, __('There was an error capturing the payment for order %s. Please, try again.', $orderID));
            }
        }

        if ($this->Order->markDelivered($orderId)) {
            return $this->_successResponse(__('Order %s has been marked as delivered', $orderID));
        }

        return $this->_exceptionResponse(
            new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data])),
            __('Failed to mark order %s as delivered. Please try again.', $orderID),
            true
        );
    }

    public function ajax_verify_secretcode($orderId = null)
    {
        $this->autoRender = false;

        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);
        if (!in_array($log['Order']['order_status'], [OrderStatus::NOT_PICKED_UP, OrderStatus::READY_FOR_DELIVERY])) {
            throw new BadRequestException('Order status is invalid ' . json_encode($log));
        }

        // Compatibility for request sent by Orders/edit
        if (empty($this->request->query)) {
            $this->request->query['data'] = $this->request->data;
        }
        if (empty($orderId)) {
            $orderId = $this->request->query('data.orderId');
        }

        $verified = $this->Order->verifySecretCode($orderId, $this->request->query('data.Order.instore_code'));

        $this->response->body(json_encode($verified));
        return $this->response;
    }

    public function verification_image($id = null)
    {
        $this->OrderLogic->isAuthorizedForOrder($id);
        $order = $this->Order->findById($id, ['id', 'verification_image_url'], null, -1);
        if ($this->request->is('delete')) {
            $success = $this->Order->doInTransaction(function() use ($order) {
                return $this->Order->save(['id' => $order['Order']['id'], 'verification_image_url' => null]) &&
                       $this->Upload->deleteFromWebroot($order['Order']['verification_image_url']);
            });
            if ($success) {
                return $this->_successResponse('The verification image has been deleted');
            }
            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors])), null, true);
        }
        $this->set('order', $order);
    }

    public function ajax_order_product($id = null)
    {
        $this->_checkOrderBrandPermission($id);

        $this->Order->bindModel(['hasMany' => ['OrderProduct']], false);
        $this->OrderProduct->bindModel(['belongsTo' => ['Product']], false);
        $order = $this->Order->find('first', [
            'contain' => [
                'OrderProduct' => ['Product']
            ],
            'conditions' => ['Order.id' => $id],
        ]);
        $this->OrderProduct->unbindModel(['belongsTo' => ['Product']], false);
        $this->Order->unbindModel(['hasMany' => ['OrderProduct']], false);
        $order['OrderProduct'] = Hash::combine($order['OrderProduct'], '{n}.id', '{n}');

        $this->set('order', $order);
    }

    public function ajax_order_product_edit()
    {
        $this->autoRender = false;

        $this->response->type('application/json');
        if ($this->request->is('GET')) {
            $this->request->data = $this->request->query['data'];
        }

        $orderId = $this->request->data['Order']['id'];
        $this->_checkOrderBrandPermission($orderId);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);

        $this->Order->bindModel([
            'belongsTo' => ['Retailer' => ['className' => 'User']],
            'hasMany' => ['OrderProduct'],
        ], false);
        $this->OrderProduct->bindModel(['belongsTo' => ['Product']], false);
        $order = $this->Order->find('first', [
            'contain' => [
                'Retailer' => ['fields' => ['defaultTax']],
                'OrderProduct' => ['Product'],
            ],
            'conditions' => ['Order.id' => $orderId],
        ]);
        $this->OrderProduct->unbindModel([
            'belongsTo' => ['Product'],
        ], false);
        $this->Order->unbindModel([
            'belongsTo' => ['Retailer'],
            'hasMany' => ['OrderProduct'],
        ], false);

        // Filter request
        $originalOrderProductsById = array();
        foreach ($order['OrderProduct'] as $orderProduct) {
            $id = $orderProduct['id'];
            $originalOrderProductsById[$id] = $orderProduct;
        }
        $this->request->data['OrderProduct'] = array_filter($this->request->data['OrderProduct'],
            function($orderProduct) use ($originalOrderProductsById) {
                return in_array($orderProduct['id'], array_keys($originalOrderProductsById));
            }
        );
        $this->request->data['OrderProduct'] = array_filter($this->request->data['OrderProduct'],
            function($orderProduct) use ($originalOrderProductsById) {
                $id = $orderProduct['id'];
                $originalOrderProduct = $originalOrderProductsById[$id];
                return $orderProduct['product_id'] != $originalOrderProduct['product_id'];
            }
        );
        if (empty($this->request->data['OrderProduct'])) {
            return json_encode(['status' => 'ok', 'message' => "No changes made"]);
        }

        $date = date('Y-m-d H:i:s');

        $cancelledOrderProducts = array_map(function($orderProduct) use ($originalOrderProductsById, $date) {
            $cancelledOrderProduct = $originalOrderProductsById[$orderProduct['id']];
            $cancelledOrderProduct['status'] = 'Cancelled';
            $cancelledOrderProduct['cancelled_at'] = $date;
            // TODO Add 'notes' field
            return $cancelledOrderProduct;
        }, $this->request->data['OrderProduct']);

        $newProductIds = array_map(function($orderProduct) {
            return $orderProduct['product_id'];
        }, $this->request->data['OrderProduct']);
        $newProducts = $this->Product->find('all', array(
            'recursive' => -1,
            'conditions' => array('id' => $newProductIds),
        ));
        $newProductsById = array();
        foreach ($newProducts as $product) {
            $productId = $product['Product']['id'];
            $newProductsById[$productId] = $product['Product'];
        }
        $taxRate = $order['Retailer']['defaultTax'] / 100;
        $taxIncluded = (bool)$order['Order']['tax_included'];
        $newOrderProducts = array_map(function($orderProduct) use ($originalOrderProductsById, $newProductsById, $taxRate, $taxIncluded, $date) {
            $newOrderProduct = $originalOrderProductsById[$orderProduct['id']];
            unset($newOrderProduct['id']);
            $newOrderProduct['product_id'] = $orderProduct['product_id'];
            $newOrderProduct['Product'] = $newProductsById[$newOrderProduct['product_id']];

            $newOrderProduct['quantity'] = $orderProduct['quantity'];
            $newOrderProduct['total_price'] = $newOrderProduct['Product']['product_price'] * $newOrderProduct['quantity'];
            $newOrderProduct['total_tax'] = round(calculate_tax_amount($newOrderProduct['total_price'], $taxRate, $taxIncluded), 2);
            $newOrderProduct['total_discount'] = null; // null will be calculated later on.
            $newOrderProduct['totalPriceConversion'] = $newOrderProduct['total_price'];
            $newOrderProduct['created'] = $date;
            $newOrderProduct['modified'] = $date;
            return $newOrderProduct;
        }, $this->request->data['OrderProduct']);

        $newOrder = $order;
        // Reset order status
        $newOrder['Order']['order_status'] = OrderStatus::NEED_TO_CONFIRM;
        $newOrder['Order']['dealer_qty_ordered'] = null;
        // Reset timestamps
        $newOrder['Order']['created_at'] = $date;
        $newOrder['Order']['updated_at'] = $date;

        $newOrder['OrderProduct'] = array_map(function($orderProduct) use ($newOrderProducts) {
            $id = $orderProduct['id'];
            return isset($newOrderProducts[$id]) ? $newOrderProducts[$id] : $orderProduct;
        }, $newOrder['OrderProduct']);

        $adjustment = array();
            //TODO: convert to use the calc discounts
        foreach (['total_price', 'total_tax', 'total_discount'] as $field) {
            $adjustment[$field] = 0;
            foreach ($newOrder['OrderProduct'] as $orderProduct) {
                $adjustment[$field] += $orderProduct[$field];
            }
            foreach ($order['OrderProduct'] as $orderProduct) {
                $adjustment[$field] -= $orderProduct[$field];
            }
            $adjustment[$field] = round($adjustment[$field], 2);
        }
        if (!$taxIncluded) {
            $adjustment['total_price'] += $adjustment['total_tax'];
        }
        $adjustment['totalPriceConversion'] = $adjustment['total_price'];
        if ($adjustment['total_price'] > 0) {
            return json_encode(['status' => 'error', 'message' => "Can only edit orders to be of equal or lesser value"]);
        }

        foreach ($adjustment as $field => $value) {
            $newOrder['Order'][$field] += $value;
        }
        $newOrder['Order']['retailerAmount'] = $newOrder['Order']['total_price'] - $newOrder['Order']['shipearlyFees'];

        if ($this->request->is('GET')) {
            $this->set('order', $order);
            $this->set('newOrder', $newOrder);
            $this->set('adjustment', $adjustment);
            $this->set('cancelledOrderProducts', $cancelledOrderProducts);
            $this->set('newOrderProducts', $newOrderProducts);
            $this->response->type('text/html');
            return $this->render();
        }
        if ($this->request->is('POST')) {
            if ($adjustment['total_price'] < 0) {
                $refundRequest = array(
                    'orderID' => $order['Order']['id'],
                    'OrderRefund' => array(
                        'restocking_fees' => 0,
                        'shipping_portion' => 0,
                        'tax_portion' => 0,
                        'amount' => -$adjustment['total_price'],
                    ),
                    'OrderRefundProduct' => array(),
                );
                $response = json_decode($this->OrderLogic->orderRefund($refundRequest), true);
                if ($response['status'] !== 'ok') {
                    CakeLog::error("Failed to edit order '{$order['Order']['orderID']}' with refund " . json_encode(['request' => $refundRequest, 'response' => $response]));
                    $this->response->body(json_encode(['status' => 'error', 'message' => "An error occured while attempting to change the order balance", 'details' => $response]));
                    return $this->response;
                }
                // Edited order_status must to be NEED_TO_CONFIRM instead of the value set by the refund
                unset($response['message']['request']['Order']['order_status']);
                $newOrder['Order'] = array_merge($newOrder['Order'], $response['message']['request']['Order']);
                $newOrder['Order']['shipearlyFees'] -= $response['message']['request']['OrderRefund']['fees'];
            }
            $newOrder['Order']['retailerAmount'] = $newOrder['Order']['total_price'] - $newOrder['Order']['shipearlyFees'];

            $this->Order->save($newOrder['Order']);
            $this->OrderProduct->saveMany($cancelledOrderProducts);
            $this->OrderProduct->saveMany($newOrderProducts);

            $this->WarehouseProductReservation->reserveLineItemSet($orderId, $this->OrderProduct->findAllForInventoryReservation($orderId));

            $this->NotificationLogic->sendOrderEditEmail($newOrder);

            return json_encode(['status' => 'ok']);
        }
    }

    public function ajax_order_product_search()
    {
        if (empty($this->request->query['term'])) {
            throw new BadRequestException('No search term provided');
        }

        $originalVirtualFields = $this->Product->virtualFields;
        try {
            $this->Product->virtualFields['brand_available_inventory'] = 'IF(TotalWarehouseProduct.product_id, TotalWarehouseProduct.available_quantity, Product.brand_inventory)';

            $products = $this->Product->find('all', [
                'recursive' => -1,
                'joins' => [
                    [
                        'table' => $this->WarehouseProduct->buildProductTotalsSubQuery(),
                        'alias' => 'TotalWarehouseProduct',
                        'type' => 'LEFT',
                        'conditions' => ['TotalWarehouseProduct.product_id' => $this->Product->primaryKeyIdentifier()],
                    ],
                ],
                'conditions' => [
                    'Product.product_title LIKE' => "{$this->request->query['term']}%",
                    'Product.product_status' => Product::STATUS_ENABLED,
                    'Product.user_id' => $this->Auth->user('id'),
                    'Product.is_fee_product' => false,
                    'Product.deleted' => false,
                ],
                'order' => 'product_title',
                'limit' => 50,
            ]);
        } finally {
            $this->Product->virtualFields = $originalVirtualFields;
        }

        $this->set('products', $products);
    }

    public function ajax_add_dealer_order_product_search($orderId = null)
    {
        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);

        if (empty($this->request->query['term'])) {
            throw new BadRequestException('No search term provided');
        }

        $brandId = $log['Order']['user_id'];
        $retailerId = $log['Order']['retailer_id'];
        $term = $this->request->query['term'];
        $excludes = (array)$this->request->query('exclude');

        $products = ($log['Order']['order_status'] === OrderStatus::NEED_TO_CONFIRM)
            ? $this->Product->findForNeedToConfirmProductSearch($brandId, $retailerId, $term, $excludes)
            : $this->Product->findForPurchaseOrderProductSearch($brandId, $retailerId, $term, $excludes);

        $this->set('products', $products);
    }

    public function ajax_add_dealer_order_product($orderId = null, $productId = null, $warehouseId = null)
    {
        $this->autoRender = false;

        if (empty($orderId) || empty($productId)) {
            // Act like the url has no route
            throw new NotFoundException('Not Found');
        }

        $inventoryTransferId = $this->request->query('inventory_transfer_id');

        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);

        $brandId = $log['Order']['user_id'];
        $retailerId = $log['Order']['retailer_id'];
        $orderType = $log['Order']['order_type'];
        $orderStatus = $log['Order']['order_status'];

        $conditions = [
            'Product.id' => $productId,
            'Product.user_id' => $brandId,
        ];
        if ($orderType !== OrderType::WHOLESALE) {
            $conditions['Product.sell_direct !='] = ProductSellDirect::EXCLUSIVELY;
        }

        $this->Product->belongsToMany('InventoryTransfer', [
            'with' => 'InventoryTransferProduct',
            'unique' => 'keepExisting',
            'conditions' => $this->InventoryTransfer->getConditionsForActiveFutureTransfers([
                'InventoryTransfer.destination_warehouse_id' => $warehouseId,
            ]),
        ]);
        $product = $this->Product->findWithDealerPricing($retailerId, 'first', [
            'contain' => [
                'InventoryTransfer' => [
                    'with' => ['InventoryTransferProduct' => ['id', 'inventory_transfer_id', 'product_id', 'available_quantity']],
                    'fields' => ['id', 'destination_warehouse_id', 'name', 'expected_arrival_date'],
                    'order' => ['InventoryTransfer.expected_arrival_date' => 'ASC'],
                ],
            ],
            'conditions' => $this->Product->getConditionsForActiveProducts($conditions),
            'fields' => [
                'Product.id',
                'Product.uuid',
                'Product.product_title',
                'Product.product_sku',
                'Product.product_upc',
                'Product.product_price',
                'Product.brand_inventory',
                'Product.brand_restock_date',
                'Product.enable_b2b_oversell',
                'Product.product_image',
                'Product.dealer_price',
                'Product.product_name',
                'Product.variant_options',
            ],
        ]);
        $this->Product->unbindModel(['hasAndBelongsToMany' => ['InventoryTransfer']], false);

        if (empty($product)) {
            return $this->response;
        }

        $quantity = 1;
        $dealerPrice = $product['Product']['dealer_price'] ?? '0.00';

        $orderProduct = [
            'order_id' => $orderId,
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'inventory_transfer_id' => $inventoryTransferId,
            'quantity' => $quantity,
            'total_price' => format_number($product['Product']['product_price'] * $quantity),
            'restock_date' => $product['Product']['brand_restock_date'],
            'remaining_quantity' => $quantity,
            'is_ordered' => true,
            'Product' => $product['Product'] + ['InventoryTransfer' => $product['InventoryTransfer']],
            'OrderRefundProduct' => [],
            'WarehouseProduct' => [],
        ];
        $orderProduct = current($this->WarehouseProduct->addWarehouseProductsToLineItems([$orderProduct], (int)$orderId));
        $orderProduct = current($this->InventoryTransferProduct->processOrderLineItemTransferFields([$orderProduct], (int)$orderId));
        if ($warehouseId) {
            $orderProduct['restock_date'] = $orderProduct['WarehouseProduct']['restock_date'];
        }

        $dealer_qty_ordered = [
            'products' => [
                $productId => [
                    'quantity' => $quantity,
                    'dealer_price' => $dealerPrice,
                    'line_total' => $dealerPrice * $quantity,
                    'restock_date' => $orderProduct['restock_date'],
                    'Product' => $orderProduct['Product'],
                ],
            ],
        ];

        $this->set('currency_code', $this->Order->fieldByConditions('currency_code', ['Order.id' => $orderId]));
        $this->set('removable', true);

        if ($orderStatus === OrderStatus::NEED_TO_CONFIRM) {
            $dealer_product = $dealer_qty_ordered['products'][$orderProduct['product_id']];
            $this->set('value', array_merge($orderProduct, [
                'dealer_price' => $dealer_product['dealer_price'],
                'line_total' => $dealer_product['dealer_price'] * $orderProduct['quantity'],
            ]));
            $this->set('enable_wholesale_topup', (
                $this->User->fieldByConditions('enable_wholesale_topup', ['id' => $brandId]) &&
                $orderType !== OrderType::SHIP_FROM_STORE
            ));
            $this->set('inventory', $this->getInventory($productId, $brandId, $retailerId));
            return $this->render('/Elements/Orders/order_products_table/need_to_confirm_product');
        }

        $this->set('warehouseId', $warehouseId);
        $this->set('pid', $productId);
        $this->set('enable_price_edit', ($this->Auth->user('user_type') == User::TYPE_MANUFACTURER));
        $this->set('enable_quantity_edit', true);

        if ($orderType === OrderType::WHOLESALE) {
            $orderProduct['total_price'] = ($dealerPrice * $orderProduct['quantity']);
            $this->set('value', $orderProduct + array(
                'dealer_price' => ($orderProduct['total_price'] / $orderProduct['quantity']),
                'line_total' => $orderProduct['total_price'],
            ));
            $this->set('isBookingOrder', ($this->Order->fieldByConditions('subType', ['Order.id' => $orderId]) === OrderType::SUB_TYPE_B2B_BOOKING));

            return $this->render('/Elements/Orders/order_products_table/purchase_order_product');
        }

        $this->set('value', $dealer_qty_ordered['products'][$productId]);
        $this->render('/Elements/Orders/order_products_table/dealer_order_product');
    }

    public function ajax_remove_dealer_order_product($orderId, $productId, $warehouseId){
        $this->autoRender = false;
        $this->response->type('application/json');

        if (empty($orderId) || empty($productId) || empty($warehouseId)) {
            throw new NotFoundException();
        }

        $this->OrderLogic->isAuthorizedForOrder($orderId);

        if (!$this->OrderProduct->existsByOrderIdProductIdWarehouseId($orderId, $productId, $warehouseId)){
            CakeLog::warning(json_encode(['Cannot remove order product that does not exist', 'data' => implode(',',[$orderId, $productId, $warehouseId])]));
        }
        if (!$this->Order->canRemoveItems($orderId)){
            CakeLog::warning(json_encode(['Cannot remove items from order', 'data' => implode(',',[$orderId])]));
            throw new BadRequestException();
        }


        $this->OrderProduct->deleteAllJoinless(['order_id' => $orderId, 'product_id' => $productId, 'warehouse_id' => $warehouseId]);
        return json_encode(['status' => 'ok']);
    }

    /**
     * Get current inventory count on nonstock popup
     *
     * @param int|int[] $productIds
     * @param int $userId
     * @param int $retailerId
     * @return int[] Map of product ids to inventory quantity
     */
    public function getInventory($productIds, $userId, $retailerId): array
    {
        $user = $this->User->findForPosApi($retailerId);

        $productsInfo = $this->Product->find('all', [
            'recursive' => -1,
            'conditions' => [
                'Product.id' => $productIds,
                'Product.user_id' => $userId,
                'Product.product_status' => ProductStatus::ENABLED,
                'Product.deleted' => false,
                'COALESCE(Product.product_upc, "") !=' => '',
            ],
            'fields' => ['id', 'productID', 'product_upc', 'taxcode', 'product_price'],
        ]);

        $posVariantIdByProductId = [];
        if ($user['User']['inventory_type'] === 'shopify_pos') {
            $posVariantIdByProductId = array_map(fn($store) => $store['inventoryVariantId'], $this->Store->findInventoryIdsByProductId((int)$retailerId));
        }

        $list = [];
        foreach ($productsInfo as $value) {
            $list[$value['Product']['id']] = 0;

            try {
                if ($user['User']['inventory_type'] == 'lightspeed_cloud') {
                    $list[$value['Product']['id']] = $this->Lightspeed->getInventoryCount($user['User']['inventory_apiuser'], $user['User']['inventory_password'], $value['Product']['product_upc'], $user['User']['vend_refresh_access_token'], $user['User']['vend_access_token_expires']);

                    if (!$this->User->refreshLightspeedAccessToken($retailerId, $this->Lightspeed->getNewAccessTokenResponse($user['User']['inventory_password']))) {
                        throw new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data]));
                    }
                } elseif ($user['User']['inventory_type'] == 'shopify_pos') {
                    $posVariantId = $posVariantIdByProductId[$val1['Product']['id']] ?? null;
                    if ($posVariantId) {
                        $list[$value['Product']['id']] = $this->ShopifyPOS->getInventoryCount($user['User']['inventory_password'], $user['User']['shop_url'], $posVariantId);
                    }
                } elseif ($user['User']['inventory_type'] == 'quickbook_pos') {
                    $productInfo = $this->Quickbook->getProductInfo($value['Product']['product_upc'], $user['User']['id']);
                    if (isset($productInfo['QuickbookProduct'])) {
                        $list[$value['Product']['id']] = $productInfo['QuickbookProduct']['qty'];
                    }
                } elseif ($user['User']['inventory_type'] == 'vend_pos') {
                    $productInfo = $this->VendPOS->getInventoryCount($user['User']['inventory_apiuser'], $user['User']['inventory_password'], $user['User']['Inventory_Store_ID'], $value['Product']['product_upc']);
                    $list[$value['Product']['id']] = $productInfo['inventory_quantity'];
                }
            } catch (Exception $e) {
                CakeLog::warning($e);
            }
        }

        return array_map(fn($quantity) => (int)max($quantity, 0), $list);
    }

    public function invoice($id = null)
    {
        $this->_validateOrderForInvoice($id);

        $order = $this->Order->findForPopup($id);
        $refundTotals = $this->OrderRefund->getTotalsByOrder($id);
        $refunds = $this->OrderRefund->findForOrderTimeline($id);

        $reftotal = $refundTotals['OrderRefund']['total_amount'];
            //TODO: convert to use the calc discounts
        $orderTotal = $order['Order']['total_price'] - $order['Order']['total_discount'];

        $this->set('order', $order);
        $this->set('subTotal', array_sum(array_column($order['OrderProduct'], 'total_price')));
        $this->set('orderTotal', $orderTotal);
        $this->set('refunds', $refunds);
        $this->set('refundTotals', $refundTotals);
        $this->set('refund_total', $reftotal);

        $orderTagUserId = $this->getOrderTagUserId($order);

        $this->set('existingTagOptions', $this->OrderTag->getTagsByOrder($id, $orderTagUserId));
        $this->set('tagOptions', $this->OrderTagName->getOrderTags($orderTagUserId));
        $this->set('CustomerAddress', $this->_formatOrderCustomerAddress($order));
        $this->set('BillingAddress', $this->_formatOrderAddress($order, 'BillingAddress'));
        $this->set('RetailerAddress', $this->User->formatUserAddress($order['Order']['retailer_id']));
        $this->set('BrandAddress', $this->User->formatUserAddress($order['Order']['user_id']));
        $this->set('customTitles', $this->User->findEcommerceCustomContent($order['Order']['user_id']));

        $this->set('CourierName', $order['Courier']['name']);
        $this->set('orderNO', $this->Order->getSourceOrderName($order));
        $this->set('show_secret_code', $this->_showSecretCode($this->Auth->user('id'), $order['Order']['user_id'], $order['Order']['retailer_id']));

        $shippingLabel = $this->_shippingLabel($order);
        $this->set('shippingLabel', $shippingLabel);

        $this->set('timeline_logs', $this->_timeline_logs($order, $refunds, $shippingLabel));
        $this->set('invoiceLink', $this->_getInvoicePdfLink($id, $order['Order']['order_type']));

        $this->_setDealerOrderPopupView($order);

        if ($this->Auth->user('user_type') === User::TYPE_RETAILER) {
            $this->set('schedule_details', $this->UserSchedule->findRetailerSchedule((int)$order['Order']['retailer_id'], (string)$order['Order']['order_type']));
        }
    }

    public function updateNotes()
    {
        if ($this->request->is('post')) {
            $orderId = $this->request->data('order_id');
            $newNotes = $this->request->data('notes');

            $order = $this->Order->get($orderId);

            try {
                $this->_checkOrderBrandPermission($orderId);

                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
            } catch (HttpException $e) {
                CakeLog::error($e);
                $this->set(['success' => false, 'message' => __('Order not found.'), '_serialize' => ['success', 'message']]);
                return;
            }

            $order['Order']['notes'] = $newNotes;
            if ($this->Order->save($order)) {
                $this->set(['success' => true, 'message' => __('Notes updated for the order.'), '_serialize' => ['success', 'message']]);
            } else {
                $this->set(['success' => false, 'message' => __('Unable to update notes.'), '_serialize' => ['success', 'message']]);
            }
        }
    }

    public function updateTags()
    {
        if ($this->request->is('post')) {
            $newTags = (array)($this->request->data('tags') ?: []);
            $orderId = $this->request->data('order_id');

            $order = $this->Order->findForPopup($orderId);
            $tagUserId = $this->getOrderTagUserId($order);

            try {
                $this->OrderLogic->isAuthorizedForOrder($orderId);

                $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
            } catch (HttpException $e) {
                CakeLog::error($e);
                $this->set(['success' => false, 'message' => __('Order not found.'), '_serialize' => ['success', 'message']]);
                return;
            }

            $tagIds = [];
            foreach ($newTags as $tagName) {
                $existingTag = $this->OrderTagName->find('first', [
                    'conditions' => [
                        'OrderTagName.name' => $tagName,
                        'OrderTagName.user_id' => $tagUserId,
                    ],
                    'fields' => ['id']
                ]);

                if (!empty($existingTag) && isset($existingTag['OrderTagName']['id'])) {
                    $tagIds[] = $existingTag['OrderTagName']['id'];
                } else {
                    $this->OrderTagName->create();
                    $data = [
                        'name' => $tagName,
                        'user_id' => $tagUserId,
                    ];
                    if ($this->OrderTagName->save($data)) {
                        $tagIds[] = $this->OrderTagName->id;
                    } else {
                        $this->set(['success' => false, 'message' => __('Unable to save order tag name: %s', $tagName), '_serialize' => ['success', 'message']]);

                        return;
                    }
                }
            }

            $orderTagIdByTagId = (array)$this->OrderTag->find('list', [
                'contain' => ['OrderTagName'],
                'conditions' => [
                    'OrderTag.order_id' => $orderId,
                    'OrderTagName.user_id' => $tagUserId,
                ],
                'fields' => ['order_tag_name_id', 'id'],
            ]);
            $newTagIds = array_values(array_diff($tagIds, array_keys($orderTagIdByTagId)));
            $newOrderTagRecords = array_map(fn(int $tagId): array => [
                'id' => null,
                'order_id' => $orderId,
                'order_tag_name_id' => $tagId,
            ], $newTagIds);
            $deletedOrderTagIds = array_values(array_diff_key($orderTagIdByTagId, array_flip($tagIds)));

            if (
                (!$newOrderTagRecords || $this->OrderTag->saveMany($newOrderTagRecords))
                && (!$deletedOrderTagIds || $this->OrderTag->deleteAllJoinless(['OrderTag.id' => $deletedOrderTagIds], false))
                && $this->OrderTagName->deleteAllOrphans(['OrderTagName.user_id' => $tagUserId])
            ) {
                $this->set(['success' => true, 'message' => __('Tags updated for the order.'), '_serialize' => ['success', 'message']]);
            } else {
                $this->set(['success' => false, 'message' => __('Unable to save order tags.'), '_serialize' => ['success', 'message']]);
            }

            $this->redirect($this->referer());
        }
    }

    private function _validateOrderForInvoice($id)
    {
        $log = $this->OrderLogic->isAuthorizedForOrder($id);
        if ($log['Order']['order_type'] === OrderType::WHOLESALE && $this->request->param('filter') !== 'dealerorderTable') {
            $this->redirect(['controller' => 'orders', 'action' => 'invoice', 'id' => $id, 'filter' => 'dealerorderTable']);
        }
        return $log;
    }

    private function _shippingLabel($order)
    {
        $shippingLabel = __('Shipping');
        if ($order['Order']['is_install']) {
            $shippingLabel = __('Installation');
        } elseif ($order['Order']['order_type'] === OrderType::LOCAL_DELIVERY && !empty($order['User']['local_delivery_shipping_title'])) {
            $shippingLabel = $order['User']['local_delivery_shipping_title'];
        }
        return $shippingLabel;
    }

    private function _timeline_logs($order, $refunds, $shippingLabel)
    {
        $currencyCode = $order['Order']['currency_code'];

        $commentEvents = array_map(function($comment) {
                return TimelineEvent::createFromOrderComment($comment);
            },
            $this->OrderComment->findAllForOrderTimeline($order['Order']['id'], $this->Auth->user('user_type'))
        );

        $customerMessageEvents = array_map(function($message) {
            return TimelineEvent::createFromCustomerMessage($message);
        },
            $this->OrderCustomerMessage->findAllForOrderTimeline($order['Order']['id'])
        );

        $fulfillmentEvents = array_map(function ($fulfillment) {
            return TimelineEvent::createFromFulfillment($fulfillment);
        }, $this->Fulfillment->findAllForOrderTimeline($order['Order']['id']));

        $refundEvents = array_map(function($refund) use ($currencyCode, $shippingLabel) {
            return TimelineEvent::createFromRefund($refund, $currencyCode, $shippingLabel);
        }, array_values($refunds));

        $dealerRefundEvents = array_map(function($refund) use ($currencyCode) {
            return TimelineEvent::createFromDealerRefund($refund, $currencyCode);
        }, $this->DealerOrderRefund->findAllForOrderTimeline($order['Order']['id']));

        $paymentEvents = [];
        if ($order['Order']['payment_captured_at']) {
            $paymentEvents[] = TimelineEvent::createFromOrderPaymentCaptured($order);
        }

        $deliveryEvents = [];
        if ($order['Order']['deliveryDate']) {
            $deliveryEvents[] = TimelineEvent::createFromDelivery($order);
        }

        $shippedEvents = [];
        if ($order['Order']['order_type'] === OrderType::SHIP_FROM_STORE) {
            if ($order['DealerOrder']['shipment_date']) {
                $shippedEvents[] = TimelineEvent::createFromDealerOrderShipment($order);
            }
            // Using the status to show the shipped to customer event item.
            // On double ship orders shipped_date is not enough to know an order has been shipped to customer.
            if ($order['Order']['shipped_date'] && $order['Order']['order_status'] === OrderStatus::DELIVERED) {
                $shippedEvents[] = TimelineEvent::createFromShipment($order);
            }
        }

        $payoutEvents = [];

        $payoutEvents = array_map(function ($payout) {
            return TimelineEvent::createFromOrderPayout($payout);
        }, $order['OrderPayout']);

        $timeline = array_merge($commentEvents, $customerMessageEvents, $fulfillmentEvents, $refundEvents, $dealerRefundEvents, $paymentEvents, $deliveryEvents, $shippedEvents, $payoutEvents);
        // sort by timestamp desc
        $timeline = usort($timeline, function (TimelineEvent $a, TimelineEvent $b) {
            return $b->timestamp - $a->timestamp;
        }) ? $timeline : [];

        return $timeline;
    }

    public function capture_payment($orderId)
    {
        $orderId = (int)$orderId;
        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);
        $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPES_BRAND);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        $this->request->allowMethod(['post', 'put']);

        $orderType = OrderType::filterOrderType($log['Order']['order_type']);

        if (
            !$log['Order']['is_commission_retailer']
            && $orderType !== OrderType::SELL_DIRECT
        ) {
            throw new BadRequestException('Invalid order type: ' . json_encode($log['Order']['order_type']));
        }

        $order = $this->Order->findById($orderId, [
            'id',
            'user_id',
            'retailer_id',
            'orderID',
            'is_commission_retailer',
            'order_type',
            'payment_status',
            'payment_method',
            'total_price',
            'total_discount',
            'transactionID',
            'stripe_account',
            'shipearlyFees',
        ], null, -1);

        $orderID = $order['Order']['orderID'];

        if ($order['Order']['payment_status'] != OrderPaymentStatus::PAID) {
            $site_type = (string)$this->User->fieldByConditions('site_type', ['User.id' => $order['Order']['user_id']]);

            try {
                if (!$this->OrderLogic->captureStripeCharge($orderId, $order['Order'])) {
                    throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                }

                $nextOrderStatus = null;
                if ($orderType === OrderType::SELL_DIRECT) {
                    if ($site_type === UserSiteType::SHOPIFY) {
                        $nextOrderStatus = OrderStatus::SHOPIFY_PAID;
                    } elseif ($site_type === UserSiteType::WOOCOMMERCE) {
                        $nextOrderStatus = OrderStatus::WC_PROCESSING;
                    }
                }

                if (
                    $nextOrderStatus
                    && !$this->Order->save(['id' => $orderId, 'order_status' => $nextOrderStatus])
                ) {
                    throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                }
            } catch (Exception $e) {
                CakeLog::error($e);

                return $this->_exceptionResponse(null, __('There was an error capturing the payment for order %s. Please, try again.', $orderID));
            }

            try {
                if (!$this->OrderPlacer->captureConsumerOrderPayment($orderId)) {
                    throw new InternalErrorException('Failed to capture the ecommerce payment.');
                }
            } catch (Exception $e) {
                CakeLog::error($e);

                $this->setFlash(__('There was an error capturing the payment for order %s in %s.', $orderID, $site_type), 'error');
            }
        }

        return $this->_successResponse(__('Order %s has been captured', $orderID));
    }

    /**
     * @param int $authUserId
     * @param int $orderBrandId
     * @param int $orderStoreId
     * @return bool
     */
    protected function _showSecretCode($authUserId, $orderBrandId, $orderStoreId)
    {
        if ($authUserId == $orderBrandId) {
            return true;
        }

        $salesRepIds = array_keys($this->ManufacturerSalesRep->listConnectedSalesReps($orderBrandId));
        if (in_array($authUserId, $salesRepIds)) {

            $isDistributorBrand = $this->ManufacturerRetailer->exists([
                    'user_id' => $orderBrandId,
                    'distributor_id' => $authUserId,
                    'retailer_id' => $orderStoreId,
            ]);

            if($isDistributorBrand) {
                return true;
            }

            $salesRepStoreIds = array_keys($this->User->listSalesRepStoreNames($authUserId));
            return in_array($orderStoreId, $salesRepStoreIds);  
        }
        return false;
    }

    /**
     * @param array $order
     * @param string $addressAlias
     * @return string
     */
    private function _formatOrderAddress(array $order, string $addressAlias)
    {
        if (empty($order[$addressAlias]['id'])) {
            return '';
        }

        $customerName = trim($order[$addressAlias]['first_name'] . ' ' . $order[$addressAlias]['last_name']);
        if ($this->Auth->user('user_type') !== User::TYPE_MANUFACTURER) {
            $customerUrl = Router::url(['controller' => 'orders', 'action' => 'message_customer', $order['Order']['id']]);
            $customerName = "<a href=\"{$customerUrl}\" class=\"ajax-message-customer\">{$customerName}</a>";
        } elseif (!empty($order['Order']['customerID'])) {
            $customerUrl = BASE_PATH . "customer/{$order['Order']['customerID']}/orders";
            $customerName = "<a href=\"{$customerUrl}\" target=\"_blank\">{$customerName}</a>";
        }

        $address = $this->addressDisplayFormat(['company_name' => $customerName] + $order[$addressAlias]);

        if (!empty($order[$addressAlias]['latitude']) && !empty($order[$addressAlias]['longitude'])) {
            $mapUrl = "https://www.google.com/maps/?q={$order[$addressAlias]['latitude']},{$order[$addressAlias]['longitude']}";
            $address .= '<br />' . "<a href=\"{$mapUrl}\" target=\"_blank\">" . __('View Map') . "</a>";
        }

        return $address;
    }

    public function cancel($id = null): ?CakeResponse
    {
        $this->_checkOrderBrandPermission($id);
        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        $this->request->allowMethod('post', 'delete');

        $this->Order->bindModel(['hasOne' => ['DealerOrder']], false);
        $order = (array)$this->Order->get($id, [
            'contain' => [
                'DealerOrder' => ['fields' => ['id']],
            ],
            'fields' => [
                'id',
                'orderID',
                'order_type',
                'order_status',
                'payment_method',
                'transactionID',
                'stripe_account',
            ],
        ]);
        $this->Order->unbindModel(['hasOne' => ['DealerOrder']], false);

        $orderID = (string)$order['Order']['orderID'];
        $transactionID = (string)$order['Order']['transactionID'];
        $isStripeOrder = ($order['Order']['payment_method'] === OrderPaymentMethod::STRIPE);

        $canDelete = (
            $order['Order']['order_type'] === OrderType::WHOLESALE &&
            $order['Order']['order_status'] === OrderStatus::PURCHASE_ORDER &&
            (!$transactionID || $isStripeOrder) &&
            empty($order['DealerOrder']['id'])
        );
        if (!$canDelete) {
            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['message' => 'Unable to cancel order'] + $order)),
                __('Unable to cancel order %s', json_encode($orderID)),
                true
            );
        }

        if ($transactionID && $isStripeOrder) {
            try {
                $this->Stripe->cancelUnconfirmedPayment($order['Order']['stripe_account'], $transactionID);
            } catch (\Stripe\Exception\ApiErrorException $e) {
                return $this->_exceptionResponse(null, __('Failed to cancel payment for order %s', json_encode($orderID)), $e);
            }
        }

        $notificationData = $this->NotificationLogic->findOrderCancellationEmailData($id);

        if (!$this->Order->cancel($id)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['message' => 'Failed to cancel order'] + $order)),
                __('Failed to cancel order %s', json_encode($orderID)),
                true
            );
        }

        $this->NotificationLogic->sendOrderCancellationEmail($notificationData);

        return $this->_successResponse(
            __('Order %s has been cancelled', json_encode($orderID)),
            ['controller' => 'orders', 'action' => 'dealerorders_index']
        );
    }

    public function save_requested_b2b_ship_date($orderId = null)
    {
        $this->request->data['Order']['requested_b2b_ship_date'] = format_from_datepicker($this->request->data('Order.requested_b2b_ship_date'));

        return $this->_save_field_from_order_header($orderId, 'requested_b2b_ship_date');
    }

    public function save_payment_option($orderId = null)
    {
        try {
            $order = $this->_checkOrderBrandPermission($orderId);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        } catch (HttpException $e) {
            return $this->_exceptionResponse($e, current($this->response->httpCodes($e->getCode())), true);
        }

        $this->request->allowMethod('post', 'put');

        $paymentMethod = $this->request->data("Order.payment_method");

        $allowedPaymentMethods = OrderPaymentMethod::getSelectOptions();
        if (!array_key_exists($paymentMethod, $allowedPaymentMethods)) {
            return $this->_exceptionResponse(new InternalErrorException("Selected payment method is not allowed."), null, true);
        }

        if (!in_array($order['Order']['order_status'], [OrderStatus::PROCESSING, OrderStatus::PURCHASE_ORDER])) {
            return $this->_exceptionResponse(new InternalErrorException("Payment method cannot be changed as the order status is not valid."), null, true);
        }

        if (!$paymentMethod) {
            return $this->_exceptionResponse(new InternalErrorException("No Payment method selected"), null, true);
        }

        if (!$this->Order->save(['id' => $orderId, 'payment_method' => $paymentMethod])) {
            if (isset($this->Order->validationErrors['payment_method'])) {
                return $this->_validationErrorFlashResponse($this->Order->validationErrors['credit_term_id']);
            }
            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data])), null, true);
        }

        return $this->_successResponse("Order '{$order['Order']['orderID']}' has been updated");
    }

    public function save_credit_term($orderId = null)
    {
        try {
            $order = $this->_checkOrderBrandPermission($orderId);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        } catch (HttpException $e) {
            return $this->_exceptionResponse($e, current($this->response->httpCodes($e->getCode())), true);
        }

        $this->request->allowMethod('post', 'put');

        $creditTermId = $this->request->data("Order.CreditTerm.id");

        if (!in_array($order['Order']['order_status'], [OrderStatus::PROCESSING, OrderStatus::PURCHASE_ORDER])) {
            return $this->_exceptionResponse(new InternalErrorException("Credit term cannot be changed as the order status is not valid."), null, true);
        }

        if (!$creditTermId) {
            return $this->_exceptionResponse(new InternalErrorException("No Credit Term selected"), null, true);
        }

        if (!$this->Order->save(['id' => $orderId, 'credit_term_id' => $creditTermId])) {
            if (isset($this->Order->validationErrors['credit_term_id'])) {
                return $this->_validationErrorFlashResponse($this->Order->validationErrors['credit_term_id']);
            }
            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data])), null, true);
        }

        return $this->_successResponse("Order '{$order['Order']['orderID']}' has been updated");
    }

    public function save_external_invoice_id($orderId = null)
    {
        return $this->_save_field_from_order_header($orderId, 'external_invoice_id');
    }

    public function save_purchase_order_number($orderId = null)
    {
        return $this->_save_field_from_order_header($orderId, 'purchase_order_number');
    }

    private function _save_field_from_order_header($orderId, string $field)
    {
        try {
            $order = $this->_checkOrderBrandPermission($orderId);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        } catch (HttpException $e) {
            return $this->_exceptionResponse($e, current($this->response->httpCodes($e->getCode())), true);
        }

        $this->request->allowMethod('post', 'put');

        if (!$this->Order->save(['id' => $orderId, $field => $this->request->data("Order.{$field}")])) {
            if (isset($this->Order->validationErrors[$field])) {
                return $this->_validationErrorFlashResponse($this->Order->validationErrors[$field]);
            }
            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data])), null, true);
        }
        return $this->_successResponse("Order '{$order['Order']['orderID']}' has been updated");
    }

    /**
     * Check if the current Auth user is a brand associated with the given order.
     *
     * @param int|string $orderId
     * @return array Log data containing (User)'Auth' and 'Order' models with fields intended for diagnostic logging.
     * @throws NotFoundException Thrown if the order does not exist. Message contains json log data.
     * @throws ForbiddenException Thrown if the order does not belong to the current Auth user. Message contains json log data.
     */
    private function _checkOrderBrandPermission($orderId)
    {
        $log = $this->OrderLogic->isAuthorizedForOrder($orderId);

        if ($this->Auth->user('id') != $log['Order']['user_id']) {
            throw new ForbiddenException(json_encode($log));
        }

        return $log;
    }

    /**
     * Webhook URL for Aftership to update last check points.
     *
     * @return CakeResponse
     * @see https://www.aftership.com/docs/api/4/webhook
     */
    public function aftershipwebhook()
    {
        $this->autoRender = false;
        if (empty($this->request->data['msg']['slug']) || empty($this->request->data['msg']['tracking_number'])) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . 'Bad Request ' . json_encode($this->request->data));
            return $this->response;
        }
        $this->_webhookServiceLog('Webhook Received ' . json_encode($this->request->data));
        $this->Btask->queueTask(Btask::TYPE_AFTERSHIP_WEBHOOK, $this->request->data);
        return $this->response;
    }

    public function admin_view($orderID = null)
    {
        $this->layout = 'admin';

        $orderID = '#' . ltrim($orderID, '#');
        $orderInfo = $this->Order->findForAdminView($orderID);
        if (empty($orderInfo['Order']['id'])) {
            return $this->_exceptionResponse(
                new NotFoundException(json_encode(['Order' => ['orderID' => $orderID]])),
                "Order '{$orderID}' not found"
            );
        }

        $refundTotals = [
            'OrderRefund' => [
                'total_amount' => format_number(array_sum(array_column($orderInfo['OrderRefund'], 'amount'))),
                'total_shipping_portion' => format_number(array_sum(array_column($orderInfo['OrderRefund'], 'shipping_portion'))),
                'total_tax_portion' => format_number(array_sum(array_column($orderInfo['OrderRefund'], 'tax_portion'))),
                'total_restocking_fees' => format_number(array_sum(array_column($orderInfo['OrderRefund'], 'restocking_fees'))),
                'total_fees' => format_number(array_sum(array_column($orderInfo['OrderRefund'], 'fees'))),
                'count' => format_number(count($orderInfo['OrderRefund']), 0),
            ],
        ];
        $reftotal = $refundTotals['OrderRefund']['total_amount'];

        $refunds = array_map(
            function($refund) {
                return ['OrderRefund' => $refund];
            },
            $orderInfo['OrderRefund']
        );
        unset($orderInfo['OrderRefund']);

        if (!empty($orderInfo['Order']['user_id'])) {
            $this->set('manufactureAddress', $this->User->formatUserAddress($orderInfo['Order']['user_id']));
        }
        if (!empty($orderInfo['Order']['retailer_id'])) {
            $this->set('retailerAddress', $this->User->formatUserAddress($orderInfo['Order']['retailer_id']));
        }

        $this->set('CustomerAddress', $this->_formatOrderCustomerAddress($orderInfo));
        $this->set('CourierName', $orderInfo['Courier']['name']);
        $this->set('orderNO', $this->Order->getSourceOrderName($orderInfo));
        $this->set('type', $this->request->param('type'));
        $this->set('order', $orderInfo);
        $this->set('refunds', $refunds);
        $this->set('refundTotals', $refundTotals);
        $this->set('disable_refund', ($reftotal >= $orderInfo['Order']['total_price']));
    }

    public function admin_dealer_order_edit($dealerOrderId = null)
    {
        if ($this->request->is(['post', 'put'])) {
            $quantities = Hash::extract($this->request->data, 'Order.dealer_quantity.{n}.{n}');
            $filtered = array_filter($quantities, function($quantity) { return $quantity > 0; });
            if (empty($quantities) || count($filtered) !== count($quantities)) {
                $e = new BadRequestException('Deleting not supported. Must have at least 1 of each line item.');
                return $this->_exceptionResponse($e, $e->getMessage());
            }

            return $this->_put_admin_dealer_order_edit(
                $dealerOrderId,
                $this->_flattenDealerProductsRequest($this->request->data),
                $this->request->data['Order']['dealer_shipping_amount']
            );
        }

        $dealerOrder = $this->DealerOrder->findForAdminEditView($dealerOrderId);
        if (empty($dealerOrder['DealerOrder']['id'])) {
            throw new NotFoundException(json_encode(['DealerOrder' => ['id' => $dealerOrderId]]));
        }
        $this->request->data = $dealerOrder;
        $this->set(compact('dealerOrder'));
    }

    /**
     * @param int $dealerOrderId
     * @param array $dealerProducts
     * @param float $shippingAmount
     * @return CakeResponse|null
     */
    private function _put_admin_dealer_order_edit($dealerOrderId, array $dealerProducts, $shippingAmount)
    {
        $dealerOrder = $this->DealerOrder->find('first', [
            'contain' => [
                'Order' => ['fields' => ['id', 'order_type', 'total_price', 'totalPriceConversion', 'tax_included']],
                'DealerOrderProduct' => ['fields' => ['id', 'dealer_order_id', 'product_id', 'warehouse_id', 'total_discount', 'tax']],
            ],
            'conditions' => ['DealerOrder.id' => $dealerOrderId],
            'fields' => ['id', 'order_id', 'tax', 'splitpayment_percentage', 'product_details'],
        ]);
        try {
            if (empty($dealerOrder['DealerOrder']['id'])) {
                throw new NotFoundException(json_encode(['DealerOrder' => ['id' => $dealerOrderId]]));
            }

            //TODO support discounts
            if (array_sum(array_column($dealerOrder['DealerOrderProduct'], 'total_discount')) > 0) {
                throw new BadRequestException('Editing items with b2b discounts is not supported');
            }
        } catch (HttpException $e) {
            return $this->_exceptionResponse($e, get_class($e) . ': ' . $e->getMessage(), true);
        }

        $dealer_qty_ordered = json_decode($dealerOrder['DealerOrder']['product_details'], true);
        $productDetailsProducts = Order::extractProductDetailsProducts($dealerProducts);

        $dealerSubtotal = array_sum(
            array_map(function($product) { return $product['quantity'] * $product['dealer_price']; }, $productDetailsProducts)
        );

        $b2bTaxPercent = $dealerOrder['DealerOrder']['tax'];
        $b2bTaxRate = $b2bTaxPercent / 100;
        $taxIncluded = (bool)$dealerOrder['Order']['tax_included'];

        $dealerTaxPercentMap = Hash::combine($dealerOrder['DealerOrderProduct'], '{n}.product_id', '{n}.tax', '{n}.warehouse_id');
        $dealerProducts = array_map(
            function($dealerProduct) use ($b2bTaxPercent, $dealerTaxPercentMap) {
                $productId = $dealerProduct['product_id'];
                $warehouseId = $dealerProduct['warehouse_id'];
                $b2bTaxPercent = $dealerTaxPercentMap[$warehouseId][$productId] ?? $b2bTaxPercent;

                return array_merge($dealerProduct, ['tax_rate' => format_number($b2bTaxPercent / 100, 6)]);
            },
            $dealerProducts
        );

        $subtotalTaxes = array_sum(array_map(
            function($product) use ($taxIncluded) {
                return round(calculate_tax_amount($product['quantity'] * $product['dealer_price'], $product['tax_rate'], $taxIncluded), 2);
            },
            $dealerProducts
        ));
        $shippingTaxes = ($dealer_qty_ordered['b2b_shipping_tax_option'])
            ? round(calculate_tax_amount($shippingAmount, $b2bTaxRate, $taxIncluded), 2)
            : 0;
        $dealerTaxes = $subtotalTaxes + $shippingTaxes;
        $dealerTotal = $dealerSubtotal + $shippingAmount;
        if (!$taxIncluded) {
            $dealerTotal += $dealerTaxes;
        }
        $splitpaymentAmount = format_number(($dealerOrder['DealerOrder']['splitpayment_percentage'] / 100) * $dealerTotal);

        $dealer_qty_ordered = array_merge($dealer_qty_ordered, array(
            'products' => $productDetailsProducts,
            'shipping_amount' => $shippingAmount,
            'splitpayment_amount' => $splitpaymentAmount,
        ));

        $dealerProductsMap = Hash::combine($dealerProducts, '{n}.product_id', '{n}', '{n}.warehouse_id');

        $save = array(
            'Order' => array(
                'id' => $dealerOrder['DealerOrder']['order_id'],
                'dealer_qty_ordered' => json_encode($dealer_qty_ordered),
            ),
            'DealerOrder' => array(
                'id' => $dealerOrderId,
                'total_price' => $dealerTotal,
                'product_total_price' => $dealerSubtotal,
                'total_tax' => $dealerTaxes,
                'shipping_amount' => $shippingAmount,
                'shipping_tax_amount' => $shippingTaxes,
                'splitpayment_amount' => $splitpaymentAmount,
                'product_details' => json_encode($dealer_qty_ordered),
            ),
            'DealerOrderProduct' => array_map(
                function($dealerOrderProduct) use ($dealerProductsMap) {
                    $productId = $dealerOrderProduct['product_id'];
                    $warehouseId = $dealerOrderProduct['warehouse_id'];

                    return array(
                        'id' => $dealerOrderProduct['id'],
                        'product_quantity' => (int)($dealerProductsMap[$warehouseId][$productId]['quantity'] ?? 0),
                        'product_price' => format_number($dealerProductsMap[$warehouseId][$productId]['dealer_price'] ?? 0.00),
                    );
                },
                $dealerOrder['DealerOrderProduct']
            ),
        );

        $orderId = $dealerOrder['Order']['id'];
        $currencyConversion = ((float)$dealerOrder['Order']['totalPriceConversion'] > 0 && (float)$dealerOrder['Order']['total_price'] > 0)
            ? (float)$dealerOrder['Order']['totalPriceConversion'] / (float)$dealerOrder['Order']['total_price']
            : 1.0;

        $successMessages = array();
        try {
            if (!$this->DealerOrder->saveAssociated($save)) {
                $save['DealerOrder']['product_details'] = json_decode($save['DealerOrder']['product_details'], true);
                throw new InternalErrorException(json_encode(['errors' => $this->DealerOrder->validationErrors, 'data' => $this->DealerOrder->data]));
            }
            $successMessages[] = 'Updated ' . json_encode(['DealerOrder' => ['id' => $save['DealerOrder']['id'], 'shipping_amount' => $save['DealerOrder']['shipping_amount']]]);
            $successMessages[] = 'Updated ' . json_encode(['DealerOrderProduct' => $save['DealerOrderProduct']]);

            if ($dealerOrder['Order']['order_type'] === OrderType::WHOLESALE) {
                if (!$this->Order->updateConfirmedPurchaseOrder($orderId, $dealer_qty_ordered, $dealerProducts, '0.00', $dealerOrder['DealerOrder']['tax'], $dealer_qty_ordered['shipping_amount'], $dealerTaxes, $currencyConversion)) {
                    throw new InternalErrorException("Failed to update order " . json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                }
            }
            $successMessages[] = 'Updated ' . json_encode(['Order' => ['id' => $orderId]]);

            return $this->_successResponse(implode('<br />', $successMessages));
        } catch (InternalErrorException $e) {
            $this->setFlash(implode('<br />', $successMessages), 'success');
            return $this->_exceptionResponse($e, null, true);
        }
    }

    public function admin_resetCode($id = null)
    {
        $order = $this->Order->record($id, ['fields' => ['id', 'user_id', 'retailer_id', 'customerEmail', 'orderID', 'order_type', 'secretcode', 'customer_name']]);
        if (empty($order['Order']['id'])) {
            throw new NotFoundException();
        }

        $code = $this->OrderLogic->generateCode();
        if ($this->NotificationLogic->codeResetMail($code, $order)) {
            if ($this->Order->resetCode($id, $code)) {
                return $this->_successResponse('A new verification code has been sent to the customer.');
            }
        }
        return $this->_exceptionResponse();
    }

    public function retailerResetCode($id = null)
    {
        $order = $this->Order->record($id, ['fields' => ['id', 'user_id', 'retailer_id', 'customerEmail', 'orderID', 'order_type', 'secretcode', 'customer_name']]);
        if (empty($order['Order']['id'])) {
            throw new NotFoundException();
        }

        $type = $this->request->params['type'] ?? $order['Order']['order_type'];
        $schedule_id = (int)$this->request->param('schedule_id');
        $schedule_link = (string)$this->UserSchedule->field('schedule_link', ['id' => $schedule_id]);

        $code = $order['Order']['secretcode'];

        // Ensure MailQueue::$data is only popuplated from this send attempt
        $this->MailQueue->clear();
        if ($this->NotificationLogic->codeResetMail($code, $order, $schedule_link)) {
            return $this->_successResponse(__('The verification code has been emailed to the customer. It may take up to 1 minute for them to receive it.'));
        } elseif (
            ($this->MailQueue->data['MailQueue']['unique_hash'] ?? false) &&
            $this->MailQueue->exists(['MailQueue.unique_hash' => $this->MailQueue->data['MailQueue']['unique_hash']])
        ) {
            return $this->_exceptionResponse(null, __('Please wait at least 1 minute for the customer to receive their verification code email before attempting to send it again.'));
        }
        return $this->_exceptionResponse();
    }

    public function message_customer($orderId = null)
    {
        $this->OrderLogic->isAuthorizedForOrder($orderId);

        $this->Order->bindModel(['belongsTo' => [
            'Brand' => ['className' => 'User', 'foreignKey' => 'user_id'],
        ]]);
        $order = $this->Order->find('first', [
            'contain' => [
                'Brand' => ['fields' => ['id', 'company_name']],
            ],
            'conditions' => ['Order.id' => $orderId],
            'fields' => [
                'Order.id',
                'Order.orderID',
                'Order.customerEmail',
                'Order.customer_name',
            ],
        ]);

        $defaultSubject = "{$order['Brand']['company_name']} - Order {$order['Order']['orderID']}";

        if ($this->request->is('post')) {
            try {
                $success = $this->OrderCustomerMessage->doInTransaction(function () use ($orderId, $order, $defaultSubject) {
                    $fromName = $this->Auth->user('company_name');
                    $fromEmail = $this->Contactpersons->fieldByConditions('email', ['Contactpersons.user_id' => $this->Auth->user('id')])
                        ?: $this->Auth->user('email_address');

                    $this->request->data['OrderCustomerMessage']['from'] = "{$fromName} <{$fromEmail}>";
                    $this->request->data['OrderCustomerMessage']['subject'] = $this->request->data['OrderCustomerMessage']['subject'] ?: $defaultSubject;

                    if (!$this->OrderCustomerMessage->addToOrder($orderId, $this->Auth->user('id'), $this->request->data)) {
                        throw new InternalErrorException(json_encode(['errors' => $this->OrderCustomerMessage->validationErrors, 'data' => $this->OrderCustomerMessage->data]));
                    }

                    $fakeEmailTemplate = $this->OrderCustomerMessage->findAsFakeEmailTemplate($this->OrderCustomerMessage->id);
                    return $this->sendEmail($fakeEmailTemplate, [], $order['Order']['customerEmail']);
                });
            } catch (InternalErrorException $e) {
                return $this->_exceptionResponse($e, null, true);
            }
            if (!$success) {
                // Exceptions from sendEmail are already logged
                return $this->_exceptionResponse(null, null, false);
            }

            if ($this->request->is('ajax')) {
                $this->response->body(json_encode(['OrderCustomerMessage' => ['id' => $this->OrderCustomerMessage->id]]));
            }
            return $this->_successResponse(__('The message has been sent to the consumer.'));
        }

        $customerName = $order['Order']['customer_name'];
        $this->set(compact('customerName', 'defaultSubject'));
    }

    public function customer_index($customerId = null)
    {
        $customer = $this->Customer->findOrderIndex($customerId);
        if (empty($customer['Customer']['id'])) {
            throw new NotFoundException(json_encode([
                'message' => 'Customer not found',
                'Customer' => ['id' => $customerId],
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'user_type', 'email_address', 'company_name'])),
            ]));
        }
        if ($customer['Customer']['user_id'] != $this->Auth->user('id')) {
            throw new ForbiddenException(json_encode([
                'message' => 'User does not have access to this customer',
                'Customer' => ['user_id' => $customer['Customer']['user_id']],
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'user_type', 'email_address', 'company_name'])),
            ]));
        }

        $query = $this->_extractIndexQueryParams($this->request);

        $conditions = $this->_orderIndexConditions(
            $this->getAuthUserIds(),
            $this->request->param('filter'),
            $query,
            $customerId
        );

        $sort = $query['sort'];
        $direction = $query['direction'];
        $limit = (int)$query['limit'];
        $page = (int)$query['page'];

        $sortOrder = [$sort => $direction];
        // Special sorting cases
        if ($sort === 'Order.display_date') {
            $sortOrder += ['Order.orderID' => $direction];
        }

        $count = $this->Order->countAllForIndex($conditions);

        $orders = $this->_findAllForIndex($conditions, $sortOrder, $limit, $page);

        $this->set('customer', $customer);
        $this->set('latestAddress', !empty($customer['LastOrder']['id']) ? ['Order' => $customer['LastOrder']] : []);
        $this->set('query', $query);
        $this->set('sort', $sort);
        $this->set('direction', $direction);
        $this->set('limit', $limit);
        $this->set('page', $page);
        $this->set('count', $count);
        $this->set('orders', $orders);

        if ($this->request->is('ajax')) {
            return $this->render('/Elements/Orders/ajax_customer_index');
        }

        return null;
    }

    public function retailerpurchaseorders_index($retailerId = null)
    {
        $query = $this->_extractIndexQueryParams($this->request);

        $conditions = $this->_dealerorderIndexConditions(
            $this->getAuthUserIds(),
            $query['order_type'],
            $query['order_status'],
            $query['fulfillment_status'],
            $query['payment_method'],
            $query['search'],
            $retailerId,
            $query['brand'],
            $query['tag'],
            "",
            ""
        );

        $sort = $query['sort'];
        $direction = $query['direction'];
        $limit = (int)$query['limit'];
        $page = (int)$query['page'];

        $sortOrder = [$sort => $direction];
        // Special sorting cases
        if ($sort === 'Order.display_date') {
            $sortOrder += ['Order.orderID' => $direction];
        } elseif ($sort === 'Courier.name') {
            $sortOrder += ['Order.shipped_date' => $direction];
        } elseif ($sort === 'DealerOrder.total_price') {
            $sortOrder += ['Order.total_price' => $direction];
        } elseif ($sort === 'Order.purchase_order_number') {
            $sortOrder = ["IF(Order.order_type = '" . OrderType::WHOLESALE . "', 1, 0)" => $direction];
            $sortOrder += ["COALESCE(IF(Order.order_type = '" . OrderType::WHOLESALE . "', Order.purchase_order_number, {$this->Order->virtualFields['customer_name']}), '')" => $direction];
        }

        $count = $this->Order->countAllForDealerOrderIndex($conditions);

        $orders = $this->Order->findAllForDealerOrderIndex($conditions, $sortOrder, $limit, $page);

        $orders = array_map(function(array $order): array {
            $orderTagUserId = $this->getOrderTagUserId($order);
            $order['OrderTagName'] = array_filter($order['OrderTagName'], function(array $tag) use ($orderTagUserId): bool {
                return (int)$tag['user_id'] === (int)$orderTagUserId;
            });

            return $order;
        }, $orders);

        $this->Order->bindModel([
            'hasOne' => [
                'DealerOrder'
            ],
        ], false);

        $totalSpentData = $this->Order->find('all', [
            'contain' => ['DealerOrder'],
            'conditions' => $conditions,
            'fields' => ['SUM(DealerOrder.total_price) as total_spent'],
        ]);
        $totalSpent = $totalSpentData[0][0]['total_spent'];

        $this->Order->unbindModel([
            'hasOne' => ['DealerOrder'],
        ], false);

        $this->set('query', $query);
        $this->set('sort', $sort);
        $this->set('direction', $direction);
        $this->set('limit', $limit);
        $this->set('page', $page);
        $this->set('count', $count);
        $this->set('orders', $orders);
        $this->set('totalSpent', $totalSpent);
        $this->set('totalOrders', $count);

        if ($this->request->is('ajax')) {
            return $this->render('/Elements/Orders/ajax_retailerspurchaseorders');
        }

        return null;
    }

    public function index(): ?CakeResponse
    {
        $query = $this->_extractIndexQueryParams($this->request);

        $conditions = $this->_orderIndexConditions(
            $this->getAuthUserIds(),
            $this->request->param('filter'),
            $query
        );

        $sort = $query['sort'];
        $direction = $query['direction'];
        $limit = (int)$query['limit'];
        $page = (int)$query['page'];

        $sortOrder = [$sort => $direction];
        // Special sorting cases
        if ($sort === 'Order.display_date') {
            $sortOrder += ['Order.orderID' => $direction];
        }

        $count = $this->Order->countAllForIndex($conditions);

        $orders = $this->_findAllForIndex($conditions, $sortOrder, $limit, $page);

        $this->set('query', $query);
        $this->set('sort', $sort);
        $this->set('direction', $direction);
        $this->set('limit', $limit);
        $this->set('page', $page);
        $this->set('count', $count);
        $this->set('orders', $orders);
        $this->set('tagOptions', $this->OrderTagName->getOrderTagsById($this->Auth->user('Branch') ?: $this->Auth->user('id')));
        $this->set('salesRepOptions', $this->ManufacturerSalesRep->listConnectedSalesReps($this->Auth->user('id'), true));
        if ($this->request->is('ajax')) {
            return $this->render('/Elements/Orders/ajax_index');
        }

        return null;
    }

    /**
     * @param array $conditions
     * @param array $sortOrder
     * @param int $limit
     * @param int $page
     * @return array
     */
    //TODO move to Order model
    private function _findAllForIndex(array $conditions, array $sortOrder, int $limit, int $page): array
    {
        $this->Order->addAssociations([
            'belongsTo' => [
                'Brand' => ['className' => 'User', 'foreignKey' => 'user_id'],
                'Retailer' => ['className' => 'User'],
                'Customer' => ['foreignKey' => 'customerID'],
                'Distributor' => ['className' => 'User'],
            ],
            'belongsToMany' => [
                'OrderTagName' => ['with' => 'OrderTag', 'unique' => 'keepExisting'],
            ],
        ]);
        $originalVirtualFields = $this->Order->virtualFields;

        $this->Order->virtualFields['total_value'] = 'ROUND(Order.total_price - COALESCE(OrderRefundTotals.total_amount, 0), 2)';

        $orders = $this->Order->find('all', [
            'contain' => [
                'Brand' => ['fields' => ['company_name']],
                'Retailer' => ['fields' => ['company_name', 'Branch']],
                'Distributor' => ['fields' => ['id', 'company_name']],
                'Customer' => ['fields' => ['firstname', 'lastname']],
                'OrderTagName' => [
                    'with' => ['OrderTag' => []],
                    'fields' => ['user_id', 'name'],
                    'order' => ['OrderTagName.name' => 'ASC'],
                ],
            ],
            'joins' => [
                [
                    'table' => $this->OrderRefund->buildTotalsSubQuery(),
                    'alias' => 'OrderRefundTotals',
                    'type' => 'LEFT',
                    'conditions' => ['OrderRefundTotals.order_id' => $this->Order->primaryKeyIdentifier()],
                ],
            ],
            'conditions' => $conditions,
            'fields' => [
                'id',
                'user_id',
                'retailer_id',
                'distributor_id',
                'orderID',
                'order_type',
                'subType',
                'order_status',
                'Order.fulfillment_status',
                'is_dealerorder',
                'is_commission_retailer',
                'currency_code',
                'shipping_amount',
                'tax_included',
                'total_tax',
                'total_price',
            //TODO: convert to use the calc discounts
                'total_discount',
                'total_value',
                'payment_status',
                'risk_level',
                'discount_code',
                'shipping_address1',
                'shipping_address2',
                'shipping_city',
                'shipping_state',
                'shipping_country',
                'shipping_zipcode',
                'shipping_telephone',
                'total_qty_ordered',
                'latitude',
                'longitude',
                'courier',
                'trackingno',
                'payment_status_name',
                'customer_name',
                'display_date',
                'has_comments',
                'has_internal_comments',
                'has_customer_messages',
                'notes',
            ],
            'order' => $sortOrder,
            'limit' => $limit,
            'page' => $page,
        ]);

        $orders = array_map(function(array $order): array {
            $orderTagUserId = $this->getOrderTagUserId($order);
            $order['OrderTagName'] = array_filter($order['OrderTagName'], function(array $tag) use ($orderTagUserId): bool {
                return (int)$tag['user_id'] === (int)$orderTagUserId;
            });

            return $order;
        }, $orders);

        $this->Order->virtualFields = $originalVirtualFields;
        $this->Order->unbindModel(['belongsTo' => ['Brand', 'Retailer', 'Distributor', 'Customer']], false);
        $this->Order->unbindModel(['hasAndBelongsToMany' => ['OrderTagName']], false);

        return $orders;
    }

    /**
     * @param int $userId
     * @param string|null $filter
     * @param array $query
     * @param int|null $customerId
     * @return array
     */
    protected function _orderIndexConditions($userId, $filter, $query, $customerId = null)
    {
        $orderStatus = $query['order_status'];
        $fullfilltype = $query['order_type'];
        $paymentStatus = $query['payment_status'];
        $fulfillmentStatus = $query['fulfillment_status'];
        $orderSearch = $query['search'];
        $storeSearch = $query['store'];
        $brandSearch = $query['brand'];
        $tagSearch = $query['tag'];
        $distributorSearch = $query['distributor'] ?? null;
        $daterange = $query['daterange'];

        $conditions = array();

        if (!empty($customerId)) {
            $conditions['Order.customerID'] = $customerId;
        }

        $conditions = $this->_setOrderIndexUserFilters($userId, $storeSearch, $brandSearch, $distributorSearch, $conditions);

        if ($orderStatus) {
            $conditions['Order.order_status'] = $orderStatus;
        }

        if ($fullfilltype) {
            if (in_array(OrderType::SELL_DIRECT, $fullfilltype, true)) {
                $conditions[]['OR'] = [
                    'Order.order_type' => $fullfilltype,
                    'Order.order_type !=' => OrderType::TYPES_NOT_SELL_DIRECT,
                ];
            } else {
                $conditions['Order.order_type'] = $fullfilltype;
            }
        }
        $conditions['Order.order_type !='][] = OrderType::WHOLESALE;

        // Zero is a valid payment status
        if ($paymentStatus) {
            $conditions['Order.payment_status'] = $paymentStatus;
        }
        if ($fulfillmentStatus) {
            $conditions['Order.fulfillment_status'] = $fulfillmentStatus;
        }

        if ($tagSearch) {
            $conditions[] = $this->OrderTag->buildExistsSubquery([
                'OrderTag.order_id = Order.id',
                'OrderTag.order_tag_name_id' => $tagSearch,
            ]);
        }

        list($start_date, $end_date) = $this->_extractDateRange($daterange);
        $conditions = $this->_setOrderIndexDateRangeFilters($start_date, $end_date, $conditions);

        if ($filter == 'shipment') {
            $conditions['Order.order_type'] = array(OrderType::SHIP_FROM_STORE, OrderType::RETAILER_ORDER);
        } elseif ($filter == 'payment') {
            $conditions['Order.order_type'] = array(OrderType::SHIP_FROM_STORE, OrderType::IN_STORE_PICKUP);
        } elseif ($filter == 'ownorders') {
            $conditions['Order.order_type'] = OrderType::RETAILER_ORDER;
            $conditions['Order.customerID'] = $userId;
            unset($conditions['Order.user_id']);
            unset($conditions['Order.retailer_id']);
        } elseif ($filter == 'customerOrder') {
            $conditions['Order.order_type !='][] = OrderType::RETAILER_ORDER;
        }

        if (!empty($orderSearch)) {
            $conditions[] = $this->Order->buildSearchCondition($orderSearch, [], [
                'conditions' => [
                    [
                        'OR' => [
                            ['OrderTagName.user_id' => null],
                            ['OrderTagName.user_id' => $this->Auth->user('Branch') ?: $this->Auth->user('id')],
                        ],
                    ],
                ],
            ]);
        }
        return $conditions;
    }

    /**
     * Set up order index user_id and retailer_id conditions. Sets view variables for brand and retailer dropdowns.
     *
     * @param int|array $userId
     * @param int|string $storeFilter
     * @param int|string $brandFilter
     * @param array $conditions
     * @return array
     */
    protected function _setOrderIndexUserFilters($userId, $storeFilter, $brandFilter, $distributorSearch, $conditions)
    {
        $user = $this->User->revertAuthParent($this->Auth->user());
        $userType = $user['user_type'];

        if (in_array($userType, [User::TYPE_RETAILER, User::TYPE_STAFF])) {
            $retailerId = ($userType === User::TYPE_STAFF)
                ? $userId
                : $user['id'];
            $storesList = $this->User->listRetailerStoreNames($retailerId);
            if($userType === User::TYPE_STAFF){
                $storesList = array_intersect_key($storesList, array_flip($userId));
            }

            $conditions['Order.retailer_id'] = array_keys($storesList);

            if (!$user['Branch'] || $userType === User::TYPE_STAFF) {
                $this->set('storesList', $storesList);
            }
            $this->set('brandsList', $this->Order->listBrandNames(['Order.retailer_id' => array_keys($storesList)]));
        } elseif ($userType == User::TYPE_SALES_REP) {
            $manufacturerRetailers = $this->ManufacturerRetailer->findAllSalesRepUserRetailerNames($userId, ['Retailer.company_name' => 'ASC']);
            $groupedStoresList = Hash::combine($manufacturerRetailers, '{n}.Retailer.id', '{n}.Retailer.company_name', '{n}.User.id');

            $conditions[]['OR'] = array_merge(
                [
                    'Order.distributor_id' => $userId,
                ],
                array_map(fn($brandId, $retailerIds) => [
                    'Order.user_id' => $brandId,
                    'COALESCE(`Order`.`branch_id`, `Order`.`retailer_id`)' => $retailerIds,
                ], array_keys($groupedStoresList), array_map('array_keys', $groupedStoresList))
            );

            $brandsList = $this->User->listSalesRepBrandNames($userId);
            $storesList = Hash::combine($manufacturerRetailers, '{n}.Retailer.id', '{n}.Retailer.company_name');

            $conditions['Order.user_id'] = array_keys($brandsList);

            $this->set('storesList', $storesList);
            $this->set('brandsList', $brandsList);
        } else/*if ($userType == User::TYPE_MANUFACTURER)*/ {
            $brandFilter = '';

            $conditions['Order.user_id'] = $userId;
            $distributorSearch = array_filter((array)$distributorSearch);
            if ($distributorSearch) {
                $conditions['Order.distributor_id'] = $distributorSearch;
            }
            $this->set('storesList', $this->Order->listStoreNames(['Order.user_id' => $userId]));
        }

        if (!empty($brandFilter)) {
            $conditions['Order.user_id'] = $brandFilter;
        }
        if (!empty($storeFilter)) {
            if ($userType == User::TYPE_SALES_REP) {
                $conditions['COALESCE(`Order`.`branch_id`, `Order`.`retailer_id`)'] = $storeFilter;
            } else {
                $conditions['Order.retailer_id'] = $storeFilter;
            }
        }

        return $conditions;
    }

    /**
     * @param string $start_date
     * @param string $end_date
     * @param array $conditions
     * @return array
     */
    protected function _setOrderIndexDateRangeFilters($start_date, $end_date, $conditions = array())
    {
        $dates = $this->Order->convertDateRange($start_date, $end_date);
        if ($start_date && $end_date) {
            $conditions['Order.created_at BETWEEN ? AND ?'] = $dates;
        } else {
            if ($start_date) {
                $conditions['Order.created_at >='] = $dates[0];
            }
            if ($end_date) {
                $conditions['Order.created_at <='] = $dates[1];
            }
        }
        return $conditions;
    }

    /**
     * Creates Dealer order in the order table as well send notification mail.
     * @return CakeResponse
     */
    public function newDealerOrder()
    {
        $this->autoRender = false;
        if ($this->request->is('ajax')) {
            if (!empty($this->request->data)) {
                $orderId = $this->request->data['orderId'];
                if (is_string($this->request->data['dealer_qty_ordered'])) {
                    $this->request->data['dealer_qty_ordered'] = json_decode($this->request->data['dealer_qty_ordered'], true);
                }

                if (!empty($this->request->data['stripeToken'])) {
                    $order = $this->Order->record($orderId, ['fields' => ['id', 'orderID', 'user_id', 'retailer_id', 'currency_code']]);
                    $stripe_account = $this->StripeUser->getAccountId($order['Order']['user_id']);
                    try {
                        $charge = $this->Stripe->chargeToken(
                            $stripe_account,
                            $this->request->data['stripeAmount'],
                            $order['Order']['currency_code'],
                            $this->request->data['stripeToken'],
                            array(
                                'description' => "Wholesale charge for dealer order {$order['Order']['orderID']}",
                                'metadata' => array('orderId' => $order['Order']['id']),
                            )
                        );
                        if (isset($charge->id)) {
                            $this->request->data['dealer_qty_ordered']['wholesale_charge_id'] = $charge->id;
                            $this->request->data['dealer_qty_ordered']['wholesale_charge_amount'] = $this->request->data['stripeAmount'];
                        } else {
                            throw new Exception('Wholesale Stripe charge succeeded but did not return a charge id');
                        }
                    } catch (Exception $e) {
                        if ($e instanceof \Stripe\Exception\ApiErrorException) {
                            CakeLog::error('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()));
                        }
                        CakeLog::error($e);
                        CakeLog::error('Failed to process wholesale Stripe charge where ' . json_encode($order));

                        $message = 'There was an error processing the payment';
                        if ($e instanceof \Stripe\Exception\CardException) {
                            $message .= ': ' . $e->getMessage();
                        }
                        $this->setFlash($message, 'error');

                        $this->response->body(json_encode(['error' => true, 'success' => false]));

                        return $this->response;
                    }
                }

                if (!$this->OrderLogic->dealerOrderEvent($orderId, $this->request->data['dealer_qty_ordered'])) {
                    $this->setFlash('Please try again.', 'error');
                    $this->response->body(json_encode(['error' => true, 'success' => false]));
                    return $this->response;
                }

                $this->NotificationLogic->newDealerOrderNotification($orderId);

                $this->setFlash('A Dealer Order has been placed.', 'success');
                $this->response->body(json_encode(['error' => false, 'success' => true]));
                return $this->response;
            }
        } else {
            $this->redirect($this->referer());
        }
    }

    /**
     * Saves Dealer Order shipment tracking info when shipped with a tracking number.
     * @return CakeResponse
     * @deprecated Since Version2.10. Requests should go to the fulfillment form instead.
     */
    public function dealerOrderShipmentTracking()
    {
        try {
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        if ($this->request->is('GET')) {
            $this->request->data = $this->Order->get($this->request->query['order_id'], [
                'fields' => ['id', 'is_dealerorder'],
            ]);

            if ($this->request->data['Order']['is_dealerorder']) {
                $this->_triggerDeprecatedShipmentWarning((int)$this->request->data['Order']['id'], 'Ship With Tracking form for dealer orders');
            } else {
                $this->_triggerDeprecatedShipmentWarning((int)$this->request->data['Order']['id'], 'Ship With Tracking form for consumer orders');
            }

            $this->set('CourierList', $this->Courier->listNamesById());

            return $this->render();
        }

        $this->autoRender = false;

        $FlashSuccess = '';
        $FlashError = '';

        $orderId = (int)$this->request->data['Order']['id'];
        $courier = (int)$this->request->data['Order']['courier'];
        $trackingNumber = (string)$this->request->data['Order']['trackingno'];

        try {
            $trackingResponse = $this->FulfillmentLogic->trackOrder($orderId, $courier, $trackingNumber);
            $trackingUrl = is_array($trackingResponse) ? ($trackingResponse['courier_tracking_link'] ?? null) : null;

            $this->Order->hasOne('DealerOrder');
            $Orderinfo = $this->Order->get($orderId, [
                'contain' => [
                    'DealerOrder' => ['fields' => ['id']],
                ],
                'fields' => ['id', 'order_status'],
            ]);
            $this->Order->unbindModel(['hasOne' => ['DealerOrder']], false);

            $dealerOrderId = $Orderinfo['DealerOrder']['id'];

            $isDealerShipment = (
                $dealerOrderId &&
                $Orderinfo['Order']['order_status'] === OrderStatus::PROCESSING
            );

            if ($isDealerShipment) {
                $this->_triggerDeprecatedShipmentWarning($orderId, 'Ship With Tracking for dealer orders');

                if (!$this->Fulfillment->createCompleteDealerOrderFulfillment($dealerOrderId, $courier, $trackingNumber, $trackingUrl)) {
                    CakeLog::warning(json_encode(['errors' => $this->Fulfillment->validationErrors, 'data' => $this->Fulfillment->data]));
                    $FlashError = 'Failed to fulfill order.';
                }

                if (!$this->request->is('ajax')) {
                    $this->DealerOrder->markShippedToDealer($dealerOrderId);
                    $this->NotificationLogic->sendNonStockEmailsForOrder($orderId, $trackingUrl);
                }
                // else: Orders/edit for the "Ship Without Tracking" endpoint will be immediately called by JS
            } else {
                $this->_triggerDeprecatedShipmentWarning($orderId, 'Ship With Tracking for consumer orders');

                $this->Order->markShippedToConsumer($orderId);
            }

            $this->NotificationLogic->sendShipmentTrackingEmailsForOrder($orderId, $trackingUrl, $isDealerShipment);
            $FlashSuccess = 'Tracking info added to the order.';
        } catch (UserFriendlyException $e) {
            $FlashError = $e->getMessage();
        } catch (Exception $e) {
            CakeLog::error($e);

            if (empty($FlashError)) {
                $FlashError = 'Failed to add tracking info to the order';
            }
        }

        if ($this->request->is('AJAX')) {
            $this->response->body(json_encode(['error' => $FlashError, 'success' => $FlashSuccess]));
            return $this->response;
        }

        if ($FlashSuccess) {
            $this->setFlash($FlashSuccess, 'success');
        }
        if ($FlashError) {
            $this->setFlash($FlashError, 'error');
        }
        return $this->redirect($this->referer());
    }

    public function fulfillment_tooltip($id = null)
    {
        $order = $this->Order->findForPopup($id);
        if (!empty($order['Order']['dealer_qty_ordered']['products'])) {
            $order['Order']['dealer_qty_ordered']['products'] = array_reduce(
                $this->Product->find('all', [
                    'recursive' => -1,
                    'conditions' => [
                        'Product.id' => array_keys(
                            array_filter($order['Order']['dealer_qty_ordered']['products'], function($dealerProduct) {
                                return empty($dealerProduct['Product']);
                            })
                        ),
                    ],
                ]),
                function($dealerProducts, $product) {
                    $pid = $product['Product']['id'];
                    $dealerProducts[$pid]['Product'] = $product['Product'];
                    return $dealerProducts;
                },
                $order['Order']['dealer_qty_ordered']['products']
            );
        }
        $this->set('order', $order);
    }

    public function dealerorders_index(): ?CakeResponse
    {
        $query = $this->_extractIndexQueryParams($this->request);

        $conditions = $this->_dealerorderIndexConditions(
            $this->getAuthUserIds(),
            $query['order_type'],
            $query['order_status'],
            $query['fulfillment_status'],
            $query['payment_method'],
            $query['search'],
            $query['store'],
            $query['brand'],
            $query['tag'],
            $query['distributor'] ?? null,
            $query['daterange']
        );

        $sort = $query['sort'];
        $direction = $query['direction'];
        $limit = (int)$query['limit'];
        $page = (int)$query['page'];

        $sortOrder = [$sort => $direction];
        // Special sorting cases
        if ($sort === 'Order.display_date') {
            $sortOrder += ['Order.orderID' => $direction];
        } elseif ($sort === 'Courier.name') {
            $sortOrder += ['Order.shipped_date' => $direction];
        } elseif ($sort === 'DealerOrder.total_price') {
            $sortOrder += ['Order.total_price' => $direction];
        } elseif ($sort === 'Order.purchase_order_number') {
            $sortOrder = ["IF(Order.order_type = '" . OrderType::WHOLESALE . "', 1, 0)" => $direction];
            $sortOrder += ["COALESCE(IF(Order.order_type = '" . OrderType::WHOLESALE . "', Order.purchase_order_number, {$this->Order->virtualFields['customer_name']}), '')" => $direction];
        }

        $count = $this->Order->countAllForDealerOrderIndex($conditions);

        $orders = $this->Order->findAllForDealerOrderIndex($conditions, $sortOrder, $limit, $page);
        $orders = $this->_calcDealerOrderIndexTotals($orders);

        $orders = array_map(function(array $order): array {
            $orderTagUserId = $this->getOrderTagUserId($order);
            $order['OrderTagName'] = array_filter($order['OrderTagName'], function(array $tag) use ($orderTagUserId): bool {
                return (int)$tag['user_id'] === (int)$orderTagUserId;
            });

            return $order;
        }, $orders);

        $this->set('query', $query);
        $this->set('sort', $sort);
        $this->set('direction', $direction);
        $this->set('limit', $limit);
        $this->set('page', $page);
        $this->set('count', $count);
        $this->set('orders', $orders);
        $this->set('has_b2b_cart_permission', $this->Permissions->userHasB2bCartPermission($this->Auth->user()));
        $this->set('tagOptions', $this->OrderTagName->getOrderTagsById($this->Auth->user('Branch') ?: $this->Auth->user('id')));
        $this->set('salesRepOptions', $this->ManufacturerSalesRep->listConnectedSalesReps($this->Auth->user('id'), true));

        if ($this->request->is('ajax')) {
            return $this->render('ajax_dealerorders_index');
        }

        return null;
    }

    /**
     * @param array $dealerOrdersData
     * @return array
     */
    protected function _calcDealerOrderIndexTotals(array $dealerOrdersData)
    {
        return array_map(function($order) {
            $dealer_qty_ordered = json_decode($order['Order']['dealer_qty_ordered'], true);
            $totalPrice = 0;

            if (!empty($order['DealerOrder']['id'])) {
                $dealer_qty_ordered = json_decode($order['DealerOrder']['product_details'], true);
                $order['Order']['shipped_date'] = $order['DealerOrder']['shipment_date'];
                // DealerOrder.total_price includes discounts
                $totalPrice = (float)$order['DealerOrder']['total_price'] - array_sum(array_column($order['DealerOrder']['DealerOrderRefund'], 'amount'));
            } elseif ($order['Order']['order_type'] === OrderType::WHOLESALE) {
                // OrderRefunds can be ignored because only DealerOrderRefunds can be created for wholesale orders
                $totalPrice = (float)$order['Order']['total_price'] - (float)$order['Order']['total_discount'];
            } elseif (!empty($dealer_qty_ordered['products'])) {
                $dealer_qty_ordered = $this->_process_dealer_qty_ordered_for_b2c_dealer_order($order, $dealer_qty_ordered);

                $b2bTaxRate = (float)($dealer_qty_ordered['b2b_tax'] ?? $order['ManufacturerRetailer']['b2b_tax']) / 100;
                $b2bShippingTaxOption = (
                    $dealer_qty_ordered['b2b_shipping_tax_option'] ??
                    $this->UserSetting->field('b2b_shipping_tax_option', ['user_id' => $order['Order']['user_id']])
                );

                $dealerSubtotal = array_sum(array_column($dealer_qty_ordered['products'], 'line_total'));

                if (empty($dealer_qty_ordered['shipping_amount'])) {
                    $order['Order']['dealer_qty_ordered'] = $dealer_qty_ordered;
                    $dealer_qty_ordered['shipping_amount'] = $this->ShippingCalculator->calculateB2bOrderIndexShipping($order);
                }
                $shippingAmount = $dealer_qty_ordered['shipping_amount'];

                $taxIncluded = (bool)$order['Order']['tax_included'];

                $dealerTaxes = calculate_tax_amount($dealerSubtotal, $b2bTaxRate, $taxIncluded);
                if ($b2bShippingTaxOption) {
                    $dealerTaxes += calculate_tax_amount($shippingAmount, $b2bTaxRate, $taxIncluded);
                }

                $totalPrice = $dealerSubtotal + $shippingAmount;
                if (!$taxIncluded) {
                    $totalPrice += $dealerTaxes;
                }
            }

            if (!empty($order['Order']['shipped_date']) && empty($order['Courier']['name'])) {
                $order['Courier']['name'] = 'N/A';
            }
            $order['Order']['dealer_qty_ordered'] = $dealer_qty_ordered;
            $order['Order']['total_price'] = format_number($totalPrice);

            return $order;
        }, $dealerOrdersData);
    }

    private function _process_dealer_qty_ordered_for_b2c_dealer_order(array $order, array $dealer_qty_ordered): array
    {
        if (!empty($dealer_qty_ordered['products'])) {
            $dealer_qty_ordered = $this->_apply_refunded_quantities_to_dealer_qty_ordered($order['OrderProduct'], $dealer_qty_ordered);
            $dealer_qty_ordered = $this->ProductStateFee->applyToDealerQtyOrderedProducts($order, $dealer_qty_ordered);
            $dealer_qty_ordered = $this->_set_dealer_qty_ordered_pricing($dealer_qty_ordered);
        }

        return $dealer_qty_ordered;
    }

    private function _apply_refunded_quantities_to_dealer_qty_ordered(array $orderProducts, array $dealer_qty_ordered): array
    {
        foreach ($orderProducts as $orderProduct) {
            $productId = $orderProduct['product_id'];
            $quantity = $dealer_qty_ordered['products'][$productId]['quantity'] ?? null;
            if ($quantity !== null) {
                $refundedQuantity = $orderProduct['refunded_quantity'] ?? array_sum(array_column($orderProduct['OrderRefundProduct'], 'quantity'));
                $dealer_qty_ordered['products'][$productId]['quantity'] = max($quantity - $refundedQuantity, 0);
            }
        }

        return $dealer_qty_ordered;
    }

    private function _set_dealer_qty_ordered_pricing(array $dealer_qty_ordered): array
    {
        $dealer_qty_ordered['products'] = array_map(function($dealerProduct) {
            // 'dealer_price' may not be set if PricingTier was not configured for product
            $dealerProduct['dealer_price'] = $dealerProduct['dealer_price'] ?? '0.00';
            $dealerProduct['line_total'] = $dealerProduct['dealer_price'] * $dealerProduct['quantity'];

            return $dealerProduct;
        }, $dealer_qty_ordered['products']);

        return $dealer_qty_ordered;
    }

    public function dealerordersExport()
    {
        $this->autoRender = false;

        $include_products = ($this->request->param('filter') === 'include_products');

        $this->request->query = $this->_extractIndexQueryParams($this->request);

        $conditions = $this->_dealerorderIndexConditions(
            $this->getAuthUserIds(),
            $this->request->query['order_type'],
            $this->request->query['order_status'],
            $this->request->query['fulfillment_status'],
            $this->request->query['payment_method'],
            $this->request->query['search'],
            $this->request->query['store'],
            $this->request->query['brand'],
            $this->request->query['tag'],
            $this->request->query['distributor'] ?? null,
            $this->request->query['daterange']
        );

        if (!$this->Order->countAllForDealerOrderIndex($conditions)) {
            return $this->_exceptionResponse(new NotFoundException(), 'No orders found');
        }

        $salesRepColumnLabels = $this->OrderSalesRep->findSalesRepExportLabels($conditions);

        $tableModel = [
            $this->PhpExcel->newExportColumn(__('Order ID'), function($dealerOrder) {
                return $dealerOrder['Order']['orderID'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('eCom ID'), function($dealerOrder) {
                return $this->Order->getSourceOrderName($dealerOrder);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Internal Order #'), function($dealerOrder) {
                return $dealerOrder['Order']['external_invoice_id'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Order Type'), function($dealerOrder) {
                return OrderType::getWholesaleType((string)$dealerOrder['Order']['order_type']);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Order Status'), function($dealerOrder) {
                return $dealerOrder['Order']['order_status'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Fulfillment Status'), function($dealerOrder) {
                return $dealerOrder['DealerOrder']['fulfillment_status'] ?? FulfillmentStatus::UNFULFILLED;
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Order Date'), function($dealerOrder) {
                return format_datetime($dealerOrder['Order']['created_at'], 'Y-m-d');
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Distributor'), function($dealerOrder) {
                return $dealerOrder['Distributor']['company_name'];
            }, ['filter' => true]),
        ];
        foreach ($salesRepColumnLabels as $idx => $label) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn($label, function($order) use ($idx) {
                    return $order['SalesRep'][$idx]['company_name'] ?? null;
                }, ['filter' => true]),
            ]);
        };
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn(__('Retailer Name'), function($dealerOrder) {
                return $dealerOrder['Retailer']['company_name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Account ID'), function($dealerOrder) {
                return $dealerOrder['ManufacturerRetailer']['external_retailer_account'];
            }, ['filter' => true]),
        ]);
        if ($include_products) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn(__('Product Title'), function($dealerOrder, $dealerOrderProduct) {
                    return $dealerOrderProduct['Product']['product_title'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('Product SKU'), function($dealerOrder, $dealerOrderProduct) {
                    return $dealerOrderProduct['Product']['product_sku'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('UPC'), function($dealerOrder, $dealerOrderProduct) {
                    return $dealerOrderProduct['Product']['product_upc'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('Category'), function($dealerOrder, $dealerOrderProduct) {
                    return $dealerOrderProduct['Product']['product_type'];
                }, ['filter' => true]),
                $this->PhpExcel->newExportColumn(__('Retail Qty'), function($dealerOrder, $dealerOrderProduct) {
                    return (int)$dealerOrderProduct['OrderProduct']['quantity'];
                }, ['filter' => true]),
            ]);
        }
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn(__('Currency'), function($order) {
                return $order['Order']['currency_code'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Retail Price'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    return format_number($dealerOrderProduct['OrderProduct']['total_price']);
                } else {
                    return format_number(array_sum(array_column($dealerOrder['OrderProduct'], 'total_price')));
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Retail Discount'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
            //TODO: convert to use the calc discounts
                    return format_number($dealerOrderProduct['OrderProduct']['total_discount']);
                } else {
                    return format_number($dealerOrder['Order']['total_discount']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Retail Tax'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    return format_number($dealerOrderProduct['OrderProduct']['total_tax']);
                } else {
                    return format_number($dealerOrder['Order']['total_tax']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Retail Shipping'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    return '';
                } else {
                    return format_number($dealerOrder['Order']['shipping_amount']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Retail Total'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    $totalPrice = $dealerOrderProduct['OrderProduct']['total_price'] - $dealerOrderProduct['OrderProduct']['total_discount'];
                    if (!$dealerOrder['Order']['tax_included']) {
                        $totalPrice += $dealerOrderProduct['OrderProduct']['total_tax'];
                    }

                    return format_number($totalPrice);
                } else {
                    return format_number($dealerOrder['Order']['total_price'] - $dealerOrder['Order']['total_discount']);
                }
            }, ['filter' => true]),
        ]);
        if ($include_products) {
            $tableModel = array_merge($tableModel, [
                $this->PhpExcel->newExportColumn(__('Invoice Qty'), function($dealerOrder, $dealerOrderProduct) {
                    return (int)$dealerOrderProduct['product_quantity'];
                }, ['filter' => true]),
            ]);
        }
        $tableModel = array_merge($tableModel, [
            $this->PhpExcel->newExportColumn(__('Invoice Price'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    return format_number($dealerOrderProduct['product_price'] * $dealerOrderProduct['product_quantity']);
                } else {
                    return format_number($dealerOrder['DealerOrder']['product_total_price']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('B2B Shipping'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    return '';
                } else {
                    return format_number($dealerOrder['DealerOrder']['shipping_amount']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('B2B Discount'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    return format_number($dealerOrderProduct['total_discount']);
                } else {
                    return format_number($dealerOrder['DealerOrder']['total_discount']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('B2B Tax'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    return format_number(calculate_tax_amount($dealerOrderProduct['product_price'] * $dealerOrderProduct['product_quantity'], $dealerOrderProduct['tax'] / 100, $dealerOrder['Order']['tax_included']));
                } else {
                    return format_number($dealerOrder['DealerOrder']['total_tax']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Wholesale Total'), function($dealerOrder, $dealerOrderProduct = array()) {
                if ($dealerOrderProduct) {
                    $totalPrice = $dealerOrderProduct['product_price'] * $dealerOrderProduct['product_quantity'];

                    $taxRate = (float)($dealerOrderProduct['tax'] / 100);
                    $taxIncluded = (bool)$dealerOrder['Order']['tax_included'];
                    $totalTax = calculate_tax_amount($totalPrice, $taxRate, $taxIncluded);
                    if (!$taxIncluded) {
                        $totalPrice += $totalTax;
                    }

                    return format_number($totalPrice);
                } else {
                    return format_number($dealerOrder['DealerOrder']['total_price']);
                }
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Address'), function($dealerOrder) {
                return $dealerOrder['Retailer']['address1'];
            }),
            $this->PhpExcel->newExportColumn(__('Address 2'), function($dealerOrder) {
                return $dealerOrder['Retailer']['address2'];
            }),
            $this->PhpExcel->newExportColumn(__('City'), function($dealerOrder) {
                return $dealerOrder['Retailer']['city'];
            }),
            $this->PhpExcel->newExportColumn(__('State/Province'), function($dealerOrder) {
                return $dealerOrder['Retailer']['State']['state_name'];
            }),
            $this->PhpExcel->newExportColumn(__('Country'), function($dealerOrder) {
                return $dealerOrder['Retailer']['Country']['country_name'];
            }),
            $this->PhpExcel->newExportColumn(__('Zip/Postal Code'), function($dealerOrder) {
                return $dealerOrder['Retailer']['zipcode'];
            }),
            $this->PhpExcel->newExportColumn(__('Phone Number'), function($dealerOrder) {
                return $dealerOrder['Retailer']['Contact']['company'];
            }),
            $this->PhpExcel->newExportColumn(__('Payment Status'), function($dealerOrder) {
                return $dealerOrder['Order']['payment_status_name'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Payment Type'), function($dealerOrder) {
                return OrderPaymentMethod::getLabel((string)$dealerOrder['Order']['payment_method']);
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Term'), function($dealerOrder) {
                return $dealerOrder['CreditTerm']['description'];
            }, ['filter' => true]),
            $this->PhpExcel->newExportColumn(__('Tags'), function($dealerOrder) {
                return implode(', ', Hash::extract($dealerOrder['OrderTagName'] ?? [], '{n}.name'));
            }, ['filter' => true]),
        ]);

        $title = ($this->Auth->user('user_type') == User::TYPE_MANUFACTURER) ? 'Wholesale' : 'Purchase Order';
        if ($include_products) {
            $title .= ' Product Sales';
        }
        $fileName = sprintf('%s %s %s.xlsx',
            $this->Auth->user('company_name'),
            $title,
            date('Y-m-d')
        );

        $this->Spout->doWithOpenWriter($fileName, function() use ($conditions, $include_products, $tableModel) {
            $this->Spout->addHeaderRow(array_column($tableModel, 'label'));

            $this->DealerOrder->streamExportData($conditions, function($dealerOrder) use ($include_products, $tableModel) {
                if (!$include_products) {
                    $this->Spout->addRow($this->PhpExcel->processExportColumns($tableModel, $dealerOrder));
                } else {
                    $this->Spout->addRows(array_map(
                        function($dealerOrderProduct) use ($tableModel, $dealerOrder) {
                            return $this->PhpExcel->processExportColumns($tableModel, $dealerOrder, $dealerOrderProduct);
                        },
                        $dealerOrder['DealerOrder']['DealerOrderProduct']
                    ));
                }
            });
        });
    }

    /**
     * Allow legacy export URL params to take priority in the request query string if provided.
     *
     * @param CakeRequest $request
     * @return array Merged CakeRequest query
     */
    protected function _mergeLegacyExportUrlParams(CakeRequest $request)
    {
        $fields = array(
            'store',
            'type',
            'status',
            'search',
        );
        $legacyParams = array_map(
            function($field) use ($request) {
                return $request->param($field);
            },
            array_combine($fields, $fields)
        );
        $filterLiteralAll = function($value) { return $value && $value !== 'all'; };
        $replacements = array_filter($legacyParams, $filterLiteralAll);
        return array_filter($replacements + $request->query, $filterLiteralAll);
    }

    private function _extractIndexQueryParams(CakeRequest $request): array
    {
        $this->IndexQueryHandler->defaultQuery['daterange'] = $this->_defaultDateRangeString();

        $request->query = $this->IndexQueryHandler->extractModifiedParams($request->query);

        return $this->IndexQueryHandler->extractAllParams($request->query);
    }

    /**
     * @param int $userId
     * @param string $orderType
     * @param string $orderStatus
     * @param string|null $fulfillmentStatus
     * @param string $paymentMethod
     * @param string $orderSearch
     * @param int|string $storeSearch
     * @param int|string $brandSearch
     * @param string|null $daterange
     * @return array
     */
    private function _dealerorderIndexConditions($userId, $orderType, $orderStatus, $fulfillmentStatus, $paymentMethod, $orderSearch, $storeSearch, $brandSearch, $tagSearch, $distributorSearch, ?string $daterange)
    {
        $conditions = array('COALESCE(Order.dealer_qty_ordered, "") !=' => "");

        if ($orderType) {
            if ($orderType === OrderType::WHOLESALE) {
                $conditions['Order.order_type'] = OrderType::WHOLESALE;
            } else {
                $conditions['Order.order_type !='] = OrderType::WHOLESALE;
            }
        }
        if ($orderStatus) {
            $conditions['Order.order_status'] = $orderStatus;
        } else {
            $conditions['Order.order_status !='] = OrderStatus::VOIDED;
        }

        if ($fulfillmentStatus) {
            $conditions["COALESCE(DealerOrder.fulfillment_status, {$this->Order->value(FulfillmentStatus::UNFULFILLED)})"] = $fulfillmentStatus;
        }
        if ($paymentMethod) {
            $conditions['Order.payment_method'] = $paymentMethod;
        }

        if ($tagSearch) {
            $conditions[] = $this->OrderTag->buildExistsSubquery([
                'OrderTag.order_id = Order.id',
                'OrderTag.order_tag_name_id' => $tagSearch,
            ]);
        }

        $conditions = $this->_setOrderIndexUserFilters($userId, $storeSearch, $brandSearch, $distributorSearch, $conditions);

        list($start_date, $end_date) = $this->_extractDateRange($daterange);
        $conditions = $this->_setOrderIndexDateRangeFilters($start_date, $end_date, $conditions);

        if (!empty($orderSearch)) {
            $conditions[] = $this->Order->buildSearchCondition($orderSearch, [], [
                'conditions' => [
                    [
                        'OR' => [
                            ['OrderTagName.user_id' => null],
                            ['OrderTagName.user_id' => $this->Auth->user('Branch') ?: $this->Auth->user('id')],
                        ],
                    ],
                ],
            ]);
        }
        return $conditions;
    }

    /**
     * @return string Date strings for the default range separated by ' - '.
     */
    private function _defaultDateRangeString(): string
    {
        $earliestYear = (int)min(2015, (int)$this->date('Y'));

        return implode(' - ', array_map(
            fn(string $date): string => format_datetime($date, 'Y-m-d'),
            ["January 1, {$earliestYear}", 'TODAY']
        ));
    }

    /**
     * @param string|null $daterange Date strings separated by ' - '.
     * @return string[] Date string array assignable to list($start_date, $end_date).
     */
    private function _extractDateRange(?string $daterange): array
    {
        $format = function($date) {
            return format_datetime($date, 'Y-m-d');
        };

        if ($daterange === null) {
            return array_map($format, ['1 YEAR AGO', 'TODAY']);
        }
        if (strpos($daterange, ' - ') === false) {
            return ['', ''];
        }
        return array_map($format, explode(' - ', $daterange, 2));
    }

    public function draft_order_add()
    {
        $this->autoRender = false;

        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if (!in_array($userType, ['Manufacturer', 'SalesRep'])) {
            return $this->_exceptionResponse(new ForbiddenException($userType), '');
        }

        $retailerList = ($userType === 'SalesRep')
            ? $this->User->listSalesRepStoreNames($userId)
            : $this->User->listConnectedRetailerNames($userId);
        $links = array_map(function($id) {
            return Router::url(['controller' => 'products', 'action' => 'index_retailer', 'retailer_id' => $id]);
        }, array_keys($retailerList));
        $retailerOptions = ['' => 'Select a Retailer'] + array_combine($links, array_values($retailerList));

        $this->response->body(json_encode(compact('retailerOptions')));
        return $this->response;
    }

    private function _triggerDeprecatedShipmentWarning(int $orderId, string $descriptor): void
    {
        $this->Order->addAssociations(['belongsTo' => ['User'], 'hasOne' => ['DealerOrder']]);
        $order = $this->Order->get($orderId, [
            'contain' => [
                'User' => ['fields' => ['id', 'email_address', 'company_name', 'site_type']],
                'DealerOrder' => ['fields' => ['id', 'created_at', 'updated_at']],
            ],
            'fields' => [
                'id',
                'orderID',
                'order_type',
                'subType',
                'is_commission_retailer',
                'is_dealerorder',
                'order_status',
                'created_at',
                'updated_at',
            ],
        ]);
        $this->Order->unbindModel(['belongsTo' => ['User'], 'hasOne' => ['DealerOrder']], false);

        $authUser = array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type']));

        $message = $descriptor . ' is deprecated. The Fulfillment form should be used instead.';
        $message .= PHP_EOL . 'Attributes: ' . json_encode($order + ['AuthUser' => $authUser]);

        deprecationWarning($message);
    }

    protected function _getInvoicePdfLink(int $id, $orderType = null, $orderStatus = null){

        if(!(bool)$orderStatus || !(bool)$orderType){
            $order = $this->Order->get($id, ['fields' => ['order_type', 'order_status']])['Order'];
            $orderType = $orderType ?: $order['order_type'];
            $orderStatus = $orderStatus ?: $order['order_status'];
        }

        $invoiceType = 'b2c';
        if(
            ($this->request->param('action') === 'edit' 
                && (($orderType === OrderType::WHOLESALE && $orderStatus !== OrderStatus::PURCHASE_ORDER)
                    || ($orderType !== OrderType::WHOLESALE && $orderStatus !== OrderStatus::NEED_TO_CONFIRM && $orderStatus !== OrderStatus::DEALER_ORDER)))
            || $this->request->param('filter') === 'dealerorderTable'
        ){
            $invoiceType = 'b2b';
        } 
        return Router::url(['controller' => 'invoice_pdf', 'action' => 'viewPdf', 'id' => $id, 'type' => $invoiceType, 'file' => basename(get_order_invoice_pdf_filepath($id))]);
    }
}
