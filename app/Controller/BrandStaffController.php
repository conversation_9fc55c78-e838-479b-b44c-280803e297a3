<?php
App::uses('App<PERSON>ontroller', 'Controller');
App::uses('User', 'Model');

/**
 * BrandStaff Controller.
 *
 * @property AuthComponent $Auth
 * @property UserLogicComponent $UserLogic
 *
 * @property BrandStaff $BrandStaff
 */
class BrandStaffController extends AppController
{
    public $components = [
        'Auth',
        'UserLogic',
    ];

    public $uses = [
        'BrandStaff',
    ];

    public function isAuthorized()
    {
        return ($this->Auth->user('user_type') === User::TYPE_MANUFACTURER) && parent::isAuthorized();
    }

    public function index()
    {
        $this->set('title_for_layout', __('Staff'));
        $this->set('staffUsers', $this->BrandStaff->findAllForIndex($this->Auth->user('id')));

        if ($this->request->is('ajax')) {
            return $this->render('/Elements/BrandStaff/ajax_index');
        }
    }

    public function view($staffId = null)
    {
        $brandId = $this->Auth->user('id');

        $staff = [];
        if ($staffId) {
            $staff = $this->BrandStaff->findForView($staffId);

            if (empty($staff['BrandStaff']['staff_id'])) {
                throw new NotFoundException('Staff account not found for staff_id: ' . json_encode($staffId));
            }
            if ($staff['BrandStaff']['brand_id'] != $brandId) {
                throw new ForbiddenException('Staff account access is forbidden for user_id: ' . json_encode($brandId));
            }
        }
        $this->set('staff', $staff);

        $breadcrumb = $staff['StaffUser']['company_name'] ?? 'New';
        $this->set('title_for_layout', 'Staff / ' . $breadcrumb);
        $this->set('breadcrumb', $breadcrumb);

        $this->set('roleOptions', $this->BrandStaff->Role->listOptions($brandId));

        $this->getLanguages();
    }

    public function add()
    {
        $this->request->allowMethod('post');

        $brandId = (int)$this->Auth->user('id');
        $staffId = $this->BrandStaff->createFromAddForm($brandId, $this->request->data);
        if (!$staffId) {
            return $this->_validationErrorFlashResponse($this->BrandStaff->validationErrors);
        }
        $this->BrandStaff->Role->deleteAllOrphansWithBrandId($brandId);

        $this->UserLogic->activationMail($staffId);

        return $this->_successResponse('New staff member created', ['controller' => 'brand_staff', 'action' => 'index']);
    }

    public function edit($staffId = null)
    {
        $existing = $this->BrandStaff->StaffUser->findById($staffId, ['id', 'user_type']);
        if (empty($existing['StaffUser']['id'])) {
            throw new NotFoundException('Staff account not found for staff_id: ' . json_encode($staffId));
        }
        $this->request->allowMethod('post', 'put');
        if ($existing['StaffUser']['user_type'] !== User::TYPE_BRAND_STAFF) {
            throw new BadRequestException('Unexpected user_type: ' . json_encode($existing['StaffUser']['user_type']));
        }

        $brandId = (int)$this->Auth->user('id');
        if (!$this->BrandStaff->saveFromEditForm($brandId, $staffId, $this->request->data)) {
            return $this->_validationErrorFlashResponse($this->BrandStaff->validationErrors);
        }
        $this->BrandStaff->Role->deleteAllOrphansWithBrandId($brandId);

        return $this->_successResponse();
    }

    public function delete($staffId = null)
    {
        $existing = $this->BrandStaff->StaffUser->findById($staffId, ['id', 'user_type'], null, -1);
        if (empty($existing['StaffUser']['id'])) {
            throw new NotFoundException('Staff account not found for staff_id: ' . json_encode($staffId));
        }
        $this->request->allowMethod('post', 'delete');
        if ($existing['StaffUser']['user_type'] !== User::TYPE_BRAND_STAFF) {
            throw new BadRequestException('Unexpected user_type: ' . json_encode($existing['StaffUser']['user_type']));
        }

        if (!$this->BrandStaff->StaffUser->delete($staffId)) {
            return $this->_exceptionResponse();
        }
        $this->BrandStaff->Role->deleteAllOrphansWithBrandId((int)$this->Auth->user('id'));

        return $this->_successResponse(__('The staff member has been deleted'), ['controller' => 'brand_staff', 'action' => 'index']);
    }
}
