<?php
App::uses('AppController', 'Controller');
App::uses('User', 'Model');
App::uses('Permissions', 'Utility');

/**
 * B2bShipToAddresses Controller
 *
 * @property B2bShipToAddress $B2bShipToAddress
 * @property Country $Country
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property State $State
 * @property User $User
 */
class B2bShipToAddressesController extends AppController
{
    public $uses = [
        'B2bShipToAddress',
        'Country',
        'ManufacturerRetailer',
        'State',
        'User',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function edit($retailerId = null)
    {
        $brandId = $this->Auth->user('id');

        $manufacturerRetailerId = $this->ManufacturerRetailer->field('id', [
            'ManufacturerRetailer.user_id' => $brandId,
            'ManufacturerRetailer.retailer_id' => $retailerId,
        ]);
        if (!$manufacturerRetailerId) {
            throw new NotFoundException(json_encode(['retailer_id' => $retailerId, 'Auth' => User::extractAuthUserLogFields($this->Auth->user())]));
        }

        if ($this->request->is(['post', 'put'])) {
            return $this->_edit_post($manufacturerRetailerId);
        }

        return $this->_edit_get($brandId, $retailerId);
    }

    private function _edit_get(int $brandId, int $retailerId): ?CakeResponse
    {
        $this->request->data = $this->B2bShipToAddress->findResolvedRetailerAddress($brandId, $retailerId);

        $this->set('countryList', $this->Country->getCountryList());
        $this->set('stateList', $this->State->getStateList($this->request->data['B2bShipToAddress']['country_id']));

        return null;
    }

    private function _edit_post(int $manufacturerRetailerId): ?CakeResponse
    {
        if (!$this->B2bShipToAddress->saveFromEditForm($manufacturerRetailerId, $this->request->data)) {
            return $this->_exceptionResponse(new BadRequestException(json_encode(['errors' => $this->B2bShipToAddress->validationErrors, 'data' => $this->B2bShipToAddress->data])), null, true);
        }

        return $this->_successResponse();
    }
}
