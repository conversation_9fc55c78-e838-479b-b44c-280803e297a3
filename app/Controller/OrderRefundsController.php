<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('App<PERSON><PERSON>roller', 'Controller');
App::uses('OrderRefundException', 'Error');
App::uses('EcommercePlacementException', 'Error');
App::uses('OrderStatus', 'Utility');

/**
 * DealerOrders Controller.
 *
 * @property FulfillmentLogicComponent $FulfillmentLogic
 * @property NotificationLogicComponent $NotificationLogic
 * @property OrderLogicComponent $OrderLogic
 * @property ShopifyComponent $Shopify
 *
 * @property Fulfillment $Fulfillment
 * @property OrderRefund $OrderRefund
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class OrderRefundsController extends AppController
{
    public $components = [
        'FulfillmentLogic',
        'NotificationLogic',
        'OrderLogic',
        'Shopify.Shopify',
    ];

    public $uses = [
        'Fulfillment',
        'OrderRefund',
        'Order',
        'OrderProduct',
        'WarehouseProductReservation',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        if ($this->request->param('admin')) {
            // Check that the user has a field unique to admins
            return (bool)$this->Auth->user('username');
        }

        try {
            // Configured in routes.php
            $orderId = $this->request->param('order_id');

            $log = $this->OrderLogic->isAuthorizedForOrder($orderId);

            if ($this->request->param('action') === 'refund_expired') {
                if (!in_array($log['Auth']['id'], [$log['Order']['user_id'], $log['Order']['distributor_id']])) {
                    throw new ForbiddenException(json_encode($log));
                }
            }

            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function refund($orderId = null)
    {
        return $this->admin_refund($orderId);
    }

    public function admin_refund($orderId = null)
    {
        $this->autoRender = false;
        if ($this->request->is('post')) {
            $responseBodyJson = $this->OrderLogic->orderRefund($this->request->data);
            $responseBody = json_decode($responseBodyJson, true);

            if ($responseBody['status'] === 'ok') {
                $this->request->data = $responseBody['message']['request'];

                if (!$this->OrderRefund->createFromRefundPopup($this->request->data)) {
                    return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->OrderRefund->validationErrors, 'data' => $this->OrderRefund->data])), null, true);
                }
                $refundId = $this->OrderRefund->id;

                $newStatus = $this->request->data['Order']['order_status'];
                if (in_array($newStatus, OrderStatus::STATUSES_REFUNDED, true)) {
                    $this->WarehouseProductReservation->release($orderId);
                } elseif ($newStatus === OrderStatus::NEED_TO_CONFIRM) {
                    $this->WarehouseProductReservation->reserveLineItemSet($orderId, $this->OrderProduct->findAllForInventoryReservation($orderId));
                } elseif ($newStatus === OrderStatus::DEALER_ORDER) {
                    $this->OrderLogic->reserveNewDealerOrderInventory($orderId);
                }

                $orderInfo = $responseBody['message']['orderInfo'];

                if (
                    $orderInfo['Order']['source_id'] &&
                    !empty($this->request->data['OrderRefund']['place_ecommerce_refund']) &&
                    (
                        $orderInfo['Order']['is_commission_retailer'] ||
                        OrderType::filterOrderType($orderInfo['Order']['order_type']) === OrderType::SELL_DIRECT
                    )
                ) {
                    try {
                        $this->_place_ecommerce_refund($refundId);
                    } catch (EcommercePlacementException $e) {
                        CakeLog::debug($e->getMessage());
                        $this->setFlash($e->getMessage(), 'error');
                    } catch (Exception $e) {
                        CakeLog::error($e);
                        $this->setFlash('An error occurred while attempting to link the e-commerce refund to this order', 'error');
                    }
                }

                $amount = $this->request->data['OrderRefund']['amount'];
                $this->NotificationLogic->orderRefundEmail($orderInfo, $amount, $this->Auth->user('user_type'), $refundId);

                try {
                    $this->FulfillmentLogic->afterConsumerRefund((int)$orderId, (int)$refundId);
                } catch (UserFriendlyException $e) {
                    CakeLog::debug($e->getMessage());
                    $this->setFlash($e->getMessage(), 'error');
                } catch (Exception $e) {
                    CakeLog::error($e);
                    $this->setFlash('An error occurred attempting to update the order\'s fulfillment status', 'error');
                }

                $this->setFlash('Refund has been successfully processed', 'success');
            } elseif (!$this->request->is('ajax')) {
                return $this->_exceptionResponse(null, $responseBody['message']);
            }

            // Return a 200 response that may contain errors for front end to display
            if ($this->request->is('ajax')) {
                $this->response->body($responseBodyJson);
            }
            return $this->_successResponse('');
        }

        $orderInfo = $this->Order->findForRefundPopup($orderId);
        if (empty($orderInfo['Order']['id'])) {
            throw new NotFoundException(json_encode(['Order' => ['id' => $orderId]]));
        }
        $this->set('order', $orderInfo);

        $shippingLabel = 'Shipping';
        if ($orderInfo['Order']['is_install']) {
            $shippingLabel = 'Installation';
        } elseif ($orderInfo['Order']['order_type'] === Order::TYPE_LOCAL_DELIVERY && !empty($orderInfo['User']['local_delivery_shipping_title'])) {
            $shippingLabel = $orderInfo['User']['local_delivery_shipping_title'];
        }
        $this->set('shippingLabel', $shippingLabel);

        $this->set('balanceBefore', $orderInfo['Order']['adjusted_total']);
        $this->set('balanceAfter', $orderInfo['Order']['adjusted_total']);

        return $this->render('refund');
    }

    /**
     * @param int|string $refundId
     * @throws CurlException
     * @throws HttpException
     * @throws EcommercePlacementException
     * @throws ShopifyApiException
     */
    private function _place_ecommerce_refund($refundId)
    {
        $refund = $this->OrderRefund->findForEcommerceConsumerRefund($refundId);
        if (empty($refund['OrderRefund']['id'])) {
            throw new NotFoundException('Refund not found where id=' . json_encode($refundId));
        }

        $siteType = $refund['Order']['User']['site_type'];

        if (!empty($refund['OrderRefund']['source_id'])) {
            throw new EcommercePlacementException("The refund is already linked to /orders/{$refund['Order']['source_id']}/refunds/{$refund['OrderRefund']['source_id']}");
        }

        $refundSourceId = null;
        switch ($siteType) {
            case UserSiteType::SHIPEARLY:
                // Do nothing
                return;
            case UserSiteType::SHOPIFY:
                $response = $this->Shopify->createOrderRefund($refund);
                $refundSourceId = $response['id'] ?? null;
                break;
            case UserSiteType::WOOCOMMERCE:
            case UserSiteType::MAGENTO:
                //TODO Support other platforms
                throw new EcommercePlacementException('Unable to place the refund in unsupported platform: ' . json_encode($siteType));
            default:
                throw new BadRequestException(json_encode(['message' => 'Unknown site_type', 'User' => mask_secret_fields($refund['Order']['User'], ['api_key', 'secret_key'])]));
        }
        if (!$refundSourceId) {
            throw new EcommercePlacementException("Failed to place the refund in {$siteType}");
        }

        if (!$this->OrderRefund->save(['id' => $refundId, 'source_id' => $refundSourceId])) {
            throw new InternalErrorException(json_encode(['errors' => $this->OrderRefund->validationErrors, 'data' => $this->OrderRefund->data]));
        }
    }

    public function refund_expired($orderId = null)
    {
        return $this->admin_refund_expired($orderId);
    }

    public function admin_refund_expired($orderId = null)
    {
        $this->request->allowMethod('post');
        try {
            if (!$this->OrderRefund->refundExpiredCharge($orderId)) {
                return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->OrderRefund->validationErrors, 'data' => $this->OrderRefund->data])), null, true);
            }

            $this->WarehouseProductReservation->release($orderId);

            return $this->_successResponse();
        } catch (HttpException $e) {
            return $this->_exceptionResponse($e, null, true);
        }
    }
}
