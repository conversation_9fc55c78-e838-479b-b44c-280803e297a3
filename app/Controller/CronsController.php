<?php

use ShipEarlyApp\Lib\Integrations\SftpInventory;
use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppController', 'Controller');
App::uses('User', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('RetailerCreditPaymentStatus', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('ProductStatus', 'Utility');

/**
 * Class CronsController
 *
 * @property AfterShipComponent $AfterShip
 * @property OrderLogicComponent $OrderLogic
 * @property OrderPlacerComponent $OrderPlacer
 * @property NotificationLogicComponent $NotificationLogic
 * @property LockComponent $Lock
 * @property LightspeedComponent $Lightspeed
 * @property ShopifyPOSComponent $ShopifyPOS
 * @property QuickbookComponent $Quickbook
 * @property AvataxComponent $Avatax
 * @property StripeComponent $Stripe
 * @property StripeWebhookHandlerComponent $StripeWebhookHandler
 * @property VendPOSComponent $VendPOS
 * @property SquarePosComponent $SquarePos
 * @property ShopifyComponent $Shopify
 * @property ShopifyWebhookHandlerComponent $ShopifyWebhookHandler
 *
 * @property AccessToken $AccessToken
 * @property Order $Order
 * @property ProductRetailer $ProductRetailer
 * @property Courier $Courier
 * @property ImportEmail $ImportEmail
 * @property EmailTemplate $EmailTemplate
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Retailercount $Retailercount
 * @property OrderProduct $OrderProduct
 * @property OrderRefund $OrderRefund
 * @property Store $Store
 * @property QuickbookProduct $QuickbookProduct
 * @property MailQueue $MailQueue
 * @property Btask $Btask
 * @property StripeUser $StripeUser
 * @property Notification $Notification
 * @property Customer $Customer
 * @property CustomerAddress $CustomerAddress
 * @property User $User
 * @property AbandonCart $AbandonCart
 * @property Contact $Contact
 * @property ProductTier $ProductTier
 * @property RetailerCreditPayment $RetailerCreditPayment
 * @property UserSetting $UserSetting
 * @property SuccessDetails $SuccessDetails
 * @property ErrorDetails $ErrorDetails
 * @property Warehouse $Warehouse
 * @property WarehouseProduct $WarehouseProduct
 */
class CronsController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Crons';

    /**
     * @var array
     */
    public $components = [
        'AfterShip',
        'OrderLogic',
        'OrderPlacer',
        'NotificationLogic',
        'Lock',
        'Lightspeed',
        'Shopifypos.ShopifyPOS',
        'Quickbook.Quickbook',
        'Avatax',
        'Stripe.Stripe',
        'Stripe.StripeWebhookHandler',
        'Vendpos.VendPOS',
        'SquarePos',
        'Shopify.Shopify',
        'Shopify.ShopifyWebhookHandler',
    ];

    /**
     * @var array
     */
    public $uses = [
        'AccessToken',
        'Order',
        'ProductRetailer',
        'Courier',
        'ImportEmail',
        'EmailTemplate',
        'ManufacturerRetailer',
        'Retailercount',
        'OrderProduct',
        'OrderRefund',
        'Store',
        'Quickbook.QuickbookProduct',
        'MailQueue',
        'Btask',
        'StripeUser',
        'Notification',
        'Customer',
        'CustomerAddress',
        'User',
        'AbandonCart',
        'Contact',
        'ProductTier',
        'RetailerCreditPayment',
        'UserSetting',
        'SuccessDetails',
        'ErrorDetails',
        'Warehouse',
        'WarehouseProduct',
    ];

    /**
     * @var bool
     * @see Controller::$autoRender
     */
    public $autoRender = false;

    /**
     * @var bool
     * @see Controller::$layout
     */
    public $layout = '';

    /**
     *
     */
    public function beforeFilter()
    {
        $this->Auth->allow(array(
            'index',
            'ftpInventoryUpdate',
            'emailInventoryUpdate',
            'resetRetailerUploadedInventory',
            'queueEcommerceSync',
            'lastcheck',
            'newcouriers',
            'inviteRetailers',
            'UpdateRetailerCount',
            'checkNonstockOrders',
            'mapTaxCache',
            'sendMails',
            'clearMails',
            'runBackJobs',
            'clearBackJobs',
            'checkUnCaptureOrders',
            'shipFromStoreOrderNotificationEmail',
            'nonStockOrderNotificationEmail',
            'shipFromStoreOrderCancelNotification',
            'nonStockOrderCancelNotification',
            'stripeWebHook',
            'abandonCartNotification',
            'clearShopifyDetails',
            '_vendPosUpdate',
            'vendRefreshToken',
            'runSendMails',
            'automaticDealerOrder',
        ));
        parent::beforeFilter();
        set_time_limit(0);
    }

    /**
     * This cron runs on day once
     * Get all retailers and stores inventory and update on shipearly database
     */
    public function index()
    {
        CakeLog::info("Start of inventory cronjob for retailer_id=" . ($this->request->params['id'] ?? 'ALL'));

        $sftpUsernamesForReset = SftpInventory::getUsernamesForReset();
        $validSftpUsernames = array_values(array_diff(SftpInventory::getAllUsernames(), $sftpUsernamesForReset));

        $validStoreConditions = $this->getValidUsersConditions([
            [
                // Shopify uses a single access token saved as inventory_password
                // Other inventory without credentials can manage inventory with manual uploads
                'OR' => [
                    'User.inventory_apiuser !=' => '',
                    'User.inventory_type' => ['shopify_pos', 'other'],
                ],
            ],
            [
                // Other inventory without credentials can manage inventory with manual uploads
                'OR' => [
                    'User.inventory_password !=' => '',
                    'User.inventory_type' => 'other',
                ],
            ],
            [
                // Integrations requiring a refresh token
                'OR' => [
                    "COALESCE(User.vend_refresh_access_token, '') !=" => '',
                    'User.inventory_type !=' => ['lightspeed_cloud', 'vend_pos'],
                ],
            ],
            [
                'OR' => [
                    'User.inventory_apiuser' => $validSftpUsernames,
                    'User.inventory_type !=' => 'ascend_rms',
                ],
            ],
            [
                'OR' => [
                    'User.inventory_apiuser !=' => $sftpUsernamesForReset,
                    'User.inventory_type !=' => 'other',
                ],
            ],
        ]);

        if (!isset($this->request->params['id'])) {
            $success = $this->User->streamPagedQuery([
                'recursive' => -1,
                'conditions' => $validStoreConditions,
                'fields' => ['id'],
            ], function($retailer) {
                $this->Btask->queueStoreUpdate($retailer['User']['id']);
            });
            if ($success) {
                // Reset inventory for any invalid retailers
                $this->_resetInventory(['NOT' => [$validStoreConditions]]);
            }
        } else {
            $retailerId = $this->request->params['id'];
            $productRetailers = $this->getProductRetailers(array_merge($validStoreConditions, [
                'User.id' => $retailerId,
            ]));
            if ($productRetailers) {
                $this->processInventoryUpdates($productRetailers);
            } else {
                // Reset inventory for any invalid retailers
                $this->_resetInventory(['User.id' => $retailerId]);
            }
            $success = true;
        }

        CakeLog::info("End of inventory cronjob for retailer_id=" . ($this->request->params['id'] ?? 'ALL'));

        return $success;
    }

    public function ftpInventoryUpdate()
    {
        CakeLog::info("Start of SFTP inventory cronjob");

        $usernamesForImport = SftpInventory::getUsernamesForImport();
        CakeLog::info("Importing inventory for FTP Users:" . implode(', ', $usernamesForImport));

        $usernamesForReset = SftpInventory::getUsernamesForReset();
        CakeLog::info("Resetting inventory for FTP Users:" . implode(', ', $usernamesForReset));

        $ftpIntegrations = ['ascend_rms', 'other'];
        $ftpInventoryUserConditions = $this->getValidUsersConditions([
            'User.inventory_password !=' => '',
            'User.inventory_type' => $ftpIntegrations,
        ]);
        $importConditions = array_merge($ftpInventoryUserConditions, [
            'User.inventory_apiuser' => $usernamesForImport,
        ]);
        $resetConditions = array_merge($ftpInventoryUserConditions, [
            'User.inventory_apiuser' => array_values(array_diff($usernamesForReset, $usernamesForImport)),
        ]);

        $productRetailers = $this->getProductRetailers($importConditions);

        $this->_resetInventory($resetConditions);
        $this->processInventoryUpdates($productRetailers);

        CakeLog::info("End of SFTP inventory cronjob");
    }

    public function emailInventoryUpdate()
    {
        if (!INVENTORY_EMAIL || !INVENTORY_EMAIL_PASS) {
            CakeLog::info('Email inventory update is disabled');

            return;
        }

        CakeLog::info("Start of Email inventory cronjob");

        $mailbox = imap_open("{imap.gmail.com:993/imap/ssl}INBOX", INVENTORY_EMAIL, INVENTORY_EMAIL_PASS);
        if (!$mailbox) {
            throw new InternalErrorException('Unable to connect to email server: ' . imap_last_error());
        }

        try {
            $isDebugMode = (Configure::read('debug') > 0);
            $toAddressForSearch = INVENTORY_EMAIL_ALIAS ?: INVENTORY_EMAIL;
            $mails = imap_search($mailbox, "UNSEEN TO {$toAddressForSearch} ON {$this->date('d-M-Y')}");
            if (!$mails) {
                CakeLog::info('No new emails found');
                return;
            }

            if (!$isDebugMode) {
                foreach ($mails as $mailId) {
                    imap_setflag_full($mailbox, $mailId, "\\Seen");
                }
            }

            $pregToAddressPrefix = preg_quote(substr($toAddressForSearch, 0, strpos($toAddressForSearch, '@')), '/');
            $uuidByMailIdRaw = [];
            foreach ($mails as $mailId) {
                $mail_headers = imap_headerinfo($mailbox, $mailId);
                $toAddress = $mail_headers->toaddress ?? '';
                if (preg_match('/' . $pregToAddressPrefix . '\+([a-f0-9]{8})-(\d+)@/i', $toAddress, $matches)) {
                    $uuidByMailIdRaw[$mailId] = [
                        'uuid_prefix' => $matches[1],
                        'user_id' => (int)$matches[2],
                    ];
                } else {
                    CakeLog::info($this->_buildMailHeadersLog('Could not extract UUID', $mailbox, $mailId, $mail_headers));
                }
            }

            if (!$uuidByMailIdRaw) {
                CakeLog::info('No UUIDs found');
                return;
            }

            $uuidByUserId = (array)$this->User->find('list', [
                'recursive' => -1,
                'conditions' => ['User.id' => array_column($uuidByMailIdRaw, 'user_id')],
                'fields' => ['id', 'uuid'],
            ]);

            $uuidByMailId = [];
            foreach ($uuidByMailIdRaw as $mailId => $data) {
                $uuid = $uuidByUserId[$data['user_id']] ?? null;
                if ($uuid && str_starts_with($uuid, $data['uuid_prefix'])) {
                $uuidByMailId[$mailId] = $uuid;
                } else {
                    CakeLog::info(json_encode([
                        'message' => 'UUID prefix mismatch or user not found',
                        'mailId' => $mailId,
                        'expectedPrefix' => $data['uuid_prefix'],
                        'user_id' => $data['user_id'],
                        'actualUuid' => $uuid,
                    ]));
                }
            }
            if (empty($uuidByMailId)) {
                CakeLog::info('No matching users after UUID prefix check');
                return;
            }

            $emailInventoryUserConditions = $this->getValidUsersConditions([
                'User.uuid' => array_values($uuidByMailId),
            ]);
            $productRetailers = $this->getProductRetailers($emailInventoryUserConditions);
            $this->processEmailInventoryUpdates($productRetailers, $uuidByMailId, $mailbox);

        } finally {
            imap_close($mailbox);
        }

        CakeLog::info("End of Email inventory cronjob");
    }

    public function resetRetailerUploadedInventory()
    {
        $stores = $this->Store->findAllForRetailerInventoryReset();
        CakeLog::info(json_encode(['message' => __FUNCTION__, 'users' => array_values(Hash::combine($stores, '{n}.User.id', '{n}.User'))]));
        if (!$stores) {
            return;
        }
        $this->Store->resetInventory(['Store.id' => Hash::extract($stores, '{n}.Store.id')]);
        foreach (Hash::combine($stores, '{n}.Store.masterId', '{n}.Store.masterId') as $masterId) {
            $this->ProductRetailer->updateStoreInventories(['user_id' => $masterId]);
        }
    }

    protected function getValidUsersConditions(array $conditions = [])
    {
        return array_merge($conditions, [
            'User.user_type' => User::TYPE_RETAILER,
            'User.status' => 'Active',
            'User.setup_status' => true,
        ]);
    }

    protected function getProductRetailers(array $conditions): array
    {
        $productRetailers = $this->User->find('all', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => 'product_retailers',
                    'alias' => 'ProductRetailer',
                    'type' => 'INNER',
                    'conditions' => [
                        'OR' => [
                            'User.id = ProductRetailer.user_id',
                            'User.Branch = ProductRetailer.user_id'
                        ]
                    ]
                ],
                [
                    'table' => 'products',
                    'alias' => 'Product',
                    'type' => 'INNER',
                    'conditions' => [
                        'Product.id = ProductRetailer.product_id',
                        'Product.sell_direct !=' => Product::SELL_DIRECT_EXCLUSIVELY,
                        'Product.product_status' => ProductStatus::ENABLED,
                        'Product.deleted' => false,
                        'COALESCE(Product.product_upc, "") !=' => "",

                    ]
                ],
                [
                    'table' => 'manufacturer_retailers',
                    'alias' => 'ManufacturerRetailer',
                    'type' => 'INNER',
                    'conditions' => [
                        'ManufacturerRetailer.user_id = Product.user_id',
                        'ManufacturerRetailer.retailer_id = User.id',
                        'ManufacturerRetailer.status' => ManufacturerRetailer::STATUS_CONNECTED,
                    ]
                ],
            ],
            'conditions' => $conditions,
            'fields' => [
                'User.id',
                'User.uuid',
                'User.email_address',
                'User.company_name',
                'User.Branch',
                'User.inventory_apiuser',
                'User.inventory_password',
                'User.otherInventory',
                'User.inventory_type',
                'User.vend_access_token_expires',
                'User.vend_refresh_access_token',
                'User.shop_url',
                'User.Inventory_Store_ID',
                'Product.id',
                'Product.user_id',
                'Product.product_upc',
                'Product.product_sku',
                'ProductRetailer.id',
                'ProductRetailer.product_id',
                'ProductRetailer.user_id'
            ],
        ]);
        

        $productRetailers = Hash::combine($productRetailers, '{n}.Product.id', '{n}', '{n}.User.id');
        $productRetailers = array_map(function($productRetailer) {
            $newProductRetailer['User'] = current($productRetailer)['User'];
            $newProductRetailer['Products'] = Hash::remove(Hash::remove($productRetailer, '{n}.ProductRetailer'), '{n}.User');

            return $newProductRetailer;
        }, $productRetailers);
        return $productRetailers;
    }

    protected function processInventoryUpdates(array $productsByStore)
    {
        $uniqueMasterIds = [];
        foreach ($productsByStore as $storeId => $store) {
            CakeLog::debug(json_encode(['message' => __FUNCTION__, 'User' => array_intersect_key($store['User'], array_flip(['id', 'email_address', 'company_name', 'otherInventory', 'inventory_type']))]));

            $apiUser = (string)$store['User']['inventory_apiuser'];
            $apiPass = (string)$store['User']['inventory_password'];
            $products = (array)$store['Products'];

            try {
                switch ($store['User']['inventory_type']) {
                    case 'lightspeed_cloud':
                        $stores = $this->_lightspeedUpdate($apiUser, $apiPass, $store['User']['Inventory_Store_ID'], $products, $storeId, $store['User']['vend_refresh_access_token'], $store['User']['vend_access_token_expires']);
                        break;
                    case 'square':
                        $stores = $this->_squarePosUpdate($apiUser, $apiPass, $products, $storeId);
                        break;
                    case 'shopify_pos':
                        $stores = $this->_shopifyPosUpdate($apiUser, $apiPass, $products, $storeId, $store['User']['shop_url']);
                        break;
                    case 'vend_pos':
                        $stores = $this->_vendPosUpdate($apiUser, $apiPass, $store['User']['Inventory_Store_ID'], $products, $storeId);
                        break;
                    case 'quickbook_pos':
                        $stores = $this->_quickbookUpdate($products, $storeId);
                        break;
                    case 'other':
                        if (!SftpInventory::isUsername($apiUser)) {
                            CakeLog::info(json_encode(['message' => 'Skipping manually uploaded inventory', 'User' => array_intersect_key($store['User'], array_flip(['id', 'email_address', 'company_name', 'inventory_type']))]));
                            continue 2;
                        }
                        // no break
                    case 'ascend_rms':
                        if (SftpInventory::isUsername($apiUser) && !SftpInventory::isUsernameForImport($apiUser)) {
                            CakeLog::info(json_encode(['message' => 'Skipping SFTP user with no import', 'User' => array_intersect_key($store['User'], array_flip(['id', 'email_address', 'company_name', 'inventory_apiuser', 'inventory_type']))]));
                            continue 2;
                        }
                        $stores = $this->_sftpInventoryUpdate($apiUser, $apiPass, $products, $storeId);
                        break;
                    default:
                        throw new Exception("Unknown inventory type \"{$store['User']['inventory_type']}\"");
                }

                if (!$this->Store->saveStoreInventoryUpdates($storeId, $stores)) {
                    throw new InternalErrorException(json_encode([
                        'message' => 'Failed to update inventory',
                        'errors' => $this->Store->validationErrors,
                        'data' => $this->Store->data,
                    ]));
                }

                $masterId = $store['User']['Branch'] ?: $store['User']['id'];
                $uniqueMasterIds[$masterId] = $masterId;
            } catch (Exception $e) {
                CakeLog::error(json_encode(['message' => 'Uncaught Exception for retailer', 'User' => $store['User']]));
                CakeLog::error($e);
            }
        }

        foreach ($uniqueMasterIds as $masterId) {
            $this->ProductRetailer->updateStoreInventories(['user_id' => $masterId]);
        }
    }

    protected function processEmailInventoryUpdates(array $productsByStore, array $uuidByMailId, $mailbox)
    {
        $productsByStore = Hash::combine($productsByStore, '{n}.User.uuid', '{n}');

        $uniqueMasterIds = [];
        foreach ($uuidByMailId as $mailId => $uuid) {
            $store = (array)($productsByStore[$uuid] ?? []);
            if (!$store) {
                CakeLog::info(json_encode([
                    'message' => 'Skipping email for unknown retailer',
                    'mailId' => $mailId,
                    'uuid' => $uuid
                ]));
                continue;
            }

            CakeLog::debug(json_encode([
                'message' => __FUNCTION__,
                'User' => array_intersect_key($store['User'], array_flip(['id', 'email_address', 'company_name', 'otherInventory', 'inventory_type']))
            ]));

            $products = (array)$store['Products'];
            try {
                $stores = $this->_emailCsvInventoryUpdate($mailbox, $mailId, $products, $store['User']['id']);
                if (!$this->Store->saveStoreInventoryUpdates($store['User']['id'], $stores)) {
                    throw new InternalErrorException(json_encode([
                        'message' => 'Failed to update inventory',
                        'errors' => $this->Store->validationErrors,
                        'data' => $this->Store->data,
                    ]));
                }
                $masterId = $store['User']['Branch'] ?: $store['User']['id'];
                $uniqueMasterIds[$masterId] = $masterId;
            } catch (Exception $e) {
                CakeLog::error(json_encode(['message' => 'Uncaught Exception for retailer', 'User' => $store['User']]));
                CakeLog::error($e);
            }
        }
        foreach ($uniqueMasterIds as $masterId) {
            $this->ProductRetailer->updateStoreInventories(['user_id' => $masterId]);
        }
    }

    protected function _emailCsvInventoryUpdate($mailbox, int $mailId, array $products, int $storeId): array
    {
        $structure = imap_fetchstructure($mailbox, $mailId);

        $stores = [];
        if (!isset($structure->parts)) {
            return $stores;
        }

        for ($i = 0; $i < count($structure->parts); $i++) {
            $part = $structure->parts[$i];

            $isCsv = $part->subtype === 'CSV' || (
                isset($part->parameters[0]->value) &&
                strpos($part->parameters[0]->value, '.csv') !== false
            );

            if (!$isCsv) {
                continue;
            }

            $attachment = imap_fetchbody($mailbox, $mailId, $i + 1);
            if ($part->encoding == 3) {
                $attachment = base64_decode($attachment);
            } elseif ($part->encoding == 4) {
                $attachment = quoted_printable_decode($attachment);
            }

            $lines = explode("\n", $attachment);
            $lines = array_map('trim', $lines);
            $lines = array_filter($lines);

            if (empty($lines)) {
                continue;
            }

            $header = str_getcsv(array_shift($lines));
            $header = array_map('strtolower', array_map('trim', $header));

            $upcIndex = array_search('upc', $header);
            $qtyIndex = array_search('qty', $header) !== false ? array_search('qty', $header) : array_search('qoh', $header);

            if ($upcIndex === false || $qtyIndex === false) {
                CakeLog::warning($this->_buildMailHeadersLog("Missing UPC or QTY header in email CSV", $mailbox, $mailId));
                continue;
            }

            $parsedData = [];
            $mismatchQuantities = [];
            $nonnumericQuantities = [];

            foreach ($lines as $line) {
                $row = str_getcsv($line);
                if (!isset($row[$upcIndex]) || !isset($row[$qtyIndex])) {
                    continue;
                }

                $upc = trim(str_replace('"', '', $row[$upcIndex]));
                $qtyRaw = trim($row[$qtyIndex]);

                if ($upc === '') {
                    continue;
                }

                if (!is_numeric($qtyRaw) || ((int)$qtyRaw != $qtyRaw)) {
                    $nonnumericQuantities[$upc][] = $qtyRaw;
                    continue;
                }

                $qty = (int)$qtyRaw;
                if (isset($parsedData[$upc]) && $parsedData[$upc]['qty'] !== $qty) {
                    $mismatchQuantities[$upc][$parsedData[$upc]['qty']] = 1;
                    $mismatchQuantities[$upc][$qty] = 1;

                    $qty = min($parsedData[$upc]['qty'], $qty);
                }

                $parsedData[$upc] = ['upc' => $upc, 'qty' => $qty];
            }

            if (!empty($mismatchQuantities)) {
                $mismatchQuantities = array_map('array_keys', $mismatchQuantities);
                CakeLog::warning($this->_buildMailHeadersLog(
                    "Mismatching quantities detected in email, using lower quantity. Mismatches: " . json_encode($mismatchQuantities),
                    $mailbox,
                    $mailId
                ));
            }

            if (!empty($nonnumericQuantities)) {
                CakeLog::warning($this->_buildMailHeadersLog(
                    "Nonnumeric quantities in email: " . json_encode($nonnumericQuantities),
                    $mailbox,
                    $mailId
                ));
            }

            $productUPCs = Hash::extract($products, '{n}.Product.product_upc');
            foreach ($products as $product) {
                $productId = $product['Product']['id'];
                $upc = $product['Product']['product_upc'];

                if (isset($parsedData[$upc])) {
                    $stores[] = [
                        'itemId' => $upc,
                        'storeId' => $storeId,
                        'productId' => $productId,
                        'inventoryCount' => $parsedData[$upc]['qty'],
                    ];
                }
            }

            foreach ($parsedData as $upc => $_) {
                if (!in_array($upc, $productUPCs) && strtolower($upc) !== 'no upc') {
                    CakeLog::warning($this->_buildMailHeadersLog(
                        "No UPC matches found for UPC: {$upc} in the database for storeId: {$storeId}",
                        $mailbox,
                        $mailId
                    ));
                }
            }
        }

        return $stores;
    }

    private function _buildMailHeadersLog($message, $mailbox, $mailId, $mail_headers = null): string
    {
        App::uses('AppLogTrait', 'Log/Engine');

        return AppLogTrait::json_encode([
            'message' => $message,
            'mailId' => $mailId,
            'mail_headers' => array_intersect_key((array)($mail_headers ?? imap_headerinfo($mailbox, $mailId)), array_flip([
                'date',
                'subject',
                'message_id',
                'toaddress',
                'fromaddress',
                'senderaddress',
            ])),
        ]);
    }

    /**
     * @param array $storeConditions
     */
    protected function _resetInventory(array $storeConditions)
    {
        $storeIds = (array)$this->User->find('list', [
            'recursive' => -1,
            'conditions' => array_merge($storeConditions, [
                [
                    'OR' => [
                        $this->ProductRetailer->buildExistsSubquery([
                            'ProductRetailer.user_id' => $this->User->primaryKeyIdentifier(),
                            'OR' => [
                                'ProductRetailer.inventory_count !=' => 0,
                                'ProductRetailer.inventoryItemId !=' => null,
                            ],
                        ]),
                        $this->Store->buildExistsSubquery(['Store.storeId' => $this->User->primaryKeyIdentifier()]),
                    ],
                ],
            ]),
            'fields' => ['id', 'id'],
            'order' => false,
        ]);
        if ($storeIds) {
            $this->ProductRetailer->resetInventory(['ProductRetailer.user_id' => $storeIds]);
            $this->Store->resetInventory(['Store.storeId' => $storeIds]);
        }
    }

    /**
     * Get the shop tax rate, inventory count and itemId from lightspeed and update product information at store level
     *
     * @param string $apiUser
     * @param string $apiPass
     * @param $shopID
     * @param array $products
     * @param int $storeId
     * @param $refresh_token
     * @param $token_expires_at
     * @return array
     * @throws CurlException
     * @throws LightspeedApiException
     * @throws LightspeedOauthException
     */
    public function _lightspeedUpdate(string $apiUser, string $apiPass, $shopID, array $products, int $storeId, $refresh_token, $token_expires_at): array
    {

        $buildStoresArray = function($stores, $response, $productIdsByUpc, $shopID, $storeId){
            foreach ($response->Item as $item) {
                $upc = (string) $item->upc;
                if(empty($productIdsByUpc[$upc])){
                    continue;
                }
                $productIds = $productIdsByUpc[$upc];
                $itemId = (string) $item->itemID;
                $count = 0;
                foreach ($item->ItemShops->ItemShop as $shop) {
                    if ($shop->shopID == $shopID) {
                        $count = (int) $shop->qoh;
                        break;
                    }
                }
                foreach ($productIds as $productId) {
                    $stores[] = array(
                        'itemId' => $itemId,
                        'productId' => $productId,
                        'storeId' => $storeId,
                        'inventoryCount' => $count
                    );
                }
            }
            return $stores;
        };

        $productIdsByUpc = array();
        foreach ($products as $product) {
            $upc = $product['Product']['product_upc'];
            $productIdsByUpc[$upc][] = $product['Product']['id'];
        }
        $refreshed = array();
        $stores = array();
        $upcChunks = array_chunk($productIdsByUpc, 100, true);
        
        $totalProductCount = $this->Lightspeed->countAllProducts($apiUser, $apiPass, $refresh_token, $token_expires_at);
        CakeLog::info(sprintf('Total UPC Chunks: %d, Lightspeed Product Count: %d', count($upcChunks), $totalProductCount));
        if(count($upcChunks) <= ceil($totalProductCount/LightspeedComponent::RESPONSE_RECORD_LIMIT)) {
            CakeLog::info('Fetching inventory by UPC Chunks.');
            foreach ($upcChunks as $productIdsByUpcChunk) {
                $upcs = array_keys($productIdsByUpcChunk);

                $response = $this->Lightspeed->getAllProductInventoriesByUpc($apiUser, $apiPass, $upcs, $refresh_token, $token_expires_at);
                $refreshed = $this->Lightspeed->getNewAccessTokenResponse($apiPass, $refreshed);
                if (!empty($refreshed['access_token'])) {
                    $apiUser = $refreshed['access_token'];
                    $token_expires_at = $refreshed['expires_at'];
                }

                if (!isset($response->Item)) {
                    CakeLog::debug('Response has no Items ' . json_encode(compact('upcs', 'response')));
                    continue;
                }
                $stores = $buildStoresArray($stores, $response, $productIdsByUpc, $shopID, $storeId);
            }
        } else {
            CakeLog::info('Fetching inventory by pagination.');
            $response = $this->Lightspeed->getAllProductInventories($apiUser, $apiPass, $refresh_token, $token_expires_at);
            $stores = $buildStoresArray($stores, $response, $productIdsByUpc, $shopID, $storeId);
            while(!empty($response['next'])){
                $response = $this->Lightspeed->nextPage($response, $apiUser, $apiPass, $refresh_token, $token_expires_at);
                $stores = $buildStoresArray($stores, $response, $productIdsByUpc, $shopID, $storeId);
            }
        }

        $refreshed = $this->Lightspeed->getNewAccessTokenResponse($apiPass, $refreshed);
        if (!empty($refreshed['access_token'])) {
            $apiUser = $refreshed['access_token'];
            $token_expires_at = $refreshed['expires_at'];
        }
        $shop = $this->Lightspeed->getShopTaxRate($apiUser, $apiPass, $shopID, $refresh_token, $token_expires_at);
        $taxRate = ((float)$shop->TaxCategory->tax1Rate + (float)$shop->TaxCategory->tax2Rate) * 100;

        if(!$this->User->save(['id' => $storeId, 'defaultTax' => $taxRate])) {
            throw new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data]));
        }
        if (!$this->User->refreshLightspeedAccessToken($storeId, $refreshed)) {
            throw new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data]));
        }

        return $stores;
    }

    /**
     * @param string $apiUser
     * @param string $apiPass
     * @param array $products
     * @param int $storeId
     * @return array
     * @throws \Square\Exceptions\ApiException
     */
    public function _squarePosUpdate(string $apiUser, string $apiPass, array $products, int $storeId): array
    {
        $items = $this->SquarePos->listItems($apiPass);
        $productMap = array();
        foreach ($items as $item) {
            foreach ($item['item_data']['variations'] as $variation) {
                $variationData = $variation['item_variation_data'];
                if (!isset($variationData['sku']) || empty(Hash::extract($variationData['location_overrides'] ?? [], "{n}[location_id={$apiUser}][track_inventory=true]"))) {
                    continue;
                }
                foreach ($products as $idx => $product) {
                    $productId = $product['Product']['id'];
                    $productMatch = ($variationData['sku'] == $product['Product']['product_upc'] || $variationData['sku'] == $product['Product']['product_sku']);
                     if (!isset($productMap[$productId]) && $productMatch) {
                        $productMap[$productId]['itemId'] = $variationData['item_id'];
                        $productMap[$productId]['variationId'] = $variation['id'];
                        break;
                    }
                }
            }
        }

        $inventoriesByVariantId = $this->SquarePos->listInventoriesByVariantId($apiPass, $apiUser);
        $stores = array();
        foreach ($products as $product) {
            $productId = $product['Product']['id'];
            if (isset($productMap[$productId])) {
                $productInfo = $productMap[$productId];
                $itemId = $productInfo['itemId'];
                $variationId = $productInfo['variationId'];
                $count = $inventoriesByVariantId[$variationId];
                $stores[] = array(
                    'itemId' => $itemId,
                    'inventoryVariantId' => $variationId,
                    'productId' => $productId,
                    'storeId' => $storeId,
                    'inventoryCount' => $count
                );
            }
        }

        return $stores;
    }

    /**
     * Get the inventory count and itemId from shopify and update product information at store level
     *
     * @param string|null $apiUser
     * @param string $apiPass
     * @param array $products
     * @param int $storeId
     * @param string $domain
     * @return array
     * @throws ShopifyApiException
     */
    public function _shopifyPosUpdate(string $apiUser, string $apiPass, array $products, int $storeId, $domain): array
    {
        $productIdByUpc = Hash::combine($products, '{n}.Product.product_upc', '{n}.Product.id');

        $posVariants = $this->ShopifyPOS->getAllVariants($apiPass, $domain, [
            'fields' => implode(',', ['id', 'product_id', 'barcode', 'inventory_quantity']),
        ]);

        $stores = array_filter(array_map(function($variant) use ($storeId, $productIdByUpc) {
            $productId = $productIdByUpc[$variant['barcode']] ?? null;
            if (!$productId) {
                return null;
            }

            return [
                'itemId' => $variant['product_id'],
                'inventoryVariantId' => $variant['id'],
                'storeId' => $storeId,
                'productId' => $productId,
                'inventoryCount' => $variant['inventory_quantity'],
            ];
        }, $posVariants));

        return $stores;
    }

    /**
     * Get the inventory count and itemId from Vend Pos and update product information at store level
     *
     * @param string $apiUser
     * @param string $apiPass
     * @param $outletID
     * @param array $products
     * @param int $storeId
     * @return array
     */
    public function _vendPosUpdate(string $apiUser, string $apiPass, $outletID, array $products, int $storeId): array
    {
        $stores = array();
        foreach ($products as $product) {
            $productId = $product['Product']['id'];
            $upc = $product['Product']['product_upc'];
            $productInfo = $this->VendPOS->getInventoryCount($apiUser, $apiPass, $outletID, $upc);
            sleep(2);
            if (isset($productInfo['inventory_quantity'])) {
                $count = $productInfo['inventory_quantity'];
                $itemId = $productInfo['id'];
                $stores[] = array(
                    'itemId' => $itemId,
                    'storeId' => $storeId,
                    'productId' => $productId,
                    'inventoryCount' => $count
                );
            }
        }

        return $stores;
    }

    /**
     * Get the inventory count and itemId from Quickbook product table and update product information at store level
     *
     * @param array $products
     * @param int $storeId
     * @return array
     */
    public function _quickbookUpdate(array $products, int $storeId): array
    {
        $stores = array();
        foreach ($products as $product) {
            $productId = $product['Product']['id'];
            $upc = $product['Product']['product_upc'];
            $productInfo = $this->Quickbook->getProductInfo($upc, $storeId);
            if (isset($productInfo['QuickbookProduct']['qty'])) {
                $count = $productInfo['QuickbookProduct']['qty'];
                $itemId = $productInfo['QuickbookProduct']['listId'];
                $stores[] = array(
                    'itemId' => $itemId,
                    'storeId' => $storeId,
                    'productId' => $productId,
                    'inventoryCount' => $count
                );
            }
        }

        return $stores;
    }

    public function _sftpInventoryUpdate(string $apiUser, string $apiPass, array $products, int $storeId): array
    {
        if (!$apiPass || !SftpInventory::isUsernameForImport($apiUser)) {
            return [];
        }

        $sftp = new SftpInventory($apiUser);
        $stores = [];
        $newQty = $sftp->getInventoryData();
        foreach ($products as $product) {
            $productId = $product['Product']['id'];
            $upc = $product['Product']['product_upc'];
            if (isset($newQty[$upc]['qty'])) {
                $count = $newQty[$upc]['qty'];
                $itemId = $newQty[$upc]['upc'];
                $stores[] = array(
                    'itemId' => $itemId,
                    'storeId' => $storeId,
                    'productId' => $productId,
                    'inventoryCount' => $count
                );
            }
        }
        if (empty($stores)) {
            CakeLog::warning("No UPC matches found between import and database for user_id:{$storeId}. UPCs from database:" . json_encode(Hash::extract($products, '{n}.Product.product_upc')));
        } elseif ($this->Store->validateManyFast($stores)) {
            $sftp->cleanupFiles();
        }

        return $stores;
    }

    /**
     * Queue eCommerce sync jobs for active brands.
     */
    public function queueEcommerceSync()
    {
        $this->Lock->applyGlobalLock($this->request->url, [$this->Cron, 'queueActiveUsers']);
    }

    /**
     * This cron runs on every 10 min
     * It update last checkpoint for the customer Order
     */
    public function lastcheck()
    {
        $this->Lock->applyGlobalLock($this->request->url, function() {

        $order = $this->Order->findAllForLastCheckpointUpdate();
        foreach ($order as $value) {
            try {
                $lastcheck = $this->AfterShip->getLastCheckpoint($value['Courier']['slug'], $value['Order']['trackingno']);
                $city = $lastcheck['checkpoint']['city'];

                if ($value['Order']['last_check_point'] != $city) {
                    $this->Order->save(array('id' => $value['Order']['id'], 'last_check_point' => $city));
                    $notification_msg = "Order No: " . $value['Order']['orderID'] . " has been moved from " . $value['Order']['last_check_point'] . " city to " . $city;
                    $this->Notification->createNotification($value['Order']['user_id'], $value['Order']['user_id'], Notification::TYPE_AFTER_SHIP_CHECKPOINT_UPDATE, '', $notification_msg);
                }
            } catch (Exception $e) {
                CakeLog::error(strval($e));
            }
        }

        });
        return true;
    }

    /**
     * Update courier data from AfterShip.
     *
     * This cron runs weekly on Tue at 08:00 UTC.
     */
    public function newcouriers()
    {
        return $this->Lock->applyGlobalLock($this->request->url, function() {
            if (!$this->Courier->syncAllAfterShipCouriers($this->AfterShip->getAllCouriers())) {
                $message = sprintf('Failed to sync couriers with AfterShip in %s:%d', __FILE__, __LINE__);
                $message .= "\nException Attributes: " . json_encode(['errors' => $this->Courier->validationErrors, 'data' => $this->Courier->data]);

                throw new InternalErrorException($message);
            }

            return true;
        });
    }

    /**
     * This cron runs on every 10 min
     * Check the imported retailer from ImportEmail table
     * Check the the no of time and last triggered date.
     * send the invitation email upto 10 times
     */
    public function inviteRetailers()
    {
        $this->Lock->applyGlobalLock($this->request->url, function() {
            $emails = $this->ImportEmail->findAllForRetailerInvitations();
            if (!$emails) {
                return;
            }

            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Invite Email');
            $variables = array();
            foreach ($emails as $value) {
                //invite email
                $variables['{invitation_link}'] = BASE_PATH . "signup/Retailer";
                $variables['{brand_name}'] = $value['User']['company_name'];
                $variables['{site_name}'] = SITE_NAME;
                $this->NotificationLogic->sendEmail($emailTemplateArr, $variables, $value['ImportEmail']['email']);
            }

            $this->ImportEmail->incrementEmailSendCounts(Hash::extract($emails, '{n}.ImportEmail.id'));
        });
        return true;
    }

    /**
     * This cron runs on day once
     * Update the current month retailer count on database
     * @return bool
     */
    public function UpdateRetailerCount()
    {
        if (date('Y-m-d') == date('Y-m-01')) {
            $dealersCount = $this->ManufacturerRetailer->getRetailerCountByManufacturer();
            $dealerInsert = array();
            foreach ($dealersCount as $key => $value) {
                $dealerInsert[$key]['userId'] = $value['ManufacturerRetailer']['user_id'];
                $dealerInsert[$key]['dealerCount'] = $value['0']['Retailers'];
                $dealerInsert[$key]['month'] = str_pad(date('m', strtotime("first day of -1 month")), 2, "0", STR_PAD_LEFT);
            }
            $this->Retailercount->saveAll($dealerInsert);
        }
        return true;
    }

    /**
     * This cron runs on every 1 min
     * Check the mailQueue table and send the mails one by one
     * @return bool
     */
    public function runSendMails()
    {
        $mails = $this->Lock->applyGlobalLock($this->request->url, [$this->MailQueue, 'dequeueEmails']);
        foreach ($mails as $value) {
            $id = $value['MailQueue']['id'];
            try {
                $this->_sendQueuedEmail($value);
                $this->MailQueue->setStatus($id, MailQueue::STATUS_CLOSED);
            } catch (Exception $e) {
                CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . 'Uncaught exception sending email: ' . json_encode($value), 'email');
                CakeLog::error(strval($e), 'email');

                $status = ($value['MailQueue']['status'] !== MailQueue::STATUS_ERROR)
                    ? MailQueue::STATUS_ERROR
                    : MailQueue::STATUS_CLOSED;
                $this->MailQueue->setStatus($id, $status);
            }
        }
        return true;
    }

    /**
     * Temporary fix
     */
    public function sendMails() {
        $this->autoRender = false;
        return true;
    }

    /**
     * This cron runs on every 2hr
     * Just clear all successfully triggered mails from mailqueue
     * @return bool
     */
    public function clearMails()
    {
        $this->MailQueue->clearSentEmails();
        return true;
    }

    /**
     * This cron runs on eveny 1 min
     * get all the Btask based the status
     * and update the status to success when it completed.
     * @return bool
     */
    public function runBackJobs()
    {
        $this->Btask->batchSize = BTASK_BATCH_SIZE;
        $btaskList = $this->Lock->applyGlobalLock($this->request->url, [$this->Btask, 'findBackJobs']);
        $count = count($btaskList);
        if ($count) {
            CakeLog::debug("Processing {$count} btasks.", 'webhookService');
        }
        foreach ($btaskList as $value) {
            try {
                $type = $value['Btask']['type'];
                $parameters = json_decode($value['Btask']['data'], true);

                if ($type === Btask::TYPE_AVALARA) {
                    array_push($parameters, false);
                    call_user_func_array([$this->Avatax, 'setTotalTax'], $parameters);
                } elseif ($type === Btask::TYPE_LIGHTSPEED_ORDER) {
                    array_push($parameters, false);
                    call_user_func_array([$this->Lightspeed, 'createSale'], $parameters);
                } elseif ($type === Btask::TYPE_SHOPIFY_ORDER) {
                    array_push($parameters, false);
                    call_user_func_array([$this->ShopifyPOS, 'createShopifyPosOrder'], $parameters);
                } elseif ($type === Btask::TYPE_AFTERSHIP_WEBHOOK) {
                    $this->_aftershipWebhookHandler($parameters);
                } elseif ($type === Btask::TYPE_STRIPE_WEBHOOK) {
                    $this->_stripeWebhookHandler($parameters);
                } elseif ($type === Btask::TYPE_SHOPIFY_WEBHOOK) {
                    $this->ShopifyWebhookHandler->handleWebhookTask($parameters);
                } elseif ($type === Btask::TYPE_INVENTORY_RESERVATION) {
                    $this->_reserveEcommerceInventory($parameters['warehouse_product_id']);
                } elseif ($type === Btask::TYPE_CREATE_ECOMMERCE_CONSUMER_ORDER) {
                    $this->OrderPlacer->createEcommerceConsumerOrder($parameters['order_id'], false);
                } elseif ($type === Btask::TYPE_CREATE_ECOMMERCE_DEALER_ORDER) {
                    $this->OrderPlacer->createEcommerceDealerOrder($parameters['dealer_order_id']);
                } elseif ($type === Btask::TYPE_URL) {
                    $this->requestAction($parameters['url'], array_diff_key($parameters, array_flip(['url'])));
                } else {
                    CakeLog::warning("Btask for '{$type}' not supported", 'webhookService');
                }
            } catch (Exception $e) {
                CakeLog::error($e, 'webhookService');
            }
            $this->Btask->closeTask($value['Btask']['id']);
        }
        return true;
    }

    /**
     * This cron runs on every 2hr
     * Clear Successfully completed bTask from shipearly
     * @return bool
     */
    public function clearBackJobs()
    {
        return $this->Btask->clearBackJobs();
    }

    /**
     * This cron runs on every 10min
     * Get all uncaptured(payment) orders from order table
     * get the transactionId and capture the amount using Stripe(Stripe only hold the captured amount up to 6days)
     */
    public function checkUnCaptureOrders()
    {
        $this->Lock->applyGlobalLock($this->request->url, function() {
            $orders = $this->Order->findAllWithExpiringStripeCharge([
                'Order.id',
                'Order.user_id',
                'Order.retailer_id',
                'Order.is_commission_retailer',
                'Order.order_type',
                'Order.payment_method',
                'Order.total_price',
                'Order.total_discount',
                'Order.transactionID',
                'Order.stripe_account',
                'Order.shipearlyFees',
            ]);
            foreach ($orders as $order) {
                try {
                    if (!$this->OrderLogic->captureStripeCharge((int)$order['Order']['id'], $order['Order'])) {
                        throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                    }
                } catch (Exception $e) {
                    CakeLog::error($e);
                }
            }
        });

        return true;
    }

    /**
     * This cron runs on every 10mins
     * Get all ship from store order and check if the order have any tracking number
     * if there is no tracking number, then send a remainder mail to retailer
     */
    public function shipFromStoreOrderNotificationEmail()
    {
        return true;
    }

    /**
     * This cron runs on every 10mins
     * Get all Non Stock order and check if the order have any Delivery date
     * if there is no date, then send a remainder mail to retailer
     */
    public function nonStockOrderNotificationEmail()
    {
        $this->Lock->applyGlobalLock($this->request->url, function() {
            $this->NotificationLogic->sendNonStockOrderReminders();
        });
        return true;
    }

    /**
     * This cron runs on every 10mins
     * get all shipfromstore order exceed 2 days limit
     * and cancel that orders and payment
     */
    public function shipFromStoreOrderCancelNotification() 
    {
        return true;
    }

    /**
     * This cron runs on every 10mins
     * get all abandon cart notifications exceed 3hrs limit
     */
    public function abandonCartNotification() 
    {
        $this->Lock->applyGlobalLock($this->request->url, function() {

            // to record conversions we will need to keep these rows.
            // Since rows here are created as a checkout is started, 
            // We need to only record items that are emailed then purchased as conversions.
            // Items that are purchased before an email should be deleted here
            $this->AbandonCart->deleteAll(array(
                'AbandonCart.status' => 1,
                'AbandonCart.preOrderID !=' => null
            ), false, false);

            $newCarts = $this->AbandonCart->findAllForEmails();
            foreach ($newCarts as $cart) {
                if ($this->NotificationLogic->abandonCartNotificationMail($cart)) {
                    $this->AbandonCart->clear();
                    $this->AbandonCart->save(['id' => $cart['AbandonCart']['id'], 'status' => 1]);
                }
            }

        });
        return true;
    }

    /**
     * Remove old Shopify Success and Error Pages.
     *
     * This cron runs on every 10min.
     *
     * @return bool
     */
    public function clearShopifyDetails()
    {
        $this->SuccessDetails->deleteAll(['SuccessDetails.created_at <= NOW() - INTERVAL 1 HOUR'], false);
        $this->ErrorDetails->deleteAll(['ErrorDetails.created_at <= NOW() - INTERVAL 1 HOUR'], false);
        $this->AccessToken->deleteAllExpired();

        return true;
    }

    /**
     * Refresh tokens of expired POS accounts.
     * This cron runs every 10 mins.
     */
    public function vendRefreshToken() 
    {
        $this->Lock->applyGlobalLock($this->request->url, function() {
            $this->_refreshVendAccessTokens();
            $this->_refreshSquareAccessTokens();
        });
        return true;
    }

    private function _refreshVendAccessTokens()
    {
        $users = $this->_findUserIdAccessTokens(array(
            'vend_access_token_expires <= NOW() - INTERVAL 24 HOUR',
            'inventory_type' => 'vend_pos',
            'status' => 'Active'
        ));
        if(empty($users)) {
            return;
        }

        $saveMany = array_filter(
            array_map(function($user) {
                try {
                    $result = $this->VendPOS->refreshToken((string)$user['vend_refresh_access_token'], (string)$user['inventory_password']);
                    if (!empty($result['access_token'])) {
                        return array(
                            'id' => $user['id'],
                            'inventory_apiuser' => $result['access_token'],
                            'vend_access_token_expires' => (new DateTime("@{$result['expires']}"))->format('Y-m-d H:i:s'),
                        );
                    }
                } catch (Exception $e) {
                    CakeLog::error(strval($e));
                }
                return null;
            }, $users)
        );

        $this->_saveRefreshedAccessTokens($users, $saveMany);
    }

    private function _refreshSquareAccessTokens()
    {
        $users = $this->_findUserIdAccessTokens(array(
            'vend_access_token_expires <= NOW()',
            'inventory_type' => 'square',
            'status' => 'Active'
        ));
        if (empty($users)) {
            return;
        }

        $saveMany = array_filter(
            array_map(function($user) {
                try {
                    $result = $this->SquarePos->renewAccessToken($user['inventory_password']);
                    if (!empty($result['access_token'])) {
                        return array(
                            'id' => $user['id'],
                            'inventory_password' => $result['access_token'],
                            'vend_access_token_expires' => $result['expires_at'],
                        );
                    }
                } catch (Exception $e) {
                    CakeLog::error(strval($e));
                }
                return null;
            }, $users)
        );

        $this->_saveRefreshedAccessTokens($users, $saveMany);
    }

    private function _findUserIdAccessTokens($conditions)
    {
        $users = $this->User->find('all', array(
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => array(
                'id',
                'email_address',
                'inventory_type',
                'inventory_apiuser',
                'inventory_password',
                'vend_access_token_expires',
                'vend_refresh_access_token',
            ),
            'limit' => 1000,
        ));
        $users = Hash::combine($users, '{n}.User.id', '{n}.User');

        $invalidUsers = array_filter($users, function($user) {
            return ($user['inventory_type'] === 'square') ? !$user['inventory_password'] : !$user['vend_refresh_access_token'];
        });
        if ($invalidUsers) {
            CakeLog::warning(__METHOD__ . ' line, ' . __LINE__ . ' - Missing refresh tokens for ' . json_encode($this->_formatRefreshTokenUsersLog($invalidUsers)));
        }

        return array_diff_key($users, $invalidUsers);
    }

    private function _saveRefreshedAccessTokens($users, $saveMany)
    {
        $savedUsers = array();
        if ($saveMany) {
            if ($this->User->saveMany($saveMany)) {
                $savedUsers = array_intersect_key($users, $saveMany);
                CakeLog::info(__METHOD__ . ' line, ' . __LINE__ . ' - Successfully refreshed access tokens for ' . json_encode($this->_formatRefreshTokenUsersLog($savedUsers)));
            }
        }

        $failedUsers = array_diff_key($users, $savedUsers);
        if ($failedUsers) {
            CakeLog::error(__METHOD__ . ' line, ' . __LINE__ . ' - Failed to refresh access tokens for ' . json_encode($this->_formatRefreshTokenUsersLog($failedUsers)));
        }
    }

    private function _formatRefreshTokenUsersLog($users)
    {
        return array_map(function($user) {
            return array(
                'user_id' => $user['id'],
                'email_address' => $user['email_address'],
                'inventory_type' => $user['inventory_type'],
                'expired_at' => $user['vend_access_token_expires'],
            );
        }, array_values($users));
    }

    /**
     * This cron runs on every 10mins
     * get all non stock orders exceed 36hrs limit
     * and cancel that orders and payment
     */
    public function nonStockOrderCancelNotification() 
    {
        return true;
    }

    /**
     * This cron runs on every 10mins
     * get all non stock orders exceed 24hrs limit
     * and place dealer order
     */
    public function automaticDealerOrder() 
    {
        $this->Lock->applyGlobalLock($this->request->url, function() {

            $orderIds = $this->Order->listIdsForAutomaticDealerOrders();
            foreach ($orderIds as $orderId) {
                if ($this->OrderLogic->dealerOrderEvent($orderId)) {
                    $this->NotificationLogic->newDealerOrderNotification($orderId, 'auto');
                }
            }

        });
        return true;
    }

    /**
     * Stripe WebHook
     * This method initiated by stripe events
     * capture some events and update details on shipearly
     * @return CakeResponse
     */
    public function stripeWebHook()
    {
        $supportedTypes = array(
            'account.updated',
            'account.application.deauthorized',
            'customer.subscription.created',
            'customer.subscription.updated',
            'payment_intent.succeeded',
            'payment_intent.payment_failed',
            'payout.reconciliation_completed',
            'payout.canceled',
            'payout.paid',
            'payout.failed',
            'transfer.reversed',
            'transfer.paid',
            'transfer.failed',
        );
        $type = $this->request->data('type');
        if (empty($type)) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . 'Bad Request ' . json_encode($this->request->data));
            return $this->response;
        }
        if (!in_array($type, $supportedTypes)) {
            $this->_webhookServiceLog("Webhook for '{$type}' not supported");
            return $this->response;
        }
        $this->_webhookServiceLog('Webhook Received ' . json_encode($this->request->data));
        $this->Btask->queueTask(Btask::TYPE_STRIPE_WEBHOOK, $this->request->data);
        return $this->response;
    }

    protected function _stripeWebhookHandler($data)
    {
        CakeLog::debug(json_encode(compact('data')));

        switch ($data['type']) {
        case 'account.updated':
            $accountIds = array($data['data']['object']['id']);
            if (!empty($data['user_id'])) {
                $accountIds[] = $data['user_id'];
            }
            $stripeUsers = $this->StripeUser->findAllByStripeUserId($accountIds, ['id', 'user_id', 'stripe_user_id'], null, null, null, -1);
            if (empty($stripeUsers)) {
                throw new NotFoundException('No StripeUser found for ' . json_encode($accountIds));
            }
            foreach ($stripeUsers as $stripeUser) {
                try {
                    // Use API resource instead of webhook payload because webhook payload is not verified
                    // see https://stripe.com/docs/webhooks/signatures#verify-manually
                    $account = $this->Stripe->getAccount($stripeUser['StripeUser']['stripe_user_id']);
                    
                    $this->StripeUser->updateUserAccount($stripeUser['StripeUser']['id'], $stripeUser['StripeUser']['user_id'], $account);
                } catch (Exception $e) {
                    CakeLog::error($e);
                }
            }
            break;
        case 'account.application.deauthorized':
            if (empty($data['user_id']) || Hash::get($data, 'data.object.id') != STRIPE_CLIENT_ID) {
                throw new BadRequestException("Invalid Stripe '{$data['type']}' event " . json_encode($data));
            }
            $accountId = $data['user_id'];
            $conditions = ['StripeUser.stripe_user_id' => $accountId];
            if (!$this->StripeUser->exists($conditions)) {
                throw new NotFoundException("No StripeUser found for deauthorized account '{$accountId}'");
            }
            if (!$this->StripeUser->disconnectAll($conditions)) {
                throw new InternalErrorException('Unable to disconnect deauthorized Stripe account ' . json_encode(['stripe_user_id' => $accountId, 'user_id' => $this->StripeUser->User->id]));
            }
            $this->_webhookServiceLog('Disconnected deauthorized Stripe account ' . json_encode(['stripe_user_id' => $accountId, 'user_id' => $this->StripeUser->User->id]));
            break;
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
            if (empty($data['data']['object']['customer'])) {
                throw new BadRequestException("Invalid Stripe '{$data['type']}' event" . json_encode($data));
            }
            $stripe_cus_id = $data['data']['object']['customer'];
            $this->StripeUser->save(array(
                'id' => $this->StripeUser->field('id', ['stripe_cus_id' => $stripe_cus_id]),
                'stripe_cus_id' => $stripe_cus_id,
                'subscription_period_start' => date('Y-m-d H:i:s', $data['data']['object']['current_period_start']),
                'subscription_period_end' => date('Y-m-d H:i:s', $data['data']['object']['current_period_end']),
            ));
            break;
        case 'payment_intent.succeeded':
            $this->_updatePaymentStatus(Hash::get($data, 'data.object.id'), RetailerCreditPaymentStatus::COMPLETE);
            break;
        case 'payment_intent.payment_failed':
            $this->_updatePaymentStatus(Hash::get($data, 'data.object.id'), RetailerCreditPaymentStatus::FAILED);
            break;
        default:
            $this->StripeWebhookHandler->handle($data);
        }
    }

    protected function _updatePaymentStatus($paymentId, $status)
    {
        $creditPaymentId = $this->RetailerCreditPayment->findIdByStripeChargeId($paymentId);
        if ($creditPaymentId) {
            $this->RetailerCreditPayment->updatePaymentStatus($creditPaymentId, $status);
        }
    }

    protected function _aftershipWebhookHandler($data)
    {
        if (empty($data['msg']['slug']) || empty($data['msg']['tracking_number'])) {
            throw new BadRequestException('Aftership webhook data ' . json_encode($data));
        }
        $tracking = $this->AfterShip->getTracking($data['msg']['slug'], $data['msg']['tracking_number']);
        $this->_webhookServiceLog(compact('tracking'));

        $orders = $this->Order->findAllForAfterShipWebhook((string)$tracking['tracking_number'], (string)$tracking['slug']);
        if (empty($orders)) {
            throw new NotFoundException("No orders found with courier slug '{$tracking['slug']}' and tracking number '{$tracking['tracking_number']}'");
        }
        foreach ($orders as $order) {
            $this->_webhookServiceLog("Order '{$order['Order']['orderID']}' tracking status is '{$tracking['tag']}'");
            if ($tracking['tag'] === 'Delivered') {
                $this->NotificationLogic->sendNonStockCourierDeliveredEmail($order['Order']['id']);
            }
            $this->_webhookServiceLog("Updating checkpoint " . json_encode(['orderID' => $order['Order']['orderID'], 'last_check_point' => $tracking['LastCheckPoint']]));
            $this->Order->clear();
            $this->Order->save(['id' => $order['Order']['id'], 'last_check_point' => $tracking['LastCheckPoint']]);
        }
    }

    protected function _reserveEcommerceInventory($warehouseProductId): bool
    {
        $this->Product->addAssociations(['belongsTo' => ['User'], 'hasOne' => ['WarehouseProduct']]);
        $product = $this->Product->find('first', [
            'contain' => [
                'User' => ['fields' => ['id', 'email_address', 'site_type', 'shop_url', 'api_key', 'secret_key']],
                'WarehouseProduct' => ['fields' => ['id']],
            ],
            'joins' => [
                [
                    'table' => $this->WarehouseProduct->buildProductTotalsSubQuery(),
                    'alias' => 'TotalWarehouseProduct',
                    'type' => 'LEFT',
                    'conditions' => ['TotalWarehouseProduct.product_id = Product.id'],
                ],
            ],
            'conditions' => ['WarehouseProduct.id' => $warehouseProductId],
            'fields' => [
                'Product.id',
                'Product.user_id',
                'Product.inventory_item_id',
                'TotalWarehouseProduct.product_id',
                'TotalWarehouseProduct.reserved_quantity',
            ],
        ]);
        $this->Product->unbindModel(['belongsTo' => ['User'], 'hasOne' => ['WarehouseProduct']], false);

        if (empty($product['WarehouseProduct']['id'])) {
            throw new NotFoundException(json_encode(['message' => 'Not Found', 'warehouse_product_id' => $warehouseProductId]));
        }

        $user = $product['User'];
        // For logs
        $User = mask_secret_fields($user, ['api_key', 'secret_key']);

        $reserved = $this->Warehouse->find('first', [
            'recursive' => -1,
            'conditions' => [
                'Warehouse.user_id' => $product['Product']['user_id'],
                'Warehouse.name' => Warehouse::NAME_RESERVED,
            ],
            'fields' => ['Warehouse.id', 'Warehouse.source_id'],
        ]);
        if (empty($reserved['Warehouse']['id'])) {
            $message = 'User does not have a warehouse named \'' . Warehouse::NAME_RESERVED . '\'';
            CakeLog::info(json_encode(compact('message', 'User')));
            return false;
        }

        if ($user['site_type'] !== UserSiteType::SHOPIFY) {
            $message = 'User site_type not supported';
            throw new ForbiddenException(json_encode(compact('message', 'User')));
        }

        $permissions = $this->Shopify->listMissingAccessScopes($user['api_key'], $user['secret_key'], $user['shop_url'], [
            'write_inventory',
        ]);
        if ($permissions) {
            $message = 'Required Shopify API permissions are not enabled for this app';
            throw new ForbiddenException(json_encode(compact('message', 'permissions', 'User')));
        }

        $locationId = $reserved['Warehouse']['source_id'];
        $inventoryItemId = $product['Product']['inventory_item_id'];
        $reservedQuantity = -$product['TotalWarehouseProduct']['reserved_quantity'];

        return (bool)$this->Shopify->setInventoryLevel($user['api_key'], $user['secret_key'], $user['shop_url'], $inventoryItemId, $locationId, $reservedQuantity);
    }
}
