<?php

use Stripe\Exception\ApiErrorException;

App::uses('AppController', 'Controller');
App::uses('CreditLine', 'Lib/RetailerCredit');
App::uses('RetailerCreditPaymentStatus', 'Utility');
App::uses('RetailerCreditPaymentType', 'Utility');

/**
 * RetailerCredits Controller
 *
 * @property IndexQueryHandlerComponent $IndexQueryHandler
 * @property StripeComponent $Stripe
 * 
 * @property LegacyRetailerCredit $LegacyRetailerCredit
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property RetailerCredit $RetailerCredit
 * @property RetailerCreditPayment $RetailerCreditPayment
 * @property RetailerCreditTerm $RetailerCreditTerm
 * @property RetailerCreditVoucher $RetailerCreditVoucher
 * @property StripeConnectCustomer $StripeConnectCustomer
 * @property StripeUser $StripeUser
 * @property User $User
 */
class RetailerCreditsController extends AppController
{
    public $components = [
        'IndexQueryHandler' => [
            'defaultQuery' => [
                'sort' => 'RetailerCredit.credit_date',
                'direction' => 'DESC',
                'limit' => '10',
                'page' => '1'
            ],
        ],
        'Stripe.Stripe',
    ];

    public $uses = [
        'ManufacturerRetailer',
        'LegacyRetailerCredit',
        'RetailerCredit',
        'RetailerCreditPayment',
        'RetailerCreditTerm',
        'RetailerCreditVoucher',
        'StripeUser',
        'StripeConnectCustomer',
        'User',
    ];

    public function index()
    {
        list($brandId, $retailerId) = $this->_extractUserIds($this->request);
        $this->request->query = $this->IndexQueryHandler->extractAllParams($this->request->query);
        $limit = $this->request->query('limit') ?: 10;
        $page = $this->request->query('page') ?: 1;
        $sortField = $this->request->query('sort');
        $sortDirection = $this->request->query('direction');

        $locationId = $this->request->query('locationId');
        $selectedStatus = $this->request->query('selectedStatus');

        $this->set('currencyCode', $this->getCurrencyCodeForIndex($brandId, $retailerId));
        $this->set('title_for_layout', $this->getTitleForIndex($brandId, $retailerId));

        list($startDate, $endDate) = [$this->request->query('startDate'), $this->request->query('endDate')];
        if (empty($startDate)) {
            $startDate = '6 MONTHS AGO';
        }
        if (empty($endDate)) {
            $endDate = 'TODAY';
        }
        list($startDate, $endDate) = $this->RetailerCredit->convertDateRange($startDate, $endDate);
        $this->set(compact('startDate', 'endDate'));

        $this->set('totals', $this->RetailerCredit->findTotals($brandId, $retailerId));
        $this->set('voucher_totals', $this->RetailerCreditVoucher->findTotals($brandId, $retailerId));
        $credits = $this->RetailerCredit->getCreditsForIndex($brandId, $retailerId, $startDate, $endDate, $locationId, $selectedStatus, $limit, $page, $sortField, $sortDirection);
        $credits = $this->getCreditLineObjects($credits);
        $this->set('credits', $credits);

        $locations = $this->User->listRetailerStoreNames($retailerId);
        $stripeConnectedAccountId = $this->StripeUser->getAccountId($brandId);

        $this->set('locations', $locations);
        $this->set('locationId', $locationId);

        $statuses = RetailerCreditStatus::getPaymentStatuses();
        $this->set('statuses', $statuses);
        $this->set('selectedStatus', $selectedStatus);
        $this->set('creditCount', $this->RetailerCredit->getCountForIndex($brandId, $retailerId, $startDate, $endDate, $locationId, $selectedStatus));
        $this->set('limit', $limit);
        $this->set('page', $page);
        $this->set('sortField', $sortField);
        $this->set('sortOrder', $sortDirection);
        $this->set('connectedAccountId', $stripeConnectedAccountId);
        $this->set('brandId', $brandId);
        $this->set('retailerId', $retailerId);

        // permission settings
        $this->set('canMakePayment', $this->userCanMakePayment());
        $this->set('canCreateCredit', $this->userCanCreateCredit());
        $this->set('canEditCredit', $this->userCanEditCredit());
    }


    protected function getShippedDate(array $credit)
    {
        return !empty($credit['Order']['shipped_date']) ? date_create($credit['Order']['shipped_date']) : false;
    }

    protected function getDueDate(array $credit)
    {
        $shippedDate = $this->getShippedDate($credit);
        $dueDate = $shippedDate ?: false;
        $daysdue = $credit['CreditTerm']['days_due'] ?? 0;

        return $dueDate ? date_add($dueDate, new DateInterval("P{$daysdue}D")) : false;
    }

    public function create()
    {
        if (!$this->userCanCreateCredit()) {
            throw new ForbiddenException(json_encode($this->Auth->user()));
        }

        list($brandId, $retailerId) = $this->_extractUserIds($this->request);

        if($this->request->is('get')){
            $this->createGet($brandId, $retailerId);
        } else if($this->request->is('post')){
            $this->createPost($brandId, $retailerId);
        }
    }

    protected function createPost($brandId, $retailerId){
        $this->request->data['RetailerCredit']['user_id'] = $brandId;
        $this->request->data['RetailerCredit']['retailer_id'] = $retailerId;
        $this->request->data['RetailerCredit']['credit_date'] = format_from_datepicker($this->request->data['RetailerCredit']['credit_date']);
        if (!$this->RetailerCredit->createManualCredit($this->request->data)) {
            $errors = $this->RetailerCredit->validationErrors;

            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $errors, 'data' => $this->RetailerCredit->data])),
                !empty($errors['value'])
                    ? implode('<br />', $errors['value'])
                    : 'The transaction could not be saved. Please, try again.',
                true
            );
        }

        return $this->_successResponse('The transaction has been saved.');
    }

    protected function createGet($brandId, $retailerId)
    {
        $this->set('creditTerms', $this->RetailerCreditTerm->findCreditTermOptions($brandId, $retailerId));
        $this->render('/RetailerCredits/create_retailer_credit');
    }

    public function makePayment()
    {
        if (!$this->userCanMakePayment()) {
            throw new ForbiddenException(json_encode($this->Auth->user()));
        }

        if($this->request->is('post')){
            $this->makePaymentPost();
        } else {
            $this->makePaymentGet();
        }
    }

    public function makePaymentPost()
    {
        if (!$this->userCanMakePayment()) {
            throw new ForbiddenException(json_encode(User::extractAuthUserLogFields($this->Auth->user())));
        }

        $creditPayments = $this->request->data('RetailerCreditPayment.credits');

        $creditId = null;
        foreach ($creditPayments as $creditId => $payment) {
            if (!in_array($this->Auth->user('id'), $this->RetailerCredit->getBrandAndRetailerIdForCredit($creditId))) {
                throw new ForbiddenException(json_encode(User::extractAuthUserLogFields($this->Auth->user())));
            }
            if ($this->RetailerCredit->isPaid($creditId)) {
                return $this->_exceptionResponse(null, 'Cannot create payment for fully paid credit.');
            }
        }

        if ($this->Auth->user('user_type') === User::TYPE_RETAILER) {
            $retailerId = $this->Auth->user('id');
            $paymentMethodId = $this->request->data('RetailerCreditPayment.paymentMethod');
            $succeededPayments = [];
            $failedPayments = [];
            list($stripeCustomerId, $connectedAccountId, $mandateId, $currencyCode) = $this->extractPaymentDetails($retailerId, $paymentMethodId, $creditId);
            try{
                $this->validatePaymentDetails($connectedAccountId, $paymentMethodId, $currencyCode, $creditPayments);
            } catch (BadRequestException $e) {
                return $this->_exceptionResponse(
                    $e,
                    $e->getMessage(),
                    true
                );
            } catch (Exception $e) {
                return $this->_exceptionResponse(
                    null,
                    'An error occurred attempting to make the payment.',
                    $e
                );
            }
            foreach ($creditPayments as $creditId => $payment) {
                $credit = $this->RetailerCredit->findForPayment($creditId);
                $invoiceNumber = $credit['RetailerCredit']['invoice_number'];
                $metadata = [
                    'creditId' => $creditId,
                    'retailer' => $credit['Retailer']['company_name'],
                    'brand' => $credit['User']['company_name'],
                    'creditDescription' => $credit['RetailerCredit']['description'],
                ];
                $payment['amount'] = (int)round($payment['amount'] * 100);

                try {
                    $result = $this->Stripe->createBankDebit($stripeCustomerId, $paymentMethodId, $connectedAccountId, $mandateId, $payment['amount'], $currencyCode, $invoiceNumber, $metadata);
                    $creditPayments[$creditId]['stripe_charge_id'] = $result->id;
                    $creditPayments[$creditId]['payment_status'] = RetailerCreditPaymentStatus::PENDING;
                    $creditPayments[$creditId]['type'] = RetailerCreditPaymentType::STRIPE;
                    $succeededPayments[] = ['credit' => $credit, 'payment' => $payment];
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    CakeLog::error($e);
                    // don't save the payment if the failed to push to stripe.
                    unset($creditPayments[$creditId]);
                    $failedPayments[] = ['credit' => $credit, 'payment' => $payment];
                }
            }

            // if payments failed, display to user and continue to save successful payments
            if(!empty($failedPayments)){
                $failedPaymentInvoices = implode(', ', Hash::extract($failedPayments, '{n}.credit.RetailerCredit.invoice_number'));
                $this->setFlash("An error occurred attempting to make some payments. Invoices : {$failedPaymentInvoices}", 'error');
            }

            $succeededPaymentInvoices = implode(', ', Hash::extract($succeededPayments, '{n}.credit.RetailerCredit.invoice_number'));
            $this->setFlash(__('Payment recorded for Invoices: %s', $succeededPaymentInvoices), 'success');

        } else if ($this->Auth->user('user_type') === User::TYPE_MANUFACTURER) {
            $voucherError = $this->RetailerCredit->checkForInvalidVoucherPaymentMessage($creditId, $creditPayments);
            if ($voucherError) {
                return $this->_exceptionResponse(null, $voucherError);
            }
        }

        $createdBy = (int)(User::revertAuthParent($this->Auth->user())['id']);
        // Assuming all payments are for credits belonging to the same retailer/brand combo
        list($brandId, $retailerId) = $this->RetailerCredit->getBrandAndRetailerIdForCredit($creditId);
        if (!$this->RetailerCreditPayment->createPayments($createdBy, $brandId, $retailerId, $creditPayments)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $this->RetailerCreditPayment->validationErrors, 'data' => $this->RetailerCreditPayment->data])),
                'An error occurred attempting to make the payment.',
                true
            );
        }

        // set success message, don't overwrite message build from $succeededPayments
        if($this->getFlash('success') === ''){
            $this->setFlash('Payments recorded', 'success');
        }

        return $this->_successResponse('');
    }

    public function makePaymentGet()
    {
        if (!$this->userCanMakePayment()) {
            throw new ForbiddenException(json_encode(User::extractAuthUserLogFields($this->Auth->user())));
        }
        $showPaymentMethods = false;
        $showPaymentTypeOptions = true;
        $creditIds = $this->request->query('data.RetailerCredit.makePayment');
        list($brandId, $retailerId) = $this->RetailerCredit->getBrandAndRetailerIdForCredit($creditIds);

        $credits = $this->RetailerCredit->getForPayment($creditIds);
        $credits = Hash::combine($credits, '{n}.RetailerCredit.id', '{n}');
        $currencyCode = $this->getCurrencyCodeForIndex($brandId, $retailerId);
        $this->set('credits', $this->getCreditLineObjects($credits));
        $this->set('currencyCode', $currencyCode);
        $this->set('validPaymentMethodTypes', $this->Stripe->getPaymentMethodTypesByCurrency($currencyCode));

        $paymentMethods= [];
        if($this->Auth->user('user_type') === User::TYPE_RETAILER)
        {
            $stripeConnectedAccountId = $this->StripeUser->getAccountId($brandId);
            $stripeCustomerId = $this->StripeConnectCustomer->getConnectCustomerId($brandId, $retailerId);
            $paymentMethods = $this->Stripe->listPaymentMethods($stripeConnectedAccountId, $stripeCustomerId);
            $showPaymentMethods = true;
            $showPaymentTypeOptions = false;
        }

        $paymentTypeOptions = array_combine(RetailerCreditPaymentType::BRAND_TYPES, array_map('ucwords', RetailerCreditPaymentType::BRAND_TYPES));

        $this->set('paymentTypeOptions', $paymentTypeOptions);
        $this->set('showPaymentTypeOptions', $showPaymentTypeOptions);
        $this->set('paymentMethods', $paymentMethods);
        $this->set('showPaymentMethods', $showPaymentMethods);
        $this->set('brandId', $brandId);

    }

    public function payments(int $id)
    {
        $credit = $this->RetailerCredit->getForPaymentList($id);

        if(!(bool)$credit){
            throw new NotFoundException("Credit not found for id: {$id}");
        }

        $this->set('credit', $credit);
        $this->set('currencyCode', $this->getCurrencyCodeForIndex($credit[$this->RetailerCredit->alias]['user_id'], $credit[$this->RetailerCredit->alias]['retailer_id']));

    }

    public function ajax_update()
    {
        if (!$this->userCanEditCredit()) {
            throw new ForbiddenException(json_encode($this->Auth->user()));
        }
        $retailerCredit = current($this->request->data('RetailerCredit'));
        $retailerCredit['due_date'] = format_from_datepicker($retailerCredit['due_date']);

        if (!$this->RetailerCredit->save($retailerCredit)) {
            $errors = $this->RetailerCredit->validationErrors;

            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $errors, 'data' => $this->RetailerCredit->data])),
                !empty($errors['value'])
                    ? implode('<br />', $errors['value'])
                    : 'The due date could not be updated. Please, try again.',
                true
            );
        }

        $status = CreditLine::getStatus($this->RetailerCredit, $this->RetailerCredit->getForStatus($this->RetailerCredit->id));
        $this->set('status', $status);

        return $this->_successResponse('The due date has been updated.');
    }

    /**
     * @param CakeRequest $request
     * @return array [$brandId, $retailerId]
     * @throws BadRequestException
     */
    protected function _extractUserIds(CakeRequest $request): array
    {
        $brandId = $request->param('user_id');
        $retailerId = $this->Auth->user('id');
        if ($this->Auth->user('user_type') == User::TYPE_MANUFACTURER) {
            $brandId = $this->Auth->user('id');
            $retailerId = $request->param('retailer_id');
        } else if ($this->Auth->user('user_type') == User::TYPE_SALES_REP) {
            $retailerId = $request->param('retailer_id');
        }

        if (!$this->ManufacturerRetailer->validateBrandAndRetailerIds((int)$brandId, (int)$retailerId)) {
            $users = $this->User->findAllById([$brandId, $retailerId], ['id', 'email_address', 'user_type', 'Branch'], null, null, null, -1);

            throw new ForbiddenException('URL provided an invalid user id ' . json_encode($users));
        }

        return [$brandId, $retailerId];
    }

    protected function getCreditLineObjects(array $credits)
    {
        return array_map(function($credit) {
            return CreditLine::createFromCredit($this->RetailerCredit, $credit);
        }, $credits);
        
    }

    protected function userCanMakePayment(){
        $authUser = User::revertAuthParent($this->Auth->user());
        $userType = Hash::get($authUser, 'user_type');

        if(in_array($userType, [User::TYPE_MANUFACTURER, User::TYPE_RETAILER])){
            return true;
        } else if (in_array($userType, [User::TYPE_BRAND_STAFF, User::TYPE_STAFF])){
            return $this->Permissions->userHasPermission($authUser, Permissions::NAME_RETAILER_CREDITS, Permissions::LEVEL_EDIT);
        }

        return false;
    }

    protected function userCanCreateCredit(){
        $authUser = User::revertAuthParent($this->Auth->user());
        $userType = Hash::get($authUser, 'user_type');

        if($userType === User::TYPE_MANUFACTURER){
            return true;
        } else if ($userType === User::TYPE_BRAND_STAFF){
            return $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_RETAILER_CREDITS, Permissions::LEVEL_EDIT);
        }
        return false;
    }

    protected function userCanEditCredit(){
        return $this->userCanCreateCredit();
    }

    protected function getTitleForIndex($brand_id, $retailer_id)
    {
        $title = '';
        $retailerName = false;
        $brandName = false;
        if ($this->Auth->user('user_type') == User::TYPE_MANUFACTURER) {
            $retailerName = $this->User->field('company_name', ['id' => $retailer_id]);
        } else if ($this->Auth->user('user_type') == User::TYPE_SALES_REP) {
            $retailerName = $this->User->field('company_name', ['id' => $retailer_id]);
            $brandName = $this->User->field('company_name', ['id' => $brand_id]);
        } else if ($this->Auth->user('user_type') == User::TYPE_RETAILER) {
            $brandName = $this->User->field('company_name', ['id' => $brand_id]);
        }
        
        if($brandName && $retailerName){
            return "{$brandName} - {$retailerName}";
        }
        return $brandName ?: $retailerName;
    }

    protected function getCurrencyCodeForIndex($brandId, $retailerId)
    {
        return $this->ManufacturerRetailer->getCurrencyCodeForCredits((int)$brandId, (int)$retailerId);
    }

    /**
     * 
     * @param mixed $connectedAccountId 
     * @param mixed $paymentMethodId 
     * @param mixed $currencyCode 
     * @param mixed $payments 
     * @return void
     */
    protected function validatePaymentDetails($connectedAccountId, $paymentMethodId, $currencyCode, $payments)
    {
        $paymentMethod = $this->Stripe->getPaymentMethod($connectedAccountId, $paymentMethodId);
        if(!in_array($paymentMethod->type, $this->Stripe->getPaymentMethodTypesByCurrency($currencyCode))){
            throw new BadRequestException("Payment method cannot be used for currency: {$currencyCode}");
        }

        if (!str_starts_with($paymentMethodId, 'pm_')) {
            throw new BadRequestException("Invalid payment method: {$paymentMethodId}");
        }

        foreach ($payments as $payment) {
            if($payment['amount'] < RetailerCreditPayment::MINIMUM_PAYMENT_AMOUNT){
                throw new BadRequestException('Payment amount must be greater than ' . RetailerCreditPayment::MINIMUM_PAYMENT_AMOUNT);
            }
        }

        $this->assertCreditsBelongToSingleBrand(array_keys($payments));
    }

    /**
     * 
     * @param mixed $retailerId 
     * @param mixed $paymentMethodId 
     * @param mixed $creditId 
     * @return array 
     * @throws InvalidArgumentException 
     * @throws MissingTableException 
     * @throws MissingDatasourceException 
     * @throws ApiErrorException 
     */
    protected function extractPaymentDetails($retailerId, $paymentMethodId, $creditId): array
    {
        $credit = $this->RetailerCredit->findForPayment($creditId);
        $userId = $credit['RetailerCredit']['user_id'];
        $stripeCustomerId = $this->StripeConnectCustomer->getConnectCustomerId($userId, $retailerId);
        $connectedAccountId = $this->StripeUser->getAccountId($userId);
        $currencyCode = $this->getCurrencyCodeForIndex($userId, $retailerId);
        $mandateId = $this->Stripe->getMandateForPaymentMethod($connectedAccountId, $stripeCustomerId, $paymentMethodId);
        return [$stripeCustomerId, $connectedAccountId, $mandateId, $currencyCode];
    }

    protected function assertCreditsBelongToSingleBrand(array $retailerCreditIds)
    {
        $brandCount = $this->RetailerCredit->find('count', [
            'conditions' => [
                'id' => $retailerCreditIds
            ],
            'group' => [
                'user_id'
            ],
            'fields' => [
                'user_id',
            ],
        ]);

        if($brandCount !== 1){
            throw new BadRequestException('All credits must belong to the same brand.');
        }
    }
}
