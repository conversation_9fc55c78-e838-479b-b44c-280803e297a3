<?php
App::uses('AppController', 'Controller');
App::uses('User', 'Model');
App::uses('Permissions', 'Utility');

/**
 * ProductPricing Controller.
 *
 * @property AuthComponent $Auth
 * @property PermissionsComponent $Permissions
 *
 * @property Product $Product
 */
class ProductPricingController extends AppController
{
    public $components = [
        'Auth',
        'Permissions',
    ];

    public $uses = [
        'Product',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $authUser = $this->Auth->user();

            $this->Permissions->assertUserIsType($authUser, User::TYPE_MANUFACTURER);

            $requiredLevel = $this->request->is(['post', 'put', 'delete'])
                ? Permissions::LEVEL_EDIT
                : Permissions::LEVEL_NONE;
            $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_PRODUCTS, $requiredLevel);

            $productId = $this->request->param('product_id');
            $record = $this->Product->findForAuthorization($productId);
            if (empty($record['Product']['id'])) {
                throw new NotFoundException('Product not found where id=' . json_encode($productId));
            }
            if ($authUser['id'] !== $record['Product']['user_id']) {
                throw new ForbiddenException(json_encode($record + ['Auth' => User::extractAuthUserLogFields($authUser)]));
            }
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function edit($productId = null)
    {
        if ($this->request->is(['post', 'put'])) {
            if ($this->Product->saveFromPricingEdit($productId, $this->request->data)) {
                return $this->_successResponse('Product pricing has been updated successfully');
            }

            return $this->_exceptionResponse(new BadRequestException(json_encode(['errors' => $this->Product->validationErrors, 'data' => $this->Product->data])), null, true);
        } else {
            $this->request->data = $this->Product->findForPricingEdit($productId);
        }

        $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_PRODUCTS, Permissions::LEVEL_EDIT));
    }
}
