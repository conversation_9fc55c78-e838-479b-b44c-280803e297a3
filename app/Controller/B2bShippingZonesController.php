<?php
App::uses('AppController', 'Controller');

/**
 * B2bShippingZones Controller.
 *
 * @property AuthComponent $Auth
 *
 * @property B2bShippingZone $B2bShippingZone
 * @property B2bShippingRate $B2bShippingRate
 * @property PricingTier $PricingTier
 * @property Product $Product
 * @property User $User
 */
class B2bShippingZonesController extends AppController
{
    public $components = array('Auth');

    public $uses = array('B2bShippingZone', 'B2bShippingRate', 'PricingTier', 'Product', 'User');

    private $indexUrl = ['controller' => 'users', 'action' => 'shipment_setting'];

    public function isAuthorized()
    {
        return ($this->Auth->user('user_type') === 'Manufacturer') && parent::isAuthorized();
    }

    public function index()
    {
        return $this->redirect($this->indexUrl);
    }

    public function edit($id = null)
    {
        $userId = (int)$this->Auth->user('id');
        if ($id) {
            $this->_validateShippingZone($id, $userId);
        }

        if ($this->request->is(['post', 'put'])) {
            if (empty($this->request->data['B2bShippingZone']['pricing_tier_ids'])) {
                $message = 'You must select least one pricing tier';
                return $this->_exceptionResponse(new BadRequestException($message), $message);
            }
            if (empty($this->request->data['B2bShippingRate'])) {
                $message = 'You must add at least one shipping rate';
                return $this->_exceptionResponse(new BadRequestException($message), $message);
            }
            if (!$this->B2bShippingZone->saveFromEdit($id, $userId, $this->request->data)) {
                return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->B2bShippingZone->validationErrors, 'data' => $this->B2bShippingZone->data])), null, true);
            }
            $operation = ($id) ? 'Updated' : 'Created';
            $message = "{$operation} {$this->request->data['B2bShippingZone']['name']} wholesale shipping rates";
            return $this->_successResponse($message, ['controller' => 'b2b_shipping_zones', 'action' => 'edit', 'id' => $this->B2bShippingZone->id]);
        }

        $zone = array();
        if ($id) {
            $zone = $this->B2bShippingZone->findForEdit($id);
            if (empty($zone['B2bShippingZone']['id'])) {
                throw new NotFoundException();
            }
        }
        $this->set('zone', $zone);

        $tierOptions = $this->PricingTier->getSelectOptions($userId);
        $disabledTierIds = $this->B2bShippingZone->listDisabledTierIds($userId, $id);
        if (count($tierOptions) === count($disabledTierIds)) {
            $message = 'All pricing tiers have already been assigned to a shipping rate. Please create a new pricing tier or edit an existing shipping rate instead.';
            return $this->_exceptionResponse(new BadRequestException($message), $message);
        }
        $this->set('tierOptions', $tierOptions);
        $this->set('disabledTierIds', $disabledTierIds);

        $this->set('currency_code', $this->Auth->user('currency_code'));

        $breadcrumb = Hash::get($zone, 'B2bShippingZone.name', 'New');
        $this->set('title_for_layout', 'Shipping / ' . $breadcrumb);
        $this->set('breadcrumb', $breadcrumb);
        $this->set('indexUrl', $this->indexUrl);
    }

    public function delete($id = null)
    {
        $this->autoRender = false;

        $userId = (int)$this->Auth->user('id');
        $zone = $this->_validateShippingZone($id, $userId);
        $this->request->allowMethod('post', 'delete');

        if (!$this->B2bShippingZone->delete($id)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode($zone)),
                "Unable to remove {$zone['B2bShippingZone']['name']} wholesale shipping rates. Please, try again.",
                true
            );
        }
        return $this->_successResponse("{$zone['B2bShippingZone']['name']} wholesale shipping rates have been removed", $this->indexUrl);
    }

    public function b2b_shipping_rate_form($type = null, $rateId = null)
    {
        $userId = (int)$this->Auth->user('id');
        $rate = $this->_b2b_shipping_rate($userId, $type, $rateId);

        $this->set('rateId', $rateId);
        $this->set('type', $type);
        $this->set('productCategories', $this->Product->getWholesaleProductTypesByUser($userId));
        $this->set('productTitles', $this->Product->getProductTitlesByUser($userId));
        $this->set('currency_code', $this->Auth->user('currency_code'));
        $this->set('rate', $rate);
    }

    public function ajax_b2b_shipping_rate_row($type = null, $rateId = null)
    {
        $userId = (int)$this->Auth->user('id');
        $rate = $this->_b2b_shipping_rate($userId, $type, $rateId);

        if (empty($rate['B2bShippingRate']['key'])) {
            $rate['B2bShippingRate']['key'] = generate_key([
                $rate['B2bShippingRate']['type'],
                $rate['B2bShippingRate']['name'],
            ]);
        }

        $this->set('currency_code', $this->Auth->user('currency_code'));
        $this->set('rate', $rate);

        $this->render('/Elements/B2bShippingZones/ajax_b2b_shipping_rate_row');
    }

    private function _b2b_shipping_rate($userId, $type, $rateId): array
    {
        if (!B2bShippingRate::hasType($type)) {
            throw new BadRequestException('Invalid url type: ' . json_encode($type));
        }

        if ($rateId) {
            $this->_validateShippingRate($rateId, $userId);
        }

        $rate = $this->_read_b2b_shipping_rate_form($rateId, $type, $this->request->query('data'));

        if (!empty($rateId)) {
            $existing = $this->B2bShippingRate->findForEdit($rateId);
            if (empty($existing['B2bShippingRate']['id'])) {
                throw new NotFoundException();
            }
            $rate['B2bShippingRate'] += $existing['B2bShippingRate'];
        }

        return $rate;
    }

    private function _read_b2b_shipping_rate_form($rateId, $type, $data): array
    {
        $data = (array)$data;
        foreach (['price', 'min_range', 'max_range'] as $field) {
            $value = Hash::get($data, "B2bShippingRate.{$field}");
            $data['B2bShippingRate'][$field] = is_numeric($value) ? format_number($value) : null;
        }
        if (isset($data['B2bShippingRate']['product_categories'])) {
            $data['B2bShippingRate']['product_categories'] = array_filter((array)$data['B2bShippingRate']['product_categories']);
        }
        if (isset($data['B2bShippingRate']['product_titles'])) {
            $data['B2bShippingRate']['product_titles'] = array_filter((array)$data['B2bShippingRate']['product_titles']);
        }
        return ['B2bShippingRate' => ['id' => $rateId, 'type' => $type] + (array)Hash::get($data, 'B2bShippingRate')];
    }

    private function _validateShippingZone($id, $userId): array
    {
        $zone = (array)$this->B2bShippingZone->get($id, [
            'fields' => ['id', 'user_id', 'name'],
        ]);
        if ($zone['B2bShippingZone']['user_id'] != $userId) {
            throw new ForbiddenException();
        }

        return $zone;
    }

    private function _validateShippingRate($id, $userId): array
    {
        $rate = (array)$this->B2bShippingRate->get($id, [
            'contain' => [
                'B2bShippingZone' => ['fields' => ['id', 'user_id']],
            ],
            'fields' => ['id', 'type', 'name'],
        ]);
        if ($rate['B2bShippingZone']['user_id'] != $userId) {
            throw new ForbiddenException();
        }

        return $rate;
    }

}
