<?php

App::uses('AppController', 'Controller');
use ShipEarlyApp\Lib\Utility\SupportedLanguages;

/**
 * Class MenusController
 *
 * @property Menu $Menu
 * @property MenuItem $MenuItem
 */
class MenusController extends AppController
{
    public $name = 'Menus';

    public $uses = ['Menu', 'MenuItem'];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
        } catch (ForbiddenException $e) {
            CakeLog::error($e);

            return false;
        }

        return true;
    }

    public function index()
    {
        $this->set('title_for_layout', __('Menus'));

        $userId = $this->Auth->user('id');
        $menus = $this->Menu->find('all', [
            'recursive' => -1,
            'conditions' => ['Menu.user_id' => $userId],
            'contain' => [
                'MenuItem' => [
                    'fields' => ['id', 'menu_id'],
                    'order' => ['MenuItem.menu_item_order' => 'ASC']
                ],
            ],
            'fields' => ['id', 'user_id', 'name'],
        ]);

        $menuItemIds = Hash::extract($menus, '{n}.MenuItem.{n}.id');
        if (!empty($menuItemIds)) {
            $translatedItems = $this->Menu->MenuItem->find('all', [
                'recursive' => -1,
                'conditions' => ['MenuItem.id' => $menuItemIds],
                'fields' => ['id', 'menu_id', 'label', 'url', 'menu_item_order'],
                'order' => ['MenuItem.menu_item_order' => 'ASC'],
            ]);
            $translatedItems = Hash::combine($translatedItems, '{n}.MenuItem.id', '{n}.MenuItem');
        } else {
            $translatedItems = [];
        }

        foreach ($menus as &$menu) {
            if (!empty($menu['MenuItem'])) {
                $menuItems = [];
                foreach ($menu['MenuItem'] as $item) {
                    $itemId = $item['id'];
                    if (isset($translatedItems[$itemId])) {
                        $menuItems[] = $translatedItems[$itemId];
                    }
                }
                $menu['MenuItem'] = $menuItems;
            }
        }

        $this->set(compact('menus'));
        $this->render('index');
    }

    public function add()
    {
        $this->autoRender = false;
    
        if ($this->request->is('post')) {
            $this->request->data['Menu']['user_id'] = $this->Auth->user('id');
            $this->Menu->create();
    
            if ($this->Menu->save($this->request->data)) {
                $this->response->body(json_encode(['success' => true]));
            } else {
                $this->response->body(json_encode(['success' => false, 'message' => 'Failed to create menu.']));
            }
        } else {
            $this->response->body(json_encode(['success' => false, 'message' => 'Invalid request method.']));
        }

        $this->redirect($this->referer());
    }

    public function edit($id = null)
    {
        $this->set('title_for_layout', 'Edit Menu');

        if (!$id || !$this->Menu->exists($id)) {
            throw new NotFoundException(__('Invalid menu'));
        }

        $menu = $this->Menu->record($id, [
            'conditions' => ['Menu.user_id' => $this->Auth->user('id')],
            'fields' => ['id', 'name'],
        ]);

        if (!$menu) {
            throw new ForbiddenException(__('You are not authorized to edit this menu.'));
        }

        $allMenuTranslations = $this->Menu->recordWithAllTranslations($id, [
            'fields' => ['id', 'name'],
        ]);
        foreach ($allMenuTranslations['Menu'] as $locale => $translated) {
            if (in_array($locale, SupportedLanguages::getLocalesSet(), true)) {
                $menu['Menu'][$locale] = $translated;
            }
        }

        $menuItems = $this->MenuItem->find('all', [
            'conditions' => ['MenuItem.menu_id' => $id],
            'fields' => ['id', 'label', 'url', 'menu_id', 'menu_item_order'],
            'order' => ['MenuItem.menu_item_order' => 'ASC']
        ]);

        foreach ($menuItems as $index => $menuItem) {
            $translated = $this->MenuItem->recordWithAllTranslations((int)$menuItem['MenuItem']['id'], [
                'fields' => ['id', 'label', 'url', 'menu_id', 'menu_item_order']
            ]);
            foreach ($translated['MenuItem'] as $locale => $translation) {
                if (in_array($locale, SupportedLanguages::getLocalesSet(), true)) {
                    $menuItems[$index]['MenuItem'][$locale] = $translation;
                }
            }
        }
        $menu['MenuItem'] = Hash::extract($menuItems, '{n}.MenuItem');

        if ($this->request->is(['post', 'put'])) {
            $data = $this->request->data;
            $data['Menu']['id'] = $id;
            $data['Menu']['user_id'] = $this->Auth->user('id');

            if ($this->Menu->save($data['Menu'])) {
                if (!empty($data['MenuItem'])) {
                    $orderCounter = 0;
                    if (!empty($menu['MenuItem'])) {
                        $orderCounter = max(array_column($menu['MenuItem'], 'menu_item_order'));
                    }
                    foreach ($data['MenuItem'] as $item) {
                        if (!empty($item['label']) && !empty($item['url'])) {
                            if (!preg_match('#^https?://#i', $item['url'])) {
                                $item['url'] = 'http://' . $item['url'];
                            }
                            $item['menu_id'] = $id;
                            $item['user_id'] = $data['Menu']['user_id'];

                            if (!empty($item['id'])) {
                                $this->MenuItem->id = $item['id'];
                                $this->MenuItem->save($item);
                            } else {
                                $orderCounter++;
                                $item['menu_item_order'] = $orderCounter;
                                $this->MenuItem->create();
                                $this->MenuItem->save($item);
                            }
                        }
                    }
                }

                return $this->redirect(['action' => 'edit', $id]);
            }

            $this->Flash->error(__('Could not update the menu. Please try again.'));
        } else {
            $this->request->data = $menu;
        }

        $this->set(compact('menu'));
        $this->render('edit');
    }

    public function update_menu_item_field()
    {
        $this->autoRender = false;
        $this->response->type('json');

        if (!$this->request->is('post')) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Invalid request'
            ]));
            return;
        }

        $id = $this->request->data('id');
        $field = $this->request->data('field');
        $value = $this->request->data('value');
        $locale = trim($this->request->data('selectedLocale'));
        $selectedLocale = $locale ?: SupportedLanguages::DEFAULT_LOCALE;

        if ($field === 'url' && !preg_match('#^https?://#i', $value)) {
            $value = 'http://' . $value;
        }
        if (!in_array($field, ['label', 'url']) || !$id) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Invalid data'
            ]));
            return;
        }

        if (!$this->MenuItem->exists($id)) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Item not found'
            ]));
            return;
        }
        $this->MenuItem->locale = $selectedLocale;
        $this->MenuItem->id = $id;
        if ($this->MenuItem->save([$field => $value])) {
            $this->response->body(json_encode([
                'success' => true
            ]));
            return;
        }

        $this->response->body(json_encode([
            'success' => false,
            'message' => 'Save failed'
        ]));
    }

    public function delete_menu_item($id = null)
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;
        $this->response->type('json');

        if (!$id || !$this->MenuItem->exists($id)) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Invalid menu item'
            ]));
            return;
        }

        if (!$this->MenuItem->exists(['MenuItem.id' => $id, 'MenuItem.user_id' => $this->Auth->user('id')])) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'You are not authorized to delete this item.'
            ]));
            return;
        }

        if ($this->MenuItem->delete($id)) {
            $this->response->body(json_encode([
                'success' => true
            ]));
            return;
        }

        $this->response->body(json_encode([
            'success' => false,
            'message' => 'Failed to delete the item.'
        ]));
    }

    public function reorder_menu_items()
    {
        $this->request->allowMethod('post');

        $itemOrder = $this->request->data('order');
        $menuId = $this->request->data('menu_id');

        $validItemIds = array_values(
            $this->Menu->MenuItem->find('list', [
                'conditions' => ['MenuItem.menu_id' => $menuId],
                'fields' => ['id', 'id'],
            ])
        );

        $invalidIds = array_diff($itemOrder, $validItemIds);
        if (!empty($invalidIds)) {
            return $this->set([
                'success' => false,
                'message' => __('Some menu items were not found.'),
                '_serialize' => ['success', 'message']
            ]);
        }

        $saveData = [];
        foreach ($itemOrder as $index => $itemId) {
            $saveData[] = [
                'id' => $itemId,
                'menu_item_order' => $index + 1
            ];
        }

        if (!$this->Menu->MenuItem->saveMany($saveData)) {
            return $this->set([
                'success' => false,
                'message' => __('Failed to update item order.'),
                '_serialize' => ['success', 'message']
            ]);
        }

        return $this->set([
            'success' => true,
            '_serialize' => ['success']
        ]);
    }

    public function updateName()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;
        $this->response->type('json');

        $id = $this->request->data('id');
        $name = trim($this->request->data('name'));
        $locale = trim($this->request->data('locale'));
        $selectedLocale = $locale ?: SupportedLanguages::DEFAULT_LOCALE;
        if (!$id || !$name) {
            $this->response->body(json_encode(['success' => false, 'message' => 'Invalid input']));
            return $this->response;
        }

        $menu = $this->Menu->findById($id);
        if (!$menu) {
            $this->response->body(json_encode(['success' => false, 'message' => 'Menu not found']));
            return $this->response;
        }

        if ($menu['Menu']['user_id'] !== $this->Auth->user('id')) {
            $this->response->body(json_encode(['success' => false, 'message' => 'You are not authorized to edit this menu']));
            return $this->response;
        }

        $menu['Menu']['name'] = $name;
        $this->Menu->locale = $selectedLocale;
        if ($this->Menu->save($menu)) {
            $this->response->body(json_encode(['success' => true]));
        } else {
            $this->response->body(json_encode(['success' => false, 'message' => 'Failed to update the menu name']));
        }

        return $this->response;
    }
}
