<?php
App::uses('App<PERSON>ontroller', 'Controller');

/**
 * Class ManufacturersController
 *
 * @property PhpExcelComponent $PhpExcel
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Order $Order
 * @property Product $Product
 * @property ProductRetailer $ProductRetailer
 * @property User $User
 */
class ManufacturersController extends AppController
{
    /**
     * @var array
     */
    public $components = array('PhpExcel');
    /**
     * @var array
     */
    public $uses = array('ManufacturerRetailer', 'Order', 'Product', 'ProductRetailer', 'User');

    public function beforeFilter()
    {
        parent::beforeFilter();
        if (!in_array($this->Auth->user('user_type'), [User::TYPE_RETAILER, User::TYPE_STAFF])) {
            $this->redirect('/');
        }
    }

    /**
     * List associated brands with current retailer
     *
     * Url: /manufacturers or /activeBrand
     */
    public function index()
    {
        if ($this->request->is('ajax')) {
            $type = $this->request->query['type'];
            $noRecords = $this->request->query['noRecords'];
            $page = $this->request->query['pageNumber'];
            $count = $this->request->query['count'];
            $order = trim($this->request->query['sortField'] . ' ' . $this->request->query['sortOrder']);
            $search = $this->request->query('search');
        } else {
            // Route '/activeBrand'
            $type = ($this->request->param('filter') === 'active')
                ? array(1)
                : array(0, 1, 2);
            $noRecords = 12;
            $page = 1;
            $count = null;
            $order = 'ManufacturerRetailer.created DESC';
            $search = $this->request->data('search');
        }

        $this->set('title_for_layout', __('Brands'));
        $this->set('noRecords', $noRecords);
        $this->set('type', $type);

        $retailerId = $this->User->getMainRetailerId($this->Auth->user('id'));

        $conditions = (count($type) === 1)
            ? array('ManufacturerRetailer.status' => $type)
            : array();
        if ($search) {
            $conditions[] = $this->User->buildSearchCondition($search);
        }

        if (empty($count)) {
            $count = $this->User->findBrandsForConnect($retailerId, 'count', array(
                'conditions' => $conditions,
            ));
        }
        $this->set('count', $count);

        $paging = paging($page, $noRecords, $count);
        $this->set('paging', $paging);

        $brands = $this->User->findBrandsForConnect($retailerId, 'all', array(
            'conditions' => $conditions,
            'fields' => array(
                'User.id',
                'User.company_name',
                'User.avatar',
                'User.city',
                'User.state_id',
                'State.id',
                'State.state_name',
                'ManufacturerRetailer.id',
                'ManufacturerRetailer.user_id',
                'ManufacturerRetailer.retailer_id',
                'ManufacturerRetailer.status',
                'PricingTier.currencytype',
                'RetailerCredit.user_id',
                'RetailerCredit.retailer_id',
                'RetailerCredit.total_balance',
            ),
            'order' => $order,
            'limit' => $noRecords,
            'offset' => $paging['offset'],
        ));

        $this->set('brands', $brands);
        $this->set('currency_code', $this->Auth->user('currency_code'));

        if ($this->request->is('ajax')) {
            $this->render('/Elements/Manufacturers/manufacturers', false);
        }
    }

}
