<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;
use ShipEarlyApp\Lib\Utility\UserSiteType;
use PragmaRX\Google2FA\Google2FA;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelLow;
use Endroid\QrCode\Writer\PngWriter;

App::uses('AppController', 'Controller');
App::uses('User', 'Model');
App::uses('StripeCapability', 'Stripe.Enum');
App::uses('RateOption', 'Utility');
App::uses('EmailTemplate', 'Model');

/**
 * Class UsersController
 *
 * @property UploadComponent $Upload
 * @property UserLogicComponent $UserLogic
 * @property LightspeedComponent $Lightspeed
 * @property SquarePosComponent $SquarePos
 * @property BranchLogicComponent $BranchLogic
 * @property QuickbookComponent $Quickbook
 * @property StripeComponent $Stripe
 * @property ShopifyPOSComponent $ShopifyPOS
 * @property VendPOSComponent $VendPOS
 * @property PasswordResetComponent $PasswordReset
 * @property CurrencyComponent $Currency
 *
 * @property Administrator $Administrator
 * @property B2bShippingZone $B2bShippingZone
 * @property B2bShipToAddress $B2bShipToAddress
 * @property BrandStaff $BrandStaff
 * @property User $User
 * @property EmailTemplate $EmailTemplate
 * @property UserEmailTemplate $UserEmailTemplate
 * @property Cron $Cron
 * @property UserCategories $UserCategories
 * @property State $State
 * @property Country $Country
 * @property Contact $Contact
 * @property Contactpersons $Contactpersons
 * @property Category $Category
 * @property AppModel $Review
 * @property ProductCategories $ProductCategories
 * @property OrderProduct $OrderProduct
 * @property Notification $Notification
 * @property ProductRetailer $ProductRetailer
 * @property QuickbookCompany $QuickbookCompany
 * @property RetailDisplay $RetailDisplay
 * @property StripeUser $StripeUser
 * @property StripeUserCapability $StripeUserCapability
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property ManufacturerRetailerSalesRep $ManufacturerRetailerSalesRep
 * @property UserSetting $UserSetting
 * @property Page $Page
 * @property ShippingZone $ShippingZone
 * @property UserAddress $UserAddress
 * @property UserCurrency $UserCurrency
 * @property UserShippingCarrier $UserShippingCarrier
 * @property UserShippingCarrierRate $UserShippingCarrierRate
 * @property UserShippingPackage $UserShippingPackage
 * @property UserSubdomain $UserSubdomain
 * @property UserTax $UserTax
 */
class UsersController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Users';
    /**
     * @var string
     */
    public $id = '';

    public $components = [
        'Upload',
        'UserLogic',
        'Lightspeed',
        'SquarePos',
        'BranchLogic',
        'Quickbook.Quickbook',
        'Stripe.Stripe',
        'Shopifypos.ShopifyPOS',
        'Vendpos.VendPOS',
        'PasswordReset',
        'Currency',
    ];

    /**
     * @var array
     */
    public $uses = array('Administrator', 'B2bShippingZone', 'B2bShipToAddress', 'BrandStaff', 'User', 'EmailTemplate', 'UserEmailTemplate', 'Cron', 'UserCategories', 'State', 'Country', 'Contact', 'Contactpersons', 'Category', 'Review', 'ProductCategories', 'OrderProduct', 'Notification', 'ProductRetailer', 'Quickbook.QuickbookCompany', 'RetailDisplay', 'StripeUser', 'StripeUserCapability', 'ManufacturerRetailer', 'ManufacturerRetailerSalesRep', 'UserSetting', 'Page', 'ShippingZone', 'UserAddress', 'UserCurrency', 'UserShippingCarrier', 'UserShippingCarrierRate', 'UserShippingPackage', 'UserSubdomain', 'UserTax');
    /**
     * @var array
     */
    public $helpers = array('StarRating');

    /**
     *
     */
    public function beforeFilter()
    {
        parent::beforeFilter();

        $publicActions = [
            'admin_login',
            'admin_logout',
            'admin_check2faRequirement',
            'loginAs',
            'forgot_password',
            'signup',
            'login',
            'logout',
            'retailer_search',
            'get_states',
            'get_timezone_options',
            'activate',
            'getstates',
            'subscription',
            'stripepaymentdetails',
            'resendLink',
            'reset_password',
            'shopifypos',
            'getstatesbycountry',
        ];

        // Allow OAuth to be processed by a subdomain that may not have an authenticated user
        $isReturningFromOAuthWithCsrfToken = !empty($this->request->query['code']) && !empty($this->request->query['state']);
        if ($isReturningFromOAuthWithCsrfToken) {
            $publicActions = array_merge($publicActions, [
                'lightSpeedConnect',
                'squareConnect',
                'stripeconnect',
                'vendPosConnect',
            ]);
        }

        $this->Auth->allow($publicActions);
    }

    /**
     * Shipearly super admin login page
     */
    function admin_login()
    {
        $this->layout = '';
        $this->set('title_for_layout', 'Login');
        $id = $this->Auth->user('id');
        if (empty($id)) {
            if ($this->request->is('post')) {
                $user = $this->Auth->identify($this->request, $this->response);
                if (!empty($user['2fa_secret'])) {
                    if (!$this->isWithinBypassDuration($user)) {
                        $code = $this->request->data['Administrator']['2fa_code'];
                        if (!$this->verifyCode($user['2fa_secret'], $code)) {
                            $this->setFlash("Invalid secret code. Please try again!", 'error');
                            return $this->redirect($this->referer());
                        }
                    }
                }
                if ($this->Auth->login($user)) {
                    $this->Administrator->save([
                        'id' => $user['id'],
                        'last_login' => $this->date(),
                    ]);
                    $this->Session->write('Auth.User.user_type', 'admin');
                    $this->redirect($this->Auth->redirectUrl());
                } else {
                    $this->setFlash("Invalid username or password. Please try again!", 'error');
                }
            }
        } else {
            $this->redirect($this->Auth->redirectUrl());
        }
    }

    /**
     * Shipearly super admin 2FA verification setup
     */

    public function admin_check2faRequirement()
    {
        $this->layout = '';
        $this->request->allowMethod('post', 'put');
        $user = $this->Auth->identify($this->request, $this->response);
        if (!$user) {
            $this->_ajaxErrorResponse(['error' => 'Authentication failed']);
            return;
        }
        $requires2FA = !empty($user['2fa_secret']);
        if ($requires2FA) {
            if ($this->isWithinBypassDuration($user)) {
                $requires2FA = false;
            }
        }
        $this->_ajaxSuccessResponse(['requires_2fa' => $requires2FA]);
    }

    protected function isWithinBypassDuration($user)
    {
        $defaultDuration = 3600;
        $bypassDurationInSeconds = isset($user['2fa_bypass_duration']) ? (int)$user['2fa_bypass_duration'] : $defaultDuration;
        $lastLoginTimestamp = strtotime($user['last_login']);
        $currentTimestamp = $this->time();

        return $lastLoginTimestamp && ($currentTimestamp - $lastLoginTimestamp < $bypassDurationInSeconds);
    }

    public function admin_2fa()
    {
        $userId = $this->Auth->user('id');
        $user = $this->Administrator->get($userId, ['fields' => ['id', 'username', '2fa_secret']]);
        $qrCodeUri = null;
        $secret = null;

        if ($user['Administrator']['2fa_secret'] === null) {

             // If no secret is set, generate one
            $google2fa = new Google2FA();
            $secret = $google2fa->generateSecretKey();
            $qrCodeContent = $google2fa->getQRCodeUrl('ShipEarly', $user['Administrator']['username'], $secret);

            // Generate QR code
            $result = Builder::create()
                ->writer(new PngWriter())
                ->data($qrCodeContent)
                ->errorCorrectionLevel(new ErrorCorrectionLevelLow())
                ->size(200)
                ->margin(10)
                ->build();

            $qrCodeUri = $result->getDataUri();

        }

        $this->set('qrCode', $qrCodeUri);
        $this->set('secret', $secret);
    }

    protected function verifyCode($secret, $code)
    {
        $google2fa = new Google2FA();
        return $google2fa->verifyKey($secret, $code);
    }

    public function admin_verify2fa()
    {
        if ($this->request->is('ajax') && $this->request->is('post')) {
            $userId = $this->Auth->user('id');
            $user = $this->Administrator->find('first', [
                'recursive' => -1,
                'conditions' => ['Administrator.id' => $userId],
                'fields' => ['id', '2fa_secret'],
            ]);
            $code = $this->request->data('code');
            $secret = $this->request->data('secret');

            if (empty($user) || empty($secret)) {
                $this->response->body(json_encode(['success' => false, 'message' => 'Administrator or 2FA secret not found!']));
                $this->response->type('json');
                return $this->response;
            }

            if (!$this->verifyCode($secret, $code)) {
                $this->response->body(json_encode(['success' => false, 'message' => 'Invalid 2FA code. Please try again!']));
                return $this->response;
            }

            if ($this->Administrator->save(['id' => $userId,'2fa_secret' => $secret])) {
                $this->response->body(json_encode(['success' => true, 'message' => '2FA code verified successfully!']));
            } else {
                $this->response->body(json_encode(['success' => false, 'message' => 'Failed to verify 2FA. Please try again!']));
            }

            $this->response->type('json');
            return $this->response;
        }
    }

    public function admin_revoke2fa()
    {
        if ($this->request->is('ajax') && $this->request->is('post')) {
            $userId = $this->Auth->user('id');
            $user = $this->Administrator->find('first', [
                'recursive' => -1,
                'conditions' => ['Administrator.id' => $userId],
                'fields' => ['id', '2fa_secret'],
            ]);
            if (empty($user) || empty($user['Administrator']['2fa_secret'])) {
                $this->response->body(json_encode(['success' => false, 'message' => 'Administrator or 2FA secret not found!']));
                $this->response->type('json');
                return $this->response;
            }

            if ($this->Administrator->save(['id' => $userId, '2fa_secret' => null])) {
                $this->response->body(json_encode(['success' => true, 'message' => '2FA revoked successfully!']));
            } else {
                $this->response->body(json_encode(['success' => false, 'message' => 'Failed to revoke 2FA. Please try again!']));
            }

            $this->response->type('json');
            return $this->response;
        }
    }

    public function admin_update2faDuration()
    {
        $this->layout = 'admin';
        if ($this->request->is('post')) {
            $newByPassDuration = null;
            if ($this->request->data['2fa_bypass_duration'] !== null) {
                $bypassDurationInMinutes = (int)$this->request->data['2fa_bypass_duration'];
                $newByPassDuration = $bypassDurationInMinutes * 60;
            }
            $userId = $this->Auth->user('id');

            $data = [
                'id' => $userId,
                '2fa_bypass_duration' => $newByPassDuration,
            ];

            $success = $this->Administrator->save($data);
            $response = ['success' => (bool)$success];

            if ($this->request->is('ajax')) {
                $this->autoRender = false;
                $this->response->type('json');
                $this->response->body(json_encode($response));
            } else {
                $this->set('response', $response);
            }
        }
    }

    /**
     * Shipearly super admin logout
     */
    public function admin_logout()
    {
        $this->autoRender = false;
        $this->redirect($this->Auth->logout());
    }

    public function admin_login_as($id)
    {
        if (!$this->User->exists(['User.id' => $id])) {
            throw new NotFoundException('User not found for id: ' . json_encode($id));
        }
    }

    public function loginAs($userId)
    {
        $this->autoRender = false;

        if (!$this->User->exists(['User.id' => $userId])) {
            throw new NotFoundException('User not found for id: ' . json_encode($userId));
        }

        $this->Auth->authenticate = $this->adminAuthenticationSettings();

        // Recreate authentication objects with the new config; normally only done once during AuthComponent::startup().
        $this->Auth->constructAuthenticate();

        if (!$this->Auth->identify($this->request, $this->response)) {
            throw new UnauthorizedException();
        }

        $this->Auth->logout();
        $this->syncUserSession($userId);

        if (!$this->_checkUserLoginStatus()) {
            return $this->redirect($this->Auth->logout());
        }

        if ($this->Auth->user('user_type') === User::TYPE_RETAILER && !$this->Auth->user('Branch') && $this->Auth->user('setup_status')) {
            $this->UserLogic->promptToConnectBankIfNotConnected($this->Auth->user('id'));
        }

        $redirectUrl = $this->Auth->redirectUrl();
        if ($this->request->is('AJAX')) {
            $this->response->body(json_encode(['redirectUrl' => $redirectUrl]));

            return $this->response;
        }

        return $this->redirect($redirectUrl);
    }

    /**
     * Shipearly user login page
     * common for all user types
     */
    public function login()
    {
        $this->layout = 'login';

        if ($this->request->param('filter') === 'Activated') {
            $this->setFlash("Your account has been activated successfully.", 'success');
        }

        if ($this->Auth->user()) {
            $this->redirect($this->Auth->redirectUrl());
        }

        $rememberMePrefill = $this->Cookie->read('remember_me_prefill');
        $rememberMeChecked = (isset($rememberMePrefill['email_address']) && isset($rememberMePrefill['password']));
        $this->set('rememberMeChecked', $rememberMeChecked);
        $this->set('userEmail', ($rememberMeChecked) ? $rememberMePrefill['email_address'] : '');
        $this->set('userPassword', ($rememberMeChecked) ? $rememberMePrefill['password'] : '');

        if ($this->request->is('post')) {
            if ($this->Auth->login()) {
                if ($this->request->data['User']['remember_me'] == 1 && $this->Auth->user('status') == 'Active') {
                    unset($this->request->data['User']['remember_me']);
                    $this->Cookie->write('remember_me', $this->request->data['User'], true, '30 Days');
                }

                if (!$this->_checkUserLoginStatus()) {
                    $this->redirect($this->Auth->logout());
                }

                if ($this->Auth->user('user_type') === User::TYPE_RETAILER && !$this->Auth->user('Branch') && $this->Auth->user('setup_status')) {
                    $this->UserLogic->promptToConnectBankIfNotConnected($this->Auth->user('id'));
                }
                $this->User->afterLogin($this->Auth->user('id'));
                $this->redirect($this->Auth->redirectUrl());
            } else {
                $this->setFlash("Invalid username or password. Please try again!", 'error');
            }
        }
    }

    /**
     * Shipearly user logout
     */
    public function logout()
    {
        $this->autoRender = false;

        $rememberMe = $this->Cookie->read('remember_me');
        $this->Cookie->delete('remember_me');

        if ($rememberMe) {
            $this->Cookie->write('remember_me_prefill', $rememberMe, true, '30 Days');
        } else {
            $this->Cookie->delete('remember_me_prefill');
        }

        $this->redirect($this->Auth->logout());
    }

    /**
     * Forget password page for shipearly user
     * common for all user types
     */
    public function forgot_password(): ?CakeResponse
    {
        if ($this->Auth->user()) {
            return $this->redirect($this->Auth->redirectUrl());
        }

        if ($this->request->is('post')) {
            $emailAddress = (string)$this->request->data['User']['email'];

            $record = $this->User->findByEmailAddress($emailAddress, ['id', 'company_name', 'status', 'language_code'], null, -1);
            if (empty($record['User']['id'])) {
                return $this->_exceptionResponse(
                    new NotFoundException('User not found for email: ' . json_encode($emailAddress)),
                    __('Email address doesn\'t match the records. Please enter the Email address you gave us at the time of registration!')
                );
            }

            $id = (int)$record['User']['id'];
            $companyName = (string)$record['User']['company_name'];
            $languageCode = (string)$record['User']['language_code'];
            $status = (string)$record['User']['status'];

            if ($languageCode) {
                Configure::write('Config.language', $languageCode);
            }

            if (!$this->_checkUserLoginStatus($status, $id)) {
                // `_checkUserLoginStatus()` also sets the flash message
                return $this->redirect($this->referer());
            }

            $token = $this->PasswordReset->createTokenState(['id' => $id]);
            $resetUrl = Router::url(['controller' => 'users', 'action' => 'reset_password', 'token' => $token], true);
            $resetUrlPlaceholder = __('Reset Password');
            $buttonHtml = <<<HTML
<div style="text-align: center;">
    <a href="{$resetUrl}" style="display: inline-block; background-color: #428bca; color: #fff; text-decoration: none; padding: 7px 10px; border-radius: 10px;">{$resetUrlPlaceholder}</a>
</div>
HTML;

            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Forgot Password', null, $languageCode);
            $variables = [
                '{user_name}' => $companyName,
                '{resetUrl}' => $buttonHtml,
                '{site_name}' => SITE_NAME,
            ];
            if (!$this->NotificationLogic->sendEmail($emailTemplateArr, $variables, $emailAddress)) {
                // Details logged by `sendEmail()` so just inform the user of an error
                return $this->_exceptionResponse();
            }

            return $this->_successResponse(
                __('Your account details have been mailed to the email address provided.'),
                $this->Auth->loginAction
            );
        }

        $this->layout = 'login';
        $this->set('title_for_layout', __('Forgot Password'));

        return null;
    }

    /**
     * Determines if the user status is valid for login and sets an appropriate flash message if not.
     *
     * @param string|null $userStatus defaults to Auth::user('status')
     * @param int|null $userId defaults to Auth::user('id')
     * @return bool True if the user status is valid for login, false otherwise.
     */
    protected function _checkUserLoginStatus($userStatus = null, $userId = null)
    {
        if ($userStatus === null) {
            $userStatus = $this->Auth->user('status');
        }
        if ($userId === null) {
            $userId = $this->Auth->user('id');
        }
        $resendLinkUrl = BASE_PATH . 'resendLink/' . $userId;
        $supportEmail = SUPPORT_EMAIL;
        $errorMessages = array(
            'Approve' => "You have not activated your account. Please check your email for an activation link. Click <a href=\"{$resendLinkUrl}\">here</a> to resend the email.",
            'Inactive' => "Your account has been deactivated. If you feel this was an error, please contact the site administrator.",
            'Register' => "Your account is currently under review. Please wait for our approval before accessing your account.",
            'Reject' => "Your account has been disabled. If you would like to resume access, please contact <a href=\"mailto:{$supportEmail}\">{$supportEmail}</a>.",
            'Suspend' => "Your account has been suspended because of a subscription failure. If you feel this was an error, please contact the site administrator.",
        );
        if (!isset($errorMessages[$userStatus])) {
            return true;
        }

        $this->setFlash($errorMessages[$userStatus], 'error');

        // Suspended users need to login to reactivate their subscription
        if ($userStatus == 'Suspend') {
            return true;
        }
        return false;
    }

    /**
     * Registration page for shipearly user
     * common for all user types
     *
     * @param string $user_type
     */
    public function signup($user_type = null)
    {
        if (!in_array($user_type, [User::TYPE_RETAILER, User::TYPE_MANUFACTURER, User::TYPE_STAFF])) {
            throw new NotFoundException('No signup page for user type ' . json_encode($user_type));
        }

        if (
            $this->request->is('post') &&
            !empty($this->request->data['User']['password']) &&
            !empty($this->request->data['User']['confirm_password']) &&
            $this->request->data['User']['password'] === $this->request->data['User']['confirm_password']
        ) {
            $this->request->data['User']['password'] = User::passwordHasher()->hash($this->request->data['User']['password']);
            unset($this->request->data['User']['confirm_password']);

            $this->request->data['User']['user_type'] = $user_type;
            $this->request->data['User']['address'] = $this->User->getCombinedAddressField($this->request->data['User']['address1'], $this->request->data['User']['address2']);

            if (!empty($this->request->data['User']['zipcode'])) {
                /** update latitude &  longitude **/
                $geopoints = $this->_getLnt(trim($this->request->data['User']['address']), trim($this->request->data['User']['city']), trim($this->request->data['User']['zipcode']), $this->request->data['User']['state_id'], $this->request->data['User']['country_id']);
                $this->request->data['User']['latitude'] = $geopoints['lat'];
                $this->request->data['User']['longitude'] = $geopoints['lng'];
                $this->request->data['User']['in-store_radius'] = IN_STORE_RADIUS;
                $this->request->data['User']['ship_from_store_radius'] = SHIP_FROM_STORE_RADIUS;
                $this->request->data['User']['brand_revenue_model'] = SHIPEARLY_BRAND_DIRECT_PERCENTAGE;
                /** update latitude &  longitude **/
            }

            // Automatically Assign In Store to Retailers
            if ($this->request->data['User']['user_type'] === User::TYPE_RETAILER) {
                $this->request->data['User']['instore'] = 1;
            } elseif ($this->request->data['User']['user_type'] === User::TYPE_STAFF) {
                $this->request->data['User']['company_name'] = trim("{$this->request->data['User']['first_name']} {$this->request->data['User']['last_name']}");
                $this->request->data['User']['staff_role'] = Staff::ROLE_STORE_ASSOCIATE;
            }

            // Automatically Enabling sell direct option to Woocommerce Brand
            if ($this->request->data('User.site_type') === UserSiteType::WOOCOMMERCE) {
                $this->request->data['User']['admin_sell_direct'] = 1;
                $this->request->data['User']['sell_direct'] = 1;
            }

            if (!empty($this->request->data['User']['inventory_type']) && $this->request->data['User']['inventory_type'] == 'none') {
                $this->request->data['User']['inventory_type'] = 'other';
                $this->request->data['User']['otherInventory'] = 'None';
            }

            if ($this->User->addUser($this->request->data)) {
                $data = $this->User->record($this->User->id);

                // Contact table
                if (isset($this->request->data['User']['telephone']) && !empty($this->request->data['User']['telephone'])) {
                    $this->Contact->addContact($data['User']['id'], 'company', $data['User']['id'], 'telephone', $this->request->data['User']['telephone']);
                }

                $userSettingInfo = array();
                $userSettingInfo['UserSetting']['user_id'] = $data['User']['id'];
                $this->UserSetting->saveUserSetting($userSettingInfo);

                // Create contact person array
                $person = array(
                    'user_id'   => $data['User']['id'],
                    'firstname' => $this->request->data['User']['first_name'],
                    'lastname'  => $this->request->data['User']['last_name'],
                    'email'     => $this->request->data['User']['email_address']
                );

                // Create default contact person
                if ($this->Contactpersons->addContactPerson($person)) {
                    $typeUid = $this->Contactpersons->getLastInsertId();
                    $this->Contact->addContact($data['User']['id'], 'person', $typeUid, 'telephone', $this->request->data['User']['telephone']);
                }

                if ($user_type === User::TYPE_RETAILER) {
                    $brandId = (int)($this->UserSubdomain->findWhitelabelSettings()['user_id'] ?? 0);
                    $brand = ($brandId) ? $this->User->record($brandId, [
                        'conditions' => [
                            'User.user_type' => User::TYPE_MANUFACTURER,
                            'User.status' => 'Active',
                            'User.setup_status' => true,
                        ],
                        'fields' => ['id', 'email_address', 'company_name', 'avatar'],
                    ]) : [];
                    if (!empty($brand['User']['id'])) {
                        $response = $this->_connectToBrand($data, $brand);
                        if (!empty($response['success'])) {
                            $categoryIds = $this->UserCategories->getUserCategories($brand['User']['id']);
                            $this->UserLogic->UpdateUserCat([], $categoryIds, $data['User']['id'], $data['User']['user_type'], $data['User']['Branch'], false);
                        }
                    }
                }                
                
                $this->UserLogic->newRetailerNotification($data);
                
                if (!empty($brand)){
                    $this->UserLogic->_sendRegistrationMail($data, $brand);                    
                } else {
                    $this->UserLogic->_sendRegistrationMail($data);   
                }

                $this->setFlash("Your account has been created. Please monitor your email (including junk mail) for approval from Shipearly Admin", 'success');
                $this->redirect(BASE_PATH . "login");
            } else {
                $this->User->set('password', '*****');
                CakeLog::warning(__METHOD__ . ', line ' . __LINE__ . ' - ' . 'Invalid user sign up attempt ' . json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data]));
            }
        }

        $countryId = $this->request->data['User']['country_id'] ?? $this->Country->field('id', ['country_code' => 'US']);

        $this->set('title_for_layout', __('Signup'));
        $this->set('title_for_submit', __('Register'));
        $this->getCountryState($countryId);
        $this->getLanguages();
    }

    public function retailer_search() {
        $this->autoRender = false;

        $keyFilter = array_flip(['zipcode']);
        $this->request->query = array_intersect_key($this->request->query, $keyFilter);

        if (!empty($this->request->query)) {
            $conditions = $this->request->query;
            $conditions['user_type'] = 'Retailer';
            $conditions['status'] = 'Active';
            $conditions['setup_status'] = 1;

            $retailers = $this->User->find('all', array(
                'conditions' => $conditions
            ));
            if (!empty($retailers)) {
                return array_reduce($retailers, function($carry, $item) {
                    return $carry . "\n<option value=\"{$item['User']['id']}\">{$item['User']['company_name']}</option>";
                }, '<option value="">Select a Retailer</option>');
            }
        }
        return '<option value="">No Retailers Found</option>';
    }

    /**
     * Connect your shipearly application with stripe account
     * Create new stripe account if you not have any stripe account
     * Else use the old account
     */
    public function stripeconnect()
    {
        $userId = (int)$this->Auth->user('id');
        $stripeUser = $this->StripeUser->getStripeUserFields($userId, ['id', 'stripe_user_id', 'is_activated']);

        if ($this->request->is('post')) {
            if (!empty($stripeUser['StripeUser']['stripe_user_id']) && !$stripeUser['StripeUser']['is_activated']) {
                return $this->redirect($this->Stripe->getActivationUrl($stripeUser['StripeUser']['stripe_user_id']));
            }

            $user = $this->User->get($userId, [
                'fields' => [
                    'id',
                    'email_address',
                    'country_id',
                    'company_name',
                    'address1',
                    'address2',
                    'city',
                    'state_id',
                    'zipcode',
                ],
            ]);

            $state = $this->Stripe->createOAuthState([
                'user_id' => $userId,
                'return_url' => array_filter(Router::parse($this->referer($this->request->here(), true))),
            ]);

            $stripe_user = [
                'email' => $user['User']['email_address'],
                'country' => $this->Country->getCountryCode($user['User']['country_id']),
                'phone_number' => $this->Contact->getCompanyTelephone($userId),
                'business_name' => $user['User']['company_name'],
                'street_address' => implode(', ', array_filter([$user['User']['address1'], $user['User']['address2']])),
                'city' => $user['User']['city'],
                'state' => $this->State->getStateName($user['User']['state_id'], $user['User']['country_id']),
                'zip' => $user['User']['zipcode'],
            ];

            return $this->redirect($this->Stripe->getAuthorizeUrl($state, $stripe_user));
        }

        if (!empty($this->request->query['code']) && !empty($this->request->query['state'])) {
            $oauthState = $this->Stripe->retrieveOAuthState((string)$this->request->query['state']);
            $userId = isset($oauthState['user_id']) ? (int)$oauthState['user_id'] : null;
            $return_url = isset($oauthState['return_url']) ? (array)$oauthState['return_url'] : null;

            if (!$return_url) {
                return $this->_exceptionResponse(
                    new UnauthorizedException(json_encode(['message' => 'OAuth state token expired', 'query' => $this->request->query])),
                    __('Stripe authorization expired, please try again.'),
                    true,
                    $this->request->here
                );
            }

            // Redirect to the correct subdomain for session consistency before processing the OAuth response
            if ((string)$this->request->param('subdomain') !== (string)($return_url['subdomain'] ?? '')) {
                return $this->redirect(Router::reverse(array_merge($this->request->params, [
                    'subdomain' => (string)($return_url['subdomain'] ?? ''),
                    'url' => $this->request->query,
                ]), true));
            }

            // Override referrer for default response redirects
            $this->request->query['referer'] = Router::url($return_url, true);

            if (!$this->User->existsById($userId)) {
                return $this->_exceptionResponse(new NotFoundException(sprintf('User not found where id=%s', json_encode($userId))), null, true);
            }

            try {
                $response = $this->Stripe->getAccessToken((string)$this->request->query['code']);
            } catch (\Stripe\Exception\OAuth\OAuthErrorException $e) {
                return $this->_exceptionResponse(null, $e->getMessage(), $e);
            }

            try {
                $accountId = $this->Stripe->getAccessTokenAccountId($response);
                $stripeAccount = $this->Stripe->getAccount($accountId);
                $this->StripeUser->connectAccount($userId, $response, $stripeAccount);
            } catch (Exception $e) {
                return $this->_exceptionResponse(($e instanceof HttpException) ? $e : null, null, $e);
            }

            return $this->_successResponse(__('Your Stripe account has been connected successfully.'));
        }

        $this->set('stripe_is_activated', !empty($stripeUser['StripeUser']['stripe_user_id']) && $stripeUser['StripeUser']['is_activated']);
    }

    public function admin_stripe_disconnect($userId = null)
    {
        $this->autoRender = false;
        $this->request->allowMethod(['POST', 'DELETE']);

        $conditions = ['StripeUser.user_id' => $userId];

        if (!$this->StripeUser->exists($conditions)) {
            $this->setFlash('This user is not connected to a Stripe account', 'error');
            $this->redirect($this->referer(ADMIN_PATH, true));
        }

        if (!$this->StripeUser->disconnectAll($conditions)) {
            $message = 'Failed to disconnect stripe account';
            $this->setFlash($message, 'error');
            CakeLog::error($message . ' ' . json_encode($this->StripeUser->findAllByUserId($userId)));
            $this->redirect($this->referer(ADMIN_PATH, true));
        }

        $this->setFlash('Stripe account disconnected', 'success');
        $this->redirect($this->referer(ADMIN_PATH, true));
    }

    /**
     * User Profile page
     * To update basic shipearly user details
     */
    public function profile()
    {
        $parentUser = $this->Auth->user();
        $authUser = User::revertAuthParent($this->Auth->user());
        $id = $authUser['id'];
        $this->User->id = $id;

        $subdomain = $this->UserSubdomain->findByUserId($id);


        if ($this->request->is(['post', 'put'])) {
            $this->User->set('email_address', $this->request->data['User']['email_address']);
            if ($this->User->validates()) {
                $this->request->data['User'] = compact('id') + $this->request->data['User'];
                $this->request->data = $this->UserLogic->uploadAvatar($this->request->data);

                if ($this->request->data('User.address1')) {
                    $this->request->data['User']['address'] = $this->User->getCombinedAddressField($this->request->data['User']['address1'], $this->request->data['User']['address2']);
                }
                if ($this->request->data('User.zipcode')) {
                    $this->request->data = $this->UserLogic->updateGeoLocation($this->request->data);
                }

                foreach ($this->request->data['User']['contact_medium'] as $key => $value) {
                    if ($key === 'website' && $value && !preg_match('#^https?://#', $value)) {
                        $value = 'https://' . $value;
                    }
                    $this->Contact->addContact($this->request->data['User']['id'], 'company', $this->request->data['User']['id'], $key, $value);
                }

                if ($this->Auth->user('user_type') === User::TYPE_MANUFACTURER && isset($this->request->data['UserSubdomain'])) {
                    $this->request->data['UserSubdomain']['user_id'] = $id;

                    $uuid = (string)$this->Auth->user('uuid');

                    if (isset($this->request->data['UserSubdomain']['header_logo_dark_bg_file'])) {
                        $old_header_logo_dark_bg_url = $subdomain['UserSubdomain']['header_logo_dark_bg_url'] ?? '';
                        if (empty($this->request->data['UserSubdomain']['header_logo_dark_bg_name'])) {
                            $this->Upload->deleteFromWebroot($old_header_logo_dark_bg_url);
                            $this->request->data['UserSubdomain']['header_logo_dark_bg_url'] = '';
                        } elseif ($this->request->data('UserSubdomain.header_logo_dark_bg_file.error') !== UPLOAD_ERR_NO_FILE) {
                            try {
                                $this->request->data['UserSubdomain']['header_logo_dark_bg_url'] = $this->Upload->replaceFileInUserHash(
                                    $old_header_logo_dark_bg_url,
                                    (array)$this->request->data['UserSubdomain']['header_logo_dark_bg_file'],
                                    $uuid,
                                    'whitelabel' . DS . 'header-logo',
                                    $this->request->data['UserSubdomain']['header_logo_dark_bg_name']
                                );
                            } catch (Exception $e) {
                                CakeLog::error($e);
                                $this->setFlash('There was an error uploading the header logo. Please, try again.', 'error');
                            }
                        }
                    }

                    if (isset($this->request->data['UserSubdomain']['favicon_file'])) {
                        $old_favicon_url = $subdomain['UserSubdomain']['favicon_url'] ?? '';
                        if (empty($this->request->data['UserSubdomain']['favicon_name'])) {
                            $this->Upload->deleteFromWebroot($old_favicon_url);
                            $this->request->data['UserSubdomain']['favicon_url'] = '';
                        } elseif ($this->request->data('UserSubdomain.favicon_file.error') !== UPLOAD_ERR_NO_FILE) {
                            try {
                                $this->request->data['UserSubdomain']['favicon_url'] = $this->Upload->replaceFileInUserHash(
                                    $old_favicon_url,
                                    (array)$this->request->data['UserSubdomain']['favicon_file'],
                                    $uuid,
                                    'whitelabel' . DS . 'favicon',
                                    $this->request->data['UserSubdomain']['favicon_name']
                                );
                            } catch (Exception $e) {
                                CakeLog::error($e);
                                $this->setFlash('There was an error uploading the favicon. Please, try again.', 'error');
                            }
                        }
                    }

                    $this->request->data['UserSubdomain']['accent_color'] = strtolower((string)$this->request->data('UserSubdomain.accent_color'));

                    if (isset($this->request->data['UserSubdomain']['login_bg_image_file'])) {
                        $old_login_bg_image_url = $subdomain['UserSubdomain']['login_bg_image_url'] ?? '';
                        if (empty($this->request->data['UserSubdomain']['login_bg_image_name'])) {
                            $this->Upload->deleteFromWebroot($old_login_bg_image_url);
                            $this->request->data['UserSubdomain']['login_bg_image_url'] = '';
                        } elseif ($this->request->data('UserSubdomain.login_bg_image_file.error') !== UPLOAD_ERR_NO_FILE) {
                            try {
                                $this->request->data['UserSubdomain']['login_bg_image_url'] = $this->Upload->replaceFileInUserHash(
                                    $old_login_bg_image_url,
                                    (array)$this->request->data['UserSubdomain']['login_bg_image_file'],
                                    $uuid,
                                    'whitelabel' . DS . 'login-bg-image',
                                    $this->request->data['UserSubdomain']['login_bg_image_name']
                                );
                            } catch (Exception $e) {
                                CakeLog::error($e);
                                $this->setFlash('There was an error uploading the login bg image. Please, try again.', 'error');
                            }
                        }
                    }
                }
                if ($authUser['user_type'] === User::TYPE_STAFF) {
                    $this->request->data['User']['company_name'] = trim("{$this->request->data['Contactpersons']['firstname']} {$this->request->data['Contactpersons']['lastname']}");
                }

                $this->User->bindModel(['hasOne' => ['UserSubdomain']], false);

                $save = [
                    'User' => $this->request->data['User'],
                    'UserSubdomain' => $this->request->data('UserSubdomain'),
                ];
                $success = $this->User->saveAssociated($save);

                $this->User->unbindModel(['hasOne' => ['UserSubdomain']], false);

                if ($success) {
                    if ($authUser['user_type'] === User::TYPE_RETAILER) {
                        $this->User->updateStoreTiming($this->request->data['User']['id'], $this->request->data['storetiming']);
                    }
                    if (isset($this->request->data['Contactpersons'])) {
                        $existingPerson = $this->Contactpersons->getContactPerson($this->request->data['User']['id']);
                        if (!empty($existingPerson[0]['Contactpersons'])) {
                            $this->request->data['Contactpersons'] = array_merge($existingPerson[0]['Contactpersons'], $this->request->data['Contactpersons']);
                            $this->Contactpersons->addContactPerson($this->request->data['Contactpersons']);
                        }
                    }
                    return $this->_successResponse(__('Your account has been updated successfully'));
                }
            } else {
                $this->setFlash('Email validation failed', 'error');
            }
        } else {
            $this->request->data['User'] = $parentUser;

            if (isset($subdomain['UserSubdomain'])) {
                $subdomain['UserSubdomain']['header_logo_dark_bg_name'] = basename($subdomain['UserSubdomain']['header_logo_dark_bg_url']);
                $subdomain['UserSubdomain']['favicon_name'] = basename($subdomain['UserSubdomain']['favicon_url']);
                $subdomain['UserSubdomain']['login_bg_image_name'] = basename($subdomain['UserSubdomain']['login_bg_image_url']);

                $this->request->data['UserSubdomain'] = $subdomain['UserSubdomain'];
            }
        }

        $this->set('title_for_layout', 'User profile');

        if ($authUser['user_type'] === User::TYPE_RETAILER) {
            $timing = User::decodeStoreTiming($this->Auth->user('store_timing'));
            if (is_array($timing)) {
                $this->request->data = ['storetiming' => $timing];
                $this->set('store', $timing);
            }
        } elseif (in_array($authUser['user_type'], [User::TYPE_STAFF, User::TYPE_SALES_REP])) {
            $person = $this->Contactpersons->getContactPerson($authUser['id']);
            $this->set($person[0]);
        }

        $this->getCountryState($this->Auth->user('country_id'));

        //Contact table
        $contacts = $this->Contact->getCompany($this->Auth->user('id'));
        foreach ($contacts as $contact) {
            $this->set($contact['Contact']['contact_medium'], $contact['Contact']['value']);
        }

        $this->set('subdomain', $subdomain['UserSubdomain']['subdomain'] ?? null);
        $this->getLanguages();
    }

    /**
     * Change password page
     * common for all user types
     */
    public function change_password()
    {
        $this->setUserSession(User::revertAuthParent($this->Auth->user()));

        $this->set('title_for_layout', 'Change password');
        if ($this->request->is(['post', 'put'])) {
            $oldPassword = $this->request->data['User']['old_password'];
            $newPassword = $this->request->data['User']['new_password'];
            $this->request->data = [
                'User' => [
                    'email_address' => $this->Auth->user('email_address'),
                    'password' => $oldPassword,
                ],
            ];

            $identified = (bool)$this->Auth->identify($this->request, $this->response);
            unset($this->request->data['User']['password']);

            if (!$identified) {
                $message = __('The old password you entered is incorrect. Please try again.');

                return $this->_exceptionResponse(new UnauthorizedException($message), $message);
            }

            $data = [
                'id' => $this->Auth->user('id'),
                'password' => User::passwordHasher()->hash($newPassword),
            ];
            if ($this->User->save($data)) {
                return $this->_successResponse(__('Your account has been updated successfully'));
            }
        }
    }

    /**
     * Choosing area of interest
     * 3 max category for Brand
     * unlimited for Retailer
     */
    public function account_setting()
    {
        if ($this->Auth->user('Branch')) {
            $this->redirect($this->referer());
        }

        if ($this->request->is('post')) {
            if (empty($this->request->data['cat'])) {
                return $this->_exceptionResponse(new BadRequestException(), 'You must select at least one category');
            }

            $this->UserLogic->UpdateUserCat(
                array_keys($this->Auth->user('user_categories')),
                array_keys($this->request->data['cat']),
                $this->Auth->user('id'),
                $this->Auth->user('user_type'),
                $this->Auth->user('Branch'),
                true
            );

            if ($this->Auth->user('setup_status') != 1) {
                $this->redirect('/');
            }
            return $this->_successResponse('');
        }

        $this->set('seleced_cat', array_keys($this->Auth->user('user_categories')));
        $this->set('cat', $this->Category->getAllCategory());
    }

    /**
     * activation link shared though activate mail
     * activate the user
     */
    public function activate()
    {
        if ($this->Auth->user('id')) {
            $this->redirect($this->Auth->redirectUrl());
        }

        $user = $this->User->findByUuid($this->request->params['id'], ['id', 'uuid', 'email_address', 'user_type', 'status', 'setup_status'], null, -1);
        if (empty($user['User']['id'])) {
            throw new NotFoundException(json_encode(['User' => ['uuid' => $this->request->params['id']]]));
        }

        $userId = $user['User']['id'];
        $redirectUrl = BASE_PATH . 'login';

        if ($user['User']['status'] == 'Approve') {
            if (!$this->User->save(['id' => $userId, 'status' => 'Active'])) {
                return $this->_exceptionResponse(
                    new InternalErrorException('Failed to activate user: ' . json_encode($user)),
                    'There was an unexpected error. Please contact the ShipEarly admin for more details.',
                    true,
                    $redirectUrl
                );
            }
            if (in_array($user['User']['user_type'], [User::TYPE_STAFF, User::TYPE_SALES_REP, User::TYPE_BRAND_STAFF])) {
                $this->User->UpdateAccountSetup($userId);
            }
            return $this->_successResponse('Your Account has been activated. Please login to complete Registration.', $redirectUrl);
        }
        if ($user['User']['status'] == 'Active') {
            $this->setFlash('This account has already been activated. Please sign in below.', 'info');
        } else {
            $this->_checkUserLoginStatus($user['User']['status'], $userId);
        }
        $this->redirect($redirectUrl);
    }

    /**
     * For Brand E-commerce Configuration page
     * For Retailer Inventory configuration page
     */
    public function configuration(): ?CakeResponse
    {
        if ($this->Auth->user('user_type') === User::TYPE_RETAILER) {
            return $this->inventory_settings();
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        $id = (int)$this->Auth->user('id');
        $country = $this->Country->getCountryList();

        if ($this->request->is(['post', 'put'])) {
            $this->request->data['User'] = ['id' => $id] + $this->request->data['User'];

            $siteType = (string)$this->request->data['User']['site_type'];

            $availableApiCredentials = (array)[
                UserSiteType::MAGENTO => ['shop_url', 'api_key', 'secret_key'],
                UserSiteType::SHIPEARLY => [],
                UserSiteType::SHOPIFY => ['shop_url', 'secret_key', 'webhook_shared_secret'],
                UserSiteType::WOOCOMMERCE => ['shop_url', 'api_key', 'secret_key'],
            ][$siteType];
            $apiCredentials = (
                array_intersect_key($this->request->data['User'], array_flip($availableApiCredentials))
                + array_fill_keys(['shop_url', 'api_key', 'secret_key', 'webhook_shared_secret'], '')
            );

            $this->request->data['User'] = array_merge($this->request->data['User'], $apiCredentials);
            $this->request->data['User']['brand_accent_color'] = !empty($this->request->data['User']['brand_accent_color'])
                ? strtolower((string)$this->request->data['User']['brand_accent_color'])
                : null;

            // Reset in case the user has changed their Shopify app_id
            $this->request->data['User']['shop_app_id'] = null;

            // Automatically Enabling sell direct option to Woocommerce Brand
            if ($siteType === UserSiteType::WOOCOMMERCE) {
                $this->request->data['User']['admin_sell_direct'] = 1;
                $this->request->data['User']['sell_direct'] = 1;
            }

            if ($this->User->updateUser($this->request->data['User'])) {
                if (is_array($this->request->data('UserCurrency.currency_codes'))) {
                    $this->UserCurrency->saveCurrencySet($id, $this->request->data['UserCurrency']['currency_codes']);
                }

                if (is_array($this->request->data('UserSetting'))) {
                    $userSettingId = (int)$this->UserSetting->fieldByConditions('id', ['UserSetting.user_id' => $id]) ?: null;
                    $now = $this->date();
                    $userSettingInfo = [
                        'id' => $userSettingId,
                        'user_id' => $id,
                        'country_list' => is_array($this->request->data['UserSetting']['country_list'])
                            ? json_encode(array_intersect_key($country, array_flip($this->request->data['UserSetting']['country_list'])))
                            : null,
                        'map' => (bool)$this->request->data['UserSetting']['map'],
                        'msrp' => (bool)$this->request->data['UserSetting']['msrp'],
                        'updated_at' => $now,
                    ];
                    if (!$userSettingId) {
                        $userSettingInfo['created_at'] = $now;
                    }
                    $this->UserSetting->save($userSettingInfo);
                }

                $this->Cron->createForUser($id);

                $redirectUrl = $this->Auth->user('setup_status') ? null : '/';

                return $this->_successResponse(null, $redirectUrl);
            } else {
                if (isset($this->User->validationErrors['shop_url'])) {
                    $this->User->validationErrors['shopify_url'] = $this->User->validationErrors['shop_url'];
                }
            }
        }

        $userSetting = $this->UserSetting->findByUserId($id, ['id', 'country_list', 'map', 'msrp']);

        $this->set('title_for_layout', 'E-Commerce settings');
        $this->set('currencyCodes', $this->UserCurrency->listCurrencySet($id, true));
        $this->set('map', (bool)$userSetting['UserSetting']['map']);
        $this->set('msrp', (bool)$userSetting['UserSetting']['msrp']);
        $this->set('selectedCountry', json_decode_if_array((string)$userSetting['UserSetting']['country_list']) ?? []);
        $this->set('country', $country);

        return null;
    }

    public function inventory_settings($id = null): ?CakeResponse
    {
        $targetUser = ($id !== null)
            ? $this->User->findAuthUser($id)
            : (array)$this->Auth->user();
        $id = (int)$targetUser['id'];

        try {
            $this->Permissions->assertUserIsType($targetUser, User::TYPE_RETAILER);
            if (!in_array($id, $this->User->listRetailerStoreIds($this->Auth->user('id')), true)) {
                throw new ForbiddenException(json_encode([
                    'message' => 'Retailer does not belong to current user',
                    'Retailer' => User::extractAuthUserLogFields($targetUser),
                    'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
                ]));
            }
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }
        $this->shipearly_user = ['User' => $targetUser];

        $priceLevel = $this->Quickbook->getPriceLevel($id);
        $this->shipearly_user['User']['quickbook_priceLevel'] = $priceLevel;

        if ($this->request->is('post')) {
            if(isset($this->request->data['User']['shipping_tax_option'])) {
                if ($this->request->data['User']['shipping_tax_option'] == 'on') {
                    $this->request->data['User']['shipping_tax_option'] = 1;
                }
                if ($this->request->data['User']['shipping_tax_option'] == 'off') {
                    $this->request->data['User']['shipping_tax_option'] = 0;
                }
                $shippingtaxOption = $this->request->data['User']['shipping_tax_option'];
            }
            if(isset($this->request->data['User']['map'])) {
                if ($this->request->data['User']['map'] == 'on') {
                    $this->request->data['User']['map'] = 1;
                }
                if ($this->request->data['User']['map'] == 'off') {
                    $this->request->data['User']['map'] = 0;
                }
                $mapOption = $this->request->data['User']['map'];
            }
            if(isset($this->request->data['User']['msrp'])) {
                if ($this->request->data['User']['msrp'] == 'on') {
                    $this->request->data['User']['msrp'] = 1;
                }
                if ($this->request->data['User']['msrp'] == 'off') {
                    $this->request->data['User']['msrp'] = 0;
                }
                $msrpOption = $this->request->data['User']['msrp'];
            }
            $this->request->data['User']['id'] = $this->shipearly_user['User']['id'];

            if (isset($this->request->data['User']['shopify_url']) && $this->request->data['User']['inventory_type'] == 'shopify_pos') {
                $this->request->data['User']['shop_url'] = rtrim($this->request->data['User']['shopify_url'], '/');
            }

            if (isset($this->request->data['User']['inventory_type'])) {
                if ($this->request->data['User']['inventory_type'] == 'none') {
                    $this->request->data['User']['inventory_type'] = 'other';
                    $this->request->data['User']['otherInventory'] = 'None';
                }

                if (empty($this->request->data['User']['inventory_type'])) {
                    $this->request->data['User']['inventory_apiuser'] = $this->request->data['User']['inventory_password'] = $this->request->data['User']['otherInventory'] = '';
                } elseif ($this->request->data['User']['inventory_type'] == 'square') {
                    $this->request->data['User']['inventory_apiuser'] = $this->request->data['User']['Square_Store_ID'];
                } elseif ($this->request->data['User']['inventory_type'] == 'citrus_lime' || $this->request->data['User']['inventory_type'] == 'clover' || $this->request->data['User']['inventory_type'] == 'microsoft_rms') {
                    $this->request->data['User']['inventory_apiuser'] = $this->request->data['User']['inventory_password'] = '';
                } elseif ($this->request->data['User']['inventory_type'] !== 'other') {
                    $this->request->data['User']['otherInventory'] = '';
                }
            }

            if (isset($this->request->data['User']['inventory_type']) && $this->request->data['User']['inventory_type'] == 'quickbook_pos' && $this->shipearly_user['User']['user_type'] == 'Retailer') {
                /** Regular price as default **/
                $this->request->data['User']['quickbook_priceLevel'] = 'Price1';
                $this->request->data['User']['inventory_apiuser'] = $this->Quickbook->apikey(
                    $this->shipearly_user['User']['inventory_apiuser'],
                    $this->request->data['User']['inventory_password'],
                    $this->request->data['User']['quickbook_priceLevel'],
                    $id
                );

            }

            if (isset($this->request->data['User']['brand_abandon_cart'])) {
                if ($this->request->data['User']['brand_abandon_cart'] == 'on') {
                    $this->request->data['User']['brand_abandon_cart'] = 1;
                }
                if ($this->request->data['User']['brand_abandon_cart'] == 'off') {
                    $this->request->data['User']['brand_abandon_cart'] = 0;
                }
            }

            $siteType = $this->request->data('User.site_type');

            $this->request->data['User']['brand_accent_color'] = ($siteType === UserSiteType::SHOPIFY && !empty($this->request->data['User']['brand_accent_color']))
                ? strtolower((string)$this->request->data['User']['brand_accent_color'])
                : null;

            // Automatically Enabling sell direct option to Woocommerce Brand
            if ($siteType === UserSiteType::WOOCOMMERCE) {
                $this->request->data['User']['admin_sell_direct'] = 1;
                $this->request->data['User']['sell_direct'] = 1;
            }

            if ($this->User->updateUser($this->request->data['User'])) {
                if (isset($this->request->data['User']['inventory_type']) && $this->request->data['User']['inventory_type'] == 'quickbook_pos' && $this->shipearly_user['User']['user_type'] == 'Retailer') {
                    $this->shipearly_user['User']['quickbook_priceLevel'] = $this->request->data['User']['quickbook_priceLevel'];
                }
                /*if ($this->request->data['User']['inventory_type'] == 'shopify_pos') {
                    $this->requestAction('shopifypos/shop/update/' . $this->shipearly_user['User']['id']);
                } else if ($this->request->data['User']['inventory_type'] == 'lightspeed_cloud') {
                    $this->requestAction('crons/mapTaxCache');
                }*/
                $this->Btask->queueStoreUpdate($this->shipearly_user['User']['id']);

                $redirectUrl = ($this->shipearly_user['User']['setup_status']) ? null : '/';

                return $this->_successResponse(null, $redirectUrl);
            } else {
                if (isset($this->User->validationErrors['shop_url'])) {
                    $this->User->validationErrors['shopify_url'] = $this->User->validationErrors['shop_url'];
                }
            }
        }

        $title = __('Inventory configuration');
        if ($id !== (int)$this->Auth->user('id')) {
            $title .= ' : ' . $this->shipearly_user['User']['company_name'];
        }
        $this->set('title_for_layout', $title);
        $this->set('_shipearly_user', $this->shipearly_user);

        return $this->render('configuration');
    }

    public function checkout_settings()
    {
        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
            $this->Permissions->assertUserHasSiteType($this->Auth->user(), [UserSiteType::SHOPIFY, UserSiteType::WOOCOMMERCE]);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_CHECKOUT_SETTINGS);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e, '/');
        }

        $id = (int)$this->Auth->user('id');
        $user = $this->User->findForCheckoutSettings($id);

        if ($this->request->is(['post', 'put']) && $this->request->data('User')) {
            //Background Image for checkout page
            if (!empty($this->request->data['User']['checkout_background_image']['name'])) {
                $file = $this->request->data['User']['checkout_background_image'];
                $fileUri = $this->Upload->replaceFileInUserHash(
                    $user['User']['checkout_background_image'],
                    $file,
                    $user['User']['uuid'],
                    'checkout_settings',
                    str_replace('.', '_', uniqid('users_', true))
                );
                if ($fileUri) {
                    $this->request->data['User']['checkout_background_image'] = $fileUri;
                } else {
                    $this->setFlash('Failed to upload the checkout image.', 'error');
                }
            } else {
                $this->request->data['User']['checkout_background_image'] = $user['User']['checkout_background_image'] ?? '';
            }
            //Header logo for checkout page
            if (!empty($this->request->data['User']['checkout_logo']['name'])) {
                $file = $this->request->data['User']['checkout_logo'];
                $fileUri = $this->Upload->replaceFileInUserHash(
                    $user['User']['checkout_logo'],
                    $file,
                    $user['User']['uuid'],
                    'checkout_settings',
                    str_replace('.', '_', uniqid('users_', true))
                );
                if ($fileUri) {
                    $this->request->data['User']['checkout_logo'] = $fileUri;
                } else {
                    $this->setFlash('Failed to upload the checkout header image.', 'error');
                }
            } else {
                $this->request->data['User']['checkout_logo'] = $user['User']['checkout_logo'] ?? '';
            }
            foreach ($this->request->data('UserMapMarker') as $mapMarkerType => $mapMarker) {
                $oldFileUrl = $user['UserMapMarker'][$mapMarkerType]['uri'] ?? '';
                if(empty($mapMarker['uri'])){
                    $this->Upload->deleteFromWebroot($oldFileUrl);
                    unset($this->request->data['UserMapMarker'][$mapMarkerType]);
                } elseif ($mapMarker['file']['error'] !== UPLOAD_ERR_NO_FILE){
                    $fileUri = $this->UserLogic->replaceUserImage(
                        $oldFileUrl,
                        $mapMarker['file'],
                        $user['User']['uuid'],
                        $mapMarkerType
                    ) ?? $oldFileUrl;
                    $this->request->data['UserMapMarker'][$mapMarkerType] = ['user_id' => $id, 'uri' => $fileUri, 'type' => $mapMarkerType];
                } else {
                    $this->request->data['UserMapMarker'][$mapMarkerType] = $user['UserMapMarker'][$mapMarkerType] ?? null;
                }
            }
            $this->request->data['UserMapMarker'] = array_filter($this->request->data['UserMapMarker']);

            foreach (SupportedLanguages::getLocalesSet() as $locale) {
                $fileUrl = $this->UserLogic->replaceUserImage(
                    $user['User'][$locale]['shipping_infographic'],
                    (array)$this->request->data['User'][$locale]['shipping_infographic'],
                    (string)$user['User'][$locale]['uuid'],
                    'shipping_infographic' . DS . $locale
                );
                $this->request->data['User'][$locale]['shipping_infographic'] = $fileUrl ?: $user['User'][$locale]['shipping_infographic'];
            }

            if ($this->User->saveFromCheckoutSettings($id, $this->request->data)) {
                return $this->_successResponse(__('Your account has been updated successfully'));
            } else {
                $this->setFlash('An error occurred. Please, try again.', 'error');
            }
        } else {
            $this->request->data = $user;
        }

        $this->set('title_for_layout', 'Checkout Settings');
        $this->set('checkoutBackgroundImage', $user['User']['checkout_background_image']);
        $this->set('checkoutLogo', $user['User']['checkout_logo']);
        $this->set('displayCheckoutLogoLeft', $user['User']['display_checkout_logo_left']);
        $this->set('messageDefaults', $this->User->getMessageDefaults($user['User']['email_address']));
    }

    public function delete_checkout_background_image()
    {
        $userId = (int)$this->Auth->user('id');
        if ($this->request->is(['post', 'put'])) {
            $user = $this->User->record($userId, [
                'fields' => ['id', 'checkout_background_image'],
            ]);
            if ($user) {
                $checkoutBackgroundImage = $user['User']['checkout_background_image'];

                $deleted = $this->User->updateAllJoinless(
                    ['checkout_background_image' => null],
                    ['id' => $userId, 'checkout_background_image' => $checkoutBackgroundImage]
                );
                if ($deleted) {
                    $this->Upload->deleteFromWebroot($checkoutBackgroundImage);
                    $this->set([
                        'success' => true,
                        'message' => __('Image deleted successfully.'),
                        '_serialize' => ['success', 'message']
                    ]);
                } else {
                    $this->set([
                        'success' => false,
                        'message' => __('Failed to delete image.'),
                        '_serialize' => ['success', 'message']
                    ]);
                }
            }
        }
        $this->redirect($this->referer());
    }

    public function delete_checkout_logo()
    {
        $userId = (int)$this->Auth->user('id');
        if ($this->request->is(['post', 'put'])) {
            $user = $this->User->record($userId, [
                'fields' => ['id', 'checkout_logo'],
            ]);
            if ($user) {
                $checkoutLogo = $user['User']['checkout_logo'];

                $deleted = $this->User->updateAllJoinless(
                    ['checkout_logo' => null],
                    ['id' => $userId, 'checkout_logo' => $checkoutLogo]
                );
                if ($deleted) {
                    $this->Upload->deleteFromWebroot($checkoutLogo);
                    $this->set([
                        'success' => true,
                        'message' => __('Image deleted successfully.'),
                        '_serialize' => ['success', 'message']
                    ]);
                } else {
                    $this->set([
                        'success' => false,
                        'message' => __('Failed to delete image.'),
                        '_serialize' => ['success', 'message']
                    ]);
                }
            }
        }
        $this->redirect($this->referer());
    }

    public function admin_adddealer($id = null)
    {
        $userId = $this->request->data('retailer_id');
        if (!empty($userId)) {
            $this->shipearly_user = (array)$this->User->findById($userId, [], null, -1);
        }
        return $this->adddealer($id);
    }

    public function adddealer($id = null)
    {
        $this->autoRender = false;

        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
        $this->request->allowMethod('post', 'put');

        $auth = $this->shipearly_user;
        if (!in_array($auth['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_RETAILER, User::TYPE_SALES_REP])) {
            return $this->_exceptionResponse(new ForbiddenException('Invalid user type ' . json_encode($auth['User']['user_type'])), null, true);
        }

        $userFields = ['id', 'Branch', 'email_address', 'user_type', 'company_name', 'language_code'];
        if ($auth['User']['user_type'] === User::TYPE_SALES_REP) {
            $this->ManufacturerRetailer->addAssociations(['belongsTo' => ['User', 'Retailer' => ['className' => 'User']]]);
            $record = (array)$this->ManufacturerRetailer->get($id, [
                'contain' => [
                    'User' => ['fields' => $userFields],
                    'Retailer' => ['fields' => $userFields],
                ],
                'conditions' => [
                    'ManufacturerRetailer.distributor_id' => $auth['User']['id'],
                ],
                'fields' => ['id', 'user_id', 'retailer_id'],
            ]);
            $this->ManufacturerRetailer->unbindModel(['belongsTo' => ['User', 'Retailer']], false);

            $brand = ['User' => $record['User']];
            $retailer = ['User' => $record['Retailer']];
        } else {
            $targetUser = (array)$this->User->findById($id, $userFields, null, -1);
            if (!in_array($targetUser['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_RETAILER], true) || $targetUser['User']['user_type'] === $auth['User']['user_type']) {
                return $this->_exceptionResponse(new BadRequestException('Invalid target user type ' . json_encode($targetUser['User']['user_type'])), null, true);
            }

            if ($auth['User']['user_type'] === User::TYPE_MANUFACTURER) {
                $brand = $auth;
                $retailer = $targetUser;
            } else {
                $brand = $targetUser;
                $retailer = $auth;
            }
        }

        $response = (in_array($auth['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true))
            ? $this->_connectToRetailer($brand, $retailer)
            : $this->_connectToBrand($retailer, $brand);

        if ($this->request->is('ajax')) {
            $this->response->body(json_encode($response));
            return $this->response;
        }

        if (!empty($response['success'])) {
            $this->setFlash($response['success'], 'success');
        }
        if (!empty($response['error'])) {
            $this->setFlash($response['error'], 'error');
        }
        $this->redirect($this->referer());
    }

    private function _connectToRetailer($brand, $retailer)
    {
        $brandId = $brand['User']['id'];
        $retailerId = $retailer['User']['id'];

        $fromStatus = $this->ManufacturerRetailer->field('status', ['user_id' => $brandId, 'retailer_id' => $retailerId]);

        if ($fromStatus == ManufacturerRetailer::STATUS_CONNECTED) {
            return ['error' => 'Account already activated with this brand', 'success' => ''];
        }

        if (!$this->ManufacturerRetailer->connectToRetailer($brandId, $retailerId, $retailer['User']['Branch'])) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . json_encode(['errors' => $this->ManufacturerRetailer->validationErrors, 'data' => $this->ManufacturerRetailer->data]));
            return ['error' => 'An error occurred. Please, try again.', 'success' => ''];
        }

        $url = Router::url(['controller' => 'users', 'action' => 'contact', 'id' => $brandId]);
        $link = "<a href=\"{$url}\">{$brand['User']['company_name']}</a>";
        $notification_msg = ($fromStatus == ManufacturerRetailer::STATUS_DISCONNECTED)
            ? $link . ' has reactivate your account connection with them.'
            : $link . ' has accepted your connection request.';
        $this->Notification->createNotification($brandId, $retailerId, Notification::TYPE_RETAILER_APPROVAL, '', $notification_msg);

        if ($fromStatus == ManufacturerRetailer::STATUS_PENDING) {
            $this->UserLogic->productAssociation($brandId, $retailerId);
            $this->_sendManufactureResponseEmail($brand, $retailer);
        }

        $FlashSuccess = ($fromStatus == ManufacturerRetailer::STATUS_DISCONNECTED)
            ? 'Retailer account has been reactivated'
            : 'Retailer account has been added successfully';
        return ['error' => '', 'success' => $FlashSuccess];
    }

    private function _connectToBrand($retailer, $brand)
    {
        $brandId = $brand['User']['id'];
        $retailerId = $retailer['User']['id'];

        $fromStatus = $this->ManufacturerRetailer->field('status', ['user_id' => $brandId, 'retailer_id' => $retailerId]);

        if ($fromStatus == ManufacturerRetailer::STATUS_CONNECTED) {
            return ['error' => 'Account already activated with this brand', 'success' => ''];
        } elseif ($fromStatus == ManufacturerRetailer::STATUS_PENDING) {
            return ['error' => 'You have already sent a request to this brand and are waiting for a response', 'success' => ''];
        }

        if (!$this->ManufacturerRetailer->connectToBrand($retailerId, $brandId)) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . json_encode(['errors' => $this->ManufacturerRetailer->validationErrors, 'data' => $this->ManufacturerRetailer->data]));
            return ['error' => 'An error occurred. Please, try again.', 'success' => ''];
        }

        $notification_url = Router::url(['controller' => 'users', 'action' => 'contact', 'id' => $retailerId]);
        $notification_msg = "<a href='" . $notification_url . "'>" . $retailer['User']['company_name'] . "</a> has requested to become an authorized dealer.";
        $this->Notification->createNotification($retailerId, $brandId, Notification::TYPE_RETAILER_REQUEST, '', $notification_msg);

        $this->_sendRetailerRequestEmail($brand, $retailer);

        return ['error' => '', 'success' => 'Your request has been sent successfully to this brand'];
    }

    /**
     * Sub function to send retailer requested mail to brand
     *
     * @param array $brand
     * @param array $retailer
     * @return bool
     */
    private function _sendRetailerRequestEmail($brand, $retailer)
    {
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Retailer Request');

        $variables = array(
            '{brand_name}' => $brand['User']['company_name'],
            '{site_name}' => SITE_NAME,
            '{retailer_name}' => $retailer['User']['company_name'],
        );

        return $this->sendEmail($emailTemplateArr, $variables, $brand['User']['email_address']);
    }

    /**
     * Sub function to send response from brand to requested retailer
     *
     * @param array $brand
     * @param array $retailer
     * @return bool
     */
    private function _sendManufactureResponseEmail($brand, $retailer)
    {
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Retailer Approval', $brand['User']['id'], $retailer['User']['language_code']);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$brand['User']['company_name']}<{$brand['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $brand['User']['avatar'];
        $brandUrl = Router::url(['controller' => 'users', 'action' => 'contact', 'id' => $brand['User']['id']], true);
        $variables = array(
            '{site_name}' => SITE_NAME,
            '{brand_name}' => "<a href=\"{$brandUrl}\">{$brand['User']['company_name']}</a>",
        );

        return $this->sendEmail($emailTemplateArr, $variables, $retailer['User']['email_address']);
    }

    public function admin_removedealer($id = null)
    {
        $userId = $this->request->data('retailer_id');
        if (!empty($userId)) {
            $this->shipearly_user = (array)$this->User->findById($userId, [], null, -1);
        }
        return $this->removedealer($id);
    }

    public function removedealer($id = null)
    {
        $this->autoRender = false;

        $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
        $this->request->allowMethod('post', 'put');

        $auth = $this->shipearly_user;
        if (!in_array($auth['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_RETAILER, User::TYPE_SALES_REP])) {
            return $this->_exceptionResponse(new ForbiddenException('Invalid user type ' . json_encode($auth['User']['user_type'])), null, true);
        }

        $userFields = ['id', 'Branch', 'email_address', 'user_type', 'company_name'];
        if ($auth['User']['user_type'] === User::TYPE_SALES_REP) {
            $this->ManufacturerRetailer->addAssociations(['belongsTo' => ['User', 'Retailer' => ['className' => 'User']]]);
            $record = (array)$this->ManufacturerRetailer->get($id, [
                'contain' => [
                    'User' => ['fields' => $userFields],
                    'Retailer' => ['fields' => $userFields],
                ],
                'conditions' => [
                    'ManufacturerRetailer.distributor_id' => $auth['User']['id'],
                ],
                'fields' => ['id', 'user_id', 'retailer_id'],
            ]);
            $this->ManufacturerRetailer->unbindModel(['belongsTo' => ['User', 'Retailer']], false);

            $brand = ['User' => $record['User']];
            $retailer = ['User' => $record['Retailer']];
        } else {
            $targetUser = (array)$this->User->findById($id, $userFields, null, -1);
            if (!in_array($targetUser['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_RETAILER], true) || $targetUser['User']['user_type'] === $auth['User']['user_type']) {
                return $this->_exceptionResponse(new BadRequestException('Invalid target user type ' . json_encode($targetUser['User']['user_type'])), null, true);
            }

            if ($auth['User']['user_type'] === User::TYPE_MANUFACTURER) {
                $brand = $auth;
                $retailer = $targetUser;
            } else {
                $brand = $targetUser;
                $retailer = $auth;
            }
        }

        $response = (in_array($auth['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true))
            ? $this->_disconnectFromRetailer($brand, $retailer)
            : $this->_disconnectFromBrand($retailer, $brand);

        if ($this->request->is('ajax')) {
            $this->response->body(json_encode($response));
            return $this->response;
        }

        if (!empty($response['success'])) {
            $this->setFlash($response['success'], 'success');
        }
        if (!empty($response['error'])) {
            $this->setFlash($response['error'], 'error');
        }
        $this->redirect($this->referer());
    }

    private function _disconnectFromRetailer($brand, $retailer)
    {
        $brandId = $brand['User']['id'];
        $retailerId = $retailer['User']['id'];

        $existing = $this->ManufacturerRetailer->find('first', array(
            'recursive' => -1,
            'conditions' => array('user_id' => $brandId, 'retailer_id' => $retailerId),
            'fields' => array('id', 'status'),
        ));

        if (empty($existing['ManufacturerRetailer']['id']) && !$retailer['User']['Branch']) {
            return ['error' => 'Account not connected', 'success' => ''];
        }

        $fromStatus = Hash::get($existing, 'ManufacturerRetailer.status');

        if ($fromStatus == ManufacturerRetailer::STATUS_DISCONNECTED) {
            return ['error' => 'Account already disconnected', 'success' => ''];
        }

        if (!$this->ManufacturerRetailer->disconnectFromRetailer($brandId, $retailerId, $retailer['User']['Branch'])) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . json_encode(['errors' => $this->ManufacturerRetailer->validationErrors, 'data' => $this->ManufacturerRetailer->data]));
            return ['error' => 'An error occurred. Please, try again.', 'success' => ''];
        }

        return ['error' => '', 'success' => 'Retailer account has been disconnected'];
    }

    private function _disconnectFromBrand($retailer, $brand)
    {
        $brandId = $brand['User']['id'];
        $retailerId = $retailer['User']['id'];

        $existing = $this->ManufacturerRetailer->find('first', array(
            'recursive' => -1,
            'conditions' => array('user_id' => $brandId, 'retailer_id' => $retailerId),
            'fields' => array('id', 'status'),
        ));

        if (empty($existing['ManufacturerRetailer']['id'])) {
            return ['error' => 'Account not connected', 'success' => ''];
        }

        $fromStatus = Hash::get($existing, 'ManufacturerRetailer.status');

        if ($fromStatus == ManufacturerRetailer::STATUS_DISCONNECTED) {
            return ['error' => 'Account already disconnected', 'success' => ''];
        }

        if (!$this->ManufacturerRetailer->disconnectFromBrand($retailerId, $brandId)) {
            CakeLog::error(__METHOD__ . ', line ' . __LINE__ . ' - ' . json_encode(['errors' => $this->ManufacturerRetailer->validationErrors, 'data' => $this->ManufacturerRetailer->data]));
            return ['error' => 'An error occurred. Please, try again.', 'success' => ''];
        }

        return ['error' => '', 'success' => 'Brand account has been disconnected'];
    }

    /**
     * Common contact page for both retailer and manufacture
     *
     * @param int|null $id Url provided user id.
     */
    public function contact($id = null)
    {
        $authUserId = $this->Auth->user('id');
        $authUserType = $this->Auth->user('user_type');

        $profile = $this->User->findForContactPage($id);
        if (empty($profile['User']['id'])) {
            $message = 'User account not found.';

            return $this->_exceptionResponse(new NotFoundException($message), $message);
        }

        $userType = $profile['User']['user_type'];

        $showNotes = false;

        $returnPolicy = '';
        if ($userType === User::TYPE_MANUFACTURER) {
            $returnPolicy = $this->UserSetting->getReturnPolicy($id, $profile['User']['email_address']);
        }

        $timing = ($userType === User::TYPE_RETAILER)
            ? json_decode($this->User->getStoreTiming($profile['User']['store_timing'], $id), true)
            : array('currentTime' => '', 'timinglist' => '');

        $contact = $this->Contact->getContactList($id);

        if (in_array($authUserType, User::TYPES_BRAND, true) && $userType === User::TYPE_RETAILER) {
            $address = $this->B2bShipToAddress->findResolvedRetailerAddress($authUserId, $id);

            unset($address['B2bShipToAddress']['id']);
            $profile['User'] = array_merge($profile['User'], array_intersect_key($address['B2bShipToAddress'], $profile['User']));

            $contact['company']['telephone'] = $address['B2bShipToAddress']['telephone'];

            $showNotes = (
                !empty($this->ManufacturerRetailer->findByUserIdAndRetailerId($authUserId, $id, ['id'], null, -1))
                || $this->ManufacturerRetailerSalesRep->isConnectedToRetailer((int)$authUserId, (int)$id)
            );
        }
        
        $accountNumbers = [];
        if($authUserType === User::TYPE_SALES_REP && $userType == User::TYPE_RETAILER){
            $accountNumbers = $this->ManufacturerRetailerSalesRep->findAllAccountNumbersForSalesRepAndRetailer($authUserId, $id);
        }

        $stripeUser = $this->StripeUser->getStatusFields($id);

        $this->set('authUserType', $authUserType);
        $this->set('profile', $profile['User']);
        $this->set('returnPolicy', nl2br($returnPolicy));
        $this->set('currentTime', $timing['currentTime']);
        $this->set('timinglist', $timing['timinglist']);
        $this->set('state', $this->State->findById($profile['User']['state_id']));
        $this->set('country', $this->Country->findById($profile['User']['country_id']));
        $this->set('contact', $contact);
        $this->set('address', $this->User->addressDisplayFormat($profile['User']));
        $this->set('person', (array)Hash::get($this->Contactpersons->findByUserId($id, [], null, -1), 'Contactpersons'));
        $this->set('storeCount', $this->User->getBranchListCount($id));
        $this->set('stripeUser', $stripeUser);
        $this->set('stripeUserCapabilities', $this->StripeUserCapability->getUserCapabilities($stripeUser['StripeUser']['id'] ?? null));
        $this->set('hasB2bRetailerPermission', ($authUserType !== User::TYPE_RETAILER && $this->User->hasB2bRetailerPermission($authUserId, $id)));
        $this->set('accountNumbers', $accountNumbers);
        $this->set('showNotes', $showNotes);
        $this->set('salesReps', $this->User->findSalesRepsByRetailerId($id, $authUserId));

        if($authUserType == User::TYPE_MANUFACTURER && $userType == User::TYPE_RETAILER){
            /** @var RetailerCredit $RetailerCredit */
            $RetailerCredit = ClassRegistry::init('RetailerCredit');
            $accountDetails = $this->ManufacturerRetailer->find('first', [
                'recursive' => -1,
                'joins' => [
                    [
                        'table' => 'pricing_tiers',
                        'alias' => 'PricingTier',
                        'type' => 'LEFT',
                        'conditions' => [
                            'PricingTier.id = ManufacturerRetailer.pricingtierid'
                        ]
                    ],
                    [
                        'table' => $RetailerCredit->buildTotalsSubquery(['RetailerCredit.user_id' => $authUserId]),
                        'alias' => 'RetailerCredit',
                        'type' => 'LEFT',
                        'conditions' => [
                            "RetailerCredit.user_id = ManufacturerRetailer.user_id",
                            "RetailerCredit.retailer_id = ManufacturerRetailer.retailer_id",
                        ],
                    ]
                ],
                'conditions' => [
                    'ManufacturerRetailer.retailer_id' => $profile['User']['id'],
                    'ManufacturerRetailer.user_id' => $authUserId,
                ],
                'fields' => [
                    'ManufacturerRetailer.external_retailer_account',
                    'ManufacturerRetailer.credit_limit',
                    'PricingTier.pricingtiername',
                    'PricingTier.currencytype',
                    'RetailerCredit.total_balance',
                ],
            ]);

            $openToBuy = $this->Currency->formatAsDecimal(
                $accountDetails['ManufacturerRetailer']['credit_limit'] - $accountDetails['RetailerCredit']['total_balance'],
                (string)($accountDetails['PricingTier']['currencytype'] ?? $this->Auth->user('currency_code'))
            );
            $this->set('pricingTierName', $accountDetails['PricingTier']['pricingtiername']);
            $this->set('accountId', $accountDetails['ManufacturerRetailer']['external_retailer_account']);
            $this->set('creditLimit', $accountDetails['ManufacturerRetailer']['credit_limit']);
            $this->set('openToBuy', $openToBuy);
            $this->set('currencyCode',$accountDetails['PricingTier']['currencytype'] ?? $this->Auth->user('currency_code'));
            $this->set('userHasEditPermission', $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT));

            $alreadySelectedProductIds = $this->RetailDisplay->find('list', [
                'conditions' => ['user_id' => $profile['User']['id']],
                'fields' => ['product_id', 'product_id'],
                'recursive' => -1
            ]);
            $products = $this->Product->findForRetailerOnDisplaySettings($authUserId, $profile['User']['id']);
            $this->set('products', $products);
            $this->set('alreadySelectedProductIds', $alreadySelectedProductIds);

            /** @var RetailCategory $RetailCategory */
            $RetailCategory = ClassRegistry::init('RetailCategory');
            $categories = $RetailCategory->getCategoriesByRetailerId($authUserId, $id );
            $allCategories = $RetailCategory->getCategoriesOptionsByUser($authUserId);

            $this->set('pricingTierName', $accountDetails['PricingTier']['pricingtiername']);
            $this->set('accountId', $accountDetails['ManufacturerRetailer']['external_retailer_account']);
            $this->set('categories', $categories);
            $this->set('allCategories', $allCategories);

            $this->render('/Retailers/view');
        }
    }

    public function admin_contact($id = null)
    {
        $this->layout = 'admin';

        if (!$this->User->exists(['User.id' => $id])) {
            return $this->_exceptionResponse(new NotFoundException(), 'User not found');
        }

        $this->User->bindModel([
            'belongsTo' => ['CheckoutDomain' => ['className' => 'UserDomain']],
            'hasOne' => ['UserSubdomain'],
            'hasMany' => ['UserDomain'],
        ], false);

        $profiles = $this->User->record($id, [
            'contain' => [
                'UserSubdomain' => ['fields' => ['user_id', 'subdomain']],
                'UserDomain' => ['fields' => ['id', 'user_id', 'domain']],
            ],
        ]);
        $profiles['UserDomain'] = Hash::combine((array)$profiles['UserDomain'], '{n}.id', '{n}');

        if ($this->request->is(['post', 'put'])) {
            $this->request->data['User'] = compact('id') + (array)$this->request->data('User');
            if (isset($this->request->data['User']['address1']) && isset($this->request->data['User']['address2'])) {
                $this->request->data['User']['address'] = $this->User->getCombinedAddressField($this->request->data['User']['address1'], $this->request->data['User']['address2']);
            }
            if ($profiles['User']['user_type'] == User::TYPE_RETAILER) {
                $addressFieldsUpdated = isset($this->request->data['User']['address']) || isset($this->request->data['User']['city']) || isset($this->request->data['User']['zipcode']);
                if ($addressFieldsUpdated) {
                    $state = $this->State->findWithCountryName($profiles['User']['state_id']);
                    $geopoints = findGeocode(
                        $this->request->data['User']['address'] ?? '',
                        $this->request->data['User']['city'] ?? '',
                        $this->request->data['User']['zipcode'] ?? '',
                        $state['State']['state_name'],
                        $state['State']['country_code']
                    );
                    $this->request->data['User']['latitude'] = $geopoints['lat'] ?? null;
                    $this->request->data['User']['longitude'] = $geopoints['lng'] ?? null;
                }
            }

            $checkoutDomainChanged = false;

            if (is_array($this->request->data['UserSubdomain'] ?? null)) {
                $this->request->data['UserSubdomain'] = ['user_id' => $id] + $this->request->data['UserSubdomain'];

                $checkoutDomainChanged = (
                    $checkoutDomainChanged
                    || (string)$this->request->data['UserSubdomain']['subdomain'] !== (string)$profiles['UserSubdomain']['subdomain']
                );
            }

            $domainIdsToDelete = [];
            if (is_array($this->request->data['UserDomain'] ?? null)) {
                $this->request->data['UserDomain'] = array_map(fn(array $domain): array => (
                    ['id' => $domain['id'], 'user_id' => $id] + $domain
                ), $this->request->data['UserDomain']);

                // Delete cleared domains
                $this->request->data['UserDomain'] = array_filter(
                    $this->request->data['UserDomain'],
                    fn(array $domain): bool => (bool)$domain['domain']
                );
                $domainIdsToDelete = array_diff(
                    array_column($profiles['UserDomain'], 'id'),
                    array_column($this->request->data['UserDomain'], 'id')
                );

                // Move the selected checkout domain to the belongsTo assoc which
                // can assign User.checkout_domain_id from a newly created record.
                $checkoutDomainId = (string)$this->request->data['User']['checkout_domain_id'];
                $this->request->data['CheckoutDomain'] = $this->request->data['UserDomain'][$checkoutDomainId] ?? null;
                unset($this->request->data['UserDomain'][$checkoutDomainId]);

                // Clear checkout_domain_id if the selected checkout domain was empty.
                if (!isset($this->request->data['CheckoutDomain'])) {
                    $this->request->data['User']['checkout_domain_id'] = '';
                    $checkoutDomainId = '';
                }

                $checkoutDomainChanged = (
                    $checkoutDomainChanged
                    || (string)$this->request->data['User']['checkout_domain_id'] !== (string)$profiles['User']['checkout_domain_id']
                );
            }

            if ($this->User->saveAssociated($this->request->data)) {
                $telephone = (string)($this->request->data['Contact']['telephone'] ?? '');
                if ($telephone) {
                    $this->Contact->addContact($id, 'company', $id, 'telephone', $telephone);
                }
                if ($domainIdsToDelete && !$this->UserSubdomain->UserDomain->deleteAllJoinless(['UserDomain.id' => $domainIdsToDelete], false)) {
                    CakeLog::warning(json_encode(['message' => 'Failed to delete domains', 'ids' => $domainIdsToDelete]));
                }
                if (
                    $checkoutDomainChanged
                    && $profiles['User']['site_type'] === UserSiteType::SHOPIFY
                    && $profiles['User']['secret_key']
                    && $profiles['User']['shop_url']
                ) {
                    $this->Btask->queueShopifySetup((string)$profiles['User']['uuid']);
                }

                return $this->_successResponse('Updated ' . json_encode($this->request->data, JSON_PRETTY_PRINT));
            }
        } else {
            $this->request->data = $profiles;
        }

        $this->User->unbindModel([
            'belongsTo' => ['CheckoutDomain'],
            'hasOne' => ['UserSubdomain'],
            'hasMany' => ['UserDomain'],
        ], false);

        $this->set('brandStaffUsers', $this->BrandStaff->findAllForIndex($id));

        $contacts = $this->Contact->getContactList($id);
        $persons = $this->Contactpersons->getContactPerson($id);
        $this->set('couponValid', true);

        $stripeCustomer = $this->StripeUser->getStripeUserFields($id, [
            'id',
            'is_activated',
            'charges_enabled',
            'payouts_enabled',
            'stripe_cus_id',
            'stripe_email',
            'stripe_user_id',
            'subscription_period_start',
            'subscription_period_end',
        ]);
        $stripeUserCapabilities = $this->StripeUserCapability->getUserCapabilities($stripeCustomer['StripeUser']['id'] ?? null);
        $this->set('stripeUserCapabilities', $stripeUserCapabilities);
        if (!empty($stripeCustomer['StripeUser']['stripe_user_id'])) {
            $this->set('stripeDetails', $stripeCustomer['StripeUser']);
        }

        if (!empty($stripeCustomer['StripeUser']['stripe_cus_id'])) {
            $this->set('couponValid', false);
        }

        if ($profiles['User']['user_type'] == 'Retailer') {
            $timing = json_decode($this->User->getStoreTiming($profiles['User']['store_timing'], $id), true);
            $this->set('currentTime', $timing['currentTime']);
            $this->set('timinglist', $timing['timinglist']);
        } else {
            $this->set('currentTime', '');
            $this->set('timinglist', '');
        }

        $this->set('storeCount', $this->User->getBranchListCount($id));

        $setupConditions = in_array($profiles['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_RETAILER], true)
            ? $this->UserLogic->findProfileSetupConditions($id)
            : null;
        $this->set('setupConditions', $setupConditions);

        if ($profiles['User']['user_type'] === User::TYPE_RETAILER && !$profiles['User']['setup_status']) {
            if (
                $setupConditions->evaluateSetupStatus() &&
                $this->User->UpdateAccountSetup($id)
            ) {
                $profiles['User']['setup_status'] = true;
            }
        }

        $this->set('state', $this->State->findById($profiles['User']['state_id']));
        $this->set('country', $this->Country->findById($profiles['User']['country_id']));
        if ($profiles['User']['user_type'] === User::TYPE_RETAILER && $profiles['User']['Branch']) {
            $profiles['User']['company_code'] = $this->User->field('company_code', ['id' => $profiles['User']['Branch']]);
        }
        $this->set('profile', $profiles['User']);
        $this->set('contact', $contacts);
        //$this->set('review', $profiles['Review']);

        $this->set('address', $this->User->addressDisplayFormat($profiles['User']));
        if (isset($persons[0]['Contactpersons'])) {
            $this->set('person', $persons[0]['Contactpersons']);
        }
    }

    /**
     * Delete user account though the admin contact page
     *
     * @param int|null $id
     * @return CakeResponse|null
     */
    public function admin_contact_delete($id = null)
    {
        $this->autoRender = false;
        $this->request->allowMethod('post', 'delete');

        $user = $this->User->record($id, ['fields' => ['id', 'status']]);

        $deleted = false;
        if ($user && $user['User']['status'] == 'Register') {
            $deleted = $this->User->delete($id);
        }

        if (!$deleted) {
            return $this->_exceptionResponse(
                new InternalErrorException('Failed to delete user where ' . json_encode($user)),
                'Failed to delete user!',
                true,
                ADMIN_PATH
            );
        }
        return $this->_successResponse('User has been deleted!', ADMIN_PATH);
    }

    public function admin_revenue_model($id = null)
    {
        $this->layout = '';

        $user = $this->User->find('first', [
            'recursive' => -1,
            'conditions' => ['User.id' => $id],
            'fields' => [
                'id',
                'user_type',
                'revenue_model',
                'retailer_default_amount',
                'retailer_revenue_maximum',
                'splitpayment_percentage',
            ],
        ]);
        if ($user['User']['user_type'] !== 'Manufacturer') {
            throw new BadRequestException('Invalid user_type: ' . $user['User']['user_type']);
        }

        if ($this->request->is(['post', 'put'])) {
            $this->request->data['User']['id'] = $id;

            $success = $this->User->save($this->request->data, [
                'validate' => true,
                'fieldList' => [
                    'id',
                    'revenue_model',
                    'retailer_default_amount',
                    'retailer_revenue_maximum',
                    'splitpayment_percentage',
                ],
            ]);
            if (!$success) {
                return $this->_exceptionResponse(new InternalErrorException());
            }
            return $this->_successResponse();
        }

        $this->set('defaultPercentage', SHIPEARLY_RETAILER_PERCENTAGE);
        $this->set('defaultAmount', SHIPEARLY_RETAILER_DEFAULT_AMOUNT);
        $this->request->data = $user;
    }

    public function admin_brand_revenue_model($id = null)
    {
        $this->layout = '';

        $user = $this->User->find('first', [
            'recursive' => -1,
            'conditions' => ['User.id' => $id],
            'fields' => [
                'id',
                'user_type',
                'brand_revenue_model',
                'brand_direct_default_amount',
                'brand_revenue_maximum',
            ],
        ]);
        if ($user['User']['user_type'] !== 'Manufacturer') {
            throw new BadRequestException('Invalid user_type: ' . $user['User']['user_type']);
        }

        if ($this->request->is(['post', 'put'])) {
            $this->request->data['User']['id'] = $id;

            $success = $this->User->save($this->request->data, [
                'validate' => true,
                'fieldList' => [
                    'id',
                    'brand_revenue_model',
                    'brand_direct_default_amount',
                    'brand_revenue_maximum',
                ],
            ]);
            if (!$success) {
                return $this->_exceptionResponse(new InternalErrorException());
            }
            return $this->_successResponse();
        }

        $this->set('defaultPercentage', SHIPEARLY_BRAND_DIRECT_PERCENTAGE);
        $this->set('defaultAmount', SHIPEARLY_BRAND_DIRECT_DEFAULT_AMOUNT);
        $this->request->data = $user;
    }

    public function admin_change_store_radius($id = null)
    {
        $this->layout = '';

        $user = $this->User->find('first', [
            'recursive' => -1,
            'conditions' => ['User.id' => $id],
            'fields' => [
                'id',
                'user_type',
                'in-store_radius',
                'ship_from_store_radius',
            ],
        ]);
        if ($user['User']['user_type'] !== 'Manufacturer') {
            throw new BadRequestException('Invalid user_type: ' . $user['User']['user_type']);
        }

        if ($this->request->is(['post', 'put'])) {
            $this->request->data['User']['id'] = $id;

            if ($this->request->data['User']['in-store_radius'] === '') {
                $this->request->data['User']['in-store_radius'] = IN_STORE_RADIUS;
            }
            if ($this->request->data['User']['ship_from_store_radius'] === '') {
                $this->request->data['User']['ship_from_store_radius'] = SHIP_FROM_STORE_RADIUS;
            }

            $success = $this->User->save($this->request->data, [
                'validate' => true,
                'fieldList' => [
                    'id',
                    'in-store_radius',
                    'ship_from_store_radius',
                ],
            ]);
            if (!$success) {
                return $this->_exceptionResponse(new InternalErrorException());
            }
            return $this->_successResponse();
        }

        $this->set('defaultInStoreRadius', IN_STORE_RADIUS);
        $this->set('defaultShipFromStoreRadius', SHIP_FROM_STORE_RADIUS);
        $this->request->data = $user;
    }

    public function admin_store_associate_pin($id = null)
    {
        $user = $this->User->findById($id, array(
            'id',
            'user_type',
            'store_associate_pin',
        ));
        if (empty($user['User']['id'])) {
            throw new NotFoundException();
        }
        if ($user['User']['user_type'] !== User::TYPE_STAFF) {
            throw new BadRequestException('Invalid user_type: ' . json_encode($user['User']['user_type']));
        }

        if ($this->request->is(['post', 'put'])) {
            if (!$this->User->save(['id' => $id, 'store_associate_pin' => $this->request->data['User']['store_associate_pin']])) {
                return $this->_exceptionResponse();
            }
            return $this->_successResponse();
        }

        $this->request->data = $user;
    }

    /**
     * Update contact person information for both brand and Retailer store
     */
    public function editperson()
    {
        $this->set('title_for_layout', __('Customer Support'));

        $persons = $this->Contactpersons->getContactPerson($this->shipearly_user['User']['id']);
        if (isset($persons[0]['Contactpersons'])) {
            $this->set('edit', $persons[0]['Contactpersons']);
        }

        $contacts = $this->Contact->getPerson($this->shipearly_user['User']['id']);
        foreach ($contacts as $contact) {
            $this->set($contact['Contact']['contact_medium'], $contact['Contact']['value']);
        }

        if ($this->request->is('post')) {
            if (isset($persons[0]['Contactpersons']['id'])) {
                $this->request->data['Contactpersons']['id'] = $persons[0]['Contactpersons']['id'];
            }
            $this->request->data['Contactpersons']['user_id'] = $this->shipearly_user['User']['id'];
            unset($this->request->data['Contactpersons']['avatar']);

            if ($this->Contactpersons->addContactPerson($this->request->data['Contactpersons'])) {
                $typeUid = $this->Contactpersons->id;
                foreach ($this->request->data['Contactpersons']['contact_medium'] as $key => $value) {
                    $this->Contact->addContact($this->shipearly_user['User']['id'], 'person', $typeUid, $key, $value);
                }
                $this->setFlash(__('Your account has been updated successfully'), 'success');
            }
            if ($this->shipearly_user['User']['setup_status'] != 1) {
                $this->redirect('/');
            }
            $this->redirect(BASE_PATH . "profile");
        }
    }

    /**
     * List states dropdown options
     */
    public function getstates()
    {
        $this->autoRender = false;

        // Prefer GET but allow POST for legacy compatibility
        $countryId = $this->request->query['country_id'] ?? $this->request->data('country_id');

        $states = $this->State->findAllNamesAndCodesByCountryId($countryId);

        // Placeholder option must be declared with an empty value
        $options = "<option value=\"\">Select State/Province</option>";
        foreach ($states as $stateId => $state) {
            $options .= "<option value=\"{$stateId}\" data-code=\"{$state['state_code']}\">{$state['state_name']}</option>";
        }

        $this->response->body($options);
        return $this->response;
    }

    public function getstatesbycountry()
    {
        $this->autoRender = false;

        // Prefer GET but allow POST for legacy compatibility
        $countryId = $this->request->query['country_id'] ?? $this->request->data('country_id');

        $states = $this->State->getStateList($countryId);

        $this->response->body(json_encode($states));
        return $this->response;
    }

    public function get_timezone_options()
    {
        $this->autoRender = false;

        $timezoneOptions = $this->getTimezoneOptions($this->request->query('country_id'));

        // Placeholder option must be declared with an empty value
        $options = '<option value="">' . __('Select a Time Zone') . '</option>';
        foreach ($timezoneOptions as $timezone => $region) {
            $options .= "<option value=\"{$timezone}\">{$region}</option>";
        }

        $this->response->body($options);
        return $this->response;
    }

    /**
     *
     */
    /*public function review()
    {
        $this->set('title_for_layout', 'Review');
        $this->set('userID', $this->params['userID']);
        if ($this->request->is('post')) {
            $this->autoRender = false;
            $error = false;
            if (empty($this->request->data['Review']['comment']) && empty($this->request->data['Review']['title'])) {
                $error = true;
            }
            if ($error) {
                $FlashError = "Review title and comment fields can't be empty";
                echo json_encode(array('error' => $FlashError, 'success' => '', 'redirect' => ''));
                //$this->setFlash($FlashError, 'error');
            } else {
                $review = $this->request->data['Review'];
                $review['user_id'] = $this->Auth->user('id');
                $review['to'] = $this->params['userID'];
                $rat = 0;
                if (isset($this->request->data['Rating']['rating'])) {
                    $rat = $this->request->data['Rating']['rating'];
                }
                $review['rating'] = $rat;
                $review['type'] = $this->Auth->user('user_type') . "_review";
                $review['created'] = date('Y-m-d H:i:s');
                if ($this->Review->save($review)) {
                    //$this->Notification->createNotification($this->Auth->User('id'), $this->params['userID'], 'Review', $this->Review->getLastInsertId());
                    $FlashSuccess = "Review added";
                    $FlashRedirect = BASE_PATH . "contact/" . $this->params['userID'];
                    $this->setFlash($FlashSuccess, 'success');
                    echo json_encode(array('error' => '', 'success' => $FlashSuccess, 'redirect' => $FlashRedirect));
                } else {
                    $FlashError = "Please try again";
                    echo json_encode(array('error' => $FlashError, 'success' => '', 'redirect' => ''));
                }
            }
        }
    }*/

    /**
     *
     */
    /*public function reviews()
    {
        $this->set('title_for_layout', 'Reviews');
        $reviews = $this->Review->find('all', array('conditions' => array('to' => $this->params['userID']), 'order' => 'rating DESC'));
        $this->set('review', $reviews);
        $this->set('user_type', $this->User->findById($this->params['userID'], array('user_type')));
        $this->set('id', $this->params['userID']);
    }*/

    /**
     * Lightspeed dropdowns options for employee, register, and store gathered from the Lightspeed API.
     *
     * @param int $retailerId
     * @return CakeResponse
     * @throws CurlException
     * @throws LightspeedOauthException
     */
    public function lightSpeedList($retailerId = null)
    {
        $this->autoRender = false;

        $retailer = $this->User->findForPosApi($retailerId);
        if (empty($retailer['User']['id'])) {
            throw new NotFoundException('Retailer not for id ' . json_encode($retailerId));
        }
        if ($retailer['User']['user_type'] !== 'Retailer') {
            throw new ForbiddenException('Invalid user type ' . json_encode($retailer['User']['user_type']));
        }
        if ($retailer['User']['inventory_type'] !== 'lightspeed_cloud') {
            throw new BadRequestException('Retailer is set to the wrong inventory type ' . json_encode($retailer['User']['inventory_type']));
        }

        $storeId = (int)$this->request->query('shopId') ?: null;

        try {
            $list = $this->Lightspeed->getAllList(
                $retailer['User']['inventory_apiuser'],
                $retailer['User']['inventory_password'],
                $storeId,
                $retailer['User']['vend_refresh_access_token'],
                $retailer['User']['vend_access_token_expires']
            );

            $data = array('Employee' => '', 'Register' => '', 'Shop' => '');

            $data['Employee'] = '<option value="">Select Employee</option>';
            foreach ($list['Employee'] as $id => $value) {
                $data['Employee'] .= "<option value=\"{$id}\">{$value}</option>";
            }

            $data['Register'] = '<option value="">Select Register</option>';
            foreach ($list['Register'] as $id => $value) {
                $data['Register'] .= "<option value=\"{$id}\">{$value}</option>";
            }

            $data['Shop'] = '<option value="">Select Store</option>';
            foreach ($list['Shop'] as $id => $value) {
                $data['Shop'] .= "<option value=\"{$id}\">{$value}</option>";
            }

            $this->response->body(json_encode(['status' => 'success', 'msg' => $data]));
            return $this->response;
        } catch (LightspeedApiException $e) {
            CakeLog::error(strval($e));
            $this->response->body(json_encode(['status' => 'error', 'msg' => $e->getXmlElement()]));
            return $this->response;
        }
    }


    public function getLightspeedTaxRate()
    {
        $retailerId = $this->request->data('retailerId');
        $shopId = $this->request->data('shopId');

        if(!$retailerId || !$shopId){
            $this->_ajaxErrorResponse('Missing required request arguments.');
            return;
        }

        $retailer = $this->User->findForPosApi($retailerId);
        $shop = $this->Lightspeed->getShopTaxRate($retailer['User']['inventory_apiuser'], $retailer['User']['inventory_password'], $shopId, $retailer['User']['vend_refresh_access_token'], $retailer['User']['vend_access_token_expires']);
        $message['taxRate'] = format_number(((float)$shop->TaxCategory->tax1Rate + (float)$shop->TaxCategory->tax2Rate) * 100, 6);

        $this->_ajaxSuccessResponse($message);
    }

    /**
     * VendPos dropdown(user, register, outlet)
     * information gathered using Vendpos api
     */
    public function vendPosList($retailerId = null)
    {
        $this->autoRender = false;

        $retailer = $this->User->findForPosApi($retailerId);
        if (empty($retailer['User']['id'])) {
            throw new NotFoundException('User not found where id=' . json_encode($retailerId));
        }
        if ($retailer['User']['user_type'] !== User::TYPE_RETAILER) {
            throw new ForbiddenException('Invalid user type: ' . json_encode($retailer['User']['user_type']));
        }
        if ($retailer['User']['inventory_type'] !== 'vend_pos') {
            throw new BadRequestException('Invalid inventory type: ' . json_encode($retailer['User']['inventory_type']));
        }

        try {
            $list = $this->VendPOS->getAllList($retailer['User']['inventory_apiuser'], $retailer['User']['inventory_password']);

            $data = [];

            $data['Employee'] = '<option value="">Select User</option>';
            foreach ($list['Employee'] as $id => $value) {
                $data['Employee'] .= "<option value=\"{$id}\">{$value}</option>";
            }

            $data['Register'] = '<option value="">Select Register</option>';
            foreach ($list['Register'] as $id => $value) {
                $data['Register'] .= "<option value=\"{$id}\">{$value}</option>";
            }

            $data['Shop'] = '<option value="">Select Outlet</option>';
            foreach ($list['Shop'] as $id => $value) {
                $data['Shop'] .= "<option value=\"{$id}\">{$value}</option>";
            }

            $this->response->body(json_encode(['status' => 'success', 'msg' => $data]));
        } catch (Exception $e) {
            CakeLog::error($e);

            $this->response->body(json_encode(['status' => 'error', 'msg' => $e->getMessage()]));
        }

        return $this->response;
    }

    public function squarePosList($retailerId = null)
    {
        $this->autoRender = false;

        $retailer = $this->User->findForPosApi($retailerId);
        if (empty($retailer['User']['id'])) {
            throw new NotFoundException('User not found where id=' . json_encode($retailerId));
        }
        if ($retailer['User']['user_type'] !== User::TYPE_RETAILER) {
            throw new ForbiddenException('Invalid user type: ' . json_encode($retailer['User']['user_type']));
        }
        if ($retailer['User']['inventory_type'] !== 'square') {
            throw new BadRequestException('Invalid inventory type: ' . json_encode($retailer['User']['inventory_type']));
        }

        try {
            $locations = $this->SquarePos->listLocations($retailer['User']['inventory_password']);

            $response = '<option value="">Select Location</option>';
            foreach ($locations as $location) {
                $response .= "<option value=\"{$location['id']}\">{$location['name']}</option>";
            }

            $this->response->body(json_encode(['status' => 'success', 'msg' => $response]));
        } catch (Exception $e) {
            CakeLog::error($e);

            $this->response->body(json_encode(['status' => 'error', 'msg' => json_decode_if_array($e->getMessage()) ?: $e->getMessage()]));
        }

        return $this->response;
    }

    /**
     * Validate given shopify pos apikey is valid or not.
     */
    public function shopifypos()
    {
        $this->autoRender = false;
        if (isset($this->request->data['key']) && isset($this->request->data['Pass']) && isset($this->request->data['Domain'])) {
            try {
                $shopInfo = $this->ShopifyPOS->getShopSettings((string)$this->request->data['Pass'], (string)$this->request->data['Domain'], [
                    'fields' => 'id',
                ]);
                if (!empty($shopInfo['id'])) {
                    echo json_encode(array('status' => 'success'));
                    exit;
                }
            } catch (Exception $e) {
                return new CakeResponse(array('body' => json_encode(array('status' => 'error')), 'status' => 200));
            }
        }
        return new CakeResponse(array('body' => json_encode(array('status' => 'error')), 'status' => 200));
    }

    public function storehours($id = null)
    {
        if (empty($id)) {
            $id = (int)$this->Auth->user('id');
        }
        $redirectUrl = ($this->Auth->user('setup_status') != 1) ? '/' : $this->referer();
        $this->_storehours($id, $redirectUrl);
    }

    public function admin_storehours($id = null)
    {
        $this->layout = 'admin';
        $this->_storehours($id, "/admin/contact/{$id}");
        $this->render('storehours', 'admin');
    }

    /**
     * Store hours page for locations and staff tabs.
     *
     * @param int $id
     * @param string $redirectUrl
     * @return CakeResponse|null
     */
    private function _storehours($id, $redirectUrl = '/')
    {
        $this->User->bindModel(array('belongsTo' => array(
            'Master' => array(
                'className' => 'User',
                'foreignKey' => 'Branch',
            ),
        )));
        $timing = $this->User->find('first', [
            'contain' => ['Master'],
            'conditions' => ['User.id' => $id],
            'fields' => [
                'User.id',
                'User.Branch',
                'User.user_type',
                'User.store_timing',
                'Master.id',
                'Master.store_timing',
            ],
        ]);
        if (empty($timing['User']['id'])) {
            throw new NotFoundException('User not found where id=' . json_encode($id));
        }
        if (!in_array($timing['User']['user_type'], [User::TYPE_RETAILER, User::TYPE_STAFF], true)) {
            throw new BadRequestException('Invalid user_type=' . json_encode($timing['User']['user_type']));
        }

        if ($this->request->is('post')) {
            if (!$this->User->updateStoreTiming($id, $this->request->data['storetiming'])) {
                return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data])), null, true, $redirectUrl);
            }
            return $this->_successResponse('Operating hours updated successfully', $redirectUrl);
        }

        $storeTiming = !empty($timing['User']['store_timing']) ? $timing['User']['store_timing'] : $timing['Master']['store_timing'];
        $store = User::decodeStoreTiming($storeTiming);

        $this->set('store', $store);
        $this->request->data['storetiming'] = $store;
    }

    /**
     * Common Shipment settings page for brand and retailer
     */
    public function shipment_dealer_options()
    {
        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), [User::TYPE_MANUFACTURER, User::TYPE_RETAILER], false);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_SHIPMENT_SETTINGS);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        if ($this->request->is('post')) {
            $redirect = '/shipment/configuration';
            if ($this->Auth->user('setup_status') != 1) {
                $redirect = '/';
            }
            $this->_shipment_dealer_options_post($this->Auth->user(), $redirect);
        }

        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        $userSetting = $this->UserSetting->findByUserId($userId, ['shipping_tax_option', 'b2b_shipping_tax_option']);
        $this->set('shippingTaxOption', $userSetting['UserSetting']['shipping_tax_option'] ?? null);
        $this->set('b2bShippingTaxOption', $userSetting['UserSetting']['b2b_shipping_tax_option'] ?? null);

        if ($this->request->is('ajax')) {
            $ajaxView = in_array($userType, User::TYPES_RETAILER, true)
                ? '/Elements/Users/<USER>/retailer_dealer_options'
                : '/Elements/Users/<USER>/brand_dealer_options';

            return $this->render($ajaxView);
        }

        return null;
    }

    public function admin_shipment_setting($userId = null)
    {
        $this->layout = 'admin';

        $authUser = $this->User->findAuthUser($userId);

        if (empty($authUser['id']) || $authUser['Branch']) {
            $this->redirect($this->referer());
        }

        $this->set('shipearly_user', ['User' => $authUser]);

        if ($this->request->is('post')) {
            $redirect = '/admin/contact/' . $userId;
            $this->_shipment_dealer_options_post($authUser, $redirect);
        }

        $userSetting = $this->UserSetting->findByUserId($userId, ['shipping_tax_option', 'b2b_shipping_tax_option']);
        $this->set('shippingTaxOption', $userSetting['UserSetting']['shipping_tax_option'] ?? null);
        $this->set('b2bShippingTaxOption', $userSetting['UserSetting']['b2b_shipping_tax_option'] ?? null);
    }

    private function _shipment_dealer_options_post(array $user, string $redirect): ?CakeResponse
    {
        $userId = (int)$user['id'];
        $userType = (string)$user['user_type'];

        if ($userType === User::TYPE_MANUFACTURER) {
            $this->request->data['User'] = ['id' => $userId] + $this->request->data['User'];

            if (isset($this->request->data['User']['local_delivery_shipping'])) {
                $this->request->data['User']['local_delivery_shipping'] = (float)$this->request->data['User']['local_delivery_shipping'];
                if ($this->request->data['User']['local_delivery_shipping_option'] === RateOption::PERCENT) {
                    $this->request->data['User']['local_delivery_shipping'] /= 100;
                }
            }

            $uploadImage = function(string $key) use ($user) {
                $fileUrl = null;
                if (!empty($this->request->data['User'][$key])) {
                    $fileUrl = $this->UserLogic->replaceUserImage(
                        $user[$key],
                        (array)$this->request->data['User'][$key],
                        (string)$user['uuid'],
                        $key
                    );
                }
                if ($fileUrl) {
                    // Save immediately to avoid losing the file if an error occurs later
                    if (!$this->User->save(['id' => $user['id'], $key => $fileUrl])) {
                        throw new InternalErrorException(json_encode([
                            'message' => 'Failed to save an uploaded image',
                            'errors' => $this->User->validationErrors,
                            'data' => $this->User->data,
                        ]));
                    }
                }
                unset($this->request->data['User'][$key]);
            };
            try {
                $uploadImage('wholesale_topup_infographic');
            } catch (Exception $e) {
                return $this->_exceptionResponse(($e instanceof HttpException) ? $e : null, __('Failed to save an uploaded image'), $e);
            }

            if (!empty($this->request->data['UserSetting'])) {
                $userSettingId = (int)$this->UserSetting->field('id', ['user_id' => $userId]);
                $date = $this->date();
                $userSettingKeys = [
                    'id' => $userSettingId,
                    'updated_at' => $date,
                ];
                if (!$userSettingId) {
                    $userSettingKeys['created_at'] = $date;
                }
                $this->request->data['UserSetting'] = $userSettingKeys + $this->request->data['UserSetting'];
            }

            $fieldList = [
                'User' => [
                    'id',
                    'instore',
                    'shiptostore_free_shipping',
                    'shipment',
                    'shipfromstore_instock_only',
                    'ship_from_store_double_ship',
                    'sell_direct',
                    'sell_direct_authorize',
                    'install_rate_option',
                    'install_flat_rate',
                    'local_delivery',
                    'local_delivery_shipping_title',
                    'local_delivery_shipping_option',
                    'local_delivery_shipping',
                    'local_delivery_percent_source',
                    'local_delivery_no_retailer_listing',
                    'enable_wholesale_topup',
                ],
                'UserSetting' => [
                    'id',
                    'shipping_tax_option',
                    'b2b_shipping_tax_option',
                    'created_at',
                    'updated_at',
                ],
            ];
            $this->User->bindModel(['hasOne' => ['UserSetting']], false);
            $success = (bool)$this->User->saveAssociated($this->request->data, ['fieldList' => $fieldList]);
            $this->User->unbindModel(['hasOne' => ['UserSetting']], false);
        } elseif ($userType === User::TYPE_RETAILER) {
            $saveMany = array_map(function(int $locationId) use ($userId) {
                $data = ['id' => $locationId] + $this->request->data['User'];

                if (isset($data['do_not_list']) && ($locationId !== $userId)) {
                    // Only allow master retailers to be hidden with this field
                    $data['do_not_list'] = false;
                }

                return $data;
            }, $this->User->listRetailerStoreIds($userId));

            $fieldList = [
                'id',
                'instore',
                'shiptostore_tax',
                'do_not_list',
            ];
            $success = (bool)$this->User->saveMany($saveMany, ['fieldList' => $fieldList]);
        } else {
            throw new BadRequestException('Invalid user_type: ' . json_encode($userType));
        }

        if (!$success) {
            return $this->_exceptionResponse(new InternalErrorException(json_encode([
                'message' => 'Failed to save shipment dealer options',
                'errors' => $this->User->validationErrors,
                'data' => $this->User->data,
            ])), null, true, $redirect);
        }

        return $this->_successResponse(__('Shipment Settings updated successfully'), $redirect);
    }

    public function shipment_setting()
    {
        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), [User::TYPE_MANUFACTURER, User::TYPE_RETAILER], false);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_SHIPMENT_SETTINGS);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        $userId = (int)$this->Auth->user('id');

        $shippingOrigin = Hash::get($this->UserAddress->findShippingOrigin($userId), 'UserAddress');
        $shippingOrigin['state'] = $this->State->getStateName($shippingOrigin['state_id'], $shippingOrigin['country_id']);
        $shippingOrigin['country'] = $this->Country->getCountryName($shippingOrigin['country_id']);
        $this->set('shippingOrigin', $shippingOrigin);
        $this->set('states', $this->State->getStateList($shippingOrigin['country_id']));
        $this->set('country', $this->Country->getCountryList());

        $allzonedata = array_map(
            function($value) {
                $zoneFields = array_intersect_key($value['ShippingZone'], array_flip(['id', 'uuid', 'type', 'name']));

                $countries = $this->Country->getCountriesAndIconsByIds(json_decode($value['ShippingZone']['country_codes'], true));
                $countryFields = array(
                    'countries' => implode(', ', Hash::extract($countries, '{n}.Country.country_name')),
                    'countryflag' => Hash::get((array)current($countries), 'Country.country_icon'),
                );

                return $zoneFields + $countryFields;
            },
            $this->ShippingZone->getAllZones($userId)
        );

        $zonedata = array_filter($allzonedata, function($value) {
            return $value['type'] === ShippingZone::TYPE_CONSUMER;
        });
        $this->set('zonedata', $zonedata);

        $this->set('zonedata_wholesale', $this->B2bShippingZone->findForIndex($userId));

        $this->set('usercarriers', $this->UserShippingCarrier->getAllCarriers($userId));
        $this->set('usershippingpackages', $this->UserShippingPackage->getAllUserPackages($userId));
    }

    public function notification_settings_menu()
    {
        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_NOTIFICATION_SETTINGS);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        $templateGroups = array(
            'General' => array(
                'Order Refund',
                'Abandon cart customer notification',
                'Registration Mail',
                'Retailer Approval',
            ),
            'B2B' => array(
                'Purchase Order Confirmation',
            ),
            'In-Store Pickup' => array(
                'Instore Code',
                'Nonstock Instore Code',
                'NonStock Customer',
                'Dealer Order Customer Notifications',
                'Dealer Order Customer Notifications Without Track',
                'In-Store Pickup with Scheduling',
            ),
            'Local Delivery' => array(
                'Local Delivery, Instore Code',
                'Local Delivery, Nonstock Instore Code',
                'Local Delivery, NonStock Customer',
                'Local Delivery, Dealer Order Customer Notifications',
                'Local Delivery, Dealer Order Customer Notifications Without Track',
                'Local Delivery with Scheduling',
            ),
            'Ship From Store' => array(
                'Shipfromstore Customer',
                'Shipfromstore Tracking Customer',
                'Non-Stocking Ship from Store',
                'Shipfromstore Double Ship Tracking Customer',
            ),
            'Direct' => array(
                'Direct Order Fulfillment',
                'Direct Order Refund',
            ),
            'Commission Orders' => array(
                'Commission Order Retailer',
            ),
        );
        $emailTemplateGroups = array_map(function($templateNames) {
            return array_map([$this->EmailTemplate, 'getEmailTemplate'], $templateNames);
        }, $templateGroups);

        $this->set('title_for_layout', 'Notifications');
        $this->set('emailTemplateGroups', $emailTemplateGroups);
    }

    public function notification_settings($id = null)
    {
        $this->set(compact('id'));

        // Bypass TranslateBehavior with joinType=INNER
        $this->EmailTemplate->locale = '';
        if (!$this->EmailTemplate->exists(['EmailTemplate.id' => $id])) {
            throw new NotFoundException();
        }

        try {
            $this->Permissions->assertUserIsType($this->Auth->user(), User::TYPE_MANUFACTURER);
            $this->Permissions->assertUserHasPermission($this->Auth->user(), Permissions::NAME_NOTIFICATION_SETTINGS);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        $language = $this->request->query('lang') ?? 'eng';
        $this->EmailTemplate->locale = $language;
        $this->UserEmailTemplate->locale = $language;

        $userId = $this->Auth->user('id');
        $existing = $this->EmailTemplate->findForNotificationSetting($id, $userId, [
            'EmailTemplate.id',
            'EmailTemplate.subject',
            'EmailTemplate.content',
            'EmailTemplate.template_alias',
        ], [
            'UserEmailTemplate.id',
            'UserEmailTemplate.subject',
            'UserEmailTemplate.content',
            'UserEmailTemplate.attachment',
        ]);
        $oldAttachmentUrl = $existing['UserEmailTemplate']['attachment'] ?? '';

        if ($this->request->is(['post', 'put'])) {
            $keyFields = [
                'id' => $existing['UserEmailTemplate']['id'] ?? null,
                'user_id' => $userId,
                'email_template_id' => $id,
            ];
            $this->request->data['UserEmailTemplate'] = $keyFields + $this->request->data['UserEmailTemplate'];

            if ($this->request->data['UserEmailTemplate']['attachment']['error'] === UPLOAD_ERR_NO_FILE) {
                if (empty($this->request->data['UserEmailTemplate']['attachment_filename'])) {
                    $this->Upload->deleteFromWebroot($oldAttachmentUrl);
                    $this->request->data['UserEmailTemplate']['attachment'] = '';
                } else {
                    unset($this->request->data['UserEmailTemplate']['attachment']);
                }
            } else {
                try {
                    $this->request->data['UserEmailTemplate']['attachment'] = $this->Upload->replaceFileInUserHash(
                        $oldAttachmentUrl,
                        (array)$this->request->data['UserEmailTemplate']['attachment'],
                        (string)$this->Auth->user('uuid'),
                        'email_attachment' . DS . $id,
                        $this->request->data['UserEmailTemplate']['attachment']['name']
                    );
                } catch (Exception $e) {
                    CakeLog::error($e);
                    $this->setFlash('There was an error uploading your attachment. Please, try again.', 'error');
                    unset($this->request->data['UserEmailTemplate']['attachment']);
                }
            }

            $skipNewlineConversion = EmailTemplate::getSkipNewlineConversion($this->request->data['UserEmailTemplate']['content']);
            if (!$skipNewlineConversion) {
                $this->request->data['UserEmailTemplate']['content'] .= PHP_EOL . EmailTemplate::SKIP_NEWLINE_CONVERSION_SNIPPET;
            }

            if (!$this->UserEmailTemplate->save($this->request->data)) {
                return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->UserEmailTemplate->validationErrors, 'data' => $this->UserEmailTemplate->data])), null, true);
            }

            return $this->_successResponse('The email template has been updated.');
        } elseif ($this->request->is('delete')) {
            /** @var I18nModel $I18nModel */
            $I18nModel = ClassRegistry::init('I18nModel');
            $deleted = (
                $this->UserEmailTemplate->deleteAllJoinless(['UserEmailTemplate.id' => $existing['UserEmailTemplate']['id'] ?? null], false)
                && $I18nModel->deleteAllJoinless(['I18nModel.model' => 'UserEmailTemplate', 'I18nModel.foreign_key' => $existing['UserEmailTemplate']['id'] ?? null], false)
            );
            if (!$deleted) {
                $message = __('The email template could not be reset. Please, try again.');

                return $this->_exceptionResponse(new InternalErrorException($message), $message, true);
            }
            $this->Upload->deleteFromWebroot($oldAttachmentUrl);

            return $this->_successResponse('The email template has been reset.');
        }

        $this->request->data = $existing;
        foreach (['subject', 'content'] as $field) {
            if (empty($this->request->data['UserEmailTemplate'][$field]) && !empty($this->request->data['EmailTemplate'][$field])) {
                $this->request->data['UserEmailTemplate'][$field] = $this->request->data['EmailTemplate'][$field];
            }
        }

        $this->set('title_for_layout', $this->request->data['EmailTemplate']['template_alias'] . ' Settings');
    }

    public function payouts()
    {
        try {
            $authUser = User::revertAuthParent($this->Auth->user());
            $this->Permissions->assertUserIsType($authUser, [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP, User::TYPE_RETAILER]);
        } catch (ForbiddenException $e) {
            return $this->_permissionDeniedResponse($e);
        }

        $userId = (int)$this->Auth->user('id');

        $stripeUser = $this->StripeUser->getStatusFields($userId);
        $stripeUserId = $stripeUser['StripeUser']['id'] ?? null;
        $stripeUserCapabilities = $this->StripeUserCapability->getUserCapabilities($stripeUserId);

        if ($this->request->is(['post', 'put'])) {
            $response = $this->_payouts_post($userId, $stripeUserId);
            if ($response) {
                return $response;
            }
        } else {
            $this->request->data = [
                'User' => $this->Auth->user(),
                'StripeUser' => ['id' => $stripeUserId, 'Capabilities' => $stripeUserCapabilities],
            ];
        }

        $this->set('title_for_layout', __('Payouts'));

        $this->set('stripeUser', $stripeUser);
        $this->set('stripeUserCapabilities', $stripeUserCapabilities);

        // get 2 character lang code for widget locale
        $l10n = I18n::getInstance()->l10n;
        list($lang) = explode('-', $l10n->get());

        $querystring = http_build_query([
            'key' => PAYMENT_RAILS_ACCESS_KEY,
            'email' => $this->Auth->user('email_address'),
            'refid' => $this->Auth->user('email_address'),
            'hideEmail' => 'true',
            'addr.name' => $this->Auth->user('company_name'),
            'addr.firstName' => $this->Auth->user('first_name'),
            'addr.lastName' => $this->Auth->user('last_name'),
            'addr.street1' => $this->Auth->user('address1'),
            'addr.street2' => $this->Auth->user('address2'),
            'addr.city' => $this->Auth->user('city'),
            'addr.postalCode' => $this->Auth->user('zipcode'),
            'addr.region' => $this->State->getStateCode($this->Auth->user('state_id')),
            'addr.country' => $this->Country->getCountryCode($this->Auth->user('country_id')),
            'colors.background' => '#ebebeb',
            'ts' => time(),
            'locale' => $lang,
        ]);
        $signature = hash_hmac('sha256', $querystring, PAYMENT_RAILS_SECRET_KEY);

        $this->set('widget_link', "https://widget.paymentrails.com?{$querystring}&sign={$signature}");
    }

    protected function _payouts_post(int $userId, ?int $stripeUserId): ?CakeResponse
    {
        if (!empty($this->request->data['User'])) {
            $this->request->data['User']['id'] = $userId;
        }

        if (!empty($this->request->data['StripeUser'])) {
            $this->request->data['StripeUser']['id'] = $stripeUserId;

            // FormHelper multi-select checkboxes send single or empty values as a string
            $this->request->data['StripeUser']['Capabilities'] = array_filter((array)$this->request->data['StripeUser']['Capabilities']);
            // Add always true disabled field
            $this->request->data['StripeUser']['Capabilities'][] = StripeCapability::CARD_PAYMENTS;
        }

        $userSuccess = (
            empty($this->request->data['User']['id']) ||
            $this->User->save($this->request->data['User'], [
                'validate' => true,
                'fieldList' => ['enable_affirm_financing'],
            ])
        );
        $stripeUserSuccess = (
            empty($this->request->data['StripeUser']['id']) ||
            $this->StripeUserCapability->saveActiveCapabilityNames($this->request->data['StripeUser']['id'], $this->request->data['StripeUser']['Capabilities'])
        );

        if ($userSuccess && $stripeUserSuccess) {
            return $this->_successResponse();
        }

        if ($this->request->is('ajax')) {
            return $this->_exceptionResponse();
        }

        return null;
    }

    /**
     * Resend activation mail from login page
     *
     * @param int|null $id
     */
    public function resendLink($id = null)
    {
        if ($this->User->exists(['User.id' => $id])) {
            $this->UserLogic->activationMail($id);
            $this->setFlash('Activation mail sent to your email address. Please verify your account.', 'success');
        }
        $this->redirect(BASE_PATH . 'login');
    }

    /**
     * Change password page for Shipearly user both retailer and brand
     */
    public function reset_password($token = null): ?CakeResponse
    {
        $tokenState = $this->PasswordReset->retrieveTokenState($token);
        $id = isset($tokenState['id']) ? (int)$tokenState['id'] : null;
        if (!$id) {
            return $this->_exceptionResponse(
                new UnauthorizedException('Password reset link expired'),
                __('Your password reset link has expired. Please try again.'),
                false,
                ['controller' => 'users', 'action' => 'forgot_password']
            );
        }

        if (!$this->User->existsById($id)) {
            throw new NotFoundException('User not found where id=' . json_encode($id));
        }

        if ($this->request->is(['post', 'put']) && !empty($this->request->data['User']['new_password'])) {
            $newPassword = $this->request->data['User']['new_password'];
            $this->request->data = [];

            $data = [
                'id' => $id,
                'password' => User::passwordHasher()->hash($newPassword),
            ];
            if ($this->User->save($data)) {
                return $this->_successResponse(__('Your account has been updated successfully'), $this->Auth->loginAction);
            }
        }

        return null;
    }

    /**
     * Shipearly Subscription page for brand
     * Shipearly plan hard coded as CORE PLAN
     * @return CakeResponse
     */
    public function acc_subscription()
    {
        $this->set('title_for_layout', __('Shipearly subscription'));
        $user = $this->User->get($this->Auth->user('id'), ['fields' => ['id', 'currency_code', 'email_address', 'coupon_code']]);
        $this->set('user', $user);
        $stripeCustomerId = $this->StripeUser->getStripeUserFields($user['User']['id'], array('stripe_cus_id'));

        $this->set('subscribed', true);
        if ($this->Auth->user('status') == 'Suspend') {
            $this->set('subscribed', false);
        } elseif (empty($stripeCustomerId['StripeUser']['stripe_cus_id'])) {
            $this->set('subscribed', false);
        }
        if ($this->request->is('post')) {
            $id = $this->request->query['id'];
            // Get the credit card details submitted by the form
            $token = $this->request->data['id'];
            $card = $this->request->data['card']['id'];
            $stripeEmail = $this->request->data['email'];

            try {
                $stripeCustomerId = $this->StripeUser->getStripeUserFields($this->Auth->user('id'), array('stripe_cus_id'));
                if ($this->Auth->user('status') == 'Suspend') {
                    if(isset($stripeCustomerId['StripeUser']['stripe_cus_id']) && $stripeCustomerId['StripeUser']['stripe_cus_id'] !=null) {
                        $customer = $this->Stripe->updateCustomer($stripeCustomerId['StripeUser']['stripe_cus_id'], $token);
                    }
                } else {
                    $customer = $this->Stripe->createCustomer($token, $stripeEmail, $id, $user['User']['coupon_code']);
                }

                if ($customer) {
                    $cus = array();
                    $cus['stripe_cus_id'] = $customer->id;
                    $cus['created'] = date('Y-m-d H:i:s');
                    foreach ($customer->subscriptions->data as $value) {
                        $subscription_period_start = $value->current_period_start;
                        $subscription_period_end = $value->current_period_end;
                    }
                    $stripeUserId = $this->StripeUser->getStripeUserId($id);
                    if (!empty($stripeUserId)) {
                        if ($this->StripeUser->save(array('StripeUser' => array('id' => $stripeUserId, 'user_id' => $id, 'stripe_cus_id' => $cus['stripe_cus_id'], 'created' => $cus['created'], 'subscription_period_start' => date('Y-m-d H:i:s', $subscription_period_start), 'subscription_period_end' => date('Y-m-d H:i:s', $subscription_period_end))))) {
                            $this->setFlash("Your subscription has been activated successfully", 'success');
                        }
                    } else {
                        if ($this->StripeUser->save(array('StripeUser' => array('user_id' => $id, 'stripe_cus_id' => $cus['stripe_cus_id'], 'created' => $cus['created'], 'subscription_period_start' => date('Y-m-d H:i:s', $subscription_period_start), 'subscription_period_end' => date('Y-m-d H:i:s', $subscription_period_end))))) {
                            $this->setFlash("Your subscription has been activated successfully", 'success');
                        }
                    }
                    $url = BASE_PATH . 'shipearly/subscription';

                    if ($this->shipearly_user['User']['status'] == 'Suspend') {
                        $this->User->directActive($this->Auth->user('id'));
                        $this->syncUserSession($this->Auth->user('id'));
                        $this->setFlash("Your subscription and account has been activated successfully", 'success');
                    }

                    if ($this->shipearly_user['User']['setup_status'] != 1) {
                        $url = BASE_PATH;
                    }
                    return new CakeResponse(array('body' => json_encode(array('redirectUrl' => $url)), 'status' => 200));
                }
            } catch (Exception $e) {
                $errorMessage = $e->getMessage();
                if($errorMessage == "Your card was declined.") {
                    $this->setFlash($errorMessage, 'error');
                    return new CakeResponse(array('body' => json_encode(array('responseStatus' => 'error', 'errorMessage' => $errorMessage)), 'status' => 200));
                }
            }
        }
    }

    /**
     * Connect lightspeed using oAuth and collect access token
     */
    public function lightSpeedConnect($retailerId = null)
    {
        if (!empty($this->request->query['code']) && !empty($this->request->query['state'])) {
            $oauthState = $this->Lightspeed->retrieveOAuthState((string)$this->request->query['state']);
            $retailerId = isset($oauthState['user_id']) ? (int)$oauthState['user_id'] : null;
            $return_url = isset($oauthState['return_url']) ? (array)$oauthState['return_url'] : null;

            if (!$return_url) {
                return $this->_exceptionResponse(
                    new UnauthorizedException(json_encode(['message' => 'OAuth state token expired', 'query' => $this->request->query])),
                    __('Lightspeed authorization expired, please try again.'),
                    true,
                    ['controller' => 'users', 'action' => 'inventory_settings']
                );
            }

            // Redirect to the correct subdomain for session consistency before processing the OAuth response
            if ((string)$this->request->param('subdomain') !== (string)($return_url['subdomain'] ?? '')) {
                return $this->redirect(Router::reverse(array_merge($this->request->params, [
                    'subdomain' => (string)($return_url['subdomain'] ?? ''),
                    'url' => $this->request->query,
                ]), true));
            }

            // Override referrer for default response redirects
            $this->request->query['referer'] = Router::url($return_url, true);

            if (!$this->User->exists(['User.id' => $retailerId, 'User.user_type' => User::TYPE_RETAILER])) {
                return $this->_exceptionResponse(new NotFoundException(sprintf('Location not found where id=%s', json_encode($retailerId))), __('Location not found.'), true);
            }

            $result = $this->Lightspeed->getAccessToken((string)$this->request->query['code']);
            if (empty($result['access_token'])) {
                return $this->_exceptionResponse(
                    new InternalErrorException(json_encode(['message' => 'OAuth access token not found', 'query' => $this->request->query, 'result' => $result])),
                    __('Invalid Lightspeed account information.'),
                    true
                );
            }

            $user = [
                'id' => $retailerId,
                'inventory_apiuser' => $result['access_token'],
                'inventory_password' => $this->Lightspeed->getAccessTokenAccountId($result),
                'inventory_type' => 'lightspeed_cloud',
                'vend_access_token_expires' => date(DATETIME_FORMAT_SQL, $this->time() + (int)$result['expires_in']),
                'vend_refresh_access_token' => $result['refresh_token'],
            ];
            if ($this->User->save($user)) {
                return $this->_successResponse(__('Your Lightspeed account has been connected successfully.'));
            }

            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data])), null, true);
        }

        $retailerId = (int)($retailerId ?: $this->Auth->user('id'));

        if (!$this->User->exists(['User.id' => $retailerId, 'User.user_type' => User::TYPE_RETAILER])) {
            return $this->_exceptionResponse(new NotFoundException(sprintf('Location not found where id=%s', json_encode($retailerId))), __('Location not found.'));
        }

        $stateToken = $this->Lightspeed->createOAuthState([
            'user_id' => $retailerId,
            'return_url' => array_filter([
                'controller' => 'users',
                'action' => 'inventory_settings',
                'id' => $this->request->param('retailer_id'),
                'subdomain' => $this->request->param('subdomain'),
            ]),
        ]);

        return $this->redirect($this->Lightspeed->getAuthorizeUrl($stateToken));
    }

    /**
     * Connect vendPos using oAuth and collect access token
     */
    public function vendPosConnect($retailerId = null)
    {
        if (!empty($this->request->query['domain_prefix'])) {
            $this->VendPOS->domainPrefix = $this->request->query['domain_prefix'];
        }

        if (!empty($this->request->query['code']) && !empty($this->request->query['state'])) {
            $oauthState = $this->VendPOS->retrieveOAuthState((string)$this->request->query['state']);
            $retailerId = isset($oauthState['user_id']) ? (int)$oauthState['user_id'] : null;
            $return_url = isset($oauthState['return_url']) ? (array)$oauthState['return_url'] : null;

            if (!$return_url) {
                return $this->_exceptionResponse(
                    new UnauthorizedException(json_encode(['message' => 'OAuth state token expired', 'query' => $this->request->query])),
                    __('LightSpeed Retail (X-Series) authorization expired, please try again.'),
                    true,
                    ['controller' => 'users', 'action' => 'inventory_settings']
                );
            }

            // Redirect to the correct subdomain for session consistency before processing the OAuth response
            if ((string)$this->request->param('subdomain') !== (string)($return_url['subdomain'] ?? '')) {
                return $this->redirect(Router::reverse(array_merge($this->request->params, [
                    'subdomain' => (string)($return_url['subdomain'] ?? ''),
                    'url' => $this->request->query,
                ]), true));
            }

            // Override referrer for default response redirects
            $this->request->query['referer'] = Router::url($return_url, true);

            if (!$this->User->exists(['User.id' => $retailerId, 'User.user_type' => User::TYPE_RETAILER])) {
                return $this->_exceptionResponse(new NotFoundException(sprintf('Location not found where id=%s', json_encode($retailerId))), __('Location not found.'), true);
            }

            $result = $this->VendPOS->getAccessToken((string)$this->request->query['code']);
            if (empty($result['access_token'])) {
                return $this->_exceptionResponse(
                    new InternalErrorException(json_encode(['message' => 'OAuth access token not found', 'query' => $this->request->query, 'result' => $result])),
                    __('Invalid LightSpeed Retail (X-Series) account information.'),
                    true
                );
            }

            $user = [
                'id' => $retailerId,
                'inventory_apiuser' => $result['access_token'],
                'inventory_password' => $this->VendPOS->getAccessTokenAccountId($result),
                'inventory_type' => 'vend_pos',
                'vend_access_token_expires' => date(DATETIME_FORMAT_SQL, (int)$result['expires']),
                'vend_refresh_access_token' => $result['refresh_token'],
            ];
            if ($this->User->save($user)) {
                return $this->_successResponse(__('Your LightSpeed Retail (X-Series) account has been connected successfully.'));
            }

            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data])), null, true);
        }

        $retailerId = (int)($retailerId ?: $this->Auth->user('id'));

        if (!$this->User->exists(['User.id' => $retailerId, 'User.user_type' => User::TYPE_RETAILER])) {
            return $this->_exceptionResponse(new NotFoundException(sprintf('Location not found where id=%s', json_encode($retailerId))), __('Location not found.'));
        }

        $stateToken = $this->VendPOS->createOAuthState([
            'user_id' => $retailerId,
            'return_url' => array_filter([
                'controller' => 'users',
                'action' => 'inventory_settings',
                'id' => $this->request->param('retailer_id'),
                'subdomain' => $this->request->param('subdomain'),
            ]),
        ]);

        return $this->redirect($this->VendPOS->getAuthorizeUrl($stateToken));
    }

    public function squareConnect($retailerId = null)
    {
        if (!empty($this->request->query['code']) && !empty($this->request->query['state'])) {
            $oauthState = $this->SquarePos->retrieveOAuthState((string)$this->request->query['state']);
            $retailerId = isset($oauthState['user_id']) ? (int)$oauthState['user_id'] : null;
            $return_url = isset($oauthState['return_url']) ? (array)$oauthState['return_url'] : null;

            if (!$return_url) {
                return $this->_exceptionResponse(
                    new UnauthorizedException(json_encode(['message' => 'OAuth state token expired', 'query' => $this->request->query])),
                    __('Square authorization expired, please try again.'),
                    true,
                    ['controller' => 'users', 'action' => 'inventory_settings']
                );
            }

            // Redirect to the correct subdomain for session consistency before processing the OAuth response
            if ((string)$this->request->param('subdomain') !== (string)($return_url['subdomain'] ?? '')) {
                return $this->redirect(Router::reverse(array_merge($this->request->params, [
                    'subdomain' => (string)($return_url['subdomain'] ?? ''),
                    'url' => $this->request->query,
                ]), true));
            }

            // Override referrer for default response redirects
            $this->request->query['referer'] = Router::url($return_url, true);

            if (!$this->User->exists(['User.id' => $retailerId, 'User.user_type' => User::TYPE_RETAILER])) {
                return $this->_exceptionResponse(new NotFoundException(sprintf('Location not found where id=%s', json_encode($retailerId))), __('Location not found.'), true);
            }

            $result = $this->SquarePos->getAccessToken((string)$this->request->query['code']);
            if (empty($result['access_token'])) {
                return $this->_exceptionResponse(
                    new InternalErrorException(json_encode(['message' => 'OAuth access token not found', 'query' => $this->request->query, 'result' => $result])),
                    __('Invalid Square account information.'),
                    true
                );
            }

            $user = [
                'id' => $retailerId,
                'inventory_apiuser' => $this->SquarePos->getAccessTokenAccountId($result),
                'inventory_password' => $result['access_token'],
                'inventory_type' => 'square',
                'vend_access_token_expires' => $result['expires_at'],
                'vend_refresh_access_token' => null,
            ];
            if ($this->User->save($user)) {
                return $this->_successResponse(__('Your Square account has been connected successfully.'));
            }

            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->User->validationErrors, 'data' => $this->User->data])), null, true);
        }

        $retailerId = (int)($retailerId ?: $this->Auth->user('id'));

        if (!$this->User->exists(['User.id' => $retailerId, 'User.user_type' => User::TYPE_RETAILER])) {
            return $this->_exceptionResponse(new NotFoundException(sprintf('Location not found where id=%s', json_encode($retailerId))), __('Location not found.'));
        }

        $stateToken = $this->SquarePos->createOAuthState([
            'user_id' => $retailerId,
            'return_url' => array_filter([
                'controller' => 'users',
                'action' => 'inventory_settings',
                'id' => $this->request->param('retailer_id'),
                'subdomain' => $this->request->param('subdomain'),
            ]),
        ]);

        return $this->redirect($this->SquarePos->getAuthorizeUrl($stateToken));
    }

    /**
     * Choosing area of interest
     * 3 max category for Brand
     * unlimited for Retailer  - Done for only retailer not yet for brand
     */
    public function admin_account_setting()
    {
        $this->layout = 'admin';
        $userId = $this->request->id;
        $cat = $this->Category->getAllCategory();
        $this->set('cat', $cat);        
        $user = $this->User->record($userId);
        $selecedCat = $this->UserCategories->getUserCategories($userId);
        $catList = $this->Category->getCategoryById($selecedCat);
        $user['User']['user_categories'] = $catList;
        $this->shipearly_user = $user;
        $this->set('shipearly_user', $this->shipearly_user);
        $this->set('seleced_cat', array_keys($catList));
        if ($this->request->is('post')) {
            $this->UserLogic->UpdateUserCat(array_keys($catList), array_keys($this->request->data['cat']), $userId, $this->shipearly_user['User']['user_type'], $this->shipearly_user['User']['Branch'], true);
            $this->redirect('/admin/contact/'.$userId);
        }
    }

    /**
     * For Retailer Inventory configuration page on admin page
     */
    public function admin_configuration()
    {
        $this->layout = 'admin';
        $userId = (int)$this->request->params['id'];
        $user = $this->User->get($userId);
        $this->Permissions->assertUserIsType($user['User'], User::TYPE_RETAILER);
        $this->shipearly_user = $user;

        $selecedCat = $this->UserCategories->getUserCategories($userId);
        $catList = $this->Category->getCategoryById($selecedCat);

        if ($this->request->is('post')) {
            $this->request->data['User']['id'] = $this->shipearly_user['User']['id'];
            unset($this->request->data['User']['shop_url']);
            unset($this->request->data['User']['api_key']);
            unset($this->request->data['User']['secret_key']);
            unset($this->request->data['User']['company_code']);
            unset($this->request->data['User']['site_type']);

            if (isset($this->request->data['User']['inventory_type'])) {
                if ($this->request->data['User']['inventory_type'] == 'none') {
                    $this->request->data['User']['inventory_type'] = 'other';
                    $this->request->data['User']['otherInventory'] = 'None';
                }
                elseif ($this->request->data['User']['inventory_type'] !== 'other') {
                    $this->request->data['User']['otherInventory'] = '';
                }
            }
            $this->User->validator()->remove('company_code');
            $this->User->validator()->remove('shop_url');
            $this->User->validator()->remove('api_key');
            $this->User->validator()->remove('secret_key');
            $this->User->validator()->remove('site_type');     
            if ($this->User->updateUser($this->request->data)) {
                $this->setFlash(__('Your account has been updated successfully'), 'success');

                $user = $this->User->record($this->User->id);
                /** update user categories **/
                $this->request->data['User']['user_categories'] = $catList;
                if (isset($this->request->data['User']['inventory_type']) && $this->request->data['User']['inventory_type'] == 'quickbook_pos' && $this->shipearly_user['User']['user_type'] == 'Retailer') {
                    $user['User']['quickbook_priceLevel'] = $this->request->data['User']['quickbook_priceLevel'];
                }
                $this->redirect('/admin/contact/'.$userId);
            }
        }

        $this->set('title_for_layout', __('Inventory configuration') . ' : ' . $this->shipearly_user['User']['company_name']);
        $this->set('shipearly_user', $this->shipearly_user);
    }

    public function integrations()
    {
        $authUser = $this->Auth->user();

        $this->Permissions->assertUserIsType($authUser, [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF]);
        $this->Permissions->assertUserHasPermission($authUser, Permissions::NAME_INTEGRATION_SETTINGS, Permissions::LEVEL_EDIT);

        if ($this->request->is(['post', 'put'])) {
            if ($this->User->saveFromIntegrations($authUser['id'], $this->request->data)) {
                return $this->_successResponse();
            } else {
                $this->setFlash($this->_buildFlashMessage('error'), 'error');
            }
        } else {
            $this->request->data = $this->User->findForIntegrations($authUser['id']);
        }

        return null;
    }

}
