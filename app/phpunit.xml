<?xml version="1.0" encoding="UTF-8"?>
<phpunit
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/5.7/phpunit.xsd"
  backupGlobals="false"
  beStrictAboutCoversAnnotation="true"
  beStrictAboutOutputDuringTests="true"
  beStrictAboutTestsThatDoNotTestAnything="true"
  beStrictAboutTodoAnnotatedTests="true"
>
  <filter>
    <whitelist addUncoveredFilesFromWhitelist="false">
      <directory suffix=".php">./</directory>
      <exclude>
        <directory>./Plugin/*/Test/</directory>
        <directory>./Plugin/*/tmp/</directory>
        <directory>./Plugin/*/Vendor/</directory>
        <directory>./Plugin/*/webroot/</directory>
        <directory>./Test/</directory>
        <directory>./tmp/</directory>
        <directory>./Vendor/</directory>
        <directory>./webroot/</directory>
        <file>./index.php</file>
      </exclude>
    </whitelist>
  </filter>
  <logging>
    <log type="junit" target="./tmp/test_logs/logfile.xml" logIncompleteSkipped="false"/>
    <log type="testdox-text" target="./tmp/test_logs/testdox.txt"/>
<!--    <log type="coverage-html" target="./tmp/test_logs/coverage-html" />-->
<!--    <log type="coverage-text" target="php://stdout" showOnlySummary="true" />-->
  </logging>
</phpunit>
