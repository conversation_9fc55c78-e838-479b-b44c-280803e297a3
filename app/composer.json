{"name": "shipearly/shipearlyapp", "description": "ShipEarly core application", "license": "proprietary", "config": {"sort-packages": true, "vendor-dir": "Vendor/"}, "require": {"php": ">=7.4.0", "ext-SimpleXML": "*", "ext-curl": "*", "ext-gd": "*", "ext-imap": "*", "ext-json": "*", "ext-pdo": "*", "aftership/aftership-php-sdk": "^5.1", "automattic/woocommerce": "^3.1", "box/spout": "^3.1", "cakephp/cakephp": "2.10.*", "cakephp/localized": "^2.7", "cakephp/utility": "3.8.*", "cakephp/validation": "3.8.*", "consolibyte/quickbooks": "^3.6", "dereuromark/cakephp-shim": "0.7.*", "endroid/qr-code": "^4.6", "etubaro/cakephp-eager-loader": "^0.4.2", "facebook/php-business-sdk": "^16.0", "fortawesome/font-awesome": "^6.0", "geocoder-php/cache-provider": "^4.4", "geocoder-php/google-maps-provider": "^4.7", "google/cloud-logging": "^1.22", "google/cloud-storage": "^1.25", "guzzlehttp/guzzle": "^7.8", "http-interop/http-factory-guzzle": "^1.2", "ipinfo/ipinfo": "^2.0", "josegonzalez/dotenv": "^3.2", "knplabs/knp-snappy": "^1.3", "league/csv": "^9.6", "mobiledetect/mobiledetectlib": "^3.74", "oomphinc/composer-installers-extender": "^2.0", "php-http/guzzle7-adapter": "^1.0", "php-http/message": "^1.0", "phpoffice/phpspreadsheet": "^1.5", "phpseclib/phpseclib": "^3.0", "pragmarx/google2fa": "^8.0", "robmorgan/phinx": "0.11.*", "shipearly/wkhtmltopdf": "^0.12.5", "shippo/shippo-php": "^2.0", "square/square": "15.0.0.20211020", "stripe/stripe-php": "^15.7", "symfony/polyfill-php80": "^1.24", "taxjar/taxjar-php": "^1.10", "tinymce/tinymce": "^6.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "justinrainbow/json-schema": "^5.2", "phpunit/phpunit": "4.* || 5.*", "symfony/yaml": "^4.4|^5.0"}, "autoload": {"psr-4": {"ShipEarlyApp\\Controller\\Component\\OAuth\\": "Controller/Component/OAuth/", "ShipEarlyApp\\Lib\\": "Lib/", "ShipEarlyApp\\Plugin\\Api\\v1\\Model\\Traits\\": "Plugin/Api/v1/Model/Traits/", "ShipEarlyApp\\Plugin\\Api\\v1\\Test\\Provider\\": "Plugin/Api/v1/Test/Provider/", "ShipEarlyApp\\Plugin\\Api\\v1\\TestSuite\\": "Plugin/Api/v1/TestSuite/", "ShipEarlyApp\\Plugin\\Widgets\\Enum\\": "Plugin/Widgets/Enum/", "ShipEarlyApp\\Test\\Support\\": "Test/Support/"}}, "scripts": {"post-update-cmd": ["cp -f -<PERSON>/fortawesome/font-awesome/css/all.min.css webroot/css/font-awesome/css/all.min.css", "cp -f -R <PERSON>or/fortawesome/font-awesome/webfonts/* webroot/css/font-awesome/webfonts"], "post-install-cmd": ["cp -f -<PERSON>/fortawesome/font-awesome/css/all.min.css webroot/css/font-awesome/css/all.min.css", "cp -f -R <PERSON>or/fortawesome/font-awesome/webfonts/* webroot/css/font-awesome/webfonts"]}, "extra": {"installer-types": ["component"], "installer-paths": {"webroot/js/vendor/{$name}/": ["tinymce/tinymce"]}}}