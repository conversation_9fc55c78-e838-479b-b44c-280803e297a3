<?php

use ShipEarlyApp\Lib\Globals\AppGlobalMethods;
use ShipEarlyApp\Lib\Globals\MockGlobalMethods;

App::uses('ComponentCollection', 'Controller');
App::uses('AppTestAssertionsTrait', 'TestSuite');

/**
 * Trait AppTestCaseTrait.
 *
 * Simulate IntegrationTestCase inheriting members of AppTestCase while not
 * actually being part of the same inheritance hierarchy.
 *
 * @package app.TestSuite
 */
trait AppTestCaseTrait
{
    use AppTestAssertionsTrait;

    /**
     * @var array|string
     * @see https://book.cakephp.org/2/en/development/testing.html#loading-fixtures-in-your-test-cases
     */
    // Explicit declaration of a magic field from parent
    public $fixtures = [];

    public function setUp()
    {
        parent::setUp();

        /**
         * Fix CakeRequest url properties having a base path of the filesystem directory `dirname(env('PHP_SELF'))`.
         *
         * @see CakeRequest::_base()
         */
        Configure::write('App.base', '');

        AppGlobalMethods::setInstance(new MockGlobalMethods());
    }

    /**
     * Returns formatted date string relative to a static time.
     *
     * @param string $strtotime Date string compatible with strtotime().
     * @param string $format Format string compatible with date().
     * @return string|false Formatted date string. If the provided datetime cannot be parsed,
     * false is returned and an E_WARNING level error is emitted.
     * @see strtotime
     * @see date
     */
    public static function format_datetime($strtotime, string $format = 'Y-m-d H:i:s')
    {
        return date($format, strtotime($strtotime, static::time()));
    }

    /**
     * Sets a static timestamp
     *
     * @param bool $reset to set new static timestamp.
     * @return int timestamp
     */
    public static function time(bool $reset = false): int
    {
        return CakeTestSuiteDispatcher::time($reset);
    }

    /**
     * Generates a mocked component and adds it to the provided collection.
     *
     * Similar to generating mocked components with `ControllerTestCase::generate` but without requiring a controller.
     *
     * @param string $name
     * @param array $methods Subset of methods to mock. Empty array mocks all methods.
     * @param ComponentCollection $collection
     * @param array $config
     * @return Component|PHPUnit_Framework_MockObject_MockObject
     * @see ControllerTestCase::generate
     */
    public function getMockForComponent(string $name, array $methods, ComponentCollection $collection, array $config = []): object
    {
        list($plugin, $name) = pluginSplit($name, true);
        $class = $name . 'Component';

        App::uses($class, $plugin . 'Controller/Component');
        if (!class_exists($class)) {
            throw new MissingComponentException(compact('class'));
        }

        /** @var AppTestCase|IntegrationTestCase $this */
        $mock = $this->getMockBuilder($class)->setMethods($methods)->setConstructorArgs([$collection, $config])->getMock();

        $collection->set($name, $mock);
        $collection->enable($name);

        return $mock;
    }

    /**
     * Enables invoking of a protected or private method.
     *
     * @param callable $method Array reference to a callback: [$object, 'methodName']
     * @param mixed ...$args Parameters to be passed to the method
     * @return mixed Return value of the method
     * @throws ReflectionException if the class or method does not exist.
     */
    // Omitting param type `callable $method` because private method references are rejected
    public function callPrivateMethod($method, ...$args)
    {
        $object = $method[0];
        $methodName = $method[1];

        $reflectionMethod = new ReflectionMethod($object, $methodName);
        $reflectionMethod->setAccessible(true);

        return $reflectionMethod->invoke($object, ...$args);
    }

    /**
     * Default subdomain for the test environment.
     *
     * Useful for URL assertions in tests run from an environment where MAIN_BASE_URL has a subdomain.
     *
     * @return string
     */
    public function defaultSubdomain(): string
    {
        static $defaultSubdomain = null;

        if ($defaultSubdomain === null) {
            App::uses('SubdomainRoute', 'Routing/Route');
            $defaultSubdomain = SubdomainRoute::parseSubdomain(parse_url(MAIN_BASE_PATH, PHP_URL_HOST));
        }

        return $defaultSubdomain;
    }

    /**
     * Subdomain different from the default subdomain.
     *
     * @return string
     */
    public function otherSubdomain(): string
    {
        return 'test_' . $this->defaultSubdomain();
    }

    protected function seedRelativeInventoryTransferDates()
    {
        /** @var InventoryTransfer $InventoryTransfer */
        $InventoryTransfer = ClassRegistry::init('InventoryTransfer');
        if (!$InventoryTransfer->saveMany([
            ['id' => '1', 'expected_arrival_date' => static::format_datetime('today', 'Y-m-d')],
            ['id' => '2', 'expected_arrival_date' => static::format_datetime('today', 'Y-m-d')],
            ['id' => '3', 'expected_arrival_date' => static::format_datetime('tomorrow', 'Y-m-d')],
        ])) {
            throw new RuntimeException(json_encode(['message' => 'Failed to seed inventory transfers', 'errors' => $InventoryTransfer->validationErrors]));
        }
        $InventoryTransfer->clear();
    }
}
