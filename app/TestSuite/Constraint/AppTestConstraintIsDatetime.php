<?php

App::uses('Validation', 'Utility');

class AppTestConstraintIsDatetime extends PHPUnit_Framework_Constraint
{
    protected $format;

    public function __construct(string $format = 'Y-m-d H:i:s')
    {
        parent::__construct();
        $this->format = $format;
    }

    protected function matches($other)
    {
        $dateTime = DateTime::createFromFormat($this->format, $other);
        $formatted = ($dateTime) ? $dateTime->format('Y-m-d H:i:s') : null;

        return Validation::datetime($formatted);
    }

    public function toString()
    {
        return "is a datetime with format '{$this->format}'";
    }
}
