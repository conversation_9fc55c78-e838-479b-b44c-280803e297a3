<?php
App::uses('CakeTestFixture', 'TestSuite/Fixture');

/**
 * TableCopyTestFixture copies fixture data from a seed database alleviating
 * the need to maintain PHP-based fixture configurations.
 *
 * @package app.TestSuite.Fixture
 */
abstract class TableCopyTestFixture extends CakeTestFixture
{
    /**
     * Table data hashes used to detect if data was modified.
     *
     * @var string[]
     */
    protected static $_tableHashes = [];

    /**
     * @var array|string
     * @see https://book.cakephp.org/2/en/development/testing.html#importing-table-information-and-records
     */
    // Explicit declaration of a magic field from parent
    public $import = null;

    /**
     * The database config name from which the table will be copied.
     *
     * @var string
     */
    public $sourceDbConfig = 'test_seed';

    /**
     * Override the configured Db prefix
     * @var ?string
     */
    public $tablePrefixOverride = null;

    public function create($db)
    {
        if ($this->tableUnmodified($db)) {
            return true;
        }

        if (!empty($this->fields)) {
            return parent::create($db);
        }

        try {
            $targetTable = $this->fullTableName($db, $this->table);
            $sourceTable = $this->fullTableName($this->sourceDb(), $this->table);
            $result = $db->execute("CREATE TABLE {$targetTable} LIKE {$sourceTable};", ['log' => false]);
            if ($result) {
                $this->created[] = $db->configKeyName;
            }
            return $result;
        } catch (Exception $e) {
            $msg = __d('cake_dev', 'Fixture creation for "%s" failed "%s"', $this->table, $e->getMessage());
            CakeLog::error($msg);
            trigger_error($msg, E_USER_WARNING);
            return false;
        }
    }

    public function drop($db)
    {
        if ($this->tableUnmodified($db)) {
            return true;
        }

        $this->disableForeignKeyChecks($db);
        try {
            if (!empty($this->fields)) {
                $result = parent::drop($db);
                if ($result) {
                    $this->dropTableHash($db);
                }
                return $result;
            }

            try {
                $targetTable = $db->fullTableName($this->table);
                $result = $db->execute("DROP TABLE IF EXISTS {$targetTable};", ['log' => false]);
                if ($result) {
                    $this->created = array_diff($this->created, [$db->configKeyName]);
                    $this->dropTableHash($db);
                }
                return $result;
            } catch (Exception $e) {
                return false;
            }
        } finally {
            $this->reenableForeignKeyChecks($db);
        }
    }

    public function insert($db)
    {
        if ($this->tableUnmodified($db)) {
            return true;
        }

        $this->disableForeignKeyChecks($db);
        try {
            if (!empty($this->records)) {
                if (empty($this->fields)) {
                    $this->fields = $db->describe($this->table);
                }
                $result = parent::insert($db);
            } else {
                $targetTable = $db->fullTableName($this->table);
                $sourceTable = $this->sourceDb()->fullTableName($this->table);
                $result = $db->execute("INSERT INTO {$targetTable} SELECT * FROM {$sourceTable};", ['log' => false]);
            }
            if ($result) {
                $this->updateTableHash($db);
            }
            return $result;
        } finally {
            $this->reenableForeignKeyChecks($db);
        }
    }

    public function truncate($db)
    {
        if ($this->tableUnmodified($db)) {
            return true;
        }

        $this->disableForeignKeyChecks($db);
        try {
            $result = parent::truncate($db);
            if ($result) {
                $this->dropTableHash($db);
            }
            return $result;
        } finally {
            $this->reenableForeignKeyChecks($db);
        }
    }

    protected function tableUnmodified(DboSource $db): bool
    {
        $checksum = $this->fetchTableHash($db);
        return ($checksum && $checksum === $this->checksum($db));
    }

    protected function fetchTableHash(DboSource $db): ?string
    {
        return $this->getTableHash();
    }

    protected function updateTableHash(DboSource $db): void
    {
        $this->setTableHash($this->checksum($db));
    }

    protected function dropTableHash(DboSource $db): void
    {
        $this->clearTableHash();
    }

    protected function getTableHash(): ?string
    {
        return static::$_tableHashes[$this->table] ?? null;
    }

    protected function setTableHash($checksum): void
    {
        static::$_tableHashes[$this->table] = $checksum;
    }

    protected function clearTableHash(): void
    {
        unset(static::$_tableHashes[$this->table]);
    }

    protected function checksum(DboSource $db)
    {
        $targetTable = $db->fullTableName($this->table);
        $db->execute("CHECKSUM TABLE {$targetTable};", ['log' => false]);
        $data = $db->fetchRow();
        return $data[0]['Checksum'] ?? null;
    }

    protected function sourceDb(): DboSource
    {
        /** @var DboSource $sourceDb */
        $sourceDb = ConnectionManager::getDataSource($this->sourceDbConfig);
        $sourceDb->cacheSources = false;
        return $sourceDb;
    }

    protected function disableForeignKeyChecks(DboSource $db)
    {
        $db->execute('/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;', ['log' => false]);
    }

    protected function reenableForeignKeyChecks(DboSource $db)
    {
        $db->execute('/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;', ['log' => false]);
    }

    protected function fullTableName(DboSource $db, $table)
    {
        $fullName = $db->fullTableName($table);
        if (!empty($this->tablePrefixOverride) && strpos($fullName, $db->config['prefix']) !== 0) {
            $fullName = str_replace($db->config['prefix'], $this->tablePrefixOverride, $fullName);
        }

        return $fullName;
    }
}
