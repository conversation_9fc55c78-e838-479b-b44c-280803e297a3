<?php

App::uses('AppTestConstraintIsDatetime', 'TestSuite/Constraint');
App::uses('AppTestConstraintStringMatchesJsonSchema', 'TestSuite/Constraint');

trait AppTestAssertionsTrait
{
    public static function assertDatetime(string $actual, string $format = 'Y-m-d H:i:s', string $message = ''): void
    {
        PHPUnit_Framework_Assert::assertThat($actual, static::isDatetime($format), $message);
    }

    public static function assertStringMatchesJsonSchema(array $expectedSchema, string $actualJson, string $message = ''): void
    {
        PHPUnit_Framework_Assert::assertThat($actualJson, static::matchesJsonSchema($expectedSchema), $message);
    }

    public static function isDatetime(string $format = 'Y-m-d H:i:s'): AppTestConstraintIsDatetime
    {
        return new AppTestConstraintIsDatetime($format);
    }

    public static function matchesJsonSchema(array $schema): AppTestConstraintStringMatchesJsonSchema
    {
        return new AppTestConstraintStringMatchesJsonSchema($schema);
    }
}
