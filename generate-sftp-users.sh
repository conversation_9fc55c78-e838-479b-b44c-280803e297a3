#!/bin/bash

OUTPUT=sftp-users.csv
# Create the CSV file with a header line if it does not exist
if [ ! -f $OUTPUT ]; then
  echo Username,Password > $OUTPUT
fi

N=1; TOTAL=${1:-1}
while [ $N -le $TOTAL ]; do
  # Generate a unique suffix of 8 random hex digits eg. shipearly-89abcdef
  USERNAME=shipearly-$(openssl rand -hex 4)
  USER_DIR=/data/sftp/$USERNAME

  echo "Creating user $N of $TOTAL in $USER_DIR ..."

  # Add a new random user with no login option
  sudo useradd -d $USER_DIR -s /sbin/nologin $USERNAME || continue
  sudo usermod -aG sftp_users $USERNAME

  # Create a working directory for the new user
  sudo mkdir -p $USER_DIR/upload
  sudo chown -R $USERNAME:$USERNAME $USER_DIR/upload
  # Set extended permissions so the sftp_sync user can RWX
  sudo chmod -R 777 $USER_DIR/upload

  # Set a random password with random chars from the OWASP Special Characters list
  # Excluding possible deliminators for this script and output CSV file ["',:]
  # https://unix.stackexchange.com/a/230676
  PASSWORD=$(LC_ALL=C tr -dc 'A-Za-z0-9!#$%&()*+-./;<=>?@[\]^_`{|}~' </dev/urandom | head -c 16)
  echo $USERNAME:$PASSWORD | sudo chpasswd

  # Output CSV
  echo $USERNAME,$PASSWORD >> $OUTPUT

  ((N++))
done

echo "Passwords saved to $OUTPUT"
while [ $N -le $TOTAL ]; do
done
