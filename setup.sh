#!/bin/bash
echo "Begin setup.sh"
echo ""
echo "Setup required temp directories..."
# See https://book.cakephp.org/2.0/en/installation.html#permissions
HTTPDUSER=`ps aux | grep -E '[a]pache|[h]ttpd|[_]www|[w]ww-data|[n]ginx' | grep -v root | head -1 | cut -d\  -f1`
sudo setfacl -R -m u:${HTTPDUSER}:rwx app/tmp
sudo setfacl -R -d -m u:${HTTPDUSER}:rwx app/tmp
for TMP_DIR in \
    "app/tmp/cache/models/" \
    "app/tmp/cache/persistent/" \
    "app/tmp/cache/views/" \
    "app/tmp/logs/" \
    "app/webroot/files/"
do
    echo ${TMP_DIR}
    sudo mkdir -p ${TMP_DIR}
    sudo chmod -R 777 ${TMP_DIR}
done
echo ""
echo "Global permission assignments..."
echo "app/Controller/service_log.txt"
sudo chmod -R 777 app/Controller/service_log.txt
echo "app/Controller/webhookservicelog.txt"
sudo chmod -R 777 app/Controller/webhookservicelog.txt
echo "app/Plugin/Quickbook/Vendor/service_log.xml"
sudo chmod -R 777 app/Plugin/Quickbook/Vendor/service_log.xml
echo "Sql/"
sudo chmod -R 777 Sql/
echo ""
echo "app/composer.phar downloading..."
( cd ./app/ && ./composer-setup.sh )
echo "app/composer.phar installed"
if [ "$1" = "--dev" ]; then
    echo "Installing app/Vendor composer dev dependencies..."
    ( cd ./app/ && php ./composer.phar install )
#    echo "Installing ShipearlyTests composer dev dependencies..."
#    ( cd ./ShipearlyTests/ && php ../app/composer.phar install )
    echo ""
    echo "Installing app NPM dev dependencies..."
    ( cd ./app/ && npm ci && npm run build )
    echo ""
    ./app/Vendor/bin/phinx migrate -e development -vv
else
    echo "Installing app/Vendor composer dependencies..."
    ( cd ./app/ && php composer.phar install --optimize-autoloader --no-dev )
    echo ""
    echo "Installing app NPM dependencies..."
    NODE_ENV=production
    ( cd ./app/ && npm ci && npm run build )
    echo ""
    ./app/Vendor/bin/phinx migrate -e production -vv
fi
app/Console/cake cache clear_all
echo ""
if [ "$(echo ${PWD}/ | grep /webroot/shopify/)" ]; then
    echo "Skipping crontab config from inside the /webroot/shopify/ directory"
elif [ "$1" = "--dev" ]; then
    echo "Skipping crontab config for dev environment. Can be added by running:"
    echo "  ( cd ./cron/ && sh generate-crontab.sh | sudo crontab - )"
#else
#    echo "Updating crontab for su..."
#    ( cd ./cron/ && sh generate-crontab.sh | sudo crontab - )
fi
echo ""
echo "End setup.sh"
